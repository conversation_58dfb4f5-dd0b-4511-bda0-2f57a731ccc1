#!/bin/bash

# FMB Java项目生产环境部署脚本
# 适用于CentOS/Ubuntu/Debian系统

echo "=== FMB Java项目生产环境部署开始 ==="

# 1. 更新系统
echo "1. 更新系统包..."
if [ -f /etc/redhat-release ]; then
    # CentOS/RHEL
    yum update -y
    yum install -y git wget curl
elif [ -f /etc/debian_version ]; then
    # Ubuntu/Debian
    apt-get update
    apt-get upgrade -y
    apt-get install -y git wget curl
fi

# 2. 安装Docker
echo "2. 安装Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker

# 3. 安装Docker Compose
echo "3. 安装Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 4. 创建项目目录
echo "4. 创建项目目录..."
mkdir -p /opt/fmb-java
cd /opt/fmb-java

# 5. 克隆代码（需要替换为你的Git仓库地址）
echo "5. 克隆项目代码..."
# git clone https://github.com/your-username/fmb-java.git .
echo "请手动上传项目代码到 /opt/fmb-java 目录"

# 6. 创建生产环境配置
echo "6. 创建生产环境配置..."
cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  fmb-java:
    build: .
    ports:
      - "8070:8070"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_DYNAMIC_DATASOURCE_MASTER_URL=****************************************************************************************************************************************************************
      - SPRING_DATASOURCE_DYNAMIC_DATASOURCE_SLAVE_URL=****************************************************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=your_redis_password_here
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - fmb-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: your_strong_password_here
      MYSQL_DATABASE: fmb_new
      MYSQL_USER: fmb
      MYSQL_PASSWORD: your_db_password_here
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup:/backup
    restart: unless-stopped
    networks:
      - fmb-network

  redis:
    image: redis:6.2-alpine
    command: redis-server --requirepass your_redis_password_here
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - fmb-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - fmb-java
    restart: unless-stopped
    networks:
      - fmb-network

volumes:
  mysql_data:
  redis_data:

networks:
  fmb-network:
    driver: bridge
EOF

# 7. 创建Nginx配置
echo "7. 创建Nginx配置..."
cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream fmb_backend {
        server fmb-java:8070;
    }

    server {
        listen 80;
        server_name your-domain.com;  # 替换为你的域名

        location / {
            proxy_pass http://fmb_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF

# 8. 创建备份脚本
echo "8. 创建备份脚本..."
cat > backup.sh << 'EOF'
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/opt/fmb-java/backup"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份MySQL数据库
docker exec -i fmb-java_mysql_1 mysqldump -u root -pyour_strong_password_here fmb_new > $BACKUP_DIR/fmb_new_$DATE.sql

# 备份Redis数据
docker exec -i fmb-java_redis_1 redis-cli --rdb /data/dump_$DATE.rdb

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

chmod +x backup.sh

# 9. 设置定时备份
echo "9. 设置定时备份..."
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/fmb-java/backup.sh") | crontab -

# 10. 创建启动脚本
echo "10. 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash
cd /opt/fmb-java
docker-compose -f docker-compose.prod.yml up -d
EOF

chmod +x start.sh

# 11. 创建重启脚本
cat > restart.sh << 'EOF'
#!/bin/bash
cd /opt/fmb-java
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --build
EOF

chmod +x restart.sh

echo "=== 部署脚本准备完成 ==="
echo "接下来需要手动操作："
echo "1. 上传项目代码到 /opt/fmb-java"
echo "2. 修改 docker-compose.prod.yml 中的密码"
echo "3. 修改 nginx.conf 中的域名"
echo "4. 执行 ./start.sh 启动项目"
echo "5. 配置SSL证书（如需要HTTPS）"