
#### 项目整体打包
```java
项目根目录下执行
mvn clean package   -DskipTests


mvn clean install -Dmaven.test.skip=true

```



#### 数据库设计约定
```

1. 新表以"fmbx_" 开头

2. 各字段默认都不为空 
datetime 默认
0000-00-00 00:00:00

date 默认
0000-00-00

3. type类型的 字段说明需要使用如下格式
字段说明后以':'号分隔,  各选项间','分割,字段值数字在前,'-'后跟含义
门店品类:1-酒店,2-演出/展览,3-场馆/乐园,4-亲子活动

4. 数据库字符集使用  

  CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

5.字段类型tinyint 改为使用smallint

```


#### url路径规划
```

所有请求开头都是 fmbx

server:
    servlet:
        context-path: /fmbx
        
        
父母邦后台       /fmbx/admin
商家后台         /fmbx/bp

公共服务         /fmbx/publictool



``` 


### 疑难问题
```
RestResponseEntityExceptionHandler 和 GlobalExceptionHandler 有冲突
加 RestResponseEntityExceptionHandler 是为了捕捉特定异常(因为GlobalExceptionHandler无法捕获,譬如访问不存在的路径 )后统一json格式
但发现一旦 jar 方式运行,之前正常的校验@valid 请求参数,  返回 也被 RestResponseEntityExceptionHandler校验了


```



#### 定时任务
```

CrontabService

```


### 从类路径读取文件
```java


        String jsonFile = "" ;
        try {

            jsonFile =  IOUtils.toString(new ClassPathResource("fmbadmintree.json").getInputStream(),"UTF-8") ;

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

```

### 关于涉及 增删改 接口 需要统一返回
```

方法开始需要统一
result.put("result", 0);

方法正常结束
result.put("result", 1);

```


## 关于前台接口  230107
1. controller 需要统一放在 src/main/java/com/fmb/server2022/controller/front 目录下并以Controller结尾
2. src/main/java/com/fmb/server2022/config/filter/FrontApiAspect.java 类里对前台接口统一做 鉴权处理
```
请求必须是post的 
url地址后携带sign=加密校验值
请求体是json字符串  
sign值是 md5(json字符串+APP对应的加密串)  ,字符串为全小写字符

```
3. FrontApiAspect 里同时兼容老系统的用户前台用户信息
```
1. 优先读取post请求体里的uid参数, 通过解密方法获得原始uid 放入MDC
2. 1不存在尝试读取header里的fsid , 并读取php redis里的session 信息 
如果能读取到最终也放入MDC

putUidInfoToMdc方法
MDC.put(FmbxFront_UID_KEY, JSON.toJSONString(uMap)); 

```


### redis 分布式锁
```

RedissonLockService.doLockJob 方法 


```

### redis 缓存
```

@Cacheable(value = "queryFmbUsers#3600", keyGenerator = "customKeyGenerator")

queryFmbUsers#3600 3600单位是秒  queryFmbUsers前面是名字


```


### 数据库字段名 是关键字 问题解决
```java

    @TableField(value = "`desc`")
    private String desc;

```


### 手动切换数据源
```java
切到从库
        DynamicDataSourceContextHolder.push(DB_slave);
切到主
        DynamicDataSourceContextHolder.push(DB_master);


```
