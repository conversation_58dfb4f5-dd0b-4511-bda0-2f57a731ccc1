# FMB 酒店预订系统 - 架构设计指南

## 📋 目录
- [项目概述](#项目概述)
- [架构模式](#架构模式)
- [数据模型](#数据模型)
- [身份验证](#身份验证)
- [开发规范](#开发规范)

---

## 🏗️ 项目概述

### 基本信息
- **项目名称**: FMB (蜂鸣宝) 酒店预订系统
- **技术栈**: Java 8 + Spring Boot 2.3.7 + MyBatis Plus
- **架构类型**: 多模块企业级应用

### 模块结构
```
fmb.java/
├── common/          # 公共工具类、异常处理、用户会话管理
├── fmbmapper/       # 数据访问层 (实体类、Mapper接口、Service)
└── server2022/      # 服务层和控制器 (主应用模块)
```

### 核心业务领域
1. **酒店管理** - Suite(套房)、Room(房间)、Sku(库存单元)
2. **订单管理** - Order(订单)、Payment(支付)、Cancel(取消)
3. **用户管理** - User(用户)、Coupon(优惠券)、Session(会话)
4. **活动管理** - Activity(活动)、Tag(标签)、Category(分类)
5. **商户管理** - BP(Business Partner)、Contract(合同)、Media(媒体)

### 技术栈详情
- **后端框架**: Spring Boot 2.3.7 + Spring MVC
- **ORM框架**: MyBatis Plus + 动态数据源
- **数据库**: MySQL (主从分离)
- **缓存**: Redis + Caffeine
- **消息队列**: Kafka + RocketMQ
- **连接池**: Druid
- **工具库**: Lombok、FastJSON、Commons

---

## 🎯 架构模式

### 1. 分层架构 (Layered Architecture)
```
┌─────────────────────────────────────┐
│           Controller Layer          │  ← HTTP请求处理
├─────────────────────────────────────┤
│            Service Layer            │  ← 业务逻辑处理
├─────────────────────────────────────┤
│            Mapper Layer             │  ← 数据访问处理
├─────────────────────────────────────┤
│            Entity Layer             │  ← 数据模型定义
└─────────────────────────────────────┘
```

**实现规范**:
- Controller只处理HTTP请求响应，不包含业务逻辑
- Service层实现具体业务逻辑，可调用多个Mapper
- Mapper层只负责数据库操作，不包含业务逻辑
- Entity层定义数据模型，使用MyBatis Plus注解

### 2. 模块化架构 (Modular Architecture)
```java
// 模块依赖关系
server2022 → fmbmapper → common
     ↓           ↓         ↓
   业务层      数据层    工具层
```

**模块职责**:
- **common**: 基础工具、异常定义、会话管理
- **fmbmapper**: 实体类、Mapper接口、Service接口及实现
- **server2022**: Controller、配置类、业务Service实现

### 3. MVC模式实现
```java
// Controller示例
@RestController
@RequestMapping("/fmbx/admin")
public class AdminController {
    @Autowired
    private AdminService adminService;
    
    @PostMapping("/login")
    public FmbRespBean login(@RequestBody @Valid LoginRequest req) {
        return adminService.login(req);
    }
}

// Service示例  
@Service
public class AdminServiceImpl implements AdminService {
    @Autowired
    private FmbAdminMapper fmbAdminMapper;
    
    public FmbRespBean login(LoginRequest req) {
        // 业务逻辑实现
    }
}
```

### 4. 数据访问模式 (DAO Pattern)
```java
// 实体类
@TableName("fmb_admin")
public class FmbAdmin {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;
    // ...
}

// Mapper接口
@Mapper
public interface FmbAdminMapper extends BaseMapper<FmbAdmin> {
    // 自定义查询方法
}

// Service接口
public interface IFmbAdminService extends IService<FmbAdmin> {
    // 业务方法定义
}
```

### 5. 企业级模式

#### 读写分离模式
```java
// 手动切换数据源
@Service
public class UserService {
    public User getUser(Long id) {
        // 切到从库读取
        DynamicDataSourceContextHolder.push("slave");
        return userMapper.selectById(id);
    }
    
    public void updateUser(User user) {
        // 切到主库写入
        DynamicDataSourceContextHolder.push("master");
        userMapper.updateById(user);
    }
}
```

#### 缓存模式
```java
// Redis缓存注解使用
@Cacheable(value = "queryFmbUsers#3600", keyGenerator = "customKeyGenerator")
public List<FmbUser> queryUsers(UserQuery query) {
    return userMapper.selectList(query);
}

// 缓存失效
@CacheEvict(value = "queryFmbUsers", allEntries = true)
public void updateUser(FmbUser user) {
    userMapper.updateById(user);
}
```

#### 分布式锁模式
```java
// Redis分布式锁使用
@Service
public class OrderService {
    public void createOrder(OrderRequest req) {
        String lockKey = "order_lock_" + req.getUserId();
        RedissonLockService.doLockJob(lockKey, 30, () -> {
            // 订单创建业务逻辑
            return processOrder(req);
        });
    }
}
```

#### 消息队列模式
```java
// Kafka消息发送
@Service
public class OrderEventService {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    public void publishOrderEvent(OrderEvent event) {
        kafkaTemplate.send("order_topic", JSON.toJSONString(event));
    }
}

// PHP-Java Redis队列通信
// PHP端写入
$order_data = array(
    'order_sn' => $order_info['order_sn'],
    'type' => 'pay_success_order',
    'pay_sn' => $trade_no
);
$redis->LPUSH('fmb_queue_lists', json_encode($order_data));

// Java端消费
@Component
public class QueueConsumer {
    @Scheduled(fixedDelay = 1000)
    public void consumeQueue() {
        String message = redisTemplate.opsForList().rightPop("fmb_queue_lists");
        // 处理消息
    }
}
```

### 6. 多租户架构
```java
// URL路径规划
/fmbx/admin/*    - 管理后台
/fmbx/bp/*       - 商户后台  
/fmbx/front/*    - 前台API
/fmbx/publictool/* - 公共服务
```

---

## 📊 数据模型

### 核心数据模型关系图
```
酒店核心模型:
FmbxSuite (套房) 
    ├── FmbxSuiteExt (扩展信息)
    ├── FmbxSku (库存单元)
    ├── FmbxSuiteStock (库存)
    ├── FmbxSuiteRoomdatePrice (日期价格)
    └── FmbxSuiteRoomSku (房型-SKU关联)

订单核心模型:
FmbOrderInfo (订单主表)
    ├── FmbOrderGoods (订单商品)
    ├── FmbOrderHotel (酒店订单)
    ├── FmbOrderActions (操作记录)
    ├── FmbPayOrders (支付订单)
    └── FmbOrderSettleDetails (结算明细)

用户体系模型:
FmbUsers (用户主表)
    ├── FmbUserExtinfo (扩展信息)
    ├── FmbUserCoupon (用户优惠券)
    └── FmbUserCashLog (资金日志)
```

### 1. 酒店核心模型

#### 酒店房型体系
```java
// 套房/房型主表
@TableName("fmbx_suite")
public class FmbxSuite {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String suiteName;      // 房型名称
    private Integer suiteType;     // 房型类型
    private BigDecimal basePrice;  // 基础价格
    private Integer stockTotal;    // 总库存
    // ...
}

// 房型扩展信息
@TableName("fmbx_suite_ext") 
public class FmbxSuiteExt {
    private Long suiteId;          // 关联套房ID
    private String description;    // 房型描述
    private String facilities;     // 设施信息
    private String images;         // 图片列表
    // ...
}

// 库存单元
@TableName("fmbx_sku")
public class FmbxSku {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long suiteId;          // 关联套房ID
    private String skuCode;        // SKU编码
    private Integer stock;         // 可用库存
    private BigDecimal price;      // SKU价格
    // ...
}
```

#### 库存价格模型
```java
// 套房库存表
@TableName("fmbx_suite_stock")
public class FmbxSuiteStock {
    private Long suiteId;          // 套房ID
    private Date stockDate;        // 库存日期
    private Integer totalStock;    // 总库存
    private Integer usedStock;     // 已用库存
    private Integer availableStock;// 可用库存
    // ...
}

// 房间日期价格
@TableName("fmbx_suite_roomdate_price")
public class FmbxSuiteRoomdatePrice {
    private Long suiteId;          // 套房ID
    private Date priceDate;        // 价格日期
    private BigDecimal weekdayPrice;  // 平日价格
    private BigDecimal weekendPrice;  // 周末价格
    private BigDecimal holidayPrice;  // 节假日价格
    // ...
}

// 库存操作记录
@TableName("fmbx_suite_stock_operate")
public class FmbxSuiteStockOperate {
    private Long suiteId;
    private Date operateDate;
    private Integer operateType;   // 操作类型：1-增加，2-减少
    private Integer operateNum;    // 操作数量
    private String operateReason;  // 操作原因
    private Long operatorId;       // 操作人ID
    // ...
}
```

### 2. 订单核心模型

#### 订单主体系
```java
// 订单主表
@TableName("fmb_order_info")
public class FmbOrderInfo {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String orderSn;        // 订单号
    private Long userId;           // 用户ID
    private Integer orderStatus;   // 订单状态
    private BigDecimal totalAmount;// 订单总金额
    private BigDecimal payAmount;  // 实付金额
    private Date createTime;       // 创建时间
    private Date payTime;          // 支付时间
    // ...
}

// 订单商品表
@TableName("fmb_order_goods")
public class FmbOrderGoods {
    private Long orderId;          // 订单ID
    private Long suiteId;          // 套房ID
    private Long skuId;            // SKU ID
    private String goodsName;      // 商品名称
    private Integer quantity;      // 数量
    private BigDecimal price;      // 单价
    private BigDecimal totalPrice; // 总价
    // ...
}

// 酒店订单扩展
@TableName("fmb_order_hotel")
public class FmbOrderHotel {
    private Long orderId;          // 订单ID
    private Date checkInDate;      // 入住日期
    private Date checkOutDate;     // 退房日期
    private Integer nights;        // 住宿夜数
    private Integer rooms;         // 房间数
    private Integer adults;        // 成人数
    private Integer children;      // 儿童数
    private String contactName;    // 联系人姓名
    private String contactPhone;   // 联系人电话
    // ...
}
```

#### 订单流程模型
```java
// 订单操作记录
@TableName("fmb_order_actions")
public class FmbOrderActions {
    private Long orderId;          // 订单ID
    private Integer actionType;    // 操作类型
    private String actionNote;     // 操作备注
    private Long operatorId;       // 操作人ID
    private Date actionTime;       // 操作时间
    // ...
}

// 订单取消
@TableName("fmb_order_cancel")
public class FmbOrderCancel {
    private Long orderId;          // 订单ID
    private Integer cancelType;    // 取消类型：1-用户取消，2-系统取消
    private String cancelReason;   // 取消原因
    private BigDecimal refundAmount; // 退款金额
    private Date cancelTime;       // 取消时间
    // ...
}

// 支付订单
@TableName("fmb_pay_orders")
public class FmbPayOrders {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String paySn;          // 支付单号
    private Long orderId;          // 关联订单ID
    private Integer payType;       // 支付方式
    private Integer payStatus;     // 支付状态
    private BigDecimal payAmount;  // 支付金额
    private String thirdPaySn;     // 第三方支付单号
    private Date payTime;          // 支付时间
    // ...
}
```

### 3. 用户体系模型

#### 用户核心模型
```java
// 用户主表
@TableName("fmb_users")
public class FmbUsers {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;       // 用户名
    private String phone;          // 手机号
    private String email;          // 邮箱
    private String nickname;       // 昵称
    private String avatar;         // 头像
    private Integer userStatus;    // 用户状态
    private Date registerTime;     // 注册时间
    private Date lastLoginTime;    // 最后登录时间
    // ...
}

// 用户扩展信息
@TableName("fmb_user_extinfo")
public class FmbUserExtinfo {
    private Long userId;           // 用户ID
    private String realName;       // 真实姓名
    private String idCard;         // 身份证号
    private Integer gender;        // 性别
    private Date birthday;         // 生日
    private String address;        // 地址
    private Integer userLevel;     // 用户等级
    private BigDecimal balance;    // 账户余额
    private Integer points;        // 积分
    // ...
}

// 用户资金变动
@TableName("fmb_user_cash_change")
public class FmbUserCashChange {
    private Long userId;           // 用户ID
    private Integer changeType;    // 变动类型
    private BigDecimal changeAmount; // 变动金额
    private BigDecimal beforeBalance; // 变动前余额
    private BigDecimal afterBalance;  // 变动后余额
    private String changeReason;   // 变动原因
    private Date changeTime;       // 变动时间
    // ...
}
```

### 4. 营销活动模型

#### 优惠券体系
```java
// 优惠券类型
@TableName("fmb_coupon_type")
public class FmbCouponType {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String couponName;     // 优惠券名称
    private Integer couponType;    // 优惠券类型：1-满减，2-折扣，3-立减
    private BigDecimal faceValue;  // 面值
    private BigDecimal minAmount;  // 最低使用金额
    private Date validStart;       // 有效期开始
    private Date validEnd;         // 有效期结束
    private Integer totalCount;    // 发行总数
    private Integer usedCount;     // 已使用数量
    // ...
}

// 用户优惠券
@TableName("fmb_user_coupon")
public class FmbUserCoupon {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;           // 用户ID
    private Long couponTypeId;     // 优惠券类型ID
    private String couponCode;     // 优惠券码
    private Integer couponStatus;  // 状态：1-未使用，2-已使用，3-已过期
    private Date getTime;          // 获取时间
    private Date useTime;          // 使用时间
    private Long orderId;          // 使用订单ID
    // ...
}
```

#### 活动模型
```java
// 活动主表
@TableName("fmbx_activity")
public class FmbxActivity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String activityName;   // 活动名称
    private Integer activityType;  // 活动类型
    private String activityDesc;   // 活动描述
    private Date startTime;        // 开始时间
    private Date endTime;          // 结束时间
    private Integer activityStatus; // 活动状态
    private Integer participantLimit; // 参与人数限制
    private Integer currentParticipants; // 当前参与人数
    // ...
}

// 活动扩展信息
@TableName("fmbx_activity_ext")
public class FmbxActivityExt {
    private Long activityId;       // 活动ID
    private String bannerImage;    // 横幅图片
    private String detailImages;   // 详情图片列表
    private String activityRules;  // 活动规则
    private String prizes;         // 奖品信息
    private String terms;          // 活动条款
    // ...
}
```

### 5. 商户合作模型

#### 商户主体系
```java
// 商户主表
@TableName("fmbx_bp_main")
public class FmbxBpMain {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String bpName;         // 商户名称
    private String bpCode;         // 商户编码
    private Integer bpType;        // 商户类型
    private Integer bpStatus;      // 商户状态
    private String contactPerson;  // 联系人
    private String contactPhone;   // 联系电话
    private String businessLicense; // 营业执照
    private Date cooperationStart; // 合作开始时间
    private Date cooperationEnd;   // 合作结束时间
    // ...
}

// 商户合同
@TableName("fmbx_bp_contract")
public class FmbxBpContract {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long bpId;             // 商户ID
    private String contractNo;     // 合同编号
    private String contractName;   // 合同名称
    private Date signDate;         // 签署日期
    private Date effectiveDate;    // 生效日期
    private Date expiryDate;       // 到期日期
    private BigDecimal commissionRate; // 佣金比例
    private String contractFile;   // 合同文件路径
    private Integer contractStatus; // 合同状态
    // ...
}

// 商户服务
@TableName("fmbx_bps")
public class FmbxBps {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long bpId;             // 商户ID
    private String serviceName;    // 服务名称
    private Integer serviceType;   // 服务类型
    private String serviceDesc;    // 服务描述
    private BigDecimal basePrice;  // 基础价格
    private Integer serviceStatus; // 服务状态
    private String serviceImages;  // 服务图片
    // ...
}
```

### 数据模型设计原则

#### 1. 命名规范
```java
// 表名规范
新表以 "fmbx_" 开头
历史表保持原有前缀 "fmb_"

// 字段规范
- 主键统一使用 id，自增类型
- 外键使用 关联表名_id 格式
- 时间字段使用 _time 后缀
- 状态字段使用 _status 后缀
- 类型字段使用 _type 后缀
```

#### 2. 字段设计约定
```java
// 默认值约定
- 各字段默认都不为空
- datetime 默认: 1970-01-01 00:00:00  
- date 默认: 0000-00-00
- 字符集: CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
- tinyint 类型改为使用 smallint

// 枚举字段说明格式
// 门店品类:1-酒店,2-演出/展览,3-场馆/乐园,4-亲子活动
private Integer storeCategory;
```

#### 3. 关联关系设计
```java
// 一对一关系 - 主表+扩展表模式
FmbxSuite ←→ FmbxSuiteExt

// 一对多关系 - 外键关联
FmbOrderInfo ←→ FmbOrderGoods (一个订单多个商品)

// 多对多关系 - 中间表关联  
SysUser ←→ SysUserRole ←→ SysRole

// 时间维度关系 - 按日期分片
FmbxSuite ←→ FmbxSuiteRoomdatePrice (按日期的价格)
```

---

## 🔐 身份验证

### 验证架构概览
```
┌─────────────────────────────────────┐
│          前台API身份验证             │
│     (签名验证 + UID解密)            │
├─────────────────────────────────────┤
│          管理后台身份验证            │
│     (Token验证 + RBAC权限)          │
├─────────────────────────────────────┤
│          商户后台身份验证            │
│     (商户认证 + 合同状态)           │
├─────────────────────────────────────┤
│          跨系统身份验证              │
│     (Redis Session + frameLogin)    │
└─────────────────────────────────────┘
```

### 1. 前台API身份验证

#### 签名验证机制
```java
// FrontApiAspect.java - 前台接口统一鉴权切面
@Aspect
@Component
public class FrontApiAspect {
    
    // 验证流程
    @Around("@annotation(FrontApi)")
    public Object verifySignature(ProceedingJoinPoint joinPoint) {
        // 1. 验证请求方法必须是POST
        // 2. 获取URL中的sign参数
        // 3. 获取请求体JSON字符串
        // 4. 计算签名: sign = md5(json字符串 + APP加密串)
        // 5. 比较签名是否一致
        
        return joinPoint.proceed();
    }
}

// 签名计算示例
public class SignatureUtil {
    public static String calculateSign(String jsonBody, String appSecret) {
        String rawString = jsonBody + appSecret;
        return MD5Util.encode(rawString).toLowerCase();
    }
}
```

#### 用户信息获取
```java
// 双重用户信息获取机制
public class FrontApiAspect {
    
    private void putUidInfoToMdc(HttpServletRequest request, String jsonBody) {
        Map<String, Object> userInfo = null;
        
        // 优先级1: POST请求体中的uid参数 (加密传输)
        JSONObject bodyJson = JSON.parseObject(jsonBody);
        String encryptedUid = bodyJson.getString("uid");
        if (StringUtils.isNotBlank(encryptedUid)) {
            String realUid = AuthCodeUtil.decode(encryptedUid);
            userInfo = getUserInfoByUid(realUid);
        }
        
        // 优先级2: Header中的fsid + PHP Redis Session
        if (userInfo == null) {
            String fsid = request.getHeader("fsid");
            if (StringUtils.isNotBlank(fsid)) {
                userInfo = getPhpSessionInfo(fsid);
            }
        }
        
        // 将用户信息放入MDC，供后续业务使用
        if (userInfo != null) {
            MDC.put(FmbxFront_UID_KEY, JSON.toJSONString(userInfo));
        }
    }
}
```

#### 前台API使用示例
```java
// 前台Controller示例
@RestController
@RequestMapping("/fmbx/front")
public class FrontUserController {
    
    @FrontApi  // 自定义注解，触发签名验证
    @PostMapping("/user/info")
    public FmbRespBean getUserInfo(@RequestBody UserInfoRequest request) {
        // 从MDC获取当前用户信息
        String userInfoJson = MDC.get(FmbxFront_UID_KEY);
        Map<String, Object> userInfo = JSON.parseObject(userInfoJson);
        Long userId = Long.valueOf(userInfo.get("uid").toString());
        
        return frontUserService.getUserInfo(userId);
    }
}

// 请求示例
POST /fmbx/front/user/info?sign=abc123def456
Content-Type: application/json

{
    "uid": "encrypted_user_id_123",
    "requestTime": 1635820800
}
```

### 2. 管理后台身份验证

#### Token机制实现
```java
// 管理员Token服务
@Service
public class FmbAdminTokenService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String ADMIN_TOKEN_PREFIX = "admin_token:";
    private static final int TOKEN_EXPIRE_HOURS = 24;
    
    // 生成Token
    public String generateToken(FmbAdmin admin) {
        String token = UUID.randomUUID().toString().replace("-", "");
        String redisKey = ADMIN_TOKEN_PREFIX + token;
        
        // Token信息存储到Redis
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("adminId", admin.getId());
        tokenInfo.put("username", admin.getUsername());
        tokenInfo.put("loginTime", System.currentTimeMillis());
        
        redisTemplate.opsForValue().set(redisKey, tokenInfo, 
            TOKEN_EXPIRE_HOURS, TimeUnit.HOURS);
        
        return token;
    }
    
    // 验证Token
    public Map<String, Object> validateToken(String token) {
        String redisKey = ADMIN_TOKEN_PREFIX + token;
        return (Map<String, Object>) redisTemplate.opsForValue().get(redisKey);
    }
    
    // 刷新Token过期时间
    public void refreshToken(String token) {
        String redisKey = ADMIN_TOKEN_PREFIX + token;
        redisTemplate.expire(redisKey, TOKEN_EXPIRE_HOURS, TimeUnit.HOURS);
    }
}
```

#### 权限控制实现
```java
// RBAC权限模型
@Entity
@TableName("sys_user")
public class SysUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String username;
    private String password;
    private Integer status;
    // ...
}

@Entity  
@TableName("sys_role")
public class SysRole {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String roleName;
    private String roleCode;
    private String description;
    // ...
}

@Entity
@TableName("sys_permission")
public class SysPermission {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String permissionName;
    private String permissionCode;
    private String resourceUrl;
    private Integer permissionType; // 1-菜单，2-按钮，3-接口
    // ...
}

// 权限验证切面
@Aspect
@Component
public class PermissionAspect {
    
    @Around("@annotation(RequirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequirePermission permission) {
        // 1. 从Token获取当前用户信息
        // 2. 查询用户角色
        // 3. 查询角色权限
        // 4. 验证是否有访问权限
        
        String currentUser = getCurrentUser();
        List<String> userPermissions = getUserPermissions(currentUser);
        
        if (!userPermissions.contains(permission.value())) {
            throw new NoPermissionException("无访问权限");
        }
        
        return joinPoint.proceed();
    }
}
```

#### 管理后台使用示例
```java
// 管理后台Controller
@RestController
@RequestMapping("/fmbx/admin")
public class AdminUserController {
    
    @RequirePermission("user:list")  // 权限验证注解
    @PostMapping("/user/list") 
    public FmbRespBean getUserList(@RequestBody UserListRequest request,
                                   HttpServletRequest httpRequest) {
        // 从Header获取Token
        String token = httpRequest.getHeader("Authorization");
        Map<String, Object> tokenInfo = adminTokenService.validateToken(token);
        
        if (tokenInfo == null) {
            return FmbRespBean.error("Token无效或已过期");
        }
        
        // 刷新Token过期时间
        adminTokenService.refreshToken(token);
        
        return adminUserService.getUserList(request);
    }
}

// 登录接口
@PostMapping("/login")
public FmbRespBean login(@RequestBody @Valid AdminLoginRequest request) {
    // 1. 验证用户名密码
    FmbAdmin admin = adminService.validateLogin(request.getUsername(), request.getPassword());
    if (admin == null) {
        return FmbRespBean.error("用户名或密码错误");
    }
    
    // 2. 生成Token
    String token = adminTokenService.generateToken(admin);
    
    // 3. 记录登录日志
    adminLogService.recordLogin(admin.getId(), request.getClientIp());
    
    // 4. 返回Token和用户信息
    Map<String, Object> result = new HashMap<>();
    result.put("token", token);
    result.put("userInfo", admin);
    result.put("permissions", getUserPermissions(admin.getId()));
    
    return FmbRespBean.ok("登录成功", result);
}
```

### 3. 商户后台身份验证

#### 商户认证体系
```java
// 商户用户实体
@TableName("fmb_shop_users")
public class FmbShopUsers {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long bpId;             // 关联商户ID
    private String username;       // 用户名
    private String password;       // 密码
    private String realName;       // 真实姓名
    private String phone;          // 手机号
    private Integer userType;      // 用户类型：1-主账号，2-子账号
    private Integer status;        // 状态：1-正常，2-禁用
    private Date lastLoginTime;    // 最后登录时间
    // ...
}

// 商户用户扩展信息
@TableName("fmb_shop_user_ext")  
public class FmbShopUserExt {
    private Long userId;           // 用户ID
    private String avatar;         // 头像
    private String email;          // 邮箱
    private String department;     // 部门
    private String position;       // 职位
    private List<String> permissions; // 权限列表
    // ...
}

// 商户接口人
@TableName("fmb_shop_interface_person")
public class FmbShopInterfacePerson {
    private Long bpId;             // 商户ID
    private String personName;     // 接口人姓名
    private String personPhone;    // 接口人电话
    private String personEmail;    // 接口人邮箱
    private String personPosition; // 接口人职位
    private Integer isPrimary;     // 是否主要接口人
    // ...
}
```

#### 商户认证服务
```java
@Service
public class BpAuthService {
    
    // 商户用户登录
    public FmbRespBean login(BpLoginRequest request) {
        // 1. 验证用户名密码
        FmbShopUsers shopUser = validateShopUser(request.getUsername(), request.getPassword());
        
        // 2. 检查商户状态
        FmbxBpMain bpMain = bpService.getBpById(shopUser.getBpId());
        if (bpMain.getBpStatus() != 1) {
            return FmbRespBean.error("商户账号已被禁用");
        }
        
        // 3. 检查合同状态
        FmbxBpContract contract = bpService.getValidContract(shopUser.getBpId());
        if (contract == null || contract.getContractStatus() != 1) {
            return FmbRespBean.error("商户合同已过期，请联系平台方");
        }
        
        // 4. 生成Token
        String token = generateBpToken(shopUser, bpMain);
        
        // 5. 返回登录信息
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userInfo", shopUser);
        result.put("bpInfo", bpMain);
        result.put("contractInfo", contract);
        
        return FmbRespBean.ok("登录成功", result);
    }
    
    // 商户权限验证
    public boolean hasPermission(Long userId, String permission) {
        FmbShopUserExt userExt = shopUserService.getUserExt(userId);
        return userExt.getPermissions().contains(permission);
    }
}
```

#### 商户后台使用示例
```java
// 商户后台Controller
@RestController
@RequestMapping("/fmbx/bp")
public class BpController {
    
    @PostMapping("/suite/list")
    public FmbRespBean getSuiteList(@RequestBody SuiteListRequest request,
                                    HttpServletRequest httpRequest) {
        // 1. 验证商户Token
        String token = httpRequest.getHeader("Authorization");
        Map<String, Object> tokenInfo = bpTokenService.validateToken(token);
        
        if (tokenInfo == null) {
            return FmbRespBean.error("Token无效");
        }
        
        Long bpId = (Long) tokenInfo.get("bpId");
        
        // 2. 查询商户房型列表
        return bpSuiteService.getSuiteList(bpId, request);
    }
    
    @PostMapping("/order/list")
    @RequireBpPermission("order:view")  // 商户权限验证
    public FmbRespBean getOrderList(@RequestBody OrderListRequest request) {
        Long bpId = getCurrentBpId();
        return bpOrderService.getOrderList(bpId, request);
    }
}
```

### 4. 跨系统身份验证

#### PHP系统集成认证
```java
// PHP Session读取服务
@Service  
public class PhpSessionService {
    
    @Autowired
    private RedisTemplate<String, Object> phpRedisTemplate;
    
    // 从PHP Redis读取Session信息
    public Map<String, Object> getPhpSessionInfo(String fsid) {
        try {
            // PHP Session存储在Redis的特定数据库
            String sessionKey = "PHPREDIS_SESSION:" + fsid;
            String sessionData = (String) phpRedisTemplate.opsForValue().get(sessionKey);
            
            if (StringUtils.isBlank(sessionData)) {
                return null;
            }
            
            // 解析PHP Session数据格式
            return parsePhpSessionData(sessionData);
            
        } catch (Exception e) {
            logger.error("读取PHP Session失败", e);
            return null;
        }
    }
    
    // 解析PHP Session数据
    private Map<String, Object> parsePhpSessionData(String sessionData) {
        // PHP Session格式解析逻辑
        // 例如: uid|s:6:"123456";username|s:8:"testuser";
        Map<String, Object> result = new HashMap<>();
        
        // 解析逻辑实现...
        
        return result;
    }
}

// frameLogin跨系统登录
@Service
public class FrameLoginService {
    
    // 系统间单点登录
    public String generateFrameLoginToken(Long userId, String targetSystem) {
        // 1. 生成临时Token
        String frameToken = UUID.randomUUID().toString();
        
        // 2. 存储用户信息到Redis，短期有效
        String redisKey = "frame_login:" + frameToken;
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", userId);
        userInfo.put("targetSystem", targetSystem);
        userInfo.put("createTime", System.currentTimeMillis());
        
        redisTemplate.opsForValue().set(redisKey, userInfo, 5, TimeUnit.MINUTES);
        
        return frameToken;
    }
    
    // 验证并消费frameLogin Token
    public Map<String, Object> validateAndConsumeFrameToken(String frameToken) {
        String redisKey = "frame_login:" + frameToken;
        Map<String, Object> userInfo = (Map<String, Object>) redisTemplate.opsForValue().get(redisKey);
        
        if (userInfo != null) {
            // 消费Token，只能使用一次
            redisTemplate.delete(redisKey);
        }
        
        return userInfo;
    }
}
```

### 5. 身份验证配置

#### 安全配置类
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    // 前台API路径配置
    private static final String[] FRONT_API_PATTERNS = {
        "/fmbx/front/**"
    };
    
    // 管理后台路径配置  
    private static final String[] ADMIN_API_PATTERNS = {
        "/fmbx/admin/**"
    };
    
    // 商户后台路径配置
    private static final String[] BP_API_PATTERNS = {
        "/fmbx/bp/**"
    };
    
    @Bean
    public FilterRegistrationBean<FrontApiFilter> frontApiFilter() {
        FilterRegistrationBean<FrontApiFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new FrontApiFilter());
        registration.addUrlPatterns("/fmbx/front/*");
        registration.setOrder(1);
        return registration;
    }
    
    @Bean  
    public FilterRegistrationBean<AdminAuthFilter> adminAuthFilter() {
        FilterRegistrationBean<AdminAuthFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new AdminAuthFilter());
        registration.addUrlPatterns("/fmbx/admin/*");
        registration.setOrder(2);
        return registration;
    }
    
    @Bean
    public FilterRegistrationBean<BpAuthFilter> bpAuthFilter() {
        FilterRegistrationBean<BpAuthFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new BpAuthFilter()); 
        registration.addUrlPatterns("/fmbx/bp/*");
        registration.setOrder(3);
        return registration;
    }
}
```

#### 异常处理配置
```java
// 身份验证相关异常
public class FrontApiSignException extends RuntimeException {
    public FrontApiSignException(String message) {
        super(message);
    }
}

public class NoPermissionException extends RuntimeException {
    public NoPermissionException(String message) {
        super(message);
    }
}

public class BadAccessException extends RuntimeException {
    public BadAccessException(String message) {
        super(message);
    }
}

// 全局异常处理
@RestControllerAdvice
public class AuthExceptionHandler {
    
    @ExceptionHandler(FrontApiSignException.class)
    public FmbRespBean handleFrontApiSignException(FrontApiSignException e) {
        return FmbRespBean.error("签名验证失败: " + e.getMessage());
    }
    
    @ExceptionHandler(NoPermissionException.class)
    public FmbRespBean handleNoPermissionException(NoPermissionException e) {
        return FmbRespBean.error("无访问权限: " + e.getMessage());
    }
    
    @ExceptionHandler(BadAccessException.class)
    public FmbRespBean handleBadAccessException(BadAccessException e) {
        return FmbRespBean.error("访问异常: " + e.getMessage());
    }
}
```

---

## 📝 开发规范

### 1. 代码组织规范

#### 包结构规范
```
com.fmb.server2022/
├── controller/          # 控制器层
│   ├── admin/          # 管理后台控制器
│   ├── bp/             # 商户后台控制器
│   └── front/          # 前台API控制器
├── service/            # 业务服务层
├── domain/             # 领域对象/DTO
├── reqdomain/          # 请求对象/VO
├── config/             # 配置类
├── mapper/             # 自定义Mapper
└── util/               # 工具类
```

#### 类命名规范
```java
// Controller命名
AdminUserController     # 管理后台用户控制器
BpOrderController       # 商户订单控制器  
FrontHotelController    # 前台酒店控制器

// Service命名
UserService            # 业务服务接口
UserServiceImpl        # 业务服务实现
FmbAdminLoginService   # 具体业务服务

// Domain命名  
UserInfoDomain         # 用户信息领域对象
OrderDetailDomain      # 订单详情领域对象

// Request命名
UserListRequest        # 用户列表请求
OrderCreateRequest     # 订单创建请求
```

### 2. 接口设计规范

#### URL路径规范
```java
// 统一前缀: /fmbx
server:
    servlet:
        context-path: /fmbx

// 各端路径分配
/fmbx/admin/*          # 管理后台
/fmbx/bp/*             # 商户后台
/fmbx/front/*          # 前台API  
/fmbx/publictool/*     # 公共服务
```

#### 接口返回规范
```java
// 统一返回格式
public class FmbRespBean {
    private Integer code;      // 状态码: 200-成功, 500-失败
    private String message;    // 返回消息
    private Object data;       // 返回数据
    
    public static FmbRespBean ok(String message, Object data) {
        return new FmbRespBean(200, message, data);
    }
    
    public static FmbRespBean error(String message) {
        return new FmbRespBean(500, message, null);
    }
}

// 增删改接口统一返回格式
@PostMapping("/create")
public FmbRespBean createUser(@RequestBody CreateUserRequest request) {
    Map<String, Object> result = new HashMap<>();
    result.put("result", 0);  // 方法开始统一设置
    
    try {
        userService.createUser(request);
        result.put("result", 1);  // 成功时设置为1
        return FmbRespBean.ok("创建成功", result);
    } catch (Exception e) {
        return FmbRespBean.error("创建失败: " + e.getMessage());
    }
}
```

#### 参数验证规范
```java
// 请求参数验证
@PostMapping("/user/create")
public FmbRespBean createUser(@RequestBody @Valid CreateUserRequest request) {
    return adminUserService.createUser(request);
}

// 请求对象定义
public class CreateUserRequest {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 20, message = "密码长度为6-20位")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    // getter/setter...
}
```

### 3. 数据库操作规范

#### Mapper使用规范
```java
// 继承BaseMapper，获得基础CRUD方法
@Mapper
public interface FmbUserMapper extends BaseMapper<FmbUser> {
    
    // 自定义查询方法
    List<FmbUser> selectUsersByCondition(@Param("condition") UserQueryCondition condition);
    
    // 复杂统计查询
    UserStatistics selectUserStatistics(@Param("startDate") Date startDate, 
                                       @Param("endDate") Date endDate);
}
```

#### 数据源切换规范
```java
@Service
public class UserService {
    
    // 查询操作使用从库
    public FmbUser getUserById(Long id) {
        DynamicDataSourceContextHolder.push("slave");
        try {
            return userMapper.selectById(id);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
    
    // 写操作使用主库
    @Transactional
    public void updateUser(FmbUser user) {
        DynamicDataSourceContextHolder.push("master");
        try {
            userMapper.updateById(user);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }
}
```

#### 分页查询规范
```java
// 使用MyBatis Plus分页插件
public class UserService {
    
    public IPage<FmbUser> getUserList(UserListRequest request) {
        Page<FmbUser> page = new Page<>(request.getCurrentPage(), request.getPageSize());
        
        QueryWrapper<FmbUser> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(request.getUsername())) {
            queryWrapper.like("username", request.getUsername());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq("status", request.getStatus());
        }
        
        return userMapper.selectPage(page, queryWrapper);
    }
}
```

### 4. 缓存使用规范

#### Redis缓存规范
```java
@Service
public class UserService {
    
    // 缓存注解使用 - 查询缓存
    @Cacheable(value = "user_info#3600", key = "#userId")
    public FmbUser getUserById(Long userId) {
        return userMapper.selectById(userId);
    }
    
    // 缓存更新
    @CacheEvict(value = "user_info", key = "#user.id")  
    public void updateUser(FmbUser user) {
        userMapper.updateById(user);
    }
    
    // 缓存删除
    @CacheEvict(value = "user_info", allEntries = true)
    public void clearAllUserCache() {
        // 清空所有用户缓存
    }
    
    // 缓存更新并返回
    @CachePut(value = "user_info", key = "#result.id")
    public FmbUser createUser(CreateUserRequest request) {
        FmbUser user = new FmbUser();
        // 设置用户信息...
        userMapper.insert(user);
        return user;
    }
}
```

#### 分布式锁使用规范
```java
@Service
public class OrderService {
    
    // 使用分布式锁保证并发安全
    public FmbRespBean createOrder(CreateOrderRequest request) {
        String lockKey = "create_order_" + request.getUserId();
        
        return RedissonLockService.doLockJob(lockKey, 30, () -> {
            // 1. 检查库存
            if (!checkStock(request.getSkuId(), request.getQuantity())) {
                throw new BadLogicException("库存不足");
            }
            
            // 2. 扣减库存
            reduceStock(request.getSkuId(), request.getQuantity());
            
            // 3. 创建订单
            FmbOrderInfo order = createOrderInfo(request);
            
            // 4. 发送订单创建事件
            publishOrderEvent(order);
            
            return FmbRespBean.ok("订单创建成功", order);
        });
    }
}
```

### 5. 异常处理规范

#### 自定义异常定义
```java
// 业务逻辑异常
public class BadLogicException extends RuntimeException {
    public BadLogicException(String message) {
        super(message);
    }
}

// 访问权限异常
public class NoPermissionException extends RuntimeException {
    public NoPermissionException(String message) {
        super(message);
    }
}

// 前台API签名异常
public class FrontApiSignException extends RuntimeException {
    public FrontApiSignException(String message) {
        super(message);
    }
}
```

#### 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    // 业务逻辑异常
    @ExceptionHandler(BadLogicException.class)
    public FmbRespBean handleBadLogicException(BadLogicException e) {
        logger.warn("业务逻辑异常: {}", e.getMessage());
        return FmbRespBean.error(e.getMessage());
    }
    
    // 参数验证异常
    @ExceptionHandler(BindException.class)
    public FmbRespBean handleBindException(BindException e) {
        String errorMsg = e.getBindingResult().getFieldErrors().stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        
        logger.warn("参数验证异常: {}", errorMsg);
        return FmbRespBean.error("参数验证失败: " + errorMsg);
    }
    
    // 系统异常
    @ExceptionHandler(Exception.class)
    public FmbRespBean handleException(Exception e) {
        long errorId = SnowflakeIdWorker.getInstance().nextId();
        logger.error("系统异常 errorId: {}", errorId, e);
        return FmbRespBean.error("系统异常，错误ID: " + errorId);
    }
}
```

### 6. 日志记录规范

#### 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="!pro">
        <logger name="com.fmb" level="DEBUG"/>
    </springProfile>
    
    <springProfile name="pro">
        <logger name="com.fmb" level="INFO"/>
    </springProfile>
    
    <!-- SQL日志 -->
    <logger name="com.fmb.server2022.mapper" level="DEBUG"/>
    
    <!-- 错误日志单独记录 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
</configuration>
```

#### 日志使用规范
```java
@Service  
public class OrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
    
    public FmbRespBean createOrder(CreateOrderRequest request) {
        logger.info("开始创建订单, userId:{}, skuId:{}", request.getUserId(), request.getSkuId());
        
        try {
            // 业务逻辑...
            FmbOrderInfo order = processOrder(request);
            
            logger.info("订单创建成功, orderId:{}, orderSn:{}", order.getId(), order.getOrderSn());
            return FmbRespBean.ok("创建成功", order);
            
        } catch (Exception e) {
            long errorId = SnowflakeIdWorker.getInstance().nextId();
            logger.error("订单创建失败 errorId:{}, request:{}", errorId, JSON.toJSONString(request), e);
            return FmbRespBean.error("创建失败，错误ID: " + errorId);
        }
    }
}
```

### 7. 测试规范

#### 单元测试规范
```java
@SpringBootTest
@Transactional
@Rollback
public class UserServiceTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    public void testCreateUser() {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setPassword("123456");
        request.setEmail("<EMAIL>");
        
        // When
        FmbRespBean result = userService.createUser(request);
        
        // Then
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
        Assert.assertNotNull(result.getData());
    }
    
    @Test
    public void testGetUserById() {
        // Given
        Long userId = 1L;
        
        // When
        FmbUser user = userService.getUserById(userId);
        
        // Then
        Assert.assertNotNull(user);
        Assert.assertEquals(userId, user.getId());
    }
}
```

### 8. 部署配置规范

#### 多环境配置
```yaml
# application.yml - 主配置
spring:
  profiles:
    active: dev
  application:
    name: fmb-server

# application-dev.yml - 开发环境
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***********************************
          username: root
          password: 123456
        slave:
          url: ***********************************
          username: root
          password: 123456

# application-pro.yml - 生产环境  
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *******************************************
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
        slave:
          url: ******************************************
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
```

#### Docker配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  fmb-app:
    build: .
    ports:
      - "8888:8888"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=fmb
    volumes:
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
```

### 9. 编码规范

#### 代码风格规范
```java
// 1. 类名使用PascalCase
public class UserService {
    
    // 2. 方法名使用camelCase
    public FmbUser getUserById(Long id) {
        // 3. 变量名使用camelCase
        String userName = user.getUsername();
        
        // 4. 常量使用UPPER_SNAKE_CASE
        private static final String DEFAULT_PASSWORD = "123456";
        
        // 5. 包名使用小写
        // com.fmb.server2022.service
    }
}

// 6. 注释规范
/**
 * 用户服务类
 * 提供用户相关的业务功能
 * 
 * <AUTHOR>
 * @since 2023-01-01
 */
public class UserService {
    
    /**
     * 根据用户ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息，如果不存在返回null
     */
    public FmbUser getUserById(Long userId) {
        // 从缓存获取用户信息
        FmbUser user = getCachedUser(userId);
        if (user != null) {
            return user;
        }
        
        // 从数据库查询
        return userMapper.selectById(userId);
    }
}
```

---

## 📚 附录

### 常用工具类说明

#### 1. 雪花算法ID生成
```java
// 使用雪花算法生成唯一ID
Long id = SnowflakeIdWorker.getInstance().nextId();
```

#### 2. 日期工具类
```java
// 日期格式化和转换
String dateStr = FmbDateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
Date date = FmbDateUtil.parse("2023-01-01", "yyyy-MM-dd");
```

#### 3. JSON处理工具
```java
// JSON序列化和反序列化
String json = JSON.toJSONString(object);
Object obj = JSON.parseObject(json, Object.class);
```

#### 4. MD5加密工具
```java
// MD5加密
String encrypted = MD5Util.encode("password");
```

### 常见问题解决方案

#### 1. 数据库字段是关键字问题
```java
@TableField(value = "`desc`")
private String desc;
```

#### 2. 类路径文件读取
```java
String jsonFile = IOUtils.toString(
    new ClassPathResource("fmbadmintree.json").getInputStream(), 
    "UTF-8"
);
```

#### 3. 定时任务配置
```java
@Component
public class CrontabService {
    
    @Scheduled(cron = "0 0 1 * * ?")  // 每天凌晨1点执行
    public void dailyTask() {
        // 定时任务逻辑
    }
}
```

### 项目构建命令

#### Maven构建命令
```bash
# 清理编译
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install -Dmaven.test.skip=true

# 运行测试
mvn test

# 启动应用
java -jar server2022/target/server2022-1.0.jar --spring.profiles.active=pro
```

---

**注意**: 本文档将作为FMB项目的架构参考标准，在后续代码开发和修改时，请严格按照本文档的规范和模式进行，确保代码的一致性和可维护性。