<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>FMB接口调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffe6e6; color: #d00; }
        .success { background: #e6ffe6; color: #060; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 FMB接口调试工具</h1>
        
        <div class="section">
            <h3>优惠券接口调试</h3>
            <p><strong>接口地址:</strong> <code>POST /fmbx/front/coupon/getCouponBySku</code></p>
            
            <label>请求参数 (JSON):</label>
            <textarea id="requestData">{
  "skuId": 123,
  "userId": 456,
  "quantity": 1,
  "checkInDate": "2025-08-02",
  "checkOutDate": "2025-08-03"
}</textarea>
            
            <button onclick="testCouponAPI()">🚀 测试接口</button>
            <button onclick="clearResult()">🧹 清空结果</button>
            
            <div id="result" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📋 调试步骤说明</h3>
            <ol>
                <li><strong>修改参数:</strong> 在上面的文本框中修改JSON参数</li>
                <li><strong>点击测试:</strong> 点击"测试接口"按钮发送请求</li>
                <li><strong>查看结果:</strong> 在下方查看返回结果和调试信息</li>
                <li><strong>查看日志:</strong> 在Java应用控制台查看详细日志</li>
            </ol>
        </div>

        <div class="section">
            <h3>🔧 常用调试技巧</h3>
            <ul>
                <li><strong>日志调试:</strong> 使用 logger.info() 记录关键信息</li>
                <li><strong>断点调试:</strong> 在IDE中设置断点，单步执行</li>
                <li><strong>参数验证:</strong> 检查请求参数是否正确</li>
                <li><strong>返回值检查:</strong> 确认返回的数据结构</li>
                <li><strong>异常处理:</strong> 捕获并分析异常信息</li>
            </ul>
        </div>
    </div>

    <script>
        async function testCouponAPI() {
            const resultDiv = document.getElementById('result');
            const requestData = document.getElementById('requestData').value;
            
            try {
                // 显示加载状态
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = '🔄 正在发送请求...';
                
                // 发送请求
                const response = await fetch('http://localhost:8070/fmbx/front/coupon/getCouponBySku', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: requestData
                });
                
                const responseText = await response.text();
                
                // 显示结果
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ 请求成功 (${response.status})</h4>
                        <p><strong>响应数据:</strong></p>
                        <pre>${formatJSON(responseText)}</pre>
                        <p><strong>💡 提示:</strong> 请查看Java应用控制台的详细日志信息</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ 请求失败 (${response.status})</h4>
                        <p><strong>错误信息:</strong></p>
                        <pre>${responseText}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ 请求异常</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>可能原因:</strong></p>
                    <ul>
                        <li>Java应用没有启动 (检查端口8070)</li>
                        <li>网络连接问题</li>
                        <li>CORS跨域问题</li>
                    </ul>
                `;
            }
        }
        
        function clearResult() {
            document.getElementById('result').style.display = 'none';
        }
        
        function formatJSON(jsonString) {
            try {
                const obj = JSON.parse(jsonString);
                return JSON.stringify(obj, null, 2);
            } catch (e) {
                return jsonString;
            }
        }
    </script>
</body>
</html>