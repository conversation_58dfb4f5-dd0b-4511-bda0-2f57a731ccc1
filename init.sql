-- 创建数据库和基础表结构
CREATE DATABASE IF NOT EXISTS fmb_new DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE fmb_new;

-- 创建用户表（示例）
CREATE TABLE IF NOT EXISTS `fmb_users` (
  `uid` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) DEFAULT NULL COMMENT '邮件地址',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `realname` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `points` int(11) DEFAULT '0' COMMENT '用户积分',
  `total_points` int(11) DEFAULT '0' COMMENT '累计积分',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`),
  KEY `idx_phone` (`phone_number`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 创建积分记录表（为后续积分功能准备）
CREATE TABLE IF NOT EXISTS `fmb_user_points_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `points` int(11) NOT NULL COMMENT '积分变动数量',
  `type` tinyint(4) NOT NULL COMMENT '类型：1=获得，2=消费',
  `source` varchar(50) DEFAULT NULL COMMENT '积分来源',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分记录表';

-- 插入测试数据
INSERT INTO `fmb_users` (`email`, `phone_number`, `username`, `realname`, `points`, `total_points`) VALUES
('<EMAIL>', '13800138000', '测试用户', '张三', 100, 500),
('<EMAIL>', '13800138001', '用户2', '李四', 200, 800);