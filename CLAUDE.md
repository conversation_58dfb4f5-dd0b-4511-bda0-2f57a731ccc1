# FMB 项目记忆

## 项目概述
- 酒店预订系统 (FMB - 蜂鸣宝)
- 技术栈: Java + Spring Boot + MyBatis Plus
- 多模块项目结构:
  - common: 公共模块
  - fmbmapper: 数据访问层
  - server2022: 服务层和控制器

## 项目结构
- `/common/` - 基础工具类、异常处理、用户会话等
- `/fmbmapper/` - 实体类、Mapper接口、Service接口和实现
- `/server2022/` - 控制器、配置类、业务逻辑

## 业务领域
- 酒店管理 (Suite, Room, Sku)
- 订单管理 (Order, Payment)
- 用户管理 (User, Coupon)
- 活动管理 (Activity)
- 商户管理 (BP - Business Partner)

## 之前的工作记录
- 已配置 lnmp-docker 环境，添加 Java8 服务支持
- 已创建 application-docker.yml 配置文件
- Java 项目构建成功，但日志配置需要调整才能正常启动
- 需要解决日志路径权限问题或修改日志配置

## 最新工作记录 (2025-07-31)
### 🔧 环境配置完成
- ✅ Docker服务全部正常运行 (MySQL, Redis, RocketMQ, Nginx)
- ✅ Java8 + Maven 环境配置完成
- ✅ 项目构建成功 (Maven clean package)

### 🛠️ 解决的问题
1. **Redis配置问题** - 补充了 database, jedis.pool 等配置
2. **应用配置缺失** - 补充了10+个FMB自定义配置项:
   ```yaml
   fmb:
     fmbImgUrl: http://localhost/images/
     fmbImgDir: /var/www/html/images/ 
     ffmpegPath: /usr/bin/ffmpeg
     serviceFax: ************
     serviceTel: ************
     categoryChild: 1
     categoryLong: 2
     categoryShow: 3
     categoryTravel: 4
     categoryBuy: 5
   ```
3. **Connection reset by peer错误** - 原因是配置缺失导致应用反复重启

### 📊 当前状态
- Java应用配置完整，正在启动中
- 端口8888被Docker占用，应用启动需要时间
- 所有依赖服务运行正常

### 🎯 下次启动验证
```bash
# 检查应用状态
curl http://localhost:8888/actuator/health

# 监控启动日志  
docker logs fmb_java -f

# 查看端口监听
docker exec fmb_java netstat -tlnp | grep 8888
```

## 当前状态
- Git 状态显示大量文件已修改 (M状态)
- 最近提交涉及库存修改和价格更新功能

## 重要配置
- 构建脚本: `build_fmbx.sh`
- 主应用入口: `server2022/src/main/java/com/fmb/Server2022Application.java`

## 待办事项
<!-- 记录未完成的任务 -->

## 注意事项
- 项目使用 MyBatis Plus 作为 ORM 框架
- 包含前端API、管理后台、商户后台多个模块
- 使用 Redis 缓存和 Kafka 消息队列