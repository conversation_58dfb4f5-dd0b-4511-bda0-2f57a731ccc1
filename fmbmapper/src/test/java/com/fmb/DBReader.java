package com.fmb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fmb.GenCode.toCamelCase;

public class DBReader {


    public static final String IS_KV_COLUMN = "isKvColumn";
    public static final String GEN_KVS = "genKvs";
    private static Logger logger = LoggerFactory.getLogger(DBReader.class);

    public ArrayList<String> allTableRead(DataSourceConfig dataSourceConfig) {

        ArrayList<String> tableNames = new ArrayList<>();
        String sql = "select TABLE_NAME from information_schema.TABLES where  TABLE_SCHEMA = 'fmb_new' and  TABLE_NAME like 'fmbx%'  ;" ;
        try (Connection conn = dataSourceConfig.getConn();

             PreparedStatement psTable = conn.prepareStatement(sql);
             ResultSet rsTable = psTable.executeQuery();
        ){

            while (rsTable.next()){
                String table_name = rsTable.getString("TABLE_NAME");
                tableNames.add(table_name) ;
                System.out.println(table_name);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


        return tableNames ;
    }


    @Test
    public void tablesRead() {

        final DataSourceConfig dataSourceConfig = genDataSourceConfig();
        final ArrayList<String> tables = allTableRead(dataSourceConfig);


        Map<String, Object> map = new HashMap<>();

        String[] tablePrefixArray = new String[0] ;

        for (String table : tables) {
            readDbInfo(table, dataSourceConfig, map, tablePrefixArray);

            p();
            System.out.println(JSON.toJSONString(map,true));
            List<Map> columns = (List<Map>) map.get("columns");

            System.out.println(columns.size());

            for (Map column : columns) {
                if (column.containsKey(IS_KV_COLUMN) &&   ((boolean) column.get(IS_KV_COLUMN))) {
//                    {
//                        "val":"1",
//                            "name":"演出"
//                    }
                   List<Map> kvs = (List<Map>) column.get(GEN_KVS);
                   new DictInfo(table,kvs,column) ;

                }
            }
//
//            columns.stream().filter(m->  map.containsKey ("isKvColumn") &&  ((boolean) map.get("isKvColumn"))  ).forEach(
//            columns.stream().filter(m->  map.containsKey ("isKvColumn") ).forEach(
//
//                    col->{
//                        p("col");
//                        System.out.println(col);
//                    }
//
//            );

            p();

            break;
        }


    }

    public static void p() {
        System.out.println("---------------------");
    }
    public static void p(String tag) {
        System.out.println("----------"+tag+"-----------");
    }


    @Test
    public void dbread() {

//        String url = "*******************************************************************************************" +
//                "&characterEncoding=UTF-8&useAffectedRows=true";
//        String username =  "root" ;
//        String passwd =  "123456" ;
        DataSourceConfig dataSourceConfig = genDataSourceConfig();

        /**
         * 表名称开始-----------
         */
        String tableName = "fmbx_bp_parter_info";
//        String tableName = "fmbx_bp_parter_hotel_info";
        /**
         * 表名称结束-----------
         */
        Map<String, Object> map = new HashMap<>();

        String[] tablePrefixArray = {"fmbx_"};

        readDbInfo(tableName, dataSourceConfig, map, tablePrefixArray);

//        System.out.println(JSON.toJSONString(map,SerializerFeature.PrettyFormat));

//        getDictFields(map) ;




        System.out.println(JSON.toJSONString(map,SerializerFeature.PrettyFormat));

        genDoaminFile(map) ;


    }

    /**
     * 找出所有的 名值对应字段
     * @param map
     */
    private void getDictFields(Map<String, Object> map) {

        List<Map<String, Object>> columns = (List<Map<String, Object>>) map.get("columns");
        for (Map<String, Object> column : columns) {
            String dbColumnComment = column.get("dBColumnComment").toString();
//            ArrayList<Object> colKvs = genKvs(dbColumnComment);

//            column.put("colKvs",colKvs) ;
        }

    }

    private ArrayList<Object> genKvs(String dbColumnComment) {
        String[] split = dbColumnComment.split(":");

        ArrayList<Object> colKvs = new ArrayList<>();
        if(split.length==2){
            String[] options = split[1].split(",");

            for (int i = 0; i < options.length; i++) {

                String[] kvt = options[i].split("-");

                HashMap<String, Object> kv = new HashMap<>();
                kv.put("name", kvt[1]);
                kv.put("val", kvt[0]);

                colKvs.add(kv) ;
            }



        }
        return colKvs;
    }

    private DataSourceConfig genDataSourceConfig() {
//                String url = "*******************************************************************************************" +
//                "&characterEncoding=UTF-8&useAffectedRows=true";
//        String username =  "root" ;
//        String passwd =  "123456" ;

        String url = "***************************************************************************************************" +
                "&characterEncoding=UTF-8&useAffectedRows=true";
        String username =  "fmb" ;
        String passwd =  "123456" ;

        return new DataSourceConfig.Builder(url, username, passwd).build();
    }

    private void genDoaminFile(Map<String, Object> map) {

        List<Map<String, Object>> columns = (List<Map<String, Object>>) map.get("columns");



        StringBuffer stringBuffer = new StringBuffer();
        StringBuffer apipostBody = new StringBuffer();
        stringBuffer.append("package com.fmb.server2022.reqdomain;\n" +
                "\n" +
                "import lombok.Data;\n" +
                "import javax.validation.constraints.NotEmpty;\n" +
                "import javax.validation.constraints.NotBlank;\n" +
                "import javax.validation.constraints.NotNull;\n") ;


        stringBuffer.append("@Data   \n" ) ;
        String cname = "Req"+System.currentTimeMillis() ;
        stringBuffer.append("public class "+cname+" {\n  " ) ;





//        apipostBody.append("{") ;
        for (Map<String, Object> key : columns) {
//            System.out.println("key->"+key);
            String toCamelCase = toCamelCase(key.get("dBColumnName").toString());
            String dbColumnComment = key.get("dBColumnComment").toString();

            String selShort = check(dbColumnComment);

            stringBuffer.append("//   "+ dbColumnComment+"\n");
            stringBuffer.append("//  数据库字段名 "+key.get("dBColumnName").toString());
            stringBuffer.append("\n" ) ;
            String opTitle = StringUtils.isNotBlank(selShort)?"选择"+selShort:"输入"+dbColumnComment;

            apipostBody.append("\n") ;
            String javaFieldType = key.get("javaFieldType").toString();
            if ("String".equals(javaFieldType)) {

                apipostBody.append("\"") ;
                apipostBody.append(toCamelCase) ;
                apipostBody.append("\":\"") ;
                apipostBody.append(dbColumnComment) ;
                apipostBody.append("\"") ;

                stringBuffer.append("@NotBlank(message = \"请" + opTitle +"\")");
                stringBuffer.append("\n" ) ;

                stringBuffer.append("private String "+ toCamelCase +" ;");
                stringBuffer.append("\n" ) ;

            }else
            if ("Integer".equals(javaFieldType) || "Double".equals(javaFieldType)) {
                stringBuffer.append("@NotNull(message = \"请" + opTitle +"\")");
                stringBuffer.append("\n" ) ;
                stringBuffer.append("private  Integer  "+toCamelCase +" ;");
                stringBuffer.append("\n" ) ;


                apipostBody.append("//"+dbColumnComment+"\n") ;
                apipostBody.append("\"") ;
                apipostBody.append(toCamelCase) ;
                apipostBody.append("\": 1") ;

                apipostBody.append("") ;
            }else {
                apipostBody.append("\"") ;
                apipostBody.append(toCamelCase) ;
                apipostBody.append("\":\"") ;
                apipostBody.append(dbColumnComment) ;
                apipostBody.append("\"") ;
            }

            apipostBody.append(",") ;

            stringBuffer.append("\n" ) ;

        }


//        apipostBody.append("}") ;

        stringBuffer.append(        "}\n") ;
        String data = stringBuffer.toString();

        System.out.println("------------------");
        System.out.println(data);
        System.out.println("------------------");
        System.out.println("------------------");
        System.out.println("{"+apipostBody.toString().substring(0,apipostBody.length()-1)+"\n}");
        System.out.println("------------------");



//        try {
//            String jfile = cname +".java";
//            FileUtils.writeStringToFile(new File("/Users/<USER>/workspace/fmbx/server2022/src/main/java/com/fmb/server2022/reqdomain/" + jfile), data,"UTF-8");
//
//            System.out.println(jfile+" write done");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }



    }

    /**
     * 根据数据库字段说明 选择类型的如下示例
     *
     * 加床策略:1-未知,2-不可加,3-免费加床不含早,4-免费加床含早,5-收费加床不含早,6-收费加床含早
     *
     *  普通的就是
     *
     * @param dbColumnComment
     * @return
     */

    private String check(String dbColumnComment) {

        String[] split = dbColumnComment.split(":");
        if (split.length==1) {
            return  "" ;
        }

        String[] split2 = split2 = split[1].split(",");

        return (split.length==2&& split2.length>1)?split[0]:""  ;
    }


    private void readDbInfo(String tableName, DataSourceConfig dataSourceConfig, Map<String, Object> map, String[] tablePrefixArray) {
        List<Map<String, Object>> columns = new ArrayList<>();

        // 获取表信息sql
        String tableSql = "select table_name , table_comment from information_schema.tables " +
                "where table_schema = (select database()) and table_name = '" + tableName + "'";
        // 获取字段信息sql
        String columnSql = "select column_name , data_type , column_comment from information_schema.columns " +
                "where table_name = '" + tableName + "' and table_schema = (select database()) and column_name != 'id_new'";
        // 利用现有的dataSourceConfig来获取数据库连接，查询表字段及备注等信息
        try (Connection conn = dataSourceConfig.getConn();
             PreparedStatement psTable = conn.prepareStatement(tableSql);
             ResultSet rsTable = psTable.executeQuery();
             PreparedStatement ps = conn.prepareStatement(columnSql);
             ResultSet rs = ps.executeQuery();


        ) {
            if (rsTable.next()) {
                String table_name = rsTable.getString("table_name");
                map.put("dBtableName", table_name);
                map.put("dBcomment", rsTable.getString("table_comment"));

                // 过滤表前缀
                if (tablePrefixArray.length > 0) {
                    for (String s : tablePrefixArray) {
                        table_name = table_name.replaceAll(s, "");
                    }
                }
                // 类名 大驼峰
                String domainClassName = StringUtils.capitalize(toCamelCase(table_name));
                map.put("javaDomainClassName", domainClassName);
                map.put("javaDomainVar",  StringUtils.uncapitalize( domainClassName));

            }

            //找主键的 标识
            boolean foundPK = false ;
            while (rs.next()) {
                Map<String, Object> columnMap = new HashMap<>();
                // 列名字、数据类型、java属性名字、java属性类型、备注

                String column_name = rs.getString("column_name");
                columnMap.put("dBColumnName", column_name);
                String data_type = rs.getString("data_type");
                columnMap.put("dBDataType", data_type);
                String fieldName = toCamelCase(column_name);
                columnMap.put("javaFieldName", fieldName);
                columnMap.put("javaFieldNameCap", StringUtils.capitalize(fieldName));
                columnMap.put("javaFieldType", columnTypeToJavaType(data_type,column_name));
                String dateFormat = columnDateType(data_type, column_name);
                columnMap.put("dateFormat", dateFormat);
                columnMap.put("haveDateFormat", StringUtils.isNotBlank(dateFormat));
                String column_comment = rs.getString("column_comment");
                columnMap.put("dBColumnComment", column_comment);
                columnMap.put("dBColumnCommentShort", column_comment);
                if(column_comment.indexOf(":")!=-1){
                    columnMap.put("dBColumnCommentShort", StringUtils.substringBefore(column_comment,":"));
                }

                ArrayList<Object> objects = genKvs(column_comment);

                columnMap.put(GEN_KVS,objects) ;
                columnMap.put(IS_KV_COLUMN,  !objects.isEmpty());

                columnMap.put("dBpriKey",  false) ;

                if (!foundPK) {
                    boolean hpk = checkColumnIsPrimaryKey(tableName, column_name, conn);
                    columnMap.put("dBpriKey", hpk) ;
                    foundPK = hpk ;
                }

                columns.add(columnMap);
            }
        } catch (Exception e) {
            logger.info("----------sql执行出错");
            e.printStackTrace();
        }

        map.put("columns", columns);
//        map.put("datetime", DateUtil.now());
//        map.put("packageName", packageName);
//        map.put("moduleName", moduleName);
    }

    private String columnDateType(String data_type, String column_name) {


        if (Arrays.asList(COLUMN_Format_Full).contains(data_type)) {
            return "yyyy-MM-dd HH:mm:ss";
        }
        if (Arrays.asList(COLUMN_Format_ymd).contains(data_type)) {
            return "yyyy-MM-dd";
        }
        return  "" ;
    }


    private static boolean checkColumnIsPrimaryKey(String table, String name, Connection conn) {


        boolean isPrimary = false ;
        String check_sql = "show index from "+table+"  where key_name = 'PRIMARY' and COLUMN_NAME = '"+name+"'" ;
        try {
            PreparedStatement pst = conn.prepareStatement(check_sql);
            ResultSet rs = pst.executeQuery();
            isPrimary = rs.next() ;

            logger.info("{} check {} is pk={}",table,name,isPrimary);

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return isPrimary;

    }

    /**
     * 数据库类型转换为java类型
     *
     * @param columnType 数据库类型
     * @param column_name
     * @return java类型
     */
    private String columnTypeToJavaType(String columnType, String column_name) {
        if (  StringUtils.isNotBlank(columnType)) {
            if (Arrays.asList(COLUMN_TYPE_STR).contains(columnType)) {
                return TYPE_STRING;
            }
            if (Arrays.asList(COLUMN_TYPE_TEXT).contains(columnType)) {
                return TYPE_STRING;
            }
            if (Arrays.asList(COLUMN_TYPE_TIME).contains(columnType)) {
                return TYPE_DATE;
            }
            if (Arrays.asList(COLUMN_TYPE_NUMBER).contains(columnType)) {
                return TYPE_INTEGER;
            }
            if (Arrays.asList(COLUMN_TYPE_BIGINT).contains(columnType)) {
                return TYPE_LONG;
            }
            if (Arrays.asList(COLUMN_TYPE_FLOAT).contains(columnType)) {
                return TYPE_DOUBLE;
            }
            if (Arrays.asList(COLUMN_TYPE_DOUBLE).contains(columnType)) {
                return TYPE_DOUBLE;
            }
            if (Arrays.asList(COLUMN_TYPE_DECIMAL).contains(columnType)) {
                return TYPE_BIGDECIMAL;
            }
        }
        System.out.println("[column_name="+column_name+"] cannot_find_type:"+columnType);
        return null;
    }

    /**
     * 数据库字符串类型
     */
    public static final String[] COLUMN_TYPE_STR = {"char", "varchar", "nvarchar", "varchar2"};

    /**
     * 数据库文本类型
     */
    public static final String[] COLUMN_TYPE_TEXT = {"tinytext", "text", "mediumtext", "longtext"};

    /**
     * 数据库时间类型
     */
    public static final String[] COLUMN_TYPE_TIME = {"datetime", "time", "date", "timestamp"};

    public static final String[] COLUMN_Format_Full = {"datetime", "timestamp"};
    public static final String[] COLUMN_Format_ymd = {"date"};

    /**
     * 数据库数字类型
     */
    public static final String[] COLUMN_TYPE_NUMBER = {"tinyint", "smallint", "mediumint", "int", "number", "integer", "bit"};

    /**
     * 数据库bigint类型
     */
    public static final String[] COLUMN_TYPE_BIGINT = {"bigint"};
    /**
     * 数据库float类型
     */
    public static final String[] COLUMN_TYPE_FLOAT = {"float"};
    /**
     * 数据库double类型
     */
    public static final String[] COLUMN_TYPE_DOUBLE = {"double"};
    /**
     * 数据库decimal类型
     */
    public static final String[] COLUMN_TYPE_DECIMAL = {"decimal"};

    /**
     * 字符串类型
     */
    public static final String TYPE_STRING = "String";

    /**
     * 整型
     */
    public static final String TYPE_INTEGER = "Integer";

    /**
     * 长整型
     */
    public static final String TYPE_LONG = "Long";

    /**
     * 浮点型
     */
    public static final String TYPE_DOUBLE = "Double";

    /**
     * 高精度计算类型
     */
    public static final String TYPE_BIGDECIMAL = "BigDecimal";

    /**
     * 时间类型
     */
    public static final String TYPE_DATE = "Date";
}
