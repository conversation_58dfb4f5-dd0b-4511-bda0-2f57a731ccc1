package com.fmb;

import java.util.List;

public class DBColumn {


    /**
     * dBDataType : tinyint
     * dBpriKey : false
     * haveDateFormat : false
     * javaFieldName : parterType
     * dBColumnName : parter_type
     * dateFormat :
     * colKvs : [{"val":"1","name":"酒店"},{"val":"2","name":"其他"}]
     * javaFieldType : Integer
     * javaFieldNameCap : ParterType
     * dBColumnComment : 商家类型:1-酒店,2-其他
     */

    private String dBDataType;
    private boolean dBpriKey;
    private boolean haveDateFormat;
    private String javaFieldName;
    private String dBColumnName;
    private String dateFormat;
    private String javaFieldType;
    private String javaFieldNameCap;
    private String dBColumnComment;
    private List<ColKvsBean> colKvs;

    public String getDBDataType() {
        return dBDataType;
    }

    public void setDBDataType(String dBDataType) {
        this.dBDataType = dBDataType;
    }

    public boolean isDBpriKey() {
        return dBpriKey;
    }

    public void setDBpriKey(boolean dBpriKey) {
        this.dBpriKey = dBpriKey;
    }

    public boolean isHaveDateFormat() {
        return haveDateFormat;
    }

    public void setHaveDateFormat(boolean haveDateFormat) {
        this.haveDateFormat = haveDateFormat;
    }

    public String getJavaFieldName() {
        return javaFieldName;
    }

    public void setJavaFieldName(String javaFieldName) {
        this.javaFieldName = javaFieldName;
    }

    public String getDBColumnName() {
        return dBColumnName;
    }

    public void setDBColumnName(String dBColumnName) {
        this.dBColumnName = dBColumnName;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public String getJavaFieldType() {
        return javaFieldType;
    }

    public void setJavaFieldType(String javaFieldType) {
        this.javaFieldType = javaFieldType;
    }

    public String getJavaFieldNameCap() {
        return javaFieldNameCap;
    }

    public void setJavaFieldNameCap(String javaFieldNameCap) {
        this.javaFieldNameCap = javaFieldNameCap;
    }

    public String getDBColumnComment() {
        return dBColumnComment;
    }

    public void setDBColumnComment(String dBColumnComment) {
        this.dBColumnComment = dBColumnComment;
    }

    public List<ColKvsBean> getColKvs() {
        return colKvs;
    }

    public void setColKvs(List<ColKvsBean> colKvs) {
        this.colKvs = colKvs;
    }

    public static class ColKvsBean {
        /**
         * val : 1
         * name : 酒店
         */

        private String val;
        private String name;

        public String getVal() {
            return val;
        }

        public void setVal(String val) {
            this.val = val;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
