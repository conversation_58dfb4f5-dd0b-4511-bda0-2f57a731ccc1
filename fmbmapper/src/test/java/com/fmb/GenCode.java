package com.fmb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;

public class GenCode {


    /**
     * 驼峰式命名法
     */
    public static String toCamelCase(String s) {
        if (s == null) {
            return null;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            if (c == '_') {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }



    @Test
    public void jsonStringProcess() {
        String json = "  {\n" +
                "    \"menu_id\": 20,\n" +
                "    \"menu_name\": \"功能1\",\n" +
                "    \"parent_menuid\": 15,\n" +
                "    \"related_permissionid\": 601,\n" +
                "    \"is_folder\": 0,\n" +
                "    \"sortval\": 100\n" +
                "  }" ;

        Arrays.asList( json.split("\n")).stream().forEach(line->{

            String[] split = line.split(":", 2);
            if (split.length==1) {
                System.out.println(line);
            }else if (split.length==2){

                System.out.println( toCamelCase(split[0]) +":"+split[1]  );
                
            }

        });

    }

    public static void main(String args[]) {
        GenCode genCode = new GenCode();
        genCode.work();
    }

    private void work() {
        fmbx();

//        fmb_new();


    }

    public void fmbx() {
//        url: *************************************************************************************************************************************************
//        password: bangong!@#
//        username: bangong

//        String url = "*************************************************************************************************************************************************";
//        String username =  "bangong" ;
//        String passwd =  "bangong!@#" ;

        String url = "***************************************************************************************************" +
                "&characterEncoding=UTF-8&useAffectedRows=true";
        String username =  "fmb" ;
        String passwd =  "123456" ;

//        String url = "**********************************************************************************************" +
//                "&characterEncoding=UTF-8&useAffectedRows=true";
//        String username =  "root" ;
//        String passwd =  "123456" ;

        String proName = "fmbmapper";

        File basePath = null;
        try {
            basePath = new File(ResourceUtils.getURL("classpath:").getPath());
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        String baseDir = StringUtils.substringBeforeLast(basePath.getAbsolutePath(), "fmbmapper");
        String projectdir = baseDir + proName;

        System.out.println("======请确保本地代码已经提交====\n");
        System.out.println("======输入任意字符回车 继续====\n");


        Scanner scanner = new Scanner(System.in);
        while (true)
        {
            String s = scanner.nextLine();
            if (StringUtils.isNotBlank(s)) {
                break;
            }
        }

        /**
         * 开始---------
         */
//        String tableName =  "fmbx_bps_room" ;
//        String tableName =  "fmbx_date_table" ;
//        String tableName =  "fmbx_business_partner_check_action" ;
//        String tableName =  "fmbx_business_partner_ext" ;
//        tableName = "fmbx_bp_contract" ;

        ArrayList<String> tables = new ArrayList<>();

//        tables.add("fmbx_date_table") ;
//        tables.add("fmbx_kafka_message") ;
//
//        //需要手动设置ID
//        tables.add("fmbx_activity_ext") ;


//        tables.add("fmbx_suite") ;
//        tables.add("fmbx_suite_room_sku") ;
//        tables.add("fmbx_hotel_reserve_sku") ;
//        tables.add("fmbx_hotel_reserve_sku") ;
//        tables.add("fmbx_suite_ext") ;
//        tables.add("fmbx_suite_price_operate") ;
//        tables.add("fmbx_suite_price_operate_log") ;
//        tables.add("fmbx_suite_room_sku") ;
//        tables.add("fmbx_suite_roomdate_price") ;
//        tables.add("fmbx_suite_stock") ;
//        tables.add("fmbx_suite_stock_operate") ;
//        tables.add("fmbx_suite_stock_operate_log") ;
//        tables.add("fmbx_suite_price_operate") ;

//        tables.add("fmb_order_info") ;
//        tables.add("fmb_order_goods") ;
//        tables.add("fmb_order_actions") ;
//        tables.add("fmb_coupon_log") ;
//        tables.add("fmb_order_trade_snapshot") ;
//        tables.add("fmb_pay_orders") ;
//        tables.add("fmb_order_hotel_ext") ;
//        tables.add("fmb_user_cash_change") ;
//        tables.add("fmb_reserve_hotel_codes") ;
        tables.add("fmb_order_cancel") ;
//        tables.add("fmb_user_coupon") ;
//        tables.add("fmbx_bp_main") ;

        /**
         * 结束---------

         */

        FastAutoGenerator.create(new DataSourceConfig.Builder(url, username, passwd)
                .typeConvert(new MySqlTypeConvertCustom()))
                .globalConfig(builder -> {

                    builder.author("youfa.hou") // 设置作者
                            .disableOpenDir()
//                            .enableSwagger() // 开启 swagger 模式
                            .fileOverride() // 覆盖已生成文件
                            .dateType(DateType.TIME_PACK)


                            // 注释时间策略。
//                            .commentDate("yyyy-MM-dd")
                            .outputDir(projectdir + "/src/main/java"); // 指定输出目录
                })

                .packageConfig(builder -> {
                    builder.parent("com.fmb.server2022") // 设置父包名
                            .moduleName("fmbx") // 设置父包模块名

                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml,
                                    projectdir + "/src/main/resources/mapper/fmbx"));
                    // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder
                            .addInclude(tables); // 设置需要生成的表名
//                            .addTablePrefix("t_", "c_"); // 设置过滤表前缀
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateConfig(builder -> {
                    //genpath/templates/entity.java.ftl   不需要增加尾部的.ftl 后缀
                    builder
                            .entity("/genpath/templates/entity.java")
                            .serviceImpl("/genpath/templates/serviceImpl.java")
                            .controller("/genpath/templates/controller.java")
//                            .disable()
//                            .disable()
                            .build();

                })
                .injectionConfig(builder -> {

                    Map<String, Object> map = new HashMap<>();
                    map.put("dbkey","fmbx") ;
                    builder.customMap(map).build() ;

                })
                .execute();

        for (String tableName : tables) {
            //把controller 挪个位置
            String controllerPath = "src/main/java/com/fmb/server2022/fmbx/controller/";
            String moveAimDir = baseDir + "server2022/" + controllerPath +
                    StringUtils.capitalize(toCamelCase(tableName))
                    +"Controller.java"  ;
            String source = baseDir + "fmbmapper/" + controllerPath +
                    StringUtils.capitalize(toCamelCase(tableName))
                    +"Controller.java"  ;

            try {
                File srcFile = new File(source);

                //直接删除controller
                srcFile.delete() ;
                //屏蔽挪动
//                File destFile = new File(moveAimDir);
//
//                if(srcFile.exists()){
//                    if (destFile.exists()) {
//                        destFile.delete();
//                    }
//                    FileUtils.moveFile(srcFile, destFile);
//                    System.out.println(srcFile.getName()+"文件转移ok");
//                }

//            FileUtils.copyFile(new File(source),new File(moveAimDir));

            } catch (Exception e) {
                e.printStackTrace();
            }
        }





    }



    public void fmb_new() {
//        url: *************************************************************************************************************************************************
//        password: bangong!@#
//        username: bangong

//        String url = "*************************************************************************************************************************************************";
//        String username =  "bangong" ;
//        String passwd =  "bangong!@#" ;

        String url = "**********************************************************************************************" +
                "&characterEncoding=UTF-8&useAffectedRows=true";
        String username =  "root" ;
        String passwd =  "123456" ;



//        String url = "***************************************************************************************************" +
//                "&characterEncoding=UTF-8&useAffectedRows=true";
//        String username =  "fmb" ;
//        String passwd =  "123456" ;


        File basePath = null;
        try {
            basePath = new File(ResourceUtils.getURL("classpath:").getPath());
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        String baseDir = StringUtils.substringBeforeLast(basePath.getAbsolutePath(), "fmbmapper");

        String projectdir = baseDir + "fmbmapper";



        System.out.println("======请确保本地代码已经提交====\n");
        System.out.println("======输入任意字符回车 继续====\n");


        Scanner scanner = new Scanner(System.in);
        while (true)
        {
            String s = scanner.nextLine();
            if (StringUtils.isNotBlank(s)) {
                break;
            }
        }



        /**
         * 开始---------
         */
//        String tableName =  "fmb_admin" ;
//        String tableName =  "fmbx_business_partner_check_action" ;
//        String tableName =  "fmbx_business_partner_ext" ;
        /**
         * 结束---------
         */

        ArrayList<String> tables = new ArrayList<>();
        tables.add("fmb_product_index") ;


        FastAutoGenerator.create(url, username, passwd)
                .globalConfig(builder -> {
                    builder
//                            .author("youfa.hou") // 设置作者
                            .disableOpenDir()
//                            .enableSwagger() // 开启 swagger 模式
                            .fileOverride() // 覆盖已生成文件
                            .dateType(DateType.TIME_PACK)

                            // 注释时间策略。
                            .commentDate("yyyy-MM-dd")
                            .outputDir(projectdir +"/src/main/java"); // 指定输出目录
                })

                .packageConfig(builder -> {
                    builder.parent("com.fmb.server2022") // 设置父包名
                            .moduleName("fmb_new") // 设置父包模块名

                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml,
                                    projectdir+ "/src/main/resources/mapper/fmb_new"));
                    // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder
                            .addInclude(tables); // 设置需要生成的表名
//                            .addTablePrefix("t_", "c_"); // 设置过滤表前缀
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateConfig(builder -> {
                    //genpath/templates/entity.java.ftl   不需要增加尾部的.ftl 后缀
                    builder.entity("/genpath/templates/entity.java")
                            .serviceImpl("/genpath/templates/serviceImpl.java")

                            .controller("")
                            .build();


                })
                .injectionConfig(builder -> {

                    Map<String, Object> map = new HashMap<>();
                    map.put("dbkey","master") ;
                    builder.customMap(map) ;

                })
                .execute();



        for (String tableName : tables) {
            //把controller 挪个位置
            String controllerPath = "src/main/java/com/fmb/server2022/fmb_new/controller/";
            String source = baseDir + "fmbmapper/" + controllerPath +
                    StringUtils.capitalize(toCamelCase(tableName))
                    +"Controller.java"  ;
            try {
                File srcFile = new File(source);

                //直接删除controller
                srcFile.delete() ;

            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }




    @org.junit.Test
    public void jsonReq() {

        String json = "{\n" +
                "    \"xaid\": 200100,\n" +
                "    \"bps_id\": 0,\n" +
                "    \"bp_id\": 0,\n" +
                "    \"bpc_id\": 0,\n" +
                "    \"ticket_type\": 0,\n" +
                "    \"sell_stat\": 1,\n" +
                "    \"title\": \"\",\n" +
                "    \"sub_title\": \"\",\n" +
                "    \"plat_id\": \"\",\n" +
                "    \"city_id\": 0,\n" +
                "    \"sales_city_ids\": \"\",\n" +
                "    \"business_type\": 0,\n" +
                "    \"is_confirm\": 0,\n" +
                "    \"banner_list\": \"\",\n" +
                "    \"video_media_id\": 0,\n" +
                "    \"ctime\": \"0000-00-00 00:00:00\",\n" +
                "    \"utime\": \"0000-00-00 00:00:00\"\n" +
                "}" ;

        JSONObject jsonObject = JSON.parseObject(json);

        Set<String> keys = jsonObject.keySet();

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("package com.fmb.server2022.reqdomain;\n" +
                "\n" +
                "import lombok.Data;\n" +
                "import javax.validation.constraints.NotEmpty;\n" +
                "import javax.validation.constraints.NotBlank;\n" +
                "import javax.validation.constraints.NotNull;\n") ;


        stringBuffer.append("@Data   \n" ) ;
        String cname = "Req"+System.currentTimeMillis() ;
        stringBuffer.append("public class "+cname+" {\n  " ) ;





        for (String key : keys) {
//            System.out.println("key->"+key);

            if (jsonObject.get(key) instanceof String) {

                stringBuffer.append("@NotBlank");
                stringBuffer.append("\n" ) ;
                stringBuffer.append("private String "+col2CamelString(key) +" ;");
                stringBuffer.append("\n" ) ;

            }
            if (jsonObject.get(key) instanceof Integer) {
                stringBuffer.append("@NotNull");
                stringBuffer.append("\n" ) ;
                stringBuffer.append("private  Integer  "+col2CamelString(key) +" ;");
                stringBuffer.append("\n" ) ;
            }

        }

        stringBuffer.append(        "}\n") ;

        try {
            String jfile = cname +".java";
            FileUtils.writeStringToFile(new File("/Users/<USER>/workspace/fmbx/server2022/src/main/java/com/fmb/server2022/reqdomain/" + jfile),stringBuffer.toString(),"UTF-8");

            System.out.println(jfile+" write done");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void stringCameTest() {


        String x = "sales_city_ids" ;

        String x1 = col2CamelString(x);
        System.out.println(x1);

    }

    public String col2CamelString(String x) {
        StringBuffer stringBuffer = new StringBuffer();
        boolean nextCharCap = false ;
        for (char c : x.toCharArray()) {

            if (c=='_') {
                nextCharCap = true;
            }else {
                if (nextCharCap) {
                    stringBuffer.append((""+c).toUpperCase()) ;
                    nextCharCap = false ;
                }else {
                    stringBuffer.append(c) ;
                }
            }
        }

        return stringBuffer.toString();
    }

}
