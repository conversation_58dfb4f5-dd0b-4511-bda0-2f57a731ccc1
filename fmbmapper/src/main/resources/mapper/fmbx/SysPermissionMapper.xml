<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fmb.server2022.fmbx.mapper.SysPermissionMapper">


    <select id="selectByRoleid" parameterType="int" resultType="com.fmb.server2022.fmbx.entity.SysPermission">

        select p.* from sys_role_permission rp ,sys_permission p
        where p.id = rp.permission_id
        and rp.role_id = #{0}

    </select>

</mapper>
