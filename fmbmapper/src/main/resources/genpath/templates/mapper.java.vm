package

import org.apache.ibatis.annotations.Mapper;

${package.Mapper}
        ;
    ${package.Entity}.${entity};
    ${superMapperClassPackage}
        ;
#if(${mapperAnnotation})
#end

/**
 * <p>
 * $!{table.comment} Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${mapperAnnotation})
@Mapper
#end
#if(${kotlin})
interface ${table.mapperName} : ${superMapperClass}<${entity}>
#else
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}
#end
