<#assign vhaveDateFile = false >
<#macro is_have_date_field t >
    <#list t.fields as field>
        <#if field.propertyType=="LocalDateTime" >
            <#assign vhaveDateFile = true >
    </#if>
    </#list>
</#macro>
<#macro idJavaFieldName table>
    <#list table.fields as field>
        <#if field.keyIdentityFlag>
            <#assign priJavaField=field.propertyName >
            <#assign priJavaFieldCap=field.propertyName?cap_first  >
        </#if>
    </#list>
</#macro>