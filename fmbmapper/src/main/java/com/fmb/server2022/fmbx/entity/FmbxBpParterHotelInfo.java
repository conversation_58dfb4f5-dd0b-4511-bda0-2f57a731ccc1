package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 供应商酒店预定信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bp_parter_hotel_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBpParterHotelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bp_id")
    private Integer bpId;

    /**
     * 酒店订房确认方式:1-微信QQ,2-订房邮箱,3-订房传真,4-其他
     */
    private Integer hotelConfirmType;

    /**
     * 预订部工作时间
     */
    private String hotelWorkTime;

    /**
     * 订房联系人
     */
    private String hotelConfirmContractName;

    /**
     * 订房电话
     */
    private String hotelConfirmContractPhone;

    /**
     * 订房传真
     */
    private String hotelConfirmContractFax;

    /**
     * 订房邮箱
     */
    private String hotelConfirmContractEmail;

    /**
     * 订房备注
     */
    private String hotelConfirmContractNote;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public Integer getHotelConfirmType() {
        return hotelConfirmType;
    }

    public void setHotelConfirmType(Integer hotelConfirmType) {
        this.hotelConfirmType = hotelConfirmType;
    }
    public String getHotelWorkTime() {
        return hotelWorkTime;
    }

    public void setHotelWorkTime(String hotelWorkTime) {
        this.hotelWorkTime = hotelWorkTime;
    }
    public String getHotelConfirmContractName() {
        return hotelConfirmContractName;
    }

    public void setHotelConfirmContractName(String hotelConfirmContractName) {
        this.hotelConfirmContractName = hotelConfirmContractName;
    }
    public String getHotelConfirmContractPhone() {
        return hotelConfirmContractPhone;
    }

    public void setHotelConfirmContractPhone(String hotelConfirmContractPhone) {
        this.hotelConfirmContractPhone = hotelConfirmContractPhone;
    }
    public String getHotelConfirmContractFax() {
        return hotelConfirmContractFax;
    }

    public void setHotelConfirmContractFax(String hotelConfirmContractFax) {
        this.hotelConfirmContractFax = hotelConfirmContractFax;
    }
    public String getHotelConfirmContractEmail() {
        return hotelConfirmContractEmail;
    }

    public void setHotelConfirmContractEmail(String hotelConfirmContractEmail) {
        this.hotelConfirmContractEmail = hotelConfirmContractEmail;
    }
    public String getHotelConfirmContractNote() {
        return hotelConfirmContractNote;
    }

    public void setHotelConfirmContractNote(String hotelConfirmContractNote) {
        this.hotelConfirmContractNote = hotelConfirmContractNote;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxBpParterHotelInfo{" +
            "bpId=" + bpId +
            ", hotelConfirmType=" + hotelConfirmType +
            ", hotelWorkTime=" + hotelWorkTime +
            ", hotelConfirmContractName=" + hotelConfirmContractName +
            ", hotelConfirmContractPhone=" + hotelConfirmContractPhone +
            ", hotelConfirmContractFax=" + hotelConfirmContractFax +
            ", hotelConfirmContractEmail=" + hotelConfirmContractEmail +
            ", hotelConfirmContractNote=" + hotelConfirmContractNote +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
