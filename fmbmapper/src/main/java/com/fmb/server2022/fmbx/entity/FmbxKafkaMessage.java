package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 有问题的kafka消息,需要重新消费或者优化
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_kafka_message")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxKafkaMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String messageId;

    /**
     * 主题
     */
    private String topic;

    /**
     * 分组值
     */
    private String groupval;

    /**
     * 消息内容
     */
    private String messageVal;

    /**
     * 状态:1-执行异常,2-超时warn
     */
    private Integer type;

    /**
     * 状态:0-新建,1-已经重试
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * 处理耗时(毫秒数)
     */
    private Integer useMillions;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 异常信息
     */
    private String exceptionInfo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }
    public String getGroupval() {
        return groupval;
    }

    public void setGroupval(String groupval) {
        this.groupval = groupval;
    }
    public String getMessageVal() {
        return messageVal;
    }

    public void setMessageVal(String messageVal) {
        this.messageVal = messageVal;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public Integer getRetryNum() {
        return retryNum;
    }

    public void setRetryNum(Integer retryNum) {
        this.retryNum = retryNum;
    }
    public Integer getUseMillions() {
        return useMillions;
    }

    public void setUseMillions(Integer useMillions) {
        this.useMillions = useMillions;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getExceptionInfo() {
        return exceptionInfo;
    }

    public void setExceptionInfo(String exceptionInfo) {
        this.exceptionInfo = exceptionInfo;
    }

    @Override
    public String toString() {
        return "FmbxKafkaMessage{" +
            "id=" + id +
            ", messageId=" + messageId +
            ", topic=" + topic +
            ", groupval=" + groupval +
            ", messageVal=" + messageVal +
            ", type=" + type +
            ", status=" + status +
            ", versionNum=" + versionNum +
            ", retryNum=" + retryNum +
            ", useMillions=" + useMillions +
            ", ctime=" + ctime +
            ", exceptionInfo=" + exceptionInfo +
        "}";
    }
}
