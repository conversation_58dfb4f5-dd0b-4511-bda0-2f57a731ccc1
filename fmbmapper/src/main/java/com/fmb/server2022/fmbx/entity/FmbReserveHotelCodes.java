package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 预约酒店用户预约码
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_reserve_hotel_codes")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbReserveHotelCodes implements Serializable {

    private static final long serialVersionUID = 1L;
    public  static final Map<Integer, String> statusMap;
    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"正常");
            map.put(2,"退款中");
            map.put(3,"失效");
            statusMap = Collections.unmodifiableMap(map);
        }
    }


    /**
     * 自增ID
     */
    @TableId(value = "code_id", type = IdType.AUTO)
    private Integer codeId;

    /**
     * 活动ID
     */
    private Integer aid;

    /**
     * 票种ID
     */
    private Integer ticketId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 电子码记录
     */
    private String exchangeCode;

    /**
     * 预约兑换码
     */
    private String code;

    /**
     * 次卡的剩余次数
     */
    private Integer number;

    /**
     * 核销使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime useTime;

    /**
     * 1正常,2退款中,3失效
     */
    private Integer status;

    /**
     * 后台操作用户
     */
    private Integer adminUid;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;


    @TableField(exist = false)
    private String codeDesc ;

    @TableField(exist = false)
    private String alertStr ;

    @TableField(exist = false)
    private String codeAllDesc ;

    @TableField(exist = false)
    private Integer sortRank ;


    @TableField(exist = false)
    private String codeUrl ;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getCodeId() {
        return codeId;
    }

    public void setCodeId(Integer codeId) {
        this.codeId = codeId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getTicketId() {
        return ticketId;
    }

    public void setTicketId(Integer ticketId) {
        this.ticketId = ticketId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getExchangeCode() {
        return exchangeCode;
    }

    public void setExchangeCode(String exchangeCode) {
        this.exchangeCode = exchangeCode;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public String getCodeDesc() {
        return codeDesc;
    }

    public String getAlertStr() {
        return alertStr;
    }

    public String getCodeAllDesc() {
        return codeAllDesc;
    }

    public void setCodeAllDesc(String codeAllDesc) {
        this.codeAllDesc = codeAllDesc;
    }

    public void setAlertStr(String alertStr) {
        this.alertStr = alertStr;
    }

    public void setCodeDesc(String codeDesc) {
        this.codeDesc = codeDesc;
    }

    public Integer getSortRank() {
        return sortRank;
    }

    public void setSortRank(Integer sortRank) {
        this.sortRank = sortRank;
    }

    public String getCodeUrl() {
        return codeUrl;
    }

    public void setCodeUrl(String codeUrl) {
        this.codeUrl = codeUrl;
    }

    @Override
    public String toString() {
        return "FmbReserveHotelCodes{" +
            "codeId=" + codeId +
            ", aid=" + aid +
            ", ticketId=" + ticketId +
            ", orderSn=" + orderSn +
            ", exchangeCode=" + exchangeCode +
            ", code=" + code +
            ", number=" + number +
            ", useTime=" + useTime +
            ", status=" + status +
            ", adminUid=" + adminUid +
            ", utime=" + utime +
            ", ctime=" + ctime +
        "}";
    }
}
