package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomdatePrice;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteRoomdatePriceMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店套餐房型日期价格表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class FmbxSuiteRoomdatePriceServiceImpl extends ServiceImpl<FmbxSuiteRoomdatePriceMapper, FmbxSuiteRoomdatePrice> implements IFmbxSuiteRoomdatePriceService {

}
