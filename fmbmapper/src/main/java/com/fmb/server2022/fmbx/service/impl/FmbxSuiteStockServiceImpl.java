package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuiteStock;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteStockMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店库存表(room_id和target_date决定组合决定库存) 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class FmbxSuiteStockServiceImpl extends ServiceImpl<FmbxSuiteStockMapper, FmbxSuiteStock> implements IFmbxSuiteStockService {

}
