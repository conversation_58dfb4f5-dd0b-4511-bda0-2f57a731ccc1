package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户主表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_bps")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxBps implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "bps_id", type = IdType.AUTO)

    private Integer bpsId;

    /**
     * 商户名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 商户类型:1-酒店,2-其他
     */

    private Integer bpsType;

    /**
     * 手机号码
     */

    private String phone;

    /**
     * 省份
     */

    private String provinces;

    /**
     * 区
     */

    private String area;

    /**
     * 酒店地址
     */

    private String address;

    /**
     * 位置经度
     */

    private Float longitude;

    /**
     * 位置纬度
     */

    private Float latitude;

    /**
     * 开业时间哪年
     */

    private String openTime;

    /**
     * 装修时间哪年
     */

    private String decorateTime;

    /**
     * 客房总数
     */

    private Integer houseNum;

    /**
     * 星级档次:1-豪华型,2-高档型,3-舒适型
     */

    private Integer starLevel;

    /**
     * 携带宠物:1-可以,0-不可以
     */
    private Integer takePet;

    /**
     * 入住时间几点后
     */

    private String playTime;

    /**
     * 退房时间几点前
     */

    private String leaveTime;

    /**
     * 状态:0-屏蔽,1-正常
     */

    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 创建者ID
     */

    private Integer createUid;



    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 台后操作用户
     */

    private Integer adminUid;


    //   可编辑的供应商id
//  数据库字段名 bpid_of_edit
    private Integer bpidOfEdit;

    //   基础信息完成度:1-完成,0-未完成
//  数据库字段名 flag_basic
    private Integer flagBasic;

    //   房型信息完成度:1-完成,0-未完成
//  数据库字段名 flag_room
    private Integer flagRoom;

    //   图片信息完成度:1-完成,0-未完成
//  数据库字段名 flag_pic
    private Integer flagPic;

    //   视频信息完成度:1-完成,0-未完成
//  数据库字段名 flag_video
    private Integer flagVideo;
    /**
     * 酒店介绍
     */

    @TableField(exist = false)
    private String introduce;
    /**
     * 酒店政策
     */
    @TableField(exist = false)
    private String policy;
    /**
     * 餐饮信息
     */

    @TableField(exist = false)
    private String foodInfo;
    /**
     * 设施服务
     */

    @TableField(exist = false)
    private String services;

    public Integer getTakePet() {
        return takePet;
    }

    public void setTakePet(Integer takePet) {
        this.takePet = takePet;
    }

    public Integer getBpidOfEdit() {
        return bpidOfEdit;
    }

    public void setBpidOfEdit(Integer bpidOfEdit) {
        this.bpidOfEdit = bpidOfEdit;
    }

    public Integer getFlagBasic() {
        return flagBasic;
    }

    public void setFlagBasic(Integer flagBasic) {
        this.flagBasic = flagBasic;
    }

    public Integer getFlagRoom() {
        return flagRoom;
    }

    public void setFlagRoom(Integer flagRoom) {
        this.flagRoom = flagRoom;
    }

    public Integer getFlagPic() {
        return flagPic;
    }

    public void setFlagPic(Integer flagPic) {
        this.flagPic = flagPic;
    }

    public Integer getFlagVideo() {
        return flagVideo;
    }

    public void setFlagVideo(Integer flagVideo) {
        this.flagVideo = flagVideo;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getFoodInfo() {
        return foodInfo;
    }

    public void setFoodInfo(String foodInfo) {
        this.foodInfo = foodInfo;
    }

    public String getServices() {
        return services;
    }

    public void setServices(String services) {
        this.services = services;
    }

    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getBpsType() {
        return bpsType;
    }

    public void setBpsType(Integer bpsType) {
        this.bpsType = bpsType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvinces() {
        return provinces;
    }

    public void setProvinces(String provinces) {
        this.provinces = provinces;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public String getOpenTime() {
        return openTime;
    }

    public void setOpenTime(String openTime) {
        this.openTime = openTime;
    }

    public String getDecorateTime() {
        return decorateTime;
    }

    public void setDecorateTime(String decorateTime) {
        this.decorateTime = decorateTime;
    }

    public Integer getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(Integer houseNum) {
        this.houseNum = houseNum;
    }

    public Integer getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(Integer starLevel) {
        this.starLevel = starLevel;
    }

    public String getPlayTime() {
        return playTime;
    }

    public void setPlayTime(String playTime) {
        this.playTime = playTime;
    }

    public String getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(String leaveTime) {
        this.leaveTime = leaveTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }

    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }

    @Override
    public String toString() {
        return "FmbxBps{" +
                "bpsId=" + bpsId +
                ", name=" + name +
                ", bpsType=" + bpsType +
                ", phone=" + phone +
                ", provinces=" + provinces +
                ", area=" + area +
                ", address=" + address +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", openTime=" + openTime +
                ", decorateTime=" + decorateTime +
                ", houseNum=" + houseNum +
                ", starLevel=" + starLevel +
                ", playTime=" + playTime +
                ", leaveTime=" + leaveTime +
                ", status=" + status +
                ", ctime=" + ctime +
                ", createUid=" + createUid +
                ", utime=" + utime +
                ", adminUid=" + adminUid +
                "}";
    }
}
