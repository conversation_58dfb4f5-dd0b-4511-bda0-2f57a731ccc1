package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuiteExt;
import com.fmb.server2022.fmbx.mapper.FmbxSuiteExtMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuiteExtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 套餐扩展表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class FmbxSuiteExtServiceImpl extends ServiceImpl<FmbxSuiteExtMapper, FmbxSuiteExt> implements IFmbxSuiteExtService {

}
