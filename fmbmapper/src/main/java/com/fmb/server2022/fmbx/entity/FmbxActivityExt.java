package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * fmbx活动主表扩展
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_activity_ext")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxActivityExt implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer xaid;

    /**
     * 富文本内容
     */
    private String richTxt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public String getRichTxt() {
        return richTxt;
    }

    public void setRichTxt(String richTxt) {
        this.richTxt = richTxt;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbxActivityExt{" +
            "xaid=" + xaid +
            ", richTxt=" + richTxt +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
