package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单佣金信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_mycps_commission_order")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbMycpsCommissionOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    public  static final Map<Integer, String> typeMap;
    public  static final Map<Integer, String> modeMap;
    public  static final Map<Integer, String> statusMap;
    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"直佣");
            map.put(2,"间佣");
            map.put(3,"额外奖励");
            typeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"返佣模式订单");
            map.put(2,"分销价模式订单");
            modeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(-1,"已失效");
            map.put(0,"提交订单");
            map.put(1,"正常");
            map.put(2,"冻结中");
            map.put(3,"提交订单");
            map.put(4,"提现中");
            map.put(-2,"分销价模式");
            statusMap = Collections.unmodifiableMap(map);
        }
    }
    /**
     * 自增ID
     */
    @TableId(value = "coid", type = IdType.AUTO)
    private Integer coid;

    /**
     * 分销商fmb_mycps表的mid
     */
    private Integer mid;

    /**
     * 分销商mid对应的uid
     */
    private Integer uid;

    /**
     * 产品/活动id
     */
    private Integer apId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 分销模式：1:返佣模式订单，2:分销价模式订单
     */
    private Integer orderMode;

    /**
     * 佣金提现表id
     */
    private Integer cashId;

    /**
     * 有效开始日期/解冻日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime validTime;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 最大商品金额，减去各种优惠后
     */
    private BigDecimal oriMoney;

    /**
     * 退款金额
     */
    private BigDecimal returnMoney;

    /**
     * 实际佣金金额
     */
    private BigDecimal realCommission;

    /**
     * 状态：-1:已失效，0:提交订单，1:正常，2:冻结中,3:提现中,4:已提现,-2:分销价模式订单不需提现
     */
    private Integer commissionStatus;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 佣金类型 1直佣 2间佣 3额外奖励
     */
    private Integer type;

    /**
     * 额外奖励名称
     */
    private String rewardName;

    /**
     * 额外奖励id
     */
    private Integer rewardId;

    /**
     * 毛利率
     */
    private BigDecimal rate;

    /**
     * 佣金与毛利率比例
     */
    private BigDecimal scale;

    private String mycpsDesc;

    public Integer getCoid() {
        return coid;
    }

    public void setCoid(Integer coid) {
        this.coid = coid;
    }
    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getApId() {
        return apId;
    }

    public void setApId(Integer apId) {
        this.apId = apId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getOrderMode() {
        return orderMode;
    }

    public void setOrderMode(Integer orderMode) {
        this.orderMode = orderMode;
    }
    public Integer getCashId() {
        return cashId;
    }

    public void setCashId(Integer cashId) {
        this.cashId = cashId;
    }
    public LocalDateTime getValidTime() {
        return validTime;
    }

    public void setValidTime(LocalDateTime validTime) {
        this.validTime = validTime;
    }
    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }
    public BigDecimal getOriMoney() {
        return oriMoney;
    }

    public void setOriMoney(BigDecimal oriMoney) {
        this.oriMoney = oriMoney;
    }
    public BigDecimal getReturnMoney() {
        return returnMoney;
    }

    public void setReturnMoney(BigDecimal returnMoney) {
        this.returnMoney = returnMoney;
    }
    public BigDecimal getRealCommission() {
        return realCommission;
    }

    public void setRealCommission(BigDecimal realCommission) {
        this.realCommission = realCommission;
    }
    public Integer getCommissionStatus() {
        return commissionStatus;
    }

    public void setCommissionStatus(Integer commissionStatus) {
        this.commissionStatus = commissionStatus;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getRewardName() {
        return rewardName;
    }

    public void setRewardName(String rewardName) {
        this.rewardName = rewardName;
    }
    public Integer getRewardId() {
        return rewardId;
    }

    public void setRewardId(Integer rewardId) {
        this.rewardId = rewardId;
    }
    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }
    public BigDecimal getScale() {
        return scale;
    }

    public void setScale(BigDecimal scale) {
        this.scale = scale;
    }

    public String getMycpsDesc() {
        return mycpsDesc;
    }

    public void setMycpsDesc(String mycpsDesc) {
        this.mycpsDesc = mycpsDesc;
    }

    @Override
    public String toString() {
        return "FmbMycpsCommissionOrder{" +
            "coid=" + coid +
            ", mid=" + mid +
            ", uid=" + uid +
            ", apId=" + apId +
            ", orderSn=" + orderSn +
            ", orderMode=" + orderMode +
            ", cashId=" + cashId +
            ", validTime=" + validTime +
            ", commissionRate=" + commissionRate +
            ", oriMoney=" + oriMoney +
            ", returnMoney=" + returnMoney +
            ", realCommission=" + realCommission +
            ", commissionStatus=" + commissionStatus +
            ", utime=" + utime +
            ", ctime=" + ctime +
            ", type=" + type +
            ", rewardName=" + rewardName +
            ", rewardId=" + rewardId +
            ", rate=" + rate +
            ", scale=" + scale +
        "}";
    }
}
