package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbUserBrowseRecord;
import com.fmb.server2022.fmbx.mapper.FmbUserBrowseRecordMapper;
import com.fmb.server2022.fmbx.service.IFmbUserBrowseRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 *  服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-08-22
 */
@Service
public class FmbUserBrowseRecordServiceImpl extends ServiceImpl<FmbUserBrowseRecordMapper, FmbUserBrowseRecord> implements IFmbUserBrowseRecordService {

}
