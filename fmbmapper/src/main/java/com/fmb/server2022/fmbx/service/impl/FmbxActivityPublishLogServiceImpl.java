package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxActivityPublishLog;
import com.fmb.server2022.fmbx.mapper.FmbxActivityPublishLogMapper;
import com.fmb.server2022.fmbx.service.IFmbxActivityPublishLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 活动上下架操作记录 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-12-06
 */
@Service
public class FmbxActivityPublishLogServiceImpl extends ServiceImpl<FmbxActivityPublishLogMapper, FmbxActivityPublishLog> implements IFmbxActivityPublishLogService {

}
