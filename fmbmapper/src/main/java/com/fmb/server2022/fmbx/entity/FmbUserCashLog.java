package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 用户账户余额流水日志
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_user_cash_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbUserCashLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    @TableId(value = "cashlog_id", type = IdType.AUTO)
    private Integer cashlogId;

    /**
     * 获取返现用户uid
     */
    private Integer uid;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 金额
     */
    private BigDecimal money;

    /**
     * 用户的标识
     */
    private String openid;

    /**
     * 该金额来源 （1为分享返现 2为下单返现 3为退货返还 4 系统操作 5 超时退款到余额 50 提现 51 为下单抵扣 52 退货扣减 ）
     */
    private Integer sourceType;

    /**
     * 1:收入,2-支出
     */
    private Integer directionType;

    /**
     * 对应别的id,因为涉及到退款，所以弄成字符
     */
    private String returnIds;

    /**
     * 第几份礼包
     */
    private Integer number;

    /**
     * 状态；1，正常；2，冻结中 3，已失效 
     */
    private Integer status;

    /**
     * 有效开始日期/解冻日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime validTime;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 后台用户的UID
     */
    private Integer adminUid;

    /**
     * 操作说明
     */
    private String note;

    public Integer getCashlogId() {
        return cashlogId;
    }

    public void setCashlogId(Integer cashlogId) {
        this.cashlogId = cashlogId;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }
    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }
    public Integer getDirectionType() {
        return directionType;
    }

    public void setDirectionType(Integer directionType) {
        this.directionType = directionType;
    }
    public String getReturnIds() {
        return returnIds;
    }

    public void setReturnIds(String returnIds) {
        this.returnIds = returnIds;
    }
    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public LocalDateTime getValidTime() {
        return validTime;
    }

    public void setValidTime(LocalDateTime validTime) {
        this.validTime = validTime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public String toString() {
        return "FmbUserCashLog{" +
            "cashlogId=" + cashlogId +
            ", uid=" + uid +
            ", orderSn=" + orderSn +
            ", money=" + money +
            ", openid=" + openid +
            ", sourceType=" + sourceType +
            ", directionType=" + directionType +
            ", returnIds=" + returnIds +
            ", number=" + number +
            ", status=" + status +
            ", validTime=" + validTime +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", adminUid=" + adminUid +
            ", note=" + note +
        "}";
    }
}
