package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbGroupTickets;
import com.fmb.server2022.fmbx.mapper.FmbGroupTicketsMapper;
import com.fmb.server2022.fmbx.service.IFmbGroupTicketsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 具体票务信息表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-24
 */
@Service
public class FmbGroupTicketsServiceImpl extends ServiceImpl<FmbGroupTicketsMapper, FmbGroupTickets> implements IFmbGroupTicketsService {

}
