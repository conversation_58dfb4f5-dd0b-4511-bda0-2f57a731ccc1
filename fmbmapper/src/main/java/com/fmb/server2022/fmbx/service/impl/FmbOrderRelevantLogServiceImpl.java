package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderRelevantLog;
import com.fmb.server2022.fmbx.mapper.FmbOrderRelevantLogMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderRelevantLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单相关日志 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
public class FmbOrderRelevantLogServiceImpl extends ServiceImpl<FmbOrderRelevantLogMapper, FmbOrderRelevantLog> implements IFmbOrderRelevantLogService {

}
