package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单对应的具体商品信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_goods")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rec_id", type = IdType.AUTO)
    private Integer recId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 第三方平台的唯一订单流水号
     */
    private String thirdSn;

    /**
     * 用户UID
     */
    private Integer uid;

    /**
     * 长线游打包产品product_id
     */
    private Integer packProductId;

    /**
     * 长线游被打包的产品product_id
     */
    private Integer productId;

    /**
     * 长线游被打包的产品供应商supplier_id
     */
    private Integer supplierId;

    /**
     * 长线游被打包的产品属性sell_id
     */
    private Integer sellId;

    /**
     * 单品类型
     */
    private String typeName;

    /**
     * 机票旅行状态1-往2-返3-往返组合
     */
    private Integer planeTripStatus;

    /**
     * 活动ID
     */
    private Integer aid;

    /**
     * 1:普通活动;2:app专享活动;3:app专享价格活动
     */
    private Integer activityType;

    /**
     * 商家ID
     */
    private Integer shopUserId;

    /**
     * 0-其它商家的；其它-自己的仓库
     */
    private Integer shopWarehouseId;

    /**
     * 商品分组ID
     */
    private Integer goodsGroupId;

    /**
     * 1:演出;2:景点3通用4酒店
     */
    private Integer goodsType;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品数量
     */
    private Integer goodsNumber;

    /**
     * 市场价格
     */
    private BigDecimal marketPrice;

    /**
     * 订单的价格
     */
    private BigDecimal goodsPrice;

    /**
     * 分销价格
     */
    private BigDecimal mycpsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime playTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime leaveTime;

    @TableField(exist = false)
    private String playDate;

    @TableField(exist = false)
    private String leaveDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 床型需求
     */
    private String bedRequire;

    /**
     * 附加表单
     */
    private String extInfo;

    /**
     * 商品行号
     */
    private Integer rowIndex;

    /**
     * 商品税费
     */
    private BigDecimal taxMoney;

    /**
     * 商品实际结算价税费
     */
    private BigDecimal settleTaxMoney;

    /**
     * 该产品票种是否在成团项目内(0:不在成团项目内,1:在成团项目内)
     */
    private Integer tuanStatus;

    /**
     * 扩展详情信息（在线选座存储几排几号）
     */
    private String extendInfo;

    /**
     * 货物的信息，如套餐名、房型名，票种名等
     */
    private String goodExt;

    @TableField(exist = false)
    private Integer hasInvoice;

    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getThirdSn() {
        return thirdSn;
    }

    public void setThirdSn(String thirdSn) {
        this.thirdSn = thirdSn;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getPackProductId() {
        return packProductId;
    }

    public void setPackProductId(Integer packProductId) {
        this.packProductId = packProductId;
    }
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }
    public Integer getSellId() {
        return sellId;
    }

    public void setSellId(Integer sellId) {
        this.sellId = sellId;
    }
    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    public Integer getPlaneTripStatus() {
        return planeTripStatus;
    }

    public void setPlaneTripStatus(Integer planeTripStatus) {
        this.planeTripStatus = planeTripStatus;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getShopWarehouseId() {
        return shopWarehouseId;
    }

    public void setShopWarehouseId(Integer shopWarehouseId) {
        this.shopWarehouseId = shopWarehouseId;
    }
    public Integer getGoodsGroupId() {
        return goodsGroupId;
    }

    public void setGoodsGroupId(Integer goodsGroupId) {
        this.goodsGroupId = goodsGroupId;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getGoodsNumber() {
        return goodsNumber;
    }

    public void setGoodsNumber(Integer goodsNumber) {
        this.goodsNumber = goodsNumber;
    }
    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getMycpsPrice() {
        return mycpsPrice;
    }

    public void setMycpsPrice(BigDecimal mycpsPrice) {
        this.mycpsPrice = mycpsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public LocalDateTime getPlayTime() {
        return playTime;
    }

    public void setPlayTime(LocalDateTime playTime) {
        this.playTime = playTime;
    }
    public LocalDateTime getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(LocalDateTime leaveTime) {
        this.leaveTime = leaveTime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getBedRequire() {
        return bedRequire;
    }

    public void setBedRequire(String bedRequire) {
        this.bedRequire = bedRequire;
    }
    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }
    public BigDecimal getTaxMoney() {
        return taxMoney;
    }

    public void setTaxMoney(BigDecimal taxMoney) {
        this.taxMoney = taxMoney;
    }
    public BigDecimal getSettleTaxMoney() {
        return settleTaxMoney;
    }

    public void setSettleTaxMoney(BigDecimal settleTaxMoney) {
        this.settleTaxMoney = settleTaxMoney;
    }
    public Integer getTuanStatus() {
        return tuanStatus;
    }

    public void setTuanStatus(Integer tuanStatus) {
        this.tuanStatus = tuanStatus;
    }
    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }
    public String getGoodExt() {
        return goodExt;
    }

    public void setGoodExt(String goodExt) {
        this.goodExt = goodExt;
    }

    public Integer getHasInvoice() {
        return hasInvoice;
    }

    public void setHasInvoice(Integer hasInvoice) {
        this.hasInvoice = hasInvoice;
    }

    public String getPlayDate() {
        if (playTime != null){
            return playTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return "";
    }

    public String getLeaveDate() {
        if (leaveTime != null){
            return leaveTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return "";
    }

    @Override
    public String toString() {
        return "FmbOrderGoods{" +
            "recId=" + recId +
            ", orderSn=" + orderSn +
            ", thirdSn=" + thirdSn +
            ", uid=" + uid +
            ", packProductId=" + packProductId +
            ", productId=" + productId +
            ", supplierId=" + supplierId +
            ", sellId=" + sellId +
            ", typeName=" + typeName +
            ", planeTripStatus=" + planeTripStatus +
            ", aid=" + aid +
            ", activityType=" + activityType +
            ", shopUserId=" + shopUserId +
            ", shopWarehouseId=" + shopWarehouseId +
            ", goodsGroupId=" + goodsGroupId +
            ", goodsType=" + goodsType +
            ", goodsId=" + goodsId +
            ", goodsNumber=" + goodsNumber +
            ", marketPrice=" + marketPrice +
            ", goodsPrice=" + goodsPrice +
            ", mycpsPrice=" + mycpsPrice +
            ", settlePrice=" + settlePrice +
            ", playTime=" + playTime +
            ", leaveTime=" + leaveTime +
            ", ctime=" + ctime +
            ", bedRequire=" + bedRequire +
            ", extInfo=" + extInfo +
            ", rowIndex=" + rowIndex +
            ", taxMoney=" + taxMoney +
            ", settleTaxMoney=" + settleTaxMoney +
            ", tuanStatus=" + tuanStatus +
            ", extendInfo=" + extendInfo +
            ", goodExt=" + goodExt +
        "}";
    }
}
