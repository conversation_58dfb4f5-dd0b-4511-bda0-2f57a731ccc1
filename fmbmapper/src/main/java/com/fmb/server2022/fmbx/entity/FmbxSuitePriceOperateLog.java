package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * sku具体到天的价格修改日志
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_price_operate_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuitePriceOperateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "price_oplog_id", type = IdType.AUTO)
    private Integer priceOplogId;

    /**
     * price_operate_id
     */
    private Integer priceOperateId;

    /**
     * skuid
     */
    private Integer skuId;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate targetDate;

    /**
     * 具体操作信息
     */
    private String operateInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getPriceOplogId() {
        return priceOplogId;
    }

    public void setPriceOplogId(Integer priceOplogId) {
        this.priceOplogId = priceOplogId;
    }
    public Integer getPriceOperateId() {
        return priceOperateId;
    }

    public void setPriceOperateId(Integer priceOperateId) {
        this.priceOperateId = priceOperateId;
    }
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public LocalDate getTargetDate() {
        return targetDate;
    }

    public void setTargetDate(LocalDate targetDate) {
        this.targetDate = targetDate;
    }
    public String getOperateInfo() {
        return operateInfo;
    }

    public void setOperateInfo(String operateInfo) {
        this.operateInfo = operateInfo;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxSuitePriceOperateLog{" +
            "priceOplogId=" + priceOplogId +
            ", priceOperateId=" + priceOperateId +
            ", skuId=" + skuId +
            ", targetDate=" + targetDate +
            ", operateInfo=" + operateInfo +
            ", ctime=" + ctime +
        "}";
    }
}
