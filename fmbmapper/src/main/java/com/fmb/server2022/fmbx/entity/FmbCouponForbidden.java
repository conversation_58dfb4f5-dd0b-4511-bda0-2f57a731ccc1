package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 券组的禁用产品表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_coupon_forbidden")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbCouponForbidden implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 优惠券组id
     */
    private Integer couponId;

    /**
     * 产品(活动)id
     */
    private Integer aid;

    /**
     * 产品所属分站
     */
    private Integer cityId;

    /**
     * 禁用类型:1(白名单)、2(黑名单)
     */
    private Integer banType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 创建人
     */
    private Integer cuid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public Integer getBanType() {
        return banType;
    }

    public void setBanType(Integer banType) {
        this.banType = banType;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getCuid() {
        return cuid;
    }

    public void setCuid(Integer cuid) {
        this.cuid = cuid;
    }

    @Override
    public String toString() {
        return "FmbCouponForbidden{" +
            "id=" + id +
            ", couponId=" + couponId +
            ", aid=" + aid +
            ", cityId=" + cityId +
            ", banType=" + banType +
            ", ctime=" + ctime +
            ", cuid=" + cuid +
        "}";
    }
}
