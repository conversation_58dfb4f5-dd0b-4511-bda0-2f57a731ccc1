package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderActions;
import com.fmb.server2022.fmbx.mapper.FmbOrderActionsMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderActionsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单操作记录列表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbOrderActionsServiceImpl extends ServiceImpl<FmbOrderActionsMapper, FmbOrderActions> implements IFmbOrderActionsService {

}
