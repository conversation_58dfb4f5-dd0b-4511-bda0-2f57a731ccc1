package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.fmbx.mapper.FmbxScheduleJobMapper;
import com.fmb.server2022.fmbx.service.IFmbxScheduleJobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 定时任务执行 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-12-08
 */
@Service
public class FmbxScheduleJobServiceImpl extends ServiceImpl<FmbxScheduleJobMapper, FmbxScheduleJob> implements IFmbxScheduleJobService {

}
