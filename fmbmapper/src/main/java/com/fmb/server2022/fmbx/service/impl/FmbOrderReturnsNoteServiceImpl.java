package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderReturnsNote;
import com.fmb.server2022.fmbx.mapper.FmbOrderReturnsNoteMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsNoteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 退货内部备注表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
public class FmbOrderReturnsNoteServiceImpl extends ServiceImpl<FmbOrderReturnsNoteMapper, FmbOrderReturnsNote> implements IFmbOrderReturnsNoteService {

}
