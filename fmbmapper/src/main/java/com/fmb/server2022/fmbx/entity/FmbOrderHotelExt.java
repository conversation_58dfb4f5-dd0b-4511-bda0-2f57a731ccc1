package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店订单扩展信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_hotel_ext")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderHotelExt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @TableId
    private String orderSn;

    /**
     * 最晚到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastArriveTime;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate occupancyDate;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate leaveDate;

    /**
     * 床型需求
     */
    private String bedRequire;

    /**
     * 无烟吸烟需求
     */
    private String smokeRequire;

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public LocalDateTime getLastArriveTime() {
        return lastArriveTime;
    }

    public void setLastArriveTime(LocalDateTime lastArriveTime) {
        this.lastArriveTime = lastArriveTime;
    }
    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }
    public LocalDate getLeaveDate() {
        return leaveDate;
    }

    public void setLeaveDate(LocalDate leaveDate) {
        this.leaveDate = leaveDate;
    }
    public String getBedRequire() {
        return bedRequire;
    }

    public void setBedRequire(String bedRequire) {
        this.bedRequire = bedRequire;
    }
    public String getSmokeRequire() {
        return smokeRequire;
    }

    public void setSmokeRequire(String smokeRequire) {
        this.smokeRequire = smokeRequire;
    }

    @Override
    public String toString() {
        return "FmbOrderHotelExt{" +
            "orderSn=" + orderSn +
            ", lastArriveTime=" + lastArriveTime +
            ", occupancyDate=" + occupancyDate +
            ", leaveDate=" + leaveDate +
            ", bedRequire=" + bedRequire +
            ", smokeRequire=" + smokeRequire +
        "}";
    }
}
