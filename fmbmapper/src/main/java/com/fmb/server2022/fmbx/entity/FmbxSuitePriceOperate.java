package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 套餐修改操作
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_price_operate")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuitePriceOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "price_operate_id", type = IdType.AUTO)
    private Integer priceOperateId;

    /**
     * fmbx活动主表id
     */
    private Integer xaid;

    /**
     * 活动标题(冗余)
     */
    private String activityName;

    /**
     * sku_id
     */
    private Integer skuId;

    /**
     * 套餐房型名字(冗余)
     */
    private String skuName;

    /**
     * 套餐id
     */
    private Integer suiteId;

    /**
     * 操作数据(json格式)
     */
    private String operateData;

    /**
     * 修改类型:1-加价模式,2-整价模式,3-整价指定日期,4-excel上传,5-加价模式切换清除数据
     */
    private Integer changeType;

    /**
     * 后台用户类型:1-父母邦后台,2-供应商后台
     */
    private Integer adminType;

    /**
     * 用户uid
     */
    private Integer adminUid;

    /**
     * 后台用户名字
     */
    private String adminName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getPriceOperateId() {
        return priceOperateId;
    }

    public void setPriceOperateId(Integer priceOperateId) {
        this.priceOperateId = priceOperateId;
    }
    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public String getOperateData() {
        return operateData;
    }

    public void setOperateData(String operateData) {
        this.operateData = operateData;
    }
    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxSuitePriceOperate{" +
            "priceOperateId=" + priceOperateId +
            ", xaid=" + xaid +
            ", activityName=" + activityName +
            ", skuId=" + skuId +
            ", skuName=" + skuName +
            ", suiteId=" + suiteId +
            ", operateData=" + operateData +
            ", changeType=" + changeType +
            ", adminType=" + adminType +
            ", adminUid=" + adminUid +
            ", adminName=" + adminName +
            ", ctime=" + ctime +
        "}";
    }
}
