package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单操作记录列表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_actions")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderActions implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "action_id", type = IdType.AUTO)
    private Integer actionId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 后台操作的用户UID
     */
    private Integer adminUid;

    /**
     * 订单状态 0.未付款 1.等待发货  2.已发货 3.退货中 4.已退货 5.交易成功  6.交易关闭
     */
    private Integer orderStatus;

    /**
     * 商品配送情况
     */
    private Integer shippingStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 操作标识，长线游重组机票时加的。plane_reset:重组机票操作；re_confirm:二次确认操作(目前只有长线游用)；note_edit:后台用户操作,修改备注（目前只有长线游用）
     */
    private String actionType;

    /**
     * 动作说明
     */
    private String actionNote;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime logTime;

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }
    public Integer getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(Integer shippingStatus) {
        this.shippingStatus = shippingStatus;
    }
    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }
    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
    public String getActionNote() {
        return actionNote;
    }

    public void setActionNote(String actionNote) {
        this.actionNote = actionNote;
    }
    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    @Override
    public String toString() {
        return "FmbOrderActions{" +
            "actionId=" + actionId +
            ", orderSn=" + orderSn +
            ", adminUid=" + adminUid +
            ", orderStatus=" + orderStatus +
            ", shippingStatus=" + shippingStatus +
            ", payStatus=" + payStatus +
            ", actionType=" + actionType +
            ", actionNote=" + actionNote +
            ", logTime=" + logTime +
        "}";
    }
}
