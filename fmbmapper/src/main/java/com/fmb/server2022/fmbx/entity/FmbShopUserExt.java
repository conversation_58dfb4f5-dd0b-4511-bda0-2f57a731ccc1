package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 结算商家扩展信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_shop_user_ext")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbShopUserExt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 长线产品ID
     */
    private Integer shopUserId;

    /**
     * 供应商ID,长线结算用
     */
    private Integer supplierId;

    /**
     * 申请单ID
     */
    private Integer applyId;

    /**
     * 商家名称
     */
    private String username;

    /**
     * 财务联系人
     */
    private String financeContact;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 固话
     */
    private String tel;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 结算总额包含
     */
    private String settleMoney;

    /**
     * 是否需要佣金发票
     */
    private String isYongjinTickets;

    /**
     * 佣金发票地址
     */
    private String yongjinShippingAddress;

    /**
     * 其他附加信息
     */
    private String note;

    /**
     * 商家开户行
     */
    private String shopBank;

    /**
     * 商家账户号
     */
    private String shopAccountSn;

    /**
     * 商家账户名称
     */
    private String shopAccount;

    /**
     * 申请备注单信息
     */
    private String remark;

    /**
     * 具体的资源或活动类型=== old：老活动系统 longline：长线系统
     */
    private String types;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }
    public Integer getApplyId() {
        return applyId;
    }

    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getFinanceContact() {
        return financeContact;
    }

    public void setFinanceContact(String financeContact) {
        this.financeContact = financeContact;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getSettleMoney() {
        return settleMoney;
    }

    public void setSettleMoney(String settleMoney) {
        this.settleMoney = settleMoney;
    }
    public String getIsYongjinTickets() {
        return isYongjinTickets;
    }

    public void setIsYongjinTickets(String isYongjinTickets) {
        this.isYongjinTickets = isYongjinTickets;
    }
    public String getYongjinShippingAddress() {
        return yongjinShippingAddress;
    }

    public void setYongjinShippingAddress(String yongjinShippingAddress) {
        this.yongjinShippingAddress = yongjinShippingAddress;
    }
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    public String getShopBank() {
        return shopBank;
    }

    public void setShopBank(String shopBank) {
        this.shopBank = shopBank;
    }
    public String getShopAccountSn() {
        return shopAccountSn;
    }

    public void setShopAccountSn(String shopAccountSn) {
        this.shopAccountSn = shopAccountSn;
    }
    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbShopUserExt{" +
            "id=" + id +
            ", shopUserId=" + shopUserId +
            ", supplierId=" + supplierId +
            ", applyId=" + applyId +
            ", username=" + username +
            ", financeContact=" + financeContact +
            ", mobile=" + mobile +
            ", tel=" + tel +
            ", email=" + email +
            ", settleMoney=" + settleMoney +
            ", isYongjinTickets=" + isYongjinTickets +
            ", yongjinShippingAddress=" + yongjinShippingAddress +
            ", note=" + note +
            ", shopBank=" + shopBank +
            ", shopAccountSn=" + shopAccountSn +
            ", shopAccount=" + shopAccount +
            ", remark=" + remark +
            ", types=" + types +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
