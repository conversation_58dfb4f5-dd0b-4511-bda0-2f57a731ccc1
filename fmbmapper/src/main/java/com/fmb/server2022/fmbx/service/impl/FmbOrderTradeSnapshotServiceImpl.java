package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderTradeSnapshot;
import com.fmb.server2022.fmbx.mapper.FmbOrderTradeSnapshotMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderTradeSnapshotService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单交易快照表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbOrderTradeSnapshotServiceImpl extends ServiceImpl<FmbOrderTradeSnapshotMapper, FmbOrderTradeSnapshot> implements IFmbOrderTradeSnapshotService {

}
