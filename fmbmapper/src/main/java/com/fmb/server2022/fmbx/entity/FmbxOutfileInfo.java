package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 外部系统文件下载父母邦系统(钉钉)
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_outfile_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxOutfileInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "outfile_id", type = IdType.AUTO)
    private Integer outfileId;

    /**
     * 存储相对路径
     */
    private String savePath;

    /**
     * url地址
     */
    private String url;

    /**
     * 原始文件名称
     */
    private String sourceFileName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 文件扩展名字
     */
    private String type;

    /**
     * 后台用户类型:1-父母邦后台,2-商家后台
     */
    private Integer adminType;

    /**
     * 后台用户uid
     */
    private Integer adminUid;

    /**
     * 业务模块类型
     */
    private String modelType;

    /**
     * 业务模型的主键id
     */
    private Integer modelId;

    /**
     * 外部系统唯一id
     */
    private String thirdFileId;

    /**
     * 文件大小
     */
    private Integer length;

    /**
     * 扩展1
     */
    private String extString1;

    /**
     * 扩展2
     */
    private String extString2;

    /**
     * 扩展数值1
     */
    private Integer extInt1;

    /**
     * 扩展数值2
     */
    private Integer extInt2;

    public Integer getOutfileId() {
        return outfileId;
    }

    public void setOutfileId(Integer outfileId) {
        this.outfileId = outfileId;
    }
    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    public String getSourceFileName() {
        return sourceFileName;
    }

    public void setSourceFileName(String sourceFileName) {
        this.sourceFileName = sourceFileName;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }
    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }
    public String getThirdFileId() {
        return thirdFileId;
    }

    public void setThirdFileId(String thirdFileId) {
        this.thirdFileId = thirdFileId;
    }
    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }
    public String getExtString1() {
        return extString1;
    }

    public void setExtString1(String extString1) {
        this.extString1 = extString1;
    }
    public String getExtString2() {
        return extString2;
    }

    public void setExtString2(String extString2) {
        this.extString2 = extString2;
    }
    public Integer getExtInt1() {
        return extInt1;
    }

    public void setExtInt1(Integer extInt1) {
        this.extInt1 = extInt1;
    }
    public Integer getExtInt2() {
        return extInt2;
    }

    public void setExtInt2(Integer extInt2) {
        this.extInt2 = extInt2;
    }

    @Override
    public String toString() {
        return "FmbxOutfileInfo{" +
            "outfileId=" + outfileId +
            ", savePath=" + savePath +
            ", url=" + url +
            ", sourceFileName=" + sourceFileName +
            ", ctime=" + ctime +
            ", type=" + type +
            ", adminType=" + adminType +
            ", adminUid=" + adminUid +
            ", modelType=" + modelType +
            ", modelId=" + modelId +
            ", thirdFileId=" + thirdFileId +
            ", length=" + length +
            ", extString1=" + extString1 +
            ", extString2=" + extString2 +
            ", extInt1=" + extInt1 +
            ", extInt2=" + extInt2 +
        "}";
    }
}
