package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 房券sku表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_hotel_reserve_sku")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxHotelReserveSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.AUTO)
    private Integer skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 套餐id
     */
    private Integer suiteId;

    /**
     * 是否可拆分:0-不能拆分,1-可以拆分
     */
    private Integer canDivide;

    /**
     * 售卖开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sellStartTime;

    /**
     * 售卖结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sellEndTime;

    /**
     * 是否限购:0-不限,1-限购
     */
    private Integer flagBuyLimit;

    /**
     * 每单最多购买
     */
    private Integer eachOrderMaxNum;

    /**
     * 每单最少购买
     */
    private Integer eachOrderMinNum;

    /**
     * 每个用户最多买多少
     */
    private Integer maxBuyAllNum;

    /**
     * 售卖倒计时显示:0-不显示,1-显示
     */
    private Integer sellCountDownShowFlag;

    /**
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 库存
     */
    private Integer stockNum;

    /**
     * 可预约入住开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveChooseStartTime;

    /**
     * 可预约入住结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveChooseEndTime;

    /**
     * 预约有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveValidityStartTime;

    /**
     * 预约有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveValidityEndTime;

    /**
     * 不可入住星期,多个以逗号分隔
     */
    private String invalidReserveWeekDays;

    /**
     * 不可入住日期,多个以逗号分隔
     */
    private String invalidReserveDate;

    /**
     * 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
     */
    private Integer isReturn;

    /**
     * 退换政策
     */
    private String returnPolicy;

    /**
     * 退款有效期天数
     */
    private Integer returnValidDay;

    /**
     * 退款有效期时间
     */
    private String returnValidTime;

    /**
     * 退款有效期秒数(计算值)
     */
    private Integer returnValidSecond;

    /**
     * 开启自助退款:0-否,1-是
     */
    private Integer isAutoReturn;

    /**
     * 售买状态(库存和当前时间在售卖时间内的结果):0-无效,1-有效
     */
    private Integer flagSell;

    /**
     * 上下架状态:0-已删除,1-已上架,2-已下架,3-待完善
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 排序值,值越大越靠前
     */
    private Integer sortValue;

    /**
     * 售卖数量
     */
    private Integer sellNum;

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public Integer getCanDivide() {
        return canDivide;
    }

    public void setCanDivide(Integer canDivide) {
        this.canDivide = canDivide;
    }
    public LocalDateTime getSellStartTime() {
        return sellStartTime;
    }

    public void setSellStartTime(LocalDateTime sellStartTime) {
        this.sellStartTime = sellStartTime;
    }
    public LocalDateTime getSellEndTime() {
        return sellEndTime;
    }

    public void setSellEndTime(LocalDateTime sellEndTime) {
        this.sellEndTime = sellEndTime;
    }
    public Integer getFlagBuyLimit() {
        return flagBuyLimit;
    }

    public void setFlagBuyLimit(Integer flagBuyLimit) {
        this.flagBuyLimit = flagBuyLimit;
    }
    public Integer getEachOrderMaxNum() {
        return eachOrderMaxNum;
    }

    public void setEachOrderMaxNum(Integer eachOrderMaxNum) {
        this.eachOrderMaxNum = eachOrderMaxNum;
    }
    public Integer getEachOrderMinNum() {
        return eachOrderMinNum;
    }

    public void setEachOrderMinNum(Integer eachOrderMinNum) {
        this.eachOrderMinNum = eachOrderMinNum;
    }
    public Integer getMaxBuyAllNum() {
        return maxBuyAllNum;
    }

    public void setMaxBuyAllNum(Integer maxBuyAllNum) {
        this.maxBuyAllNum = maxBuyAllNum;
    }
    public Integer getSellCountDownShowFlag() {
        return sellCountDownShowFlag;
    }

    public void setSellCountDownShowFlag(Integer sellCountDownShowFlag) {
        this.sellCountDownShowFlag = sellCountDownShowFlag;
    }
    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }
    public LocalDateTime getReserveChooseStartTime() {
        return reserveChooseStartTime;
    }

    public void setReserveChooseStartTime(LocalDateTime reserveChooseStartTime) {
        this.reserveChooseStartTime = reserveChooseStartTime;
    }
    public LocalDateTime getReserveChooseEndTime() {
        return reserveChooseEndTime;
    }

    public void setReserveChooseEndTime(LocalDateTime reserveChooseEndTime) {
        this.reserveChooseEndTime = reserveChooseEndTime;
    }
    public LocalDateTime getReserveValidityStartTime() {
        return reserveValidityStartTime;
    }

    public void setReserveValidityStartTime(LocalDateTime reserveValidityStartTime) {
        this.reserveValidityStartTime = reserveValidityStartTime;
    }
    public LocalDateTime getReserveValidityEndTime() {
        return reserveValidityEndTime;
    }

    public void setReserveValidityEndTime(LocalDateTime reserveValidityEndTime) {
        this.reserveValidityEndTime = reserveValidityEndTime;
    }
    public String getInvalidReserveWeekDays() {
        return invalidReserveWeekDays;
    }

    public void setInvalidReserveWeekDays(String invalidReserveWeekDays) {
        this.invalidReserveWeekDays = invalidReserveWeekDays;
    }
    public String getInvalidReserveDate() {
        return invalidReserveDate;
    }

    public void setInvalidReserveDate(String invalidReserveDate) {
        this.invalidReserveDate = invalidReserveDate;
    }
    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }
    public String getReturnPolicy() {
        return returnPolicy;
    }

    public void setReturnPolicy(String returnPolicy) {
        this.returnPolicy = returnPolicy;
    }
    public Integer getReturnValidDay() {
        return returnValidDay;
    }

    public void setReturnValidDay(Integer returnValidDay) {
        this.returnValidDay = returnValidDay;
    }
    public String getReturnValidTime() {
        return returnValidTime;
    }

    public void setReturnValidTime(String returnValidTime) {
        this.returnValidTime = returnValidTime;
    }
    public Integer getReturnValidSecond() {
        return returnValidSecond;
    }

    public void setReturnValidSecond(Integer returnValidSecond) {
        this.returnValidSecond = returnValidSecond;
    }
    public Integer getIsAutoReturn() {
        return isAutoReturn;
    }

    public void setIsAutoReturn(Integer isAutoReturn) {
        this.isAutoReturn = isAutoReturn;
    }
    public Integer getFlagSell() {
        return flagSell;
    }

    public void setFlagSell(Integer flagSell) {
        this.flagSell = flagSell;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }
    public Integer getSellNum() {
        return sellNum;
    }

    public void setSellNum(Integer sellNum) {
        this.sellNum = sellNum;
    }

    @Override
    public String toString() {
        return "FmbxHotelReserveSku{" +
            "skuId=" + skuId +
            ", skuName=" + skuName +
            ", suiteId=" + suiteId +
            ", canDivide=" + canDivide +
            ", sellStartTime=" + sellStartTime +
            ", sellEndTime=" + sellEndTime +
            ", flagBuyLimit=" + flagBuyLimit +
            ", eachOrderMaxNum=" + eachOrderMaxNum +
            ", eachOrderMinNum=" + eachOrderMinNum +
            ", maxBuyAllNum=" + maxBuyAllNum +
            ", sellCountDownShowFlag=" + sellCountDownShowFlag +
            ", marketPrice=" + marketPrice +
            ", goodsPrice=" + goodsPrice +
            ", settlePrice=" + settlePrice +
            ", stockNum=" + stockNum +
            ", reserveChooseStartTime=" + reserveChooseStartTime +
            ", reserveChooseEndTime=" + reserveChooseEndTime +
            ", reserveValidityStartTime=" + reserveValidityStartTime +
            ", reserveValidityEndTime=" + reserveValidityEndTime +
            ", invalidReserveWeekDays=" + invalidReserveWeekDays +
            ", invalidReserveDate=" + invalidReserveDate +
            ", isReturn=" + isReturn +
            ", returnPolicy=" + returnPolicy +
            ", returnValidDay=" + returnValidDay +
            ", returnValidTime=" + returnValidTime +
            ", returnValidSecond=" + returnValidSecond +
            ", isAutoReturn=" + isAutoReturn +
            ", flagSell=" + flagSell +
            ", status=" + status +
            ", versionNum=" + versionNum +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", sortValue=" + sortValue +
            ", sellNum=" + sellNum +
        "}";
    }
}
