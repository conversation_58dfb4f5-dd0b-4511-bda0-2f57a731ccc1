package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店预约记录表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_hotel_reserve_order_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbHotelReserveOrderRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "or_id", type = IdType.AUTO)
    private Integer orId;

    /**
     * 通用订单号
     */
    private String generalOrderSn;

    /**
     * 酒店预约订单号
     */
    private String hotelOrderSn;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 入住人
     */
    private String receiver;

    /**
     * 用户的预约码信息
     */
    private String code;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 通用活动ID
     */
    private Integer generalAid;

    /**
     * 酒店活动ID
     */
    private Integer hotelAid;

    /**
     * 通用票种ID
     */
    private Integer generalTicketId;

    /**
     * 酒店票种ID
     */
    private Integer hotelTicketId;

    /**
     * 酒店套餐ID
     */
    private Integer packageId;

    /**
     * 酒店份数
     */
    private Integer goodsNumber;

    /**
     * 本次预约间夜量
     */
    private Integer reserveNum;

    /**
     * 本次消耗次数
     */
    private Integer useNum;

    /**
     * 商家ID
     */
    private Integer shopUserId;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate playTime;

    /**
     * 离店时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate leaveTime;

    /**
     * 预约的房型信息
     */
    private String houseType;

    /**
     * 预约床型信息
     */
    private String bedType;

    /**
     * 后台操作用户
     */
    private Integer adminUid;

    /**
     * 酒店确认号
     */
    private String confirmSn;

    /**
     * 酒店操作人员
     */
    private String operName;

    /**
     * 状态：,1已预约 2：已取消
     */
    private Integer status;

    /**
     * 预约创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 预约取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime cancelTime;

    /**
     * 操作人员类型：1商家,2后台操作人员
     */
    private Integer operType;

    /**
     * 类型 0父母邦 1周末安排 
     */
    private Integer smsType;

    public Integer getOrId() {
        return orId;
    }

    public void setOrId(Integer orId) {
        this.orId = orId;
    }
    public String getGeneralOrderSn() {
        return generalOrderSn;
    }

    public void setGeneralOrderSn(String generalOrderSn) {
        this.generalOrderSn = generalOrderSn;
    }
    public String getHotelOrderSn() {
        return hotelOrderSn;
    }

    public void setHotelOrderSn(String hotelOrderSn) {
        this.hotelOrderSn = hotelOrderSn;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public Integer getGeneralAid() {
        return generalAid;
    }

    public void setGeneralAid(Integer generalAid) {
        this.generalAid = generalAid;
    }
    public Integer getHotelAid() {
        return hotelAid;
    }

    public void setHotelAid(Integer hotelAid) {
        this.hotelAid = hotelAid;
    }
    public Integer getGeneralTicketId() {
        return generalTicketId;
    }

    public void setGeneralTicketId(Integer generalTicketId) {
        this.generalTicketId = generalTicketId;
    }
    public Integer getHotelTicketId() {
        return hotelTicketId;
    }

    public void setHotelTicketId(Integer hotelTicketId) {
        this.hotelTicketId = hotelTicketId;
    }
    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }
    public Integer getGoodsNumber() {
        return goodsNumber;
    }

    public void setGoodsNumber(Integer goodsNumber) {
        this.goodsNumber = goodsNumber;
    }
    public Integer getReserveNum() {
        return reserveNum;
    }

    public void setReserveNum(Integer reserveNum) {
        this.reserveNum = reserveNum;
    }
    public Integer getUseNum() {
        return useNum;
    }

    public void setUseNum(Integer useNum) {
        this.useNum = useNum;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public LocalDate getPlayTime() {
        return playTime;
    }

    public void setPlayTime(LocalDate playTime) {
        this.playTime = playTime;
    }
    public LocalDate getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(LocalDate leaveTime) {
        this.leaveTime = leaveTime;
    }
    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }
    public String getBedType() {
        return bedType;
    }

    public void setBedType(String bedType) {
        this.bedType = bedType;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getConfirmSn() {
        return confirmSn;
    }

    public void setConfirmSn(String confirmSn) {
        this.confirmSn = confirmSn;
    }
    public String getOperName() {
        return operName;
    }

    public void setOperName(String operName) {
        this.operName = operName;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }
    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }
    public Integer getSmsType() {
        return smsType;
    }

    public void setSmsType(Integer smsType) {
        this.smsType = smsType;
    }

    @Override
    public String toString() {
        return "FmbHotelReserveOrderRecord{" +
            "orId=" + orId +
            ", generalOrderSn=" + generalOrderSn +
            ", hotelOrderSn=" + hotelOrderSn +
            ", uid=" + uid +
            ", mobile=" + mobile +
            ", receiver=" + receiver +
            ", code=" + code +
            ", settlePrice=" + settlePrice +
            ", generalAid=" + generalAid +
            ", hotelAid=" + hotelAid +
            ", generalTicketId=" + generalTicketId +
            ", hotelTicketId=" + hotelTicketId +
            ", packageId=" + packageId +
            ", goodsNumber=" + goodsNumber +
            ", reserveNum=" + reserveNum +
            ", useNum=" + useNum +
            ", shopUserId=" + shopUserId +
            ", playTime=" + playTime +
            ", leaveTime=" + leaveTime +
            ", houseType=" + houseType +
            ", bedType=" + bedType +
            ", adminUid=" + adminUid +
            ", confirmSn=" + confirmSn +
            ", operName=" + operName +
            ", status=" + status +
            ", ctime=" + ctime +
            ", cancelTime=" + cancelTime +
            ", operType=" + operType +
            ", smsType=" + smsType +
        "}";
    }
}
