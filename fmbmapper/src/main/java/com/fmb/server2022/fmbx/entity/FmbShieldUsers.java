package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 屏蔽用户表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_shield_users")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbShieldUsers implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父母邦uid
     */
    private Integer uid;

    /**
     * 记事狗UID
     */
    private Integer juid;

    /**
     * 操作员uid
     */
    private Integer editUid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 过期时间
     */
    private Integer endTime;

    /**
     * 屏蔽原因
     */
    private String reason;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getJuid() {
        return juid;
    }

    public void setJuid(Integer juid) {
        this.juid = juid;
    }
    public Integer getEditUid() {
        return editUid;
    }

    public void setEditUid(Integer editUid) {
        this.editUid = editUid;
    }
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "FmbShieldUsers{" +
            "id=" + id +
            ", uid=" + uid +
            ", juid=" + juid +
            ", editUid=" + editUid +
            ", username=" + username +
            ", endTime=" + endTime +
            ", reason=" + reason +
        "}";
    }
}
