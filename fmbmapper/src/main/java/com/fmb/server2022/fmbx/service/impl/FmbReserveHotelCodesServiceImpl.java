package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.mapper.FmbReserveHotelCodesMapper;
import com.fmb.server2022.fmbx.service.IFmbReserveHotelCodesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 预约酒店用户预约码 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-16
 */
@Service
public class FmbReserveHotelCodesServiceImpl extends ServiceImpl<FmbReserveHotelCodesMapper, FmbReserveHotelCodes> implements IFmbReserveHotelCodesService {

}
