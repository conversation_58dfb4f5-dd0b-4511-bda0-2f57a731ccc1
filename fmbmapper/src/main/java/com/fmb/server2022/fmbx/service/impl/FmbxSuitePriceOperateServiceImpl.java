package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuitePriceOperate;
import com.fmb.server2022.fmbx.mapper.FmbxSuitePriceOperateMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuitePriceOperateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 套餐修改操作 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class FmbxSuitePriceOperateServiceImpl extends ServiceImpl<FmbxSuitePriceOperateMapper, FmbxSuitePriceOperate> implements IFmbxSuitePriceOperateService {

}
