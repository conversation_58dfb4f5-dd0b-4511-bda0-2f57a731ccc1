package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxMedia;
import com.fmb.server2022.fmbx.mapper.FmbxMediaMapper;
import com.fmb.server2022.fmbx.service.IFmbxMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 文件上传表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-19
 */
@Service
public class FmbxMediaServiceImpl extends ServiceImpl<FmbxMediaMapper, FmbxMedia> implements IFmbxMediaService {

}
