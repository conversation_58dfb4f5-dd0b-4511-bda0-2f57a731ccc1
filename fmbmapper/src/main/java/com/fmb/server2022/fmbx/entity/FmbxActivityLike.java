package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * fmbx活动收藏表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_activity_like")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxActivityLike implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 活动的ID
     */
    private Integer xaid;

    /**
     * 活动类型:1-日历房,2-预售房券
     */
    private Integer xatype;

    /**
     * 访问IP
     */
    private String ip;

    /**
     * 用户的UID
     */
    private Integer uid;

    /**
     * 第三方引入COOKIE
     */
    private String cpsCookie;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public Integer getXatype() {
        return xatype;
    }

    public void setXatype(Integer xatype) {
        this.xatype = xatype;
    }
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getCpsCookie() {
        return cpsCookie;
    }

    public void setCpsCookie(String cpsCookie) {
        this.cpsCookie = cpsCookie;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxActivityLike{" +
            "id=" + id +
            ", xaid=" + xaid +
            ", xatype=" + xatype +
            ", ip=" + ip +
            ", uid=" + uid +
            ", cpsCookie=" + cpsCookie +
            ", ctime=" + ctime +
        "}";
    }
}
