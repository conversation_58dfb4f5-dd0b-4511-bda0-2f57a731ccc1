package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxHotelGoodsDetail;
import com.fmb.server2022.fmbx.mapper.FmbxHotelGoodsDetailMapper;
import com.fmb.server2022.fmbx.service.IFmbxHotelGoodsDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店订单每天的价格信息详情 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-07
 */
@Service
public class FmbxHotelGoodsDetailServiceImpl extends ServiceImpl<FmbxHotelGoodsDetailMapper, FmbxHotelGoodsDetail> implements IFmbxHotelGoodsDetailService {

}
