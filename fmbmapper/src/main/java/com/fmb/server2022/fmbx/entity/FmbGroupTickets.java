package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 具体票务信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_group_tickets")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbGroupTickets implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ticket_group_id", type = IdType.AUTO)
    private Integer ticketGroupId;

    /**
     * 分组名称
     */
    private String ticketGroupName;

    /**
     * 关联城市ID
     */
    private Integer cityId;

    /**
     * 省市ID
     */
    private Integer provincesId;

    /**
     * 关联区域ID
     */
    private Integer areaId;

    /**
     * 非直辖市的区ID
     */
    private Integer districtId;

    /**
     * 关联商圈ID
     */
    private Integer businessId;

    /**
     * 关联活动ID
     */
    private Integer aid;

    /**
     * 详细分类ID
     */
    private Integer catId;

    /**
     * 关联商家用户
     */
    private Integer shopUserId;

    /**
     * 0-其它商家的；其它-自己的仓库
     */
    private Integer shopWarehouseId;

    /**
     * 1:演出;2:景点;3:通用扩展;4:酒店
     */
    private Integer ticketType;

    /**
     * 商品类型id
     */
    private Integer goodsTypeId;

    /**
     * 1:电子票;2:实体票
     */
    private Integer shapeType;

    /**
     * 排序，越大越靠前
     */
    private Integer ticketSort;

    /**
     * 第三方 
     */
    private String thirdPart;

    /**
     * 海报图
     */
    private String poster;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 年龄最小值
     */
    private Integer ageMin;

    /**
     * 年龄最大值
     */
    private Integer ageMax;

    /**
     * 适合年龄描述，替代最小年龄和最大年龄
     */
    private String ageDesc;

    /**
     * 0不推荐，1推荐
     */
    private Integer isRecommend;

    /**
     * 详细说明
     */
    private String description;

    /**
     * 购买说明
     */
    private String purchaseDesc;

    /**
     * 短信模板
     */
    private String smsTpl;

    /**
     * 支付说明
     */
    private String payDesc;

    /**
     * 付款方式，1支付宝，2货到付款
     */
    private String payType;

    /**
     * 收入计入方式
     */
    private Integer incomeType;

    /**
     * 酒店设施说明
     */
    private String facilityDesc;

    /**
     * 酒店特殊说明
     */
    private String specialDesc;

    /**
     * 参与文字提示语
     */
    private String certDesc;

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 库存说明
     */
    private String stockDesc;

    /**
     * 配送费用
     */
    private BigDecimal shippingFee;

    /**
     * 运费模板id
     */
    private Integer shippingTemplateId;

    /**
     * 前台是否显示询问商家可否致电:0否,1是
     */
    private Integer showCallUser;

    /**
     * 是否有发票0不支持开票，1父母邦开票,2商家开票
     */
    private Integer isInvoice;

    /**
     * 0:不支持, 1:有效期内可退, 2:随时退, 3:过期退
     */
    private Integer isReturn;

    /**
     * 0:不支持, 1:支持
     */
    private Integer isCart;

    /**
     * 开启自助退款 0否 1是 默认0
     */
    private Integer isAutoReturn;

    /**
     * 排除票种id的字符串 英文都好分割
     */
    private String notTidStr;

    /**
     * 是否支持抢购 1否，2是
     */
    private Integer isRush;

    /**
     * 自动取消订单间隔时间
     */
    private Integer payTimeLimit;

    /**
     * 最低成团件数
     */
    private Integer tuanMinNum;

    /**
     * 券组ID
     */
    private Integer couponId;

    /**
     * 0 为不更新优惠券时间 1为优惠券时间
     */
    private Integer couponUpdate;

    /**
     * 赠劵发放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponStartTime;

    /**
     * 赠劵结束发放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponEndTime;

    /**
     * 是否需要二次确认:0无;1客服跟进；2商户确认；3市场跟进；
     */
    private Integer isConfirm;

    /**
     * 咨询回复类型：1客服；2市场；
     */
    private Integer replyType;

    /**
     * 是否需要证件 0，不需要身份证，1，一证多票 2, 一证一票  3,一证一票累加
     */
    private Integer isCert;

    /**
     * 开始出售时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startSellTime;

    /**
     * 新上架时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime onShelvesTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 合同类型：1团队境内游
     */
    private String contractType;

    /**
     * 状态 ;0等待审核;1通过;2:屏蔽
     */
    private Integer status;

    /**
     * 是否展示电子二维码 ;0 不展示 1展示
     */
    private Integer showQrcode;

    /**
     * 表单扩展字段
     */
    private String extendField;

    /**
     * 返佣方式，默认：百分比返佣，如果是格瓦拉同步的票务会做处理
     */
    private String commisionType;

    /**
     * 设置票种的返佣比例同步结算价，单位：百分比（格瓦拉同步的票务计算结算价）
     */
    private BigDecimal commisionRate;

    /**
     * 提前几天可退
     */
    private Integer endDay;

    /**
     * 提前哪个时间点之前可退，结合end_day使用
     */
    private String endDatetime;

    /**
     * 台后操作用户
     */
    private Integer adminUid;

    /**
     * 创建者ID
     */
    private Integer createUid;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 是否显示电子码：1显示 2不显示 默认1显示
     */
    private Integer showCode;

    public Integer getTicketGroupId() {
        return ticketGroupId;
    }

    public void setTicketGroupId(Integer ticketGroupId) {
        this.ticketGroupId = ticketGroupId;
    }
    public String getTicketGroupName() {
        return ticketGroupName;
    }

    public void setTicketGroupName(String ticketGroupName) {
        this.ticketGroupName = ticketGroupName;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public Integer getProvincesId() {
        return provincesId;
    }

    public void setProvincesId(Integer provincesId) {
        this.provincesId = provincesId;
    }
    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }
    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }
    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getCatId() {
        return catId;
    }

    public void setCatId(Integer catId) {
        this.catId = catId;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getShopWarehouseId() {
        return shopWarehouseId;
    }

    public void setShopWarehouseId(Integer shopWarehouseId) {
        this.shopWarehouseId = shopWarehouseId;
    }
    public Integer getTicketType() {
        return ticketType;
    }

    public void setTicketType(Integer ticketType) {
        this.ticketType = ticketType;
    }
    public Integer getGoodsTypeId() {
        return goodsTypeId;
    }

    public void setGoodsTypeId(Integer goodsTypeId) {
        this.goodsTypeId = goodsTypeId;
    }
    public Integer getShapeType() {
        return shapeType;
    }

    public void setShapeType(Integer shapeType) {
        this.shapeType = shapeType;
    }
    public Integer getTicketSort() {
        return ticketSort;
    }

    public void setTicketSort(Integer ticketSort) {
        this.ticketSort = ticketSort;
    }
    public String getThirdPart() {
        return thirdPart;
    }

    public void setThirdPart(String thirdPart) {
        this.thirdPart = thirdPart;
    }
    public String getPoster() {
        return poster;
    }

    public void setPoster(String poster) {
        this.poster = poster;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public Integer getAgeMin() {
        return ageMin;
    }

    public void setAgeMin(Integer ageMin) {
        this.ageMin = ageMin;
    }
    public Integer getAgeMax() {
        return ageMax;
    }

    public void setAgeMax(Integer ageMax) {
        this.ageMax = ageMax;
    }
    public String getAgeDesc() {
        return ageDesc;
    }

    public void setAgeDesc(String ageDesc) {
        this.ageDesc = ageDesc;
    }
    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public String getPurchaseDesc() {
        return purchaseDesc;
    }

    public void setPurchaseDesc(String purchaseDesc) {
        this.purchaseDesc = purchaseDesc;
    }
    public String getSmsTpl() {
        return smsTpl;
    }

    public void setSmsTpl(String smsTpl) {
        this.smsTpl = smsTpl;
    }
    public String getPayDesc() {
        return payDesc;
    }

    public void setPayDesc(String payDesc) {
        this.payDesc = payDesc;
    }
    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }
    public Integer getIncomeType() {
        return incomeType;
    }

    public void setIncomeType(Integer incomeType) {
        this.incomeType = incomeType;
    }
    public String getFacilityDesc() {
        return facilityDesc;
    }

    public void setFacilityDesc(String facilityDesc) {
        this.facilityDesc = facilityDesc;
    }
    public String getSpecialDesc() {
        return specialDesc;
    }

    public void setSpecialDesc(String specialDesc) {
        this.specialDesc = specialDesc;
    }
    public String getCertDesc() {
        return certDesc;
    }

    public void setCertDesc(String certDesc) {
        this.certDesc = certDesc;
    }
    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }
    public String getStockDesc() {
        return stockDesc;
    }

    public void setStockDesc(String stockDesc) {
        this.stockDesc = stockDesc;
    }
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }
    public Integer getShippingTemplateId() {
        return shippingTemplateId;
    }

    public void setShippingTemplateId(Integer shippingTemplateId) {
        this.shippingTemplateId = shippingTemplateId;
    }
    public Integer getShowCallUser() {
        return showCallUser;
    }

    public void setShowCallUser(Integer showCallUser) {
        this.showCallUser = showCallUser;
    }
    public Integer getIsInvoice() {
        return isInvoice;
    }

    public void setIsInvoice(Integer isInvoice) {
        this.isInvoice = isInvoice;
    }
    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }
    public Integer getIsCart() {
        return isCart;
    }

    public void setIsCart(Integer isCart) {
        this.isCart = isCart;
    }
    public Integer getIsAutoReturn() {
        return isAutoReturn;
    }

    public void setIsAutoReturn(Integer isAutoReturn) {
        this.isAutoReturn = isAutoReturn;
    }
    public String getNotTidStr() {
        return notTidStr;
    }

    public void setNotTidStr(String notTidStr) {
        this.notTidStr = notTidStr;
    }
    public Integer getIsRush() {
        return isRush;
    }

    public void setIsRush(Integer isRush) {
        this.isRush = isRush;
    }
    public Integer getPayTimeLimit() {
        return payTimeLimit;
    }

    public void setPayTimeLimit(Integer payTimeLimit) {
        this.payTimeLimit = payTimeLimit;
    }
    public Integer getTuanMinNum() {
        return tuanMinNum;
    }

    public void setTuanMinNum(Integer tuanMinNum) {
        this.tuanMinNum = tuanMinNum;
    }
    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }
    public Integer getCouponUpdate() {
        return couponUpdate;
    }

    public void setCouponUpdate(Integer couponUpdate) {
        this.couponUpdate = couponUpdate;
    }
    public LocalDateTime getCouponStartTime() {
        return couponStartTime;
    }

    public void setCouponStartTime(LocalDateTime couponStartTime) {
        this.couponStartTime = couponStartTime;
    }
    public LocalDateTime getCouponEndTime() {
        return couponEndTime;
    }

    public void setCouponEndTime(LocalDateTime couponEndTime) {
        this.couponEndTime = couponEndTime;
    }
    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }
    public Integer getReplyType() {
        return replyType;
    }

    public void setReplyType(Integer replyType) {
        this.replyType = replyType;
    }
    public Integer getIsCert() {
        return isCert;
    }

    public void setIsCert(Integer isCert) {
        this.isCert = isCert;
    }
    public LocalDateTime getStartSellTime() {
        return startSellTime;
    }

    public void setStartSellTime(LocalDateTime startSellTime) {
        this.startSellTime = startSellTime;
    }
    public LocalDateTime getOnShelvesTime() {
        return onShelvesTime;
    }

    public void setOnShelvesTime(LocalDateTime onShelvesTime) {
        this.onShelvesTime = onShelvesTime;
    }
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getShowQrcode() {
        return showQrcode;
    }

    public void setShowQrcode(Integer showQrcode) {
        this.showQrcode = showQrcode;
    }
    public String getExtendField() {
        return extendField;
    }

    public void setExtendField(String extendField) {
        this.extendField = extendField;
    }
    public String getCommisionType() {
        return commisionType;
    }

    public void setCommisionType(String commisionType) {
        this.commisionType = commisionType;
    }
    public BigDecimal getCommisionRate() {
        return commisionRate;
    }

    public void setCommisionRate(BigDecimal commisionRate) {
        this.commisionRate = commisionRate;
    }
    public Integer getEndDay() {
        return endDay;
    }

    public void setEndDay(Integer endDay) {
        this.endDay = endDay;
    }
    public String getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(String endDatetime) {
        this.endDatetime = endDatetime;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getShowCode() {
        return showCode;
    }

    public void setShowCode(Integer showCode) {
        this.showCode = showCode;
    }

    @Override
    public String toString() {
        return "FmbGroupTickets{" +
            "ticketGroupId=" + ticketGroupId +
            ", ticketGroupName=" + ticketGroupName +
            ", cityId=" + cityId +
            ", provincesId=" + provincesId +
            ", areaId=" + areaId +
            ", districtId=" + districtId +
            ", businessId=" + businessId +
            ", aid=" + aid +
            ", catId=" + catId +
            ", shopUserId=" + shopUserId +
            ", shopWarehouseId=" + shopWarehouseId +
            ", ticketType=" + ticketType +
            ", goodsTypeId=" + goodsTypeId +
            ", shapeType=" + shapeType +
            ", ticketSort=" + ticketSort +
            ", thirdPart=" + thirdPart +
            ", poster=" + poster +
            ", address=" + address +
            ", ageMin=" + ageMin +
            ", ageMax=" + ageMax +
            ", ageDesc=" + ageDesc +
            ", isRecommend=" + isRecommend +
            ", description=" + description +
            ", purchaseDesc=" + purchaseDesc +
            ", smsTpl=" + smsTpl +
            ", payDesc=" + payDesc +
            ", payType=" + payType +
            ", incomeType=" + incomeType +
            ", facilityDesc=" + facilityDesc +
            ", specialDesc=" + specialDesc +
            ", certDesc=" + certDesc +
            ", totalNum=" + totalNum +
            ", stockDesc=" + stockDesc +
            ", shippingFee=" + shippingFee +
            ", shippingTemplateId=" + shippingTemplateId +
            ", showCallUser=" + showCallUser +
            ", isInvoice=" + isInvoice +
            ", isReturn=" + isReturn +
            ", isCart=" + isCart +
            ", isAutoReturn=" + isAutoReturn +
            ", notTidStr=" + notTidStr +
            ", isRush=" + isRush +
            ", payTimeLimit=" + payTimeLimit +
            ", tuanMinNum=" + tuanMinNum +
            ", couponId=" + couponId +
            ", couponUpdate=" + couponUpdate +
            ", couponStartTime=" + couponStartTime +
            ", couponEndTime=" + couponEndTime +
            ", isConfirm=" + isConfirm +
            ", replyType=" + replyType +
            ", isCert=" + isCert +
            ", startSellTime=" + startSellTime +
            ", onShelvesTime=" + onShelvesTime +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", minPrice=" + minPrice +
            ", contractType=" + contractType +
            ", status=" + status +
            ", showQrcode=" + showQrcode +
            ", extendField=" + extendField +
            ", commisionType=" + commisionType +
            ", commisionRate=" + commisionRate +
            ", endDay=" + endDay +
            ", endDatetime=" + endDatetime +
            ", adminUid=" + adminUid +
            ", createUid=" + createUid +
            ", utime=" + utime +
            ", ctime=" + ctime +
            ", showCode=" + showCode +
        "}";
    }
}
