package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbPayOrders;
import com.fmb.server2022.fmbx.mapper.FmbPayOrdersMapper;
import com.fmb.server2022.fmbx.service.IFmbPayOrdersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 支付订单 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbPayOrdersServiceImpl extends ServiceImpl<FmbPayOrdersMapper, FmbPayOrders> implements IFmbPayOrdersService {

}
