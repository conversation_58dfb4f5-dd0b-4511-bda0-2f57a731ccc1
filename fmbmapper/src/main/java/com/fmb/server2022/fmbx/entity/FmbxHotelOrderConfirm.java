package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * fmbx酒店二次确认表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_hotel_order_confirm")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxHotelOrderConfirm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "confirm_id", type = IdType.AUTO)
    private Integer confirmId;

    /**
     * 收货人手机
     */
    private String mobile;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 支付来源:prepay_balance:预付款;credit_balance:信用账户支付
     */
    private String paySource;

    /**
     * 收货人地址
     */
    private String address;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 关联活动ID
     */
    private Integer aid;

    /**
     * 订单状态。0.未付款 1.等待发货 2.已发货 3.退货中 4.已退货 5.交易成功 6.交易关闭 7.支付尾款 8.支付成功后续处理中
     */
    private Integer orderStatus;

    /**
     * 类别名称
     */
    private String cateName;

    /**
     * 购买数量
     */
    private Integer buyNum;

    /**
     * 订单所属区域ID
     */
    private Integer orderCityId;

    /**
     * 二次确认状态(0=>待处理,1=>处理中,5=>处理中-预订,8=>处理中-预定催单,6=>处理中-更改,9=>处理中-更改催单,7=>处理中-取消,10=>处理中-取消催单,2=>有货-保留,3=>无货-待处理,4=>无货-退款)
     */
    private Integer confirmStatus;

    /**
     * 二次确认复审状态：0，未审，1，已审
     */
    private Integer confirmRecheckStatus;

    /**
     * 支付金额
     */
    private BigDecimal money;

    /**
     * 配送费用
     */
    private BigDecimal shippingFee;

    /**
     * 优惠金额
     */
    private BigDecimal couponMoney;

    /**
     * 实际支付金额
     */
    private BigDecimal realpayMoney;

    /**
     * 代付尾款情况下已经支付金额
     */
    private BigDecimal relpadTotalMoney;

    /**
     * 订单生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime orderCreateTime;

    /**
     * 订单支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime orderPayTime;

    /**
     * 最后确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastConfirmTime;

    /**
     * 最后一次二次确认用户id
     */
    private Integer lastConfirmUid;

    /**
     * 最后一次二次确认用户名
     */
    private String lastConfirmName;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate checkinDate;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate checkoutDate;

    /**
     * 是否结伴
     */
    private Integer isPartner;

    /**
     * 结伴状态
     */
    private Integer partnerStatus;

    /**
     * 是否团长
     */
    private Integer partnerLeader;

    /**
     * 成团状态
     */
    private Integer partnerTuanStatus;

    /**
     * 占房状态
     */
    private Integer partnerFake;

    /**
     * 票务类型
     */
    private Integer goodsType;

    /**
     * 酒店信息json
     */
    private String hotelInfo;

    /**
     * goods信息
     */
    private String goodsList;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Integer confirmId) {
        this.confirmId = confirmId;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getPaySource() {
        return paySource;
    }

    public void setPaySource(String paySource) {
        this.paySource = paySource;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }
    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }
    public Integer getBuyNum() {
        return buyNum;
    }

    public void setBuyNum(Integer buyNum) {
        this.buyNum = buyNum;
    }
    public Integer getOrderCityId() {
        return orderCityId;
    }

    public void setOrderCityId(Integer orderCityId) {
        this.orderCityId = orderCityId;
    }
    public Integer getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(Integer confirmStatus) {
        this.confirmStatus = confirmStatus;
    }
    public Integer getConfirmRecheckStatus() {
        return confirmRecheckStatus;
    }

    public void setConfirmRecheckStatus(Integer confirmRecheckStatus) {
        this.confirmRecheckStatus = confirmRecheckStatus;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }
    public BigDecimal getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(BigDecimal couponMoney) {
        this.couponMoney = couponMoney;
    }
    public BigDecimal getRealpayMoney() {
        return realpayMoney;
    }

    public void setRealpayMoney(BigDecimal realpayMoney) {
        this.realpayMoney = realpayMoney;
    }
    public BigDecimal getRelpadTotalMoney() {
        return relpadTotalMoney;
    }

    public void setRelpadTotalMoney(BigDecimal relpadTotalMoney) {
        this.relpadTotalMoney = relpadTotalMoney;
    }
    public LocalDateTime getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(LocalDateTime orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }
    public LocalDateTime getOrderPayTime() {
        return orderPayTime;
    }

    public void setOrderPayTime(LocalDateTime orderPayTime) {
        this.orderPayTime = orderPayTime;
    }
    public LocalDateTime getLastConfirmTime() {
        return lastConfirmTime;
    }

    public void setLastConfirmTime(LocalDateTime lastConfirmTime) {
        this.lastConfirmTime = lastConfirmTime;
    }
    public Integer getLastConfirmUid() {
        return lastConfirmUid;
    }

    public void setLastConfirmUid(Integer lastConfirmUid) {
        this.lastConfirmUid = lastConfirmUid;
    }
    public String getLastConfirmName() {
        return lastConfirmName;
    }

    public void setLastConfirmName(String lastConfirmName) {
        this.lastConfirmName = lastConfirmName;
    }
    public LocalDate getCheckinDate() {
        return checkinDate;
    }

    public void setCheckinDate(LocalDate checkinDate) {
        this.checkinDate = checkinDate;
    }
    public LocalDate getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(LocalDate checkoutDate) {
        this.checkoutDate = checkoutDate;
    }
    public Integer getIsPartner() {
        return isPartner;
    }

    public void setIsPartner(Integer isPartner) {
        this.isPartner = isPartner;
    }
    public Integer getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Integer partnerStatus) {
        this.partnerStatus = partnerStatus;
    }
    public Integer getPartnerLeader() {
        return partnerLeader;
    }

    public void setPartnerLeader(Integer partnerLeader) {
        this.partnerLeader = partnerLeader;
    }
    public Integer getPartnerTuanStatus() {
        return partnerTuanStatus;
    }

    public void setPartnerTuanStatus(Integer partnerTuanStatus) {
        this.partnerTuanStatus = partnerTuanStatus;
    }
    public Integer getPartnerFake() {
        return partnerFake;
    }

    public void setPartnerFake(Integer partnerFake) {
        this.partnerFake = partnerFake;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public String getHotelInfo() {
        return hotelInfo;
    }

    public void setHotelInfo(String hotelInfo) {
        this.hotelInfo = hotelInfo;
    }
    public String getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(String goodsList) {
        this.goodsList = goodsList;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbxHotelOrderConfirm{" +
            "confirmId=" + confirmId +
            ", mobile=" + mobile +
            ", orderSn=" + orderSn +
            ", receiver=" + receiver +
            ", uid=" + uid +
            ", paySource=" + paySource +
            ", address=" + address +
            ", orderName=" + orderName +
            ", aid=" + aid +
            ", orderStatus=" + orderStatus +
            ", cateName=" + cateName +
            ", buyNum=" + buyNum +
            ", orderCityId=" + orderCityId +
            ", confirmStatus=" + confirmStatus +
            ", confirmRecheckStatus=" + confirmRecheckStatus +
            ", money=" + money +
            ", shippingFee=" + shippingFee +
            ", couponMoney=" + couponMoney +
            ", realpayMoney=" + realpayMoney +
            ", relpadTotalMoney=" + relpadTotalMoney +
            ", orderCreateTime=" + orderCreateTime +
            ", orderPayTime=" + orderPayTime +
            ", lastConfirmTime=" + lastConfirmTime +
            ", lastConfirmUid=" + lastConfirmUid +
            ", lastConfirmName=" + lastConfirmName +
            ", checkinDate=" + checkinDate +
            ", checkoutDate=" + checkoutDate +
            ", isPartner=" + isPartner +
            ", partnerStatus=" + partnerStatus +
            ", partnerLeader=" + partnerLeader +
            ", partnerTuanStatus=" + partnerTuanStatus +
            ", partnerFake=" + partnerFake +
            ", goodsType=" + goodsType +
            ", hotelInfo=" + hotelInfo +
            ", goodsList=" + goodsList +
            ", ctime=" + ctime +
        "}";
    }
}
