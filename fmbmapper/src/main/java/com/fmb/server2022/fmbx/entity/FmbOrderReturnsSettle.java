package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 退货单的已经结算信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_returns_settle")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderReturnsSettle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退款ID为主的索引
     */
    @TableId
    private Integer returnId;

    /**
     * 缓存 aid 便于查找
     */
    private Integer aid;

    /**
     * 缓存 shop_user_id 便于查找
     */
    private Integer shopUserId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 货物ID
     */
    private Integer recId;

    /**
     * 退款金额
     */
    private BigDecimal money;

    /**
     * 结算退款金额
     */
    private BigDecimal settleMoney;

    /**
     * 结算ID
     */
    private Integer settleId;

    /**
     * 0 结算后发起退货，1 审核通过;2已经结算
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getSettleMoney() {
        return settleMoney;
    }

    public void setSettleMoney(BigDecimal settleMoney) {
        this.settleMoney = settleMoney;
    }
    public Integer getSettleId() {
        return settleId;
    }

    public void setSettleId(Integer settleId) {
        this.settleId = settleId;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbOrderReturnsSettle{" +
            "returnId=" + returnId +
            ", aid=" + aid +
            ", shopUserId=" + shopUserId +
            ", orderSn=" + orderSn +
            ", recId=" + recId +
            ", money=" + money +
            ", settleMoney=" + settleMoney +
            ", settleId=" + settleId +
            ", status=" + status +
            ", ctime=" + ctime +
            ", utime=" + utime +
        "}";
    }
}
