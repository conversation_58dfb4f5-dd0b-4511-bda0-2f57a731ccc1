package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxOutfileInfo;
import com.fmb.server2022.fmbx.mapper.FmbxOutfileInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbxOutfileInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 外部系统文件下载父母邦系统(钉钉) 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-21
 */
@Service
public class FmbxOutfileInfoServiceImpl extends ServiceImpl<FmbxOutfileInfoMapper, FmbxOutfileInfo> implements IFmbxOutfileInfoService {

}
