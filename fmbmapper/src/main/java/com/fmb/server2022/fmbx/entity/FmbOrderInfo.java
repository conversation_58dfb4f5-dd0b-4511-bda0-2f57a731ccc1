package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 用户订单信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "order_id", type = IdType.AUTO)
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 用户记录改签后的订单号
     */
    private String changeToOrder;

    /**
     * 改签前订单号
     */
    private String beforeOrderSn;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 外部订单号，外部支付平台生成
     */
    private String paySn;

    /**
     * 自己内部平台支付订单号
     */
    private String myPaySn;

    /**
     * 第三方平台的唯一订单流水号
     */
    private String thirdSn;

    /**
     * 购买人邮件
     */
    private String buyerEmail;

    /**
     * 接受合同邮件地址
     */
    private String receiverEmail;

    /**
     * 用户uid
     */
    private Integer uid;

    /**
     * 分销商id
     */
    private Integer mycpsId;

    /**
     * 订单状态。0.未付款 1.等待发货 2.已发货 3.退货中 4.已退货 5.交易成功 6.交易关闭 7.支付尾款 8.支付成功后续处理中
     */
    private Integer orderStatus;

    /**
     * 商品配送情况，0，未发货； 1，已发货；2，已收货；3，备货中
     */
    private Integer shippingStatus;

    /**
     * 追单状态，0，未追单，1，追单中，2，追单完成
     */
    private Integer traceStatus;

    /**
     * 支付状态；0，未付款；1，付款中 ；2，已付款
     */
    private Integer payStatus;

    /**
     * 二次确认状态(0=>待处理,1=>处理中,5=>处理中-预订,8=>处理中-预定催单,6=>处理中-更改,9=>处理中-更改催单,7=>处理中-取消,10=>处理中-取消催单,2=>有货-保留,3=>无货-待处理,4=>无货-退款)
     */
    private Integer confirmStatus;

    /**
     * 二次确认复审状态：0，未审，1，已审
     */
    private Integer confirmRecheckStatus;

    /**
     * 占房单使用状态：0待确认,1未使用,2已使用, 3确认失败
     */
    private Integer occupyStatus;

    /**
     * 结算状态0未结算，1已经结算
     */
    private Integer payClear;

    /**
     * 分销商结算状态,0:未结算,1:结算
     */
    private Integer mycpsSettleStatus;

    /**
     * 关联活动ID
     */
    private Integer aid;

    /**
     * 订单所属区域ID
     */
    private Integer orderCityId;

    /**
     * 用户所属区域ID
     */
    private Integer userCityId;

    /**
     * 对应的商家管理ID
     */
    private Integer shopUserId;

    /**
     * 0-其它商家的；其它-自己的仓库
     */
    private Integer shopWarehouseId;

    /**
     * 商品分组ID
     */
    private Integer goodsGroupId;

    /**
     * 1:演出;2景点;3通用;4酒店;15酒+景;50:跟团游;51:自由行;
     */
    private Integer goodsType;

    /**
     * 1:普通活动;2:app专享活动;3:app专享价格活动
     */
    private Integer activityType;

    /**
     * 1:电子票;2:实体票
     */
    private Integer shapeType;

    /**
     * 订单类型：1:普通,2:酒店预约单,3:返佣模式订单,4:分销价模式订单,10:知行酒店,11:六艺通
     */
    private Integer orderType;

    /**
     * 订单改签类型：1:后台;2:前台用户
     */
    private Integer changeType;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 收货人国家：1:中国
     */
    private Integer country;

    /**
     * 手机号归属城市，关联jishigou_common_district
     */
    private Integer sDistrictId;

    /**
     * 收货人省份id;关联jishigou_common_district
     */
    private Integer province;

    /**
     * 收货人城市;关联jishigou_common_district
     */
    private Integer city;

    /**
     * 收货人区域;关联jishigou_common_district
     */
    private Integer area;

    /**
     * 收货人街道
     */
    private Integer street;

    /**
     * 收货人地址
     */
    private String address;

    /**
     * 收货人邮编
     */
    private String zipcode;

    /**
     * 收货人电话
     */
    private String tel;

    /**
     * 收货人手机
     */
    private String mobile;

    /**
     * 收货人邮箱
     */
    private String email;

    /**
     * 用户微信号
     */
    private String weixin;

    /**
     * 订单附言
     */
    private String postscript;

    /**
     * 订单扩展信息
     */
    private String extInfo;

    /**
     * 参与人证件信息
     */
    private String certInfo;

    /**
     * 0:无用1:个人;2:企业
     */
    private Integer invType;

    /**
     * 发票抬头
     */
    private String invPayee;

    /**
     * 发票内容
     */
    private String invContent;

    /**
     * 成人数
     */
    private Integer adultNum;

    /**
     * 儿童数
     */
    private Integer childNum;

    /**
     * 支付金额
     */
    private BigDecimal money;

    /**
     * 配送费用
     */
    private BigDecimal shippingFee;

    /**
     * 该订单选择的运费方式0-自定义（没有方式），1-快递，2-EMS
     */
    private Integer shippingMethod;

    /**
     * 支付来源:prepay_balance:预付款;credit_balance:信用账户支付
     */
    private String paySource;

    /**
     * 付款备注
     */
    private String payNote;

    /**
     * 编辑说明
     */
    private String editorNote;

    /**
     * 商家备注
     */
    @TableField(exist = false)
    private String shopNote;

    /**
     * 订单来源页面
     */
    private String referer;

    /**
     * 下单来源/订单来源1-客户下单2-客服代下单 3-假占房单
     */
    private Integer orderReferer;

    /**
     * APP名字
     */
    private String appname;

    /**
     * 订单生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 订单确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime confirmTime;

    /**
     * 订单支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    /**
     * 二次确认操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime confirmUpdateTime;

    /**
     * 订单配送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime shippingTime;

    /**
     * 出行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime playTime;

    /**
     * 是否使用了优惠券 0为未使用 1为使用
     */
    private Integer useCoupon;

    /**
     * 优惠金额
     */
    private Integer couponMoney;

    /**
     * 优惠扣减金额
     */
    @TableField(exist = false)
    private BigDecimal couponSubMoney;

    /**
     * 文惠券金额
     */
    private Integer culturalCouponMoney;

    /**
     * 账户余额扣款
     */
    private BigDecimal cashMoney;

    /**
     * 折扣券的折扣率
     */
    private Integer discountRate;

    /**
     * app单独优惠金额
     */
    private Integer appSubMoney;

    /**
     * 实际支付金额
     */
    private BigDecimal realpayMoney;

    /**
     * cps第三方名称
     */
    private String cpsName;

    /**
     * 第三方引入COOKIE
     */
    private String cpsCookie;

    /**
     * 系统库版本
     */
    private String version;

    /**
     * 是否删除：0未删除;1前台用户删除;2后台用户删除
     */
    private Integer isDelete;

    private Integer isFirstOrder ;

    public Integer getIsFirstOrder() {
        return isFirstOrder;
    }

    public void setIsFirstOrder(Integer isFirstOrder) {
        this.isFirstOrder = isFirstOrder;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getChangeToOrder() {
        return changeToOrder;
    }

    public void setChangeToOrder(String changeToOrder) {
        this.changeToOrder = changeToOrder;
    }
    public String getBeforeOrderSn() {
        return beforeOrderSn;
    }

    public void setBeforeOrderSn(String beforeOrderSn) {
        this.beforeOrderSn = beforeOrderSn;
    }
    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }
    public String getPaySn() {
        return paySn;
    }

    public void setPaySn(String paySn) {
        this.paySn = paySn;
    }
    public String getMyPaySn() {
        return myPaySn;
    }

    public void setMyPaySn(String myPaySn) {
        this.myPaySn = myPaySn;
    }
    public String getThirdSn() {
        return thirdSn;
    }

    public void setThirdSn(String thirdSn) {
        this.thirdSn = thirdSn;
    }
    public String getBuyerEmail() {
        return buyerEmail;
    }

    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }
    public String getReceiverEmail() {
        return receiverEmail;
    }

    public void setReceiverEmail(String receiverEmail) {
        this.receiverEmail = receiverEmail;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public Integer getMycpsId() {
        return mycpsId;
    }

    public void setMycpsId(Integer mycpsId) {
        this.mycpsId = mycpsId;
    }
    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }
    public Integer getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(Integer shippingStatus) {
        this.shippingStatus = shippingStatus;
    }
    public Integer getTraceStatus() {
        return traceStatus;
    }

    public void setTraceStatus(Integer traceStatus) {
        this.traceStatus = traceStatus;
    }
    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }
    public Integer getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(Integer confirmStatus) {
        this.confirmStatus = confirmStatus;
    }
    public Integer getConfirmRecheckStatus() {
        return confirmRecheckStatus;
    }

    public void setConfirmRecheckStatus(Integer confirmRecheckStatus) {
        this.confirmRecheckStatus = confirmRecheckStatus;
    }
    public Integer getOccupyStatus() {
        return occupyStatus;
    }

    public void setOccupyStatus(Integer occupyStatus) {
        this.occupyStatus = occupyStatus;
    }
    public Integer getPayClear() {
        return payClear;
    }

    public void setPayClear(Integer payClear) {
        this.payClear = payClear;
    }
    public Integer getMycpsSettleStatus() {
        return mycpsSettleStatus;
    }

    public void setMycpsSettleStatus(Integer mycpsSettleStatus) {
        this.mycpsSettleStatus = mycpsSettleStatus;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public Integer getOrderCityId() {
        return orderCityId;
    }

    public void setOrderCityId(Integer orderCityId) {
        this.orderCityId = orderCityId;
    }
    public Integer getUserCityId() {
        return userCityId;
    }

    public void setUserCityId(Integer userCityId) {
        this.userCityId = userCityId;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getShopWarehouseId() {
        return shopWarehouseId;
    }

    public void setShopWarehouseId(Integer shopWarehouseId) {
        this.shopWarehouseId = shopWarehouseId;
    }
    public Integer getGoodsGroupId() {
        return goodsGroupId;
    }

    public void setGoodsGroupId(Integer goodsGroupId) {
        this.goodsGroupId = goodsGroupId;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    public Integer getShapeType() {
        return shapeType;
    }

    public void setShapeType(Integer shapeType) {
        this.shapeType = shapeType;
    }
    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public Integer getCountry() {
        return country;
    }

    public void setCountry(Integer country) {
        this.country = country;
    }
    public Integer getsDistrictId() {
        return sDistrictId;
    }

    public void setsDistrictId(Integer sDistrictId) {
        this.sDistrictId = sDistrictId;
    }
    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }
    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }
    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }
    public Integer getStreet() {
        return street;
    }

    public void setStreet(Integer street) {
        this.street = street;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getWeixin() {
        return weixin;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }
    public String getPostscript() {
        return postscript;
    }

    public void setPostscript(String postscript) {
        this.postscript = postscript;
    }
    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
    public String getCertInfo() {
        return certInfo;
    }

    public void setCertInfo(String certInfo) {
        this.certInfo = certInfo;
    }
    public Integer getInvType() {
        return invType;
    }

    public void setInvType(Integer invType) {
        this.invType = invType;
    }
    public String getInvPayee() {
        return invPayee;
    }

    public void setInvPayee(String invPayee) {
        this.invPayee = invPayee;
    }
    public String getInvContent() {
        return invContent;
    }

    public void setInvContent(String invContent) {
        this.invContent = invContent;
    }
    public Integer getAdultNum() {
        return adultNum;
    }

    public void setAdultNum(Integer adultNum) {
        this.adultNum = adultNum;
    }
    public Integer getChildNum() {
        return childNum;
    }

    public void setChildNum(Integer childNum) {
        this.childNum = childNum;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }
    public Integer getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(Integer shippingMethod) {
        this.shippingMethod = shippingMethod;
    }
    public String getPaySource() {
        return paySource;
    }

    public void setPaySource(String paySource) {
        this.paySource = paySource;
    }
    public String getPayNote() {
        return payNote;
    }

    public void setPayNote(String payNote) {
        this.payNote = payNote;
    }
    public String getEditorNote() {
        return editorNote;
    }

    public void setEditorNote(String editorNote) {
        this.editorNote = editorNote;
    }
    public String getReferer() {
        return referer;
    }

    public String getShopNote() {
        return shopNote;
    }

    public void setShopNote(String shopNote) {
        this.shopNote = shopNote;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }
    public Integer getOrderReferer() {
        return orderReferer;
    }

    public void setOrderReferer(Integer orderReferer) {
        this.orderReferer = orderReferer;
    }
    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(LocalDateTime confirmTime) {
        this.confirmTime = confirmTime;
    }
    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }
    public LocalDateTime getConfirmUpdateTime() {
        return confirmUpdateTime;
    }

    public void setConfirmUpdateTime(LocalDateTime confirmUpdateTime) {
        this.confirmUpdateTime = confirmUpdateTime;
    }
    public LocalDateTime getShippingTime() {
        return shippingTime;
    }

    public void setShippingTime(LocalDateTime shippingTime) {
        this.shippingTime = shippingTime;
    }
    public LocalDateTime getPlayTime() {
        return playTime;
    }

    public void setPlayTime(LocalDateTime playTime) {
        this.playTime = playTime;
    }
    public Integer getUseCoupon() {
        return useCoupon;
    }

    public void setUseCoupon(Integer useCoupon) {
        this.useCoupon = useCoupon;
    }
    public Integer getCouponMoney() {
        return couponMoney;
    }

    public BigDecimal getCouponSubMoney() {
        if (money != null && discountRate != null && discountRate>0) {
            return money.multiply( new  BigDecimal("100").subtract(  new BigDecimal(discountRate))).divide(new BigDecimal("100"));
        }else{
            if (couponMoney != null) {
                return new BigDecimal(couponMoney);
            }else {
                return BigDecimal.ZERO ;
            }

        }
    }

    public void setCouponMoney(Integer couponMoney) {
        this.couponMoney = couponMoney;
    }
    public Integer getCulturalCouponMoney() {
        return culturalCouponMoney;
    }

    public void setCulturalCouponMoney(Integer culturalCouponMoney) {
        this.culturalCouponMoney = culturalCouponMoney;
    }
    public BigDecimal getCashMoney() {
        return cashMoney;
    }

    public void setCashMoney(BigDecimal cashMoney) {
        this.cashMoney = cashMoney;
    }
    public Integer getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Integer discountRate) {
        this.discountRate = discountRate;
    }
    public Integer getAppSubMoney() {
        return appSubMoney;
    }

    public void setAppSubMoney(Integer appSubMoney) {
        this.appSubMoney = appSubMoney;
    }
    public BigDecimal getRealpayMoney() {
        return realpayMoney;
    }

    public void setRealpayMoney(BigDecimal realpayMoney) {
        this.realpayMoney = realpayMoney;
    }
    public String getCpsName() {
        return cpsName;
    }

    public void setCpsName(String cpsName) {
        this.cpsName = cpsName;
    }
    public String getCpsCookie() {
        return cpsCookie;
    }

    public void setCpsCookie(String cpsCookie) {
        this.cpsCookie = cpsCookie;
    }
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "FmbOrderInfo{" +
            "orderId=" + orderId +
            ", orderSn=" + orderSn +
            ", changeToOrder=" + changeToOrder +
            ", beforeOrderSn=" + beforeOrderSn +
            ", orderName=" + orderName +
            ", paySn=" + paySn +
            ", myPaySn=" + myPaySn +
            ", thirdSn=" + thirdSn +
            ", buyerEmail=" + buyerEmail +
            ", receiverEmail=" + receiverEmail +
            ", uid=" + uid +
            ", mycpsId=" + mycpsId +
            ", orderStatus=" + orderStatus +
            ", shippingStatus=" + shippingStatus +
            ", traceStatus=" + traceStatus +
            ", payStatus=" + payStatus +
            ", confirmStatus=" + confirmStatus +
            ", confirmRecheckStatus=" + confirmRecheckStatus +
            ", occupyStatus=" + occupyStatus +
            ", payClear=" + payClear +
            ", mycpsSettleStatus=" + mycpsSettleStatus +
            ", aid=" + aid +
            ", orderCityId=" + orderCityId +
            ", userCityId=" + userCityId +
            ", shopUserId=" + shopUserId +
            ", shopWarehouseId=" + shopWarehouseId +
            ", goodsGroupId=" + goodsGroupId +
            ", goodsType=" + goodsType +
            ", activityType=" + activityType +
            ", shapeType=" + shapeType +
            ", orderType=" + orderType +
            ", changeType=" + changeType +
            ", receiver=" + receiver +
            ", country=" + country +
            ", sDistrictId=" + sDistrictId +
            ", province=" + province +
            ", city=" + city +
            ", area=" + area +
            ", street=" + street +
            ", address=" + address +
            ", zipcode=" + zipcode +
            ", tel=" + tel +
            ", mobile=" + mobile +
            ", email=" + email +
            ", weixin=" + weixin +
            ", postscript=" + postscript +
            ", extInfo=" + extInfo +
            ", certInfo=" + certInfo +
            ", invType=" + invType +
            ", invPayee=" + invPayee +
            ", invContent=" + invContent +
            ", adultNum=" + adultNum +
            ", childNum=" + childNum +
            ", money=" + money +
            ", shippingFee=" + shippingFee +
            ", shippingMethod=" + shippingMethod +
            ", paySource=" + paySource +
            ", payNote=" + payNote +
            ", editorNote=" + editorNote +
            ", referer=" + referer +
            ", orderReferer=" + orderReferer +
            ", appname=" + appname +
            ", createTime=" + createTime +
            ", confirmTime=" + confirmTime +
            ", payTime=" + payTime +
            ", confirmUpdateTime=" + confirmUpdateTime +
            ", shippingTime=" + shippingTime +
            ", playTime=" + playTime +
            ", useCoupon=" + useCoupon +
            ", couponMoney=" + couponMoney +
            ", culturalCouponMoney=" + culturalCouponMoney +
            ", cashMoney=" + cashMoney +
            ", discountRate=" + discountRate +
            ", appSubMoney=" + appSubMoney +
            ", realpayMoney=" + realpayMoney +
            ", cpsName=" + cpsName +
            ", cpsCookie=" + cpsCookie +
            ", version=" + version +
            ", isDelete=" + isDelete +
            ", isFirstOrder=" + isFirstOrder +
        "}";
    }

}
