package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@TableName("sys_menu")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    @TableId(value = "menu_id", type = IdType.AUTO)

    private Integer menuId;

    /**
     * 菜单的中文释义
     */

    private String menuName;

    /**
     * 父菜单ID
     */

    private Integer parentMenuid;

    /**
     * 相关权限id(permission表有值就一定是子节点)
     */

    private Integer relatedPermissionid;

    /**
     * 1-目录(是目录related_permissionid是-1),0-节点
     */

    private Integer isFolder;

    /**
     * 排序值
     */

    private Integer sortval;

    public Integer getMenuId() {
        return menuId;
    }

    public void setMenuId(Integer menuId) {
        this.menuId = menuId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public Integer getParentMenuid() {
        return parentMenuid;
    }

    public void setParentMenuid(Integer parentMenuid) {
        this.parentMenuid = parentMenuid;
    }

    public Integer getRelatedPermissionid() {
        return relatedPermissionid;
    }

    public void setRelatedPermissionid(Integer relatedPermissionid) {
        this.relatedPermissionid = relatedPermissionid;
    }

    public Integer getIsFolder() {
        return isFolder;
    }

    public void setIsFolder(Integer isFolder) {
        this.isFolder = isFolder;
    }

    public Integer getSortval() {
        return sortval;
    }

    public void setSortval(Integer sortval) {
        this.sortval = sortval;
    }

    @Override
    public String toString() {
        return "SysMenu{" +
                "menuId=" + menuId +
                ", menuName=" + menuName +
                ", parentMenuid=" + parentMenuid +
                ", relatedPermissionid=" + relatedPermissionid +
                ", isFolder=" + isFolder +
                ", sortval=" + sortval +
                "}";
    }
}
