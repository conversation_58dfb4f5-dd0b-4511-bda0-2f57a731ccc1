package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxActivityExt;
import com.fmb.server2022.fmbx.mapper.FmbxActivityExtMapper;
import com.fmb.server2022.fmbx.service.IFmbxActivityExtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * fmbx活动主表扩展 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class FmbxActivityExtServiceImpl extends ServiceImpl<FmbxActivityExtMapper, FmbxActivityExt> implements IFmbxActivityExtService {

}
