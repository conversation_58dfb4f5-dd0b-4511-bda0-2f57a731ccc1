package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxDateHotelInfo;
import com.fmb.server2022.fmbx.mapper.FmbxDateHotelInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbxDateHotelInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 按日期存储酒店日历房信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-28
 */
@Service
public class FmbxDateHotelInfoServiceImpl extends ServiceImpl<FmbxDateHotelInfoMapper, FmbxDateHotelInfo> implements IFmbxDateHotelInfoService {

}
