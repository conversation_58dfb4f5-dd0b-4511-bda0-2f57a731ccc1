package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbSms;
import com.fmb.server2022.fmbx.mapper.FmbSmsMapper;
import com.fmb.server2022.fmbx.service.IFmbSmsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 发送的短信记录 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class FmbSmsServiceImpl extends ServiceImpl<FmbSmsMapper, FmbSms> implements IFmbSmsService {

}
