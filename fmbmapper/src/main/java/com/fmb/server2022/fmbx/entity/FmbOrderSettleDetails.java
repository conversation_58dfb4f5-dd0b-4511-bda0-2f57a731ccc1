package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 结算明细表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_settle_details")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderSettleDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 结算单号
     */
    private Integer settleId;

    /**
     * 单独商品订购ID,货物ID
     */
    private Integer recId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 票种ID
     */
    private Integer goodsId;

    /**
     * 商家ID
     */
    private Integer shopUserId;

    /**
     * 子商家ID
     */
    private Integer verifyShopUserId;

    /**
     * 活动ID
     */
    private Integer aid;

    /**
     * 验证码
     */
    private String codes;

    /**
     * 结算数量
     */
    private Integer settleNum;

    /**
     * 单价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 订单备注
     */
    private String postscripts;

    /**
     * 0未结算1已结算2永久不结算
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getSettleId() {
        return settleId;
    }

    public void setSettleId(Integer settleId) {
        this.settleId = settleId;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getShopUserId() {
        return shopUserId;
    }

    public void setShopUserId(Integer shopUserId) {
        this.shopUserId = shopUserId;
    }
    public Integer getVerifyShopUserId() {
        return verifyShopUserId;
    }

    public void setVerifyShopUserId(Integer verifyShopUserId) {
        this.verifyShopUserId = verifyShopUserId;
    }
    public Integer getAid() {
        return aid;
    }

    public void setAid(Integer aid) {
        this.aid = aid;
    }
    public String getCodes() {
        return codes;
    }

    public void setCodes(String codes) {
        this.codes = codes;
    }
    public Integer getSettleNum() {
        return settleNum;
    }

    public void setSettleNum(Integer settleNum) {
        this.settleNum = settleNum;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public String getPostscripts() {
        return postscripts;
    }

    public void setPostscripts(String postscripts) {
        this.postscripts = postscripts;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbOrderSettleDetails{" +
            "id=" + id +
            ", settleId=" + settleId +
            ", recId=" + recId +
            ", orderSn=" + orderSn +
            ", goodsId=" + goodsId +
            ", shopUserId=" + shopUserId +
            ", verifyShopUserId=" + verifyShopUserId +
            ", aid=" + aid +
            ", codes=" + codes +
            ", settleNum=" + settleNum +
            ", goodsPrice=" + goodsPrice +
            ", settlePrice=" + settlePrice +
            ", postscripts=" + postscripts +
            ", status=" + status +
            ", ctime=" + ctime +
        "}";
    }
}
