package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxBpsMedia;
import com.fmb.server2022.fmbx.mapper.FmbxBpsMediaMapper;
import com.fmb.server2022.fmbx.service.IFmbxBpsMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 商户图片视频 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-09-27
 */
@Service
public class FmbxBpsMediaServiceImpl extends ServiceImpl<FmbxBpsMediaMapper, FmbxBpsMedia> implements IFmbxBpsMediaService {

}
