package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.mapper.FmbGoodsCategoryMapper;
import com.fmb.server2022.fmbx.service.IFmbGoodsCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 商品分类 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class FmbGoodsCategoryServiceImpl extends ServiceImpl<FmbGoodsCategoryMapper, FmbGoodsCategory> implements IFmbGoodsCategoryService {

}
