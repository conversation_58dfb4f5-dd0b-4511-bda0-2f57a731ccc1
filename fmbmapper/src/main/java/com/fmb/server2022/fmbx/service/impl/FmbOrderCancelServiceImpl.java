package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderCancel;
import com.fmb.server2022.fmbx.mapper.FmbOrderCancelMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderCancelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单取消表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-22
 */
@Service
public class FmbOrderCancelServiceImpl extends ServiceImpl<FmbOrderCancelMapper, FmbOrderCancel> implements IFmbOrderCancelService {

}
