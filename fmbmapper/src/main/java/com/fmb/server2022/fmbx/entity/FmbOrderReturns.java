package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单退货记录列表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_returns")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderReturns implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Map<Integer, String> statusMap;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "待审核");
            map.put(1, "审核通过");
            map.put(2, "完成退货");
            map.put(3, "拒绝退货");
            map.put(4, "确认收到货");
            statusMap = Collections.unmodifiableMap(map);
        }
    }

    @TableId(value = "return_id", type = IdType.AUTO)
    private Integer returnId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单表中对应商品记录ID
     */
    private Integer recId;

    /**
     * 票种商品ID
     */
    private Integer goodsId;

    /**
     * 退货数量
     */
    private Integer goodsNum;

    /**
     * 退货类型，0为交易成功后退货（正常），1为交易关闭后发起退货
     */
    private Integer returnType;

    /**
     * 0 正常退款,1 补偿退款,2 邮费退款,4 赔偿退款
     */
    private Integer returnClass;

    /**
     * 后台用户id
     */
    private Integer adminUid;

    /**
     * 后台退货备注
     */
    private String adminDesc;

    /**
     * 用户UID
     */
    private Integer uid;

    /**
     * 退货理由
     */
    private String reason;

    /**
     * 退货原因详细描述
     */
    private String reasonTxt;

    /**
     * 退货相关凭证
     */
    private String uploadFile;

    /**
     * 退款金额 默认为订单总金额
     */
    private BigDecimal money;

    /**
     * 最大可退商品款
     */
    private BigDecimal maxMoney;

    /**
     * 父母邦承担退款
     */
    private BigDecimal fmbReturnMoney;

    /**
     * 商家承担退款
     */
    private BigDecimal shopReturnMoney;

    /**
     * 优惠券分担金额
     */
    private BigDecimal divideCouponMoney;

    /**
     * 退票的具体电子票码
     */
    private String returnCodes;

    /**
     * 退货地址信息
     */
    private String receiverTxt;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 退货账号信息，以,隔开如(支付宝,<EMAIL>,某某人)
     */
    private String buyerAccount;

    /**
     * 收货人国家: 1:中国
     */
    private Integer country;

    /**
     * 收货人省份
     */
    private Integer province;

    /**
     * 收货人城市
     */
    private Integer city;

    /**
     * 收货人区域
     */
    private Integer area;

    /**
     * 收货人街道
     */
    private Integer street;

    /**
     * 收货人地址
     */
    private String address;

    /**
     * 收货人邮编
     */
    private String zipcode;

    /**
     * 收货人电话
     */
    private String tel;

    /**
     * 收货人手机
     */
    private String mobile;

    /**
     * 收货人邮箱
     */
    private String email;

    /**
     * 物流公司
     */
    private String shippingName;

    /**
     * 物流单号
     */
    private String shippingCode;

    /**
     * 物流费
     */
    private BigDecimal shippingFee;

    /**
     * 0待审核，1审核通过，2完成退货，3拒绝退货,4确认收到货
     */
    private Integer status;

    /**
     * 第三方退款状态 1未发起退款2退款中3已完成退款 4拒绝退款
     */
    private Integer thirdPartStatus;

    /**
     * 退货处理方式 0退款不退货 1退款退货
     */
    private Integer isReturn;

    /**
     * 退货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime returnTime;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    /**
     * 退款优惠券分摊
     */
    private BigDecimal couponMoney;

    /**
     * 退款文惠券金额
     */
    private BigDecimal culturalCouponMoney;

    /**
     * 该退款单用掉的账户余额
     */
    private BigDecimal usedCashMoney;

    /**
     * 要退的余额抵扣
     */
    private BigDecimal deductMoney;

    /**
     * 要扣的余额返现
     */
    private BigDecimal backMoney;

    /**
     * 用户账户加减
     */
    private BigDecimal userCashMoney;

    /**
     * 对可退商品款扣减的钱
     */
    private BigDecimal reduceMaxMoney;

    /**
     * 所退的fmb_order_certinfo_all表中的身份信息,多个用英文逗号隔开
     */
    private String identyCids;

    /**
     * 退款时所属的同一组，由时间戳+三个随机数组成，目前只有长线游有
     */
    private String returnGroup;

    /**
     * 操作类型long_all:长线游整退， long_single:长线游分退
     */
    private String operTypes;

    /**
     * 退货所属结构new:新表结构(global_order_base)；old:旧表结构(fmb_order_info)
     */
    private String orderTypes;

    /**
     * 退款余额说明
     */
    private String cashNote;

    /**
     * 状态取消时，是否是前台用户操作 0 不是 -1 是
     */
    private Integer isUserReturn;

    /**
     * 是否是超时退款 1是 0否
     */
    private Integer isOvertimeReturn;

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getGoodsNum() {
        return goodsNum;
    }

    public void setGoodsNum(Integer goodsNum) {
        this.goodsNum = goodsNum;
    }
    public Integer getReturnType() {
        return returnType;
    }

    public void setReturnType(Integer returnType) {
        this.returnType = returnType;
    }
    public Integer getReturnClass() {
        return returnClass;
    }

    public void setReturnClass(Integer returnClass) {
        this.returnClass = returnClass;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getAdminDesc() {
        return adminDesc;
    }

    public void setAdminDesc(String adminDesc) {
        this.adminDesc = adminDesc;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
    public String getReasonTxt() {
        return reasonTxt;
    }

    public void setReasonTxt(String reasonTxt) {
        this.reasonTxt = reasonTxt;
    }
    public String getUploadFile() {
        return uploadFile;
    }

    public void setUploadFile(String uploadFile) {
        this.uploadFile = uploadFile;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public BigDecimal getMaxMoney() {
        return maxMoney;
    }

    public void setMaxMoney(BigDecimal maxMoney) {
        this.maxMoney = maxMoney;
    }
    public BigDecimal getFmbReturnMoney() {
        return fmbReturnMoney;
    }

    public void setFmbReturnMoney(BigDecimal fmbReturnMoney) {
        this.fmbReturnMoney = fmbReturnMoney;
    }
    public BigDecimal getShopReturnMoney() {
        return shopReturnMoney;
    }

    public void setShopReturnMoney(BigDecimal shopReturnMoney) {
        this.shopReturnMoney = shopReturnMoney;
    }
    public BigDecimal getDivideCouponMoney() {
        return divideCouponMoney;
    }

    public void setDivideCouponMoney(BigDecimal divideCouponMoney) {
        this.divideCouponMoney = divideCouponMoney;
    }
    public String getReturnCodes() {
        return returnCodes;
    }

    public void setReturnCodes(String returnCodes) {
        this.returnCodes = returnCodes;
    }
    public String getReceiverTxt() {
        return receiverTxt;
    }

    public void setReceiverTxt(String receiverTxt) {
        this.receiverTxt = receiverTxt;
    }
    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    public String getBuyerAccount() {
        return buyerAccount;
    }

    public void setBuyerAccount(String buyerAccount) {
        this.buyerAccount = buyerAccount;
    }
    public Integer getCountry() {
        return country;
    }

    public void setCountry(Integer country) {
        this.country = country;
    }
    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }
    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }
    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }
    public Integer getStreet() {
        return street;
    }

    public void setStreet(Integer street) {
        this.street = street;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getShippingName() {
        return shippingName;
    }

    public void setShippingName(String shippingName) {
        this.shippingName = shippingName;
    }
    public String getShippingCode() {
        return shippingCode;
    }

    public void setShippingCode(String shippingCode) {
        this.shippingCode = shippingCode;
    }
    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getThirdPartStatus() {
        return thirdPartStatus;
    }

    public void setThirdPartStatus(Integer thirdPartStatus) {
        this.thirdPartStatus = thirdPartStatus;
    }
    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }
    public LocalDateTime getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(LocalDateTime returnTime) {
        this.returnTime = returnTime;
    }
    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }
    public BigDecimal getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(BigDecimal couponMoney) {
        this.couponMoney = couponMoney;
    }
    public BigDecimal getCulturalCouponMoney() {
        return culturalCouponMoney;
    }

    public void setCulturalCouponMoney(BigDecimal culturalCouponMoney) {
        this.culturalCouponMoney = culturalCouponMoney;
    }
    public BigDecimal getUsedCashMoney() {
        return usedCashMoney;
    }

    public void setUsedCashMoney(BigDecimal usedCashMoney) {
        this.usedCashMoney = usedCashMoney;
    }
    public BigDecimal getDeductMoney() {
        return deductMoney;
    }

    public void setDeductMoney(BigDecimal deductMoney) {
        this.deductMoney = deductMoney;
    }
    public BigDecimal getBackMoney() {
        return backMoney;
    }

    public void setBackMoney(BigDecimal backMoney) {
        this.backMoney = backMoney;
    }
    public BigDecimal getUserCashMoney() {
        return userCashMoney;
    }

    public void setUserCashMoney(BigDecimal userCashMoney) {
        this.userCashMoney = userCashMoney;
    }
    public BigDecimal getReduceMaxMoney() {
        return reduceMaxMoney;
    }

    public void setReduceMaxMoney(BigDecimal reduceMaxMoney) {
        this.reduceMaxMoney = reduceMaxMoney;
    }
    public String getIdentyCids() {
        return identyCids;
    }

    public void setIdentyCids(String identyCids) {
        this.identyCids = identyCids;
    }
    public String getReturnGroup() {
        return returnGroup;
    }

    public void setReturnGroup(String returnGroup) {
        this.returnGroup = returnGroup;
    }
    public String getOperTypes() {
        return operTypes;
    }

    public void setOperTypes(String operTypes) {
        this.operTypes = operTypes;
    }
    public String getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(String orderTypes) {
        this.orderTypes = orderTypes;
    }
    public String getCashNote() {
        return cashNote;
    }

    public void setCashNote(String cashNote) {
        this.cashNote = cashNote;
    }
    public Integer getIsUserReturn() {
        return isUserReturn;
    }

    public void setIsUserReturn(Integer isUserReturn) {
        this.isUserReturn = isUserReturn;
    }
    public Integer getIsOvertimeReturn() {
        return isOvertimeReturn;
    }

    public void setIsOvertimeReturn(Integer isOvertimeReturn) {
        this.isOvertimeReturn = isOvertimeReturn;
    }

    @Override
    public String toString() {
        return "FmbOrderReturns{" +
            "returnId=" + returnId +
            ", orderSn=" + orderSn +
            ", recId=" + recId +
            ", goodsId=" + goodsId +
            ", goodsNum=" + goodsNum +
            ", returnType=" + returnType +
            ", returnClass=" + returnClass +
            ", adminUid=" + adminUid +
            ", adminDesc=" + adminDesc +
            ", uid=" + uid +
            ", reason=" + reason +
            ", reasonTxt=" + reasonTxt +
            ", uploadFile=" + uploadFile +
            ", money=" + money +
            ", maxMoney=" + maxMoney +
            ", fmbReturnMoney=" + fmbReturnMoney +
            ", shopReturnMoney=" + shopReturnMoney +
            ", divideCouponMoney=" + divideCouponMoney +
            ", returnCodes=" + returnCodes +
            ", receiverTxt=" + receiverTxt +
            ", receiver=" + receiver +
            ", buyerAccount=" + buyerAccount +
            ", country=" + country +
            ", province=" + province +
            ", city=" + city +
            ", area=" + area +
            ", street=" + street +
            ", address=" + address +
            ", zipcode=" + zipcode +
            ", tel=" + tel +
            ", mobile=" + mobile +
            ", email=" + email +
            ", shippingName=" + shippingName +
            ", shippingCode=" + shippingCode +
            ", shippingFee=" + shippingFee +
            ", status=" + status +
            ", thirdPartStatus=" + thirdPartStatus +
            ", isReturn=" + isReturn +
            ", returnTime=" + returnTime +
            ", applyTime=" + applyTime +
            ", couponMoney=" + couponMoney +
            ", culturalCouponMoney=" + culturalCouponMoney +
            ", usedCashMoney=" + usedCashMoney +
            ", deductMoney=" + deductMoney +
            ", backMoney=" + backMoney +
            ", userCashMoney=" + userCashMoney +
            ", reduceMaxMoney=" + reduceMaxMoney +
            ", identyCids=" + identyCids +
            ", returnGroup=" + returnGroup +
            ", operTypes=" + operTypes +
            ", orderTypes=" + orderTypes +
            ", cashNote=" + cashNote +
            ", isUserReturn=" + isUserReturn +
            ", isOvertimeReturn=" + isOvertimeReturn +
        "}";
    }
}
