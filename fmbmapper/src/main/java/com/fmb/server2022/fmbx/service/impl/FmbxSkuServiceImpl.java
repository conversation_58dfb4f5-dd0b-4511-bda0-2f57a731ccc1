package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.mapper.FmbxSkuMapper;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店套餐和房型绑定,相当于票种表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-29
 */
@Service
public class FmbxSkuServiceImpl extends ServiceImpl<FmbxSkuMapper, FmbxSku> implements IFmbxSkuService {

}
