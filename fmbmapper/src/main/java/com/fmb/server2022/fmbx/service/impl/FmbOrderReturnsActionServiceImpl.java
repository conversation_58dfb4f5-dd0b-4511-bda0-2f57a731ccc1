package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderReturnsAction;
import com.fmb.server2022.fmbx.mapper.FmbOrderReturnsActionMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsActionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 退货操作记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
public class FmbOrderReturnsActionServiceImpl extends ServiceImpl<FmbOrderReturnsActionMapper, FmbOrderReturnsAction> implements IFmbOrderReturnsActionService {

}
