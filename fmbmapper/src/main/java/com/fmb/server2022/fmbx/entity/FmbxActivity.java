package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * fmbx活动主表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_activity")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "xaid", type = IdType.AUTO)
    private Integer xaid;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 供应商id
     */
    private Integer bpId;

    /**
     * 合同id 多个英文逗号分隔
     */
    private String bpcId;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店
     */
    private Integer ticketType;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动副标题
     */
    private String subTitle;

    /**
     * 显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
     */
    private String platId;

    /**
     * 产品所属分站
     */
    private Integer cityId;

    /**
     * 可售卖分站(find_in_set查询)
     */
    private String salesCityIds;

    /**
     * 省份
     */
    private String provinces;

    /**
     * 省份下的区
     */
    private String provincesSub;

    /**
     * 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
     */
    private Integer businessType;

    /**
     * 二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
     */
    private Integer isConfirm;

    /**
     * 活动banner多图
     */
    private String bannerList;

    /**
     * 活动bannerurl地址,json数组
     */
    private String bannerListUrl;

    /**
     * 视频id
     */
    private Integer videoMediaId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 删除:0-正常,1-被删除
     */
    private Integer flagDelete;

    /**
     * 售卖状态:0-未售(sku无效),1-在售
     */
    private Integer flagHaveValidSku;

    /**
     * 上下架状态:0-待发布,1-已上架,2-已下架
     */
    private Integer flagPublish;

    /**
     * 活动下被标记为删除的sku个数
     */
    private Integer deletedSkuNum;

    /**
     * 最低售价
     */
    private BigDecimal minGoodsPrice;

    /**
     * 最低原价
     */
    private BigDecimal minMarketPrice;

    /**
     * 创建人的uid
     */
    private Integer createUid;

    /**
     * 创建人的名字(冗余)
     */
    private String createUidName;

    /**
     * 最后操作人uid
     */
    private Integer lastModifyUid;

    /**
     * 最后操作人的名字(冗余)
     */
    private String lastModifyUidName;

    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }
    public Integer getBpId() {
        return bpId;
    }

    public void setBpId(Integer bpId) {
        this.bpId = bpId;
    }
    public String getBpcId() {
        return bpcId;
    }

    public void setBpcId(String bpcId) {
        this.bpcId = bpcId;
    }
    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
    public Integer getTicketType() {
        return ticketType;
    }

    public void setTicketType(Integer ticketType) {
        this.ticketType = ticketType;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
    public String getPlatId() {
        return platId;
    }

    public void setPlatId(String platId) {
        this.platId = platId;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public String getSalesCityIds() {
        return salesCityIds;
    }

    public void setSalesCityIds(String salesCityIds) {
        this.salesCityIds = salesCityIds;
    }
    public String getProvinces() {
        return provinces;
    }

    public void setProvinces(String provinces) {
        this.provinces = provinces;
    }
    public String getProvincesSub() {
        return provincesSub;
    }

    public void setProvincesSub(String provincesSub) {
        this.provincesSub = provincesSub;
    }
    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }
    public String getBannerList() {
        return bannerList;
    }

    public void setBannerList(String bannerList) {
        this.bannerList = bannerList;
    }
    public String getBannerListUrl() {
        return bannerListUrl;
    }

    public void setBannerListUrl(String bannerListUrl) {
        this.bannerListUrl = bannerListUrl;
    }
    public Integer getVideoMediaId() {
        return videoMediaId;
    }

    public void setVideoMediaId(Integer videoMediaId) {
        this.videoMediaId = videoMediaId;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getFlagDelete() {
        return flagDelete;
    }

    public void setFlagDelete(Integer flagDelete) {
        this.flagDelete = flagDelete;
    }
    public Integer getFlagHaveValidSku() {
        return flagHaveValidSku;
    }

    public void setFlagHaveValidSku(Integer flagHaveValidSku) {
        this.flagHaveValidSku = flagHaveValidSku;
    }
    public Integer getFlagPublish() {
        return flagPublish;
    }

    public void setFlagPublish(Integer flagPublish) {
        this.flagPublish = flagPublish;
    }
    public Integer getDeletedSkuNum() {
        return deletedSkuNum;
    }

    public void setDeletedSkuNum(Integer deletedSkuNum) {
        this.deletedSkuNum = deletedSkuNum;
    }
    public BigDecimal getMinGoodsPrice() {
        return minGoodsPrice;
    }

    public void setMinGoodsPrice(BigDecimal minGoodsPrice) {
        this.minGoodsPrice = minGoodsPrice;
    }
    public BigDecimal getMinMarketPrice() {
        return minMarketPrice;
    }

    public void setMinMarketPrice(BigDecimal minMarketPrice) {
        this.minMarketPrice = minMarketPrice;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public String getCreateUidName() {
        return createUidName;
    }

    public void setCreateUidName(String createUidName) {
        this.createUidName = createUidName;
    }
    public Integer getLastModifyUid() {
        return lastModifyUid;
    }

    public void setLastModifyUid(Integer lastModifyUid) {
        this.lastModifyUid = lastModifyUid;
    }
    public String getLastModifyUidName() {
        return lastModifyUidName;
    }

    public void setLastModifyUidName(String lastModifyUidName) {
        this.lastModifyUidName = lastModifyUidName;
    }

    @Override
    public String toString() {
        return "FmbxActivity{" +
            "xaid=" + xaid +
            ", bpsId=" + bpsId +
            ", bpId=" + bpId +
            ", bpcId=" + bpcId +
            ", categoryId=" + categoryId +
            ", ticketType=" + ticketType +
            ", title=" + title +
            ", subTitle=" + subTitle +
            ", platId=" + platId +
            ", cityId=" + cityId +
            ", salesCityIds=" + salesCityIds +
            ", provinces=" + provinces +
            ", provincesSub=" + provincesSub +
            ", businessType=" + businessType +
            ", isConfirm=" + isConfirm +
            ", bannerList=" + bannerList +
            ", bannerListUrl=" + bannerListUrl +
            ", videoMediaId=" + videoMediaId +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", flagDelete=" + flagDelete +
            ", flagHaveValidSku=" + flagHaveValidSku +
            ", flagPublish=" + flagPublish +
            ", deletedSkuNum=" + deletedSkuNum +
            ", minGoodsPrice=" + minGoodsPrice +
            ", minMarketPrice=" + minMarketPrice +
            ", createUid=" + createUid +
            ", createUidName=" + createUidName +
            ", lastModifyUid=" + lastModifyUid +
            ", lastModifyUidName=" + lastModifyUidName +
        "}";
    }
}
