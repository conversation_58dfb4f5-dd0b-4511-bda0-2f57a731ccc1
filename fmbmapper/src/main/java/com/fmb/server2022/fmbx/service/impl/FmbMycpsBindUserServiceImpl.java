package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbMycpsBindUser;
import com.fmb.server2022.fmbx.mapper.FmbMycpsBindUserMapper;
import com.fmb.server2022.fmbx.service.IFmbMycpsBindUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 分销商与C端用户关系表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-09-18
 */
@Service
public class FmbMycpsBindUserServiceImpl extends ServiceImpl<FmbMycpsBindUserMapper, FmbMycpsBindUser> implements IFmbMycpsBindUserService {

}
