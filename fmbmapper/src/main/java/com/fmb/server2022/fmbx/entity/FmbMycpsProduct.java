package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 分销商品信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_mycps_product")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbMycpsProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "pid", type = IdType.AUTO)
    private Integer pid;

    /**
     * 产品/活动id
     */
    private Integer apId;

    /**
     * 1:演出;2:景点;3:通用扩展;4:酒店;15酒+景;50:跟团游;51:自由行;
     */
    private Integer ticketType;

    /**
     * 一级分销佣金比例
     */
    private BigDecimal commissionRateOne;

    /**
     * 二级分销佣金比例
     */
    private BigDecimal commissionRateTwo;

    /**
     * 三级分销佣金比例
     */
    private BigDecimal commissionRateThree;

    /**
     * 分销海报url
     */
    private String banner;

    /**
     * 推广文案
     */
    private String description;

    /**
     * 分销票种,1:所有票种,2:仅分销票种
     */
    private Integer mycpsTicket;

    /**
     * 分销票种最低售价
     */
    private Float sellMinPrice;

    /**
     * 分销最大价格
     */
    private Float sellMaxPrice;

    /**
     * 分销限制,1:按等级,2:按分销商
     */
    private Integer mycpsLimit;

    /**
     * 可分销等级(多个等级逗号分隔)
     */
    private String mycpsLevel;

    /**
     * 排除分销商(多个分销商逗号分隔)
     */
    private String removeMycps;

    /**
     * 按分销商限制销售时,只这些分销商可以销售(多个分销商逗号分隔)
     */
    private String allMycps;

    /**
     * 账期
     */
    private Integer overDays;

    /**
     * 补充账期
     */
    private Integer thirdOverDays;

    /**
     * 状态：0：未审核，1：可售，2：禁售
     */
    private Integer status;

    /**
     * 后台编辑用户ID
     */
    private Integer adminUid;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 一级分销间接佣金比例
     */
    private BigDecimal commissionIndirectRateOne;

    /**
     * 二级分销间接佣金比例
     */
    private BigDecimal commissionIndirectRateTwo;

    /**
     * 三级分间接销佣金比例
     */
    private BigDecimal commissionIndirectRateThree;

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }
    public Integer getApId() {
        return apId;
    }

    public void setApId(Integer apId) {
        this.apId = apId;
    }
    public Integer getTicketType() {
        return ticketType;
    }

    public void setTicketType(Integer ticketType) {
        this.ticketType = ticketType;
    }
    public BigDecimal getCommissionRateOne() {
        return commissionRateOne;
    }

    public void setCommissionRateOne(BigDecimal commissionRateOne) {
        this.commissionRateOne = commissionRateOne;
    }
    public BigDecimal getCommissionRateTwo() {
        return commissionRateTwo;
    }

    public void setCommissionRateTwo(BigDecimal commissionRateTwo) {
        this.commissionRateTwo = commissionRateTwo;
    }
    public BigDecimal getCommissionRateThree() {
        return commissionRateThree;
    }

    public void setCommissionRateThree(BigDecimal commissionRateThree) {
        this.commissionRateThree = commissionRateThree;
    }
    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public Integer getMycpsTicket() {
        return mycpsTicket;
    }

    public void setMycpsTicket(Integer mycpsTicket) {
        this.mycpsTicket = mycpsTicket;
    }
    public Float getSellMinPrice() {
        return sellMinPrice;
    }

    public void setSellMinPrice(Float sellMinPrice) {
        this.sellMinPrice = sellMinPrice;
    }
    public Float getSellMaxPrice() {
        return sellMaxPrice;
    }

    public void setSellMaxPrice(Float sellMaxPrice) {
        this.sellMaxPrice = sellMaxPrice;
    }
    public Integer getMycpsLimit() {
        return mycpsLimit;
    }

    public void setMycpsLimit(Integer mycpsLimit) {
        this.mycpsLimit = mycpsLimit;
    }
    public String getMycpsLevel() {
        return mycpsLevel;
    }

    public void setMycpsLevel(String mycpsLevel) {
        this.mycpsLevel = mycpsLevel;
    }
    public String getRemoveMycps() {
        return removeMycps;
    }

    public void setRemoveMycps(String removeMycps) {
        this.removeMycps = removeMycps;
    }
    public String getAllMycps() {
        return allMycps;
    }

    public void setAllMycps(String allMycps) {
        this.allMycps = allMycps;
    }
    public Integer getOverDays() {
        return overDays;
    }

    public void setOverDays(Integer overDays) {
        this.overDays = overDays;
    }
    public Integer getThirdOverDays() {
        return thirdOverDays;
    }

    public void setThirdOverDays(Integer thirdOverDays) {
        this.thirdOverDays = thirdOverDays;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public BigDecimal getCommissionIndirectRateOne() {
        return commissionIndirectRateOne;
    }

    public void setCommissionIndirectRateOne(BigDecimal commissionIndirectRateOne) {
        this.commissionIndirectRateOne = commissionIndirectRateOne;
    }
    public BigDecimal getCommissionIndirectRateTwo() {
        return commissionIndirectRateTwo;
    }

    public void setCommissionIndirectRateTwo(BigDecimal commissionIndirectRateTwo) {
        this.commissionIndirectRateTwo = commissionIndirectRateTwo;
    }
    public BigDecimal getCommissionIndirectRateThree() {
        return commissionIndirectRateThree;
    }

    public void setCommissionIndirectRateThree(BigDecimal commissionIndirectRateThree) {
        this.commissionIndirectRateThree = commissionIndirectRateThree;
    }

    @Override
    public String toString() {
        return "FmbMycpsProduct{" +
            "pid=" + pid +
            ", apId=" + apId +
            ", ticketType=" + ticketType +
            ", commissionRateOne=" + commissionRateOne +
            ", commissionRateTwo=" + commissionRateTwo +
            ", commissionRateThree=" + commissionRateThree +
            ", banner=" + banner +
            ", description=" + description +
            ", mycpsTicket=" + mycpsTicket +
            ", sellMinPrice=" + sellMinPrice +
            ", sellMaxPrice=" + sellMaxPrice +
            ", mycpsLimit=" + mycpsLimit +
            ", mycpsLevel=" + mycpsLevel +
            ", removeMycps=" + removeMycps +
            ", allMycps=" + allMycps +
            ", overDays=" + overDays +
            ", thirdOverDays=" + thirdOverDays +
            ", status=" + status +
            ", adminUid=" + adminUid +
            ", utime=" + utime +
            ", ctime=" + ctime +
            ", commissionIndirectRateOne=" + commissionIndirectRateOne +
            ", commissionIndirectRateTwo=" + commissionIndirectRateTwo +
            ", commissionIndirectRateThree=" + commissionIndirectRateThree +
        "}";
    }
}
