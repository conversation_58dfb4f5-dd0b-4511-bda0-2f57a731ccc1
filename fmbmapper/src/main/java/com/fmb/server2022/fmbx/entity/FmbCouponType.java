package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 优惠券主表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_coupon_type")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbCouponType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠券自增ID
     */
    @TableId(value = "coupon_id", type = IdType.AUTO)
    private Integer couponId;

    /**
     * 优惠券名
     */
    private String couponName;

    /**
     * 优惠券组标签
     */
    private String couponTag;

    /**
     * 优惠券备注
     */
    private String couponRemark;

    /**
     * 优惠券类型 （1为代金券 2为满减 3叠加券 4折扣券）
     */
    private Integer couponType;

    /**
     * 是否可叠加1-可以0-不可以
     */
    private Integer stacked;

    /**
     * 该优惠劵所属方；china_bank-中国银行
     */
    private String belongType;

    /**
     * 优惠券金额
     */
    private BigDecimal couponMoney;

    /**
     * 折扣率
     */
    private Integer discountRate;

    /**
     * 生成优惠券码使用次数
     */
    private Integer useTimes;

    /**
     * 用户等级 0为任何用户
     */
    private Integer useRank;

    /**
     * 优惠券使用范围 0为无限制
     */
    private Integer useLimit;

    /**
     * 优惠券开始使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 优惠券结束使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 优惠券最小订单金额
     */
    private BigDecimal minAmount;

    /**
     * 共生成优惠券数量
     */
    private Integer sendNum;

    /**
     * 序列化之后的信息
     */
    private String extInfo;

    /**
     *  是否可以使用 0 为不可用 1为可用
     */
    private Integer status;

    /**
     * 创建优惠券操作人ID
     */
    private Integer adminUid;

    /**
     * 创建优惠券时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 限制出行日期为数组序列化以后的格式,反序列化后KEY为日期类型:1-平日,2-周末,3-中秋,4-国庆
     */
    private String limitDate;

    /**
     * 优惠券支持的活动ID
     */
    private Integer activityId;

    /**
     * 优惠券支持平台ID:0-全平台,1-PC,2-APP,3-微信,4-APP或微信,5-微信小程序
     */
    private Integer platId;

    /**
     * 优惠券限制品类
     */
    private Integer limitType;

    /**
     * 优惠券分类
     */
    private String limitCate;

    /**
     * 对应的标签管理列表id
     */
    private Integer tagListId;

    /**
     * 优惠券最后操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 分类名
     */
    @TableField(exist = false)
    private String cateName;

    /**
     * 是否领取 0未领取 1已领取
     */
    @TableField(exist = false)
    private Integer isDrawCoupon;

    /**
     * 券描述
     */
    @TableField(exist = false)
    private String discountRateDesc;

    public String getDiscountRateDesc() {
        return discountRateDesc;
    }

    public void setDiscountRateDesc(String discountRateDesc) {
        this.discountRateDesc = discountRateDesc;
    }

    public Integer getIsDrawCoupon() {
        return isDrawCoupon;
    }

    public void setIsDrawCoupon(Integer isDrawCoupon) {
        this.isDrawCoupon = isDrawCoupon;
    }

    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }
    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
    public String getCouponTag() {
        return couponTag;
    }

    public void setCouponTag(String couponTag) {
        this.couponTag = couponTag;
    }
    public String getCouponRemark() {
        return couponRemark;
    }

    public void setCouponRemark(String couponRemark) {
        this.couponRemark = couponRemark;
    }
    public Integer getCouponType() {
        return couponType;
    }

    public void setCouponType(Integer couponType) {
        this.couponType = couponType;
    }
    public Integer getStacked() {
        return stacked;
    }

    public void setStacked(Integer stacked) {
        this.stacked = stacked;
    }
    public String getBelongType() {
        return belongType;
    }

    public void setBelongType(String belongType) {
        this.belongType = belongType;
    }
    public BigDecimal getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(BigDecimal couponMoney) {
        this.couponMoney = couponMoney;
    }
    public Integer getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Integer discountRate) {
        this.discountRate = discountRate;
    }
    public Integer getUseTimes() {
        return useTimes;
    }

    public void setUseTimes(Integer useTimes) {
        this.useTimes = useTimes;
    }
    public Integer getUseRank() {
        return useRank;
    }

    public void setUseRank(Integer useRank) {
        this.useRank = useRank;
    }
    public Integer getUseLimit() {
        return useLimit;
    }

    public void setUseLimit(Integer useLimit) {
        this.useLimit = useLimit;
    }
    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }
    public Integer getSendNum() {
        return sendNum;
    }

    public void setSendNum(Integer sendNum) {
        this.sendNum = sendNum;
    }
    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getLimitDate() {
        return limitDate;
    }

    public void setLimitDate(String limitDate) {
        this.limitDate = limitDate;
    }
    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }
    public Integer getPlatId() {
        return platId;
    }

    public void setPlatId(Integer platId) {
        this.platId = platId;
    }
    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }
    public String getLimitCate() {
        return limitCate;
    }

    public void setLimitCate(String limitCate) {
        this.limitCate = limitCate;
    }
    public Integer getTagListId() {
        return tagListId;
    }

    public void setTagListId(Integer tagListId) {
        this.tagListId = tagListId;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }

    @Override
    public String toString() {
        return "FmbCouponType{" +
            "couponId=" + couponId +
            ", couponName=" + couponName +
            ", couponTag=" + couponTag +
            ", couponRemark=" + couponRemark +
            ", couponType=" + couponType +
            ", stacked=" + stacked +
            ", belongType=" + belongType +
            ", couponMoney=" + couponMoney +
            ", discountRate=" + discountRate +
            ", useTimes=" + useTimes +
            ", useRank=" + useRank +
            ", useLimit=" + useLimit +
            ", startTime=" + startTime +
            ", endTime=" + endTime +
            ", minAmount=" + minAmount +
            ", sendNum=" + sendNum +
            ", extInfo=" + extInfo +
            ", status=" + status +
            ", adminUid=" + adminUid +
            ", ctime=" + ctime +
            ", limitDate=" + limitDate +
            ", activityId=" + activityId +
            ", platId=" + platId +
            ", limitType=" + limitType +
            ", limitCate=" + limitCate +
            ", tagListId=" + tagListId +
            ", utime=" + utime +
        "}";
    }
}
