package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店产品的房型列表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_hotel_house_types")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbHotelHouseTypes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID,房型ID
     */
    @TableId(value = "house_type_id", type = IdType.AUTO)
    private Integer houseTypeId;

    /**
     * 关联的票务ID(酒店)
     */
    private Integer ticketGroupId;

    /**
     * 房型名称
     */
    private String name;

    /**
     * 房型图片
     */
    private String image;

    /**
     * 房型数量
     */
    private Integer number;

    /**
     * 床型列表
     */
    private String bedTypeList;

    /**
     * 能否加床
     */
    private String isAddBed;

    /**
     * 是否屏蔽 1是 0否
     */
    private Integer status;

    /**
     * 建筑面积
     */
    private String buildArea;

    /**
     * 楼层描述
     */
    private String floorNumber;

    /**
     * 网络描述
     */
    private String networkType;

    /**
     * 吸烟描述
     */
    private String smokeType;

    /**
     * 相关更多描述
     */
    private String infoDesc;

    /**
     * 台后操作用户
     */
    private Integer adminUid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getHouseTypeId() {
        return houseTypeId;
    }

    public void setHouseTypeId(Integer houseTypeId) {
        this.houseTypeId = houseTypeId;
    }
    public Integer getTicketGroupId() {
        return ticketGroupId;
    }

    public void setTicketGroupId(Integer ticketGroupId) {
        this.ticketGroupId = ticketGroupId;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }
    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
    public String getBedTypeList() {
        return bedTypeList;
    }

    public void setBedTypeList(String bedTypeList) {
        this.bedTypeList = bedTypeList;
    }
    public String getIsAddBed() {
        return isAddBed;
    }

    public void setIsAddBed(String isAddBed) {
        this.isAddBed = isAddBed;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getBuildArea() {
        return buildArea;
    }

    public void setBuildArea(String buildArea) {
        this.buildArea = buildArea;
    }
    public String getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(String floorNumber) {
        this.floorNumber = floorNumber;
    }
    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }
    public String getSmokeType() {
        return smokeType;
    }

    public void setSmokeType(String smokeType) {
        this.smokeType = smokeType;
    }
    public String getInfoDesc() {
        return infoDesc;
    }

    public void setInfoDesc(String infoDesc) {
        this.infoDesc = infoDesc;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbHotelHouseTypes{" +
            "houseTypeId=" + houseTypeId +
            ", ticketGroupId=" + ticketGroupId +
            ", name=" + name +
            ", image=" + image +
            ", number=" + number +
            ", bedTypeList=" + bedTypeList +
            ", isAddBed=" + isAddBed +
            ", status=" + status +
            ", buildArea=" + buildArea +
            ", floorNumber=" + floorNumber +
            ", networkType=" + networkType +
            ", smokeType=" + smokeType +
            ", infoDesc=" + infoDesc +
            ", adminUid=" + adminUid +
            ", ctime=" + ctime +
        "}";
    }
}
