package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 退货内部备注表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_returns_note")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderReturnsNote implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "note_id", type = IdType.AUTO)
    private Integer noteId;

    /**
     * 操作人UID
     */
    private Integer adminUid;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单表中对应商品记录ID
     */
    private Integer recId;

    /**
     * 具体票种商品ID
     */
    private Integer goodsId;

    /**
     * 退货ID
     */
    private Integer returnId;

    /**
     * 退货状态   0待审核，1审核通过，2完成退货，3拒绝退货,4确认收到退货
     */
    private Integer returnStatus;

    /**
     * 上传附件
     */
    private String uploadFile;

    /**
     * 备注说明
     */
    private String actionNote;

    /**
     * 成生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Integer getNoteId() {
        return noteId;
    }

    public void setNoteId(Integer noteId) {
        this.noteId = noteId;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }
    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }
    public String getUploadFile() {
        return uploadFile;
    }

    public void setUploadFile(String uploadFile) {
        this.uploadFile = uploadFile;
    }
    public String getActionNote() {
        return actionNote;
    }

    public void setActionNote(String actionNote) {
        this.actionNote = actionNote;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "FmbOrderReturnsNote{" +
            "noteId=" + noteId +
            ", adminUid=" + adminUid +
            ", orderSn=" + orderSn +
            ", recId=" + recId +
            ", goodsId=" + goodsId +
            ", returnId=" + returnId +
            ", returnStatus=" + returnStatus +
            ", uploadFile=" + uploadFile +
            ", actionNote=" + actionNote +
            ", createTime=" + createTime +
        "}";
    }
}
