package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 酒店订单每天的价格信息详情
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_hotel_goods_detail")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxHotelGoodsDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    /**
     * ordergoods的id
     */
    private Integer recId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate roomDate;

    @TableField(exist = false)
    private LocalDate playDate;

    @TableField(exist = false)
    private LocalDate leaveDate;

    /**
     * 房间数量
     */
    private Integer roomNumber;

    /**
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 套餐id
     */
    private Integer suiteId;

    /**
     * 房型名称
     */
    private String suiteName;

    /**
     * 已退房间数量
     */
    private Integer roomNumberReturn;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public LocalDate getRoomDate() {
        return roomDate;
    }

    public void setRoomDate(LocalDate roomDate) {
        this.roomDate = roomDate;
    }
    public Integer getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(Integer roomNumber) {
        this.roomNumber = roomNumber;
    }
    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }
    public BigDecimal getSettlePrice() {
        return settlePrice;
    }

    public void setSettlePrice(BigDecimal settlePrice) {
        this.settlePrice = settlePrice;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }
    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public String getSuiteName() {
        return suiteName;
    }

    public void setSuiteName(String suiteName) {
        this.suiteName = suiteName;
    }
    public Integer getRoomNumberReturn() {
        return roomNumberReturn;
    }

    public void setRoomNumberReturn(Integer roomNumberReturn) {
        this.roomNumberReturn = roomNumberReturn;
    }

    public LocalDate getPlayDate() {
        return roomDate;
    }

    public LocalDate getLeaveDate() {
        if (roomDate != null) {
            return roomDate.plusDays(1);
        }
        return roomDate;
    }

    @Override
    public String toString() {
        return "FmbxHotelGoodsDetail{" +
            "detailId=" + detailId +
            ", recId=" + recId +
            ", orderSn=" + orderSn +
            ", roomDate=" + roomDate +
            ", roomNumber=" + roomNumber +
            ", marketPrice=" + marketPrice +
            ", goodsPrice=" + goodsPrice +
            ", settlePrice=" + settlePrice +
            ", roomId=" + roomId +
            ", roomName=" + roomName +
            ", suiteId=" + suiteId +
            ", suiteName=" + suiteName +
            ", roomNumberReturn=" + roomNumberReturn +
        "}";
    }
}
