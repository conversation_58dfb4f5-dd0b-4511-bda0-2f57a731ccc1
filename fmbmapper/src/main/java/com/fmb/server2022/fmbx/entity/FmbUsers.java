package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 用户信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_users")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbUsers implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "uid", type = IdType.AUTO)
    private Integer uid;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 用户昵称
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * QQ号码
     */
    private String qqnum;

    private String password;

    private String salt;

    private Integer regdate;

    private String gender;

    /**
     * 孩子性别
     */
    private String childGender;

    /**
     * 0 - 备孕妈妈; 1 - 准妈妈; 2 - 孩子妈妈; 3 - 备孕爸爸; 4 - 准爸爸; 5 - 孩子爸爸
     */
    private Integer type;

    private Float lat;

    private Float lng;

    /**
     * 预产期 (UNIX 时间戳)
     */
    private Integer edc;

    /**
     * 宝宝生日 (UNIX 时间戳)
     */
    private Integer childBirthday;

    /**
     * If the user has NOT visited the JinXuan.Faxian channel, set this value to 1
     */
    private Integer newer;

    /**
     * 新浪UID
     */
    private Long sinaweiboUid;

    /**
     * 新浪用户名
     */
    private String sinaweiboUsername;

    /**
     * QQ微博uid
     */
    private String qqweiboUid;

    private Integer loginLastTime;

    /**
     * 是否有马甲帐户
     */
    private Integer hasSon;

    /**
     * 新浪ACESS_TOKEN
     */
    private String accessToken;

    /**
     * access_token到期时间
     */
    private Integer expiresIn;

    /**
     * 更新TOKEN时间
     */
    private Integer tokenUpdateTime;

    private Integer departmentId;

    private Integer employeeId;

    /**
     * 0普通用户，1机器人注册
     */
    private Integer userType;

    /**
     * 活跃指数
     */
    private Integer activeNum;

    /**
     * 用户发的精华贴数量
     */
    private Integer recTopicNum;

    /**
     * 用户发帖数量
     */
    private Integer topicNum;

    /**
     * 用户回帖数量
     */
    private Integer repTopicNum;

    /**
     * 到场率
     */
    private Float arriveRatio;

    /**
     * 用户活动报名数
     */
    private Integer activityApplyNum;

    /**
     * 是否修改过EMAIL，0否，1是
     */
    private Integer isChangeEmail;

    /**
     * 邮箱是否验证过 1是，0否
     */
    private Integer isCheckEmail;

    /**
     * QQ微博用户名
     */
    private String qqweiboUsername;

    /**
     * 腾讯微博token
     */
    private String qqweiboToken;

    /**
     * 腾讯微博token过期时间
     */
    private Integer qqexpiresIn;

    /**
     * 腾讯微博token更新时间
     */
    private Integer qqtokenUpdateTime;

    /**
     * QQ号的UID
     */
    private String qqnumUid;

    /**
     * QQ号的用户名
     */
    private String qqnumUsername;

    /**
     * qq号的token
     */
    private String qqnumToken;

    /**
     * qq号token过期时间
     */
    private Integer qqnumexpiresIn;

    /**
     * qq号的token过期时间
     */
    private Integer qqnumtokenUpdateTime;

    /**
     * 微信用户名
     */
    private String weixinUsername;

    /**
     * 微信TOKEN
     */
    private String weixinToken;

    /**
     * 微信刷新TOKEN
     */
    private String weixinRefreshToken;

    /**
     * 微信UID
     */
    private String weixinUid;

    /**
     * 微信公用unionid
     */
    private String weixinUnionid;

    /**
     * 微信过期时间
     */
    private Integer weixinExpiresIn;

    /**
     * 微信更新时间
     */
    private Integer weixinTokenUpdateTime;

    /**
     * 苹果UID
     */
    private String appleUid;

    /**
     * 苹果名称
     */
    private String appleFullname;

    /**
     * 苹果code
     */
    private String appleCode;

    /**
     * APP名字
     */
    private String appname;

    /**
     * tiktok_opneid
     */
    private String tiktokOpenid;

    /**
     * tiktok_unionid
     */
    private String tiktokUnionid;

    /**
     * 下载渠道
     */
    private String referer;

    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }
    public String getQqnum() {
        return qqnum;
    }

    public void setQqnum(String qqnum) {
        this.qqnum = qqnum;
    }
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }
    public Integer getRegdate() {
        return regdate;
    }

    public void setRegdate(Integer regdate) {
        this.regdate = regdate;
    }
    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
    public String getChildGender() {
        return childGender;
    }

    public void setChildGender(String childGender) {
        this.childGender = childGender;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public Float getLat() {
        return lat;
    }

    public void setLat(Float lat) {
        this.lat = lat;
    }
    public Float getLng() {
        return lng;
    }

    public void setLng(Float lng) {
        this.lng = lng;
    }
    public Integer getEdc() {
        return edc;
    }

    public void setEdc(Integer edc) {
        this.edc = edc;
    }
    public Integer getChildBirthday() {
        return childBirthday;
    }

    public void setChildBirthday(Integer childBirthday) {
        this.childBirthday = childBirthday;
    }
    public Integer getNewer() {
        return newer;
    }

    public void setNewer(Integer newer) {
        this.newer = newer;
    }
    public Long getSinaweiboUid() {
        return sinaweiboUid;
    }

    public void setSinaweiboUid(Long sinaweiboUid) {
        this.sinaweiboUid = sinaweiboUid;
    }
    public String getSinaweiboUsername() {
        return sinaweiboUsername;
    }

    public void setSinaweiboUsername(String sinaweiboUsername) {
        this.sinaweiboUsername = sinaweiboUsername;
    }
    public String getQqweiboUid() {
        return qqweiboUid;
    }

    public void setQqweiboUid(String qqweiboUid) {
        this.qqweiboUid = qqweiboUid;
    }
    public Integer getLoginLastTime() {
        return loginLastTime;
    }

    public void setLoginLastTime(Integer loginLastTime) {
        this.loginLastTime = loginLastTime;
    }
    public Integer getHasSon() {
        return hasSon;
    }

    public void setHasSon(Integer hasSon) {
        this.hasSon = hasSon;
    }
    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    public Integer getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn) {
        this.expiresIn = expiresIn;
    }
    public Integer getTokenUpdateTime() {
        return tokenUpdateTime;
    }

    public void setTokenUpdateTime(Integer tokenUpdateTime) {
        this.tokenUpdateTime = tokenUpdateTime;
    }
    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }
    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }
    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
    public Integer getActiveNum() {
        return activeNum;
    }

    public void setActiveNum(Integer activeNum) {
        this.activeNum = activeNum;
    }
    public Integer getRecTopicNum() {
        return recTopicNum;
    }

    public void setRecTopicNum(Integer recTopicNum) {
        this.recTopicNum = recTopicNum;
    }
    public Integer getTopicNum() {
        return topicNum;
    }

    public void setTopicNum(Integer topicNum) {
        this.topicNum = topicNum;
    }
    public Integer getRepTopicNum() {
        return repTopicNum;
    }

    public void setRepTopicNum(Integer repTopicNum) {
        this.repTopicNum = repTopicNum;
    }
    public Float getArriveRatio() {
        return arriveRatio;
    }

    public void setArriveRatio(Float arriveRatio) {
        this.arriveRatio = arriveRatio;
    }
    public Integer getActivityApplyNum() {
        return activityApplyNum;
    }

    public void setActivityApplyNum(Integer activityApplyNum) {
        this.activityApplyNum = activityApplyNum;
    }
    public Integer getIsChangeEmail() {
        return isChangeEmail;
    }

    public void setIsChangeEmail(Integer isChangeEmail) {
        this.isChangeEmail = isChangeEmail;
    }
    public Integer getIsCheckEmail() {
        return isCheckEmail;
    }

    public void setIsCheckEmail(Integer isCheckEmail) {
        this.isCheckEmail = isCheckEmail;
    }
    public String getQqweiboUsername() {
        return qqweiboUsername;
    }

    public void setQqweiboUsername(String qqweiboUsername) {
        this.qqweiboUsername = qqweiboUsername;
    }
    public String getQqweiboToken() {
        return qqweiboToken;
    }

    public void setQqweiboToken(String qqweiboToken) {
        this.qqweiboToken = qqweiboToken;
    }
    public Integer getQqexpiresIn() {
        return qqexpiresIn;
    }

    public void setQqexpiresIn(Integer qqexpiresIn) {
        this.qqexpiresIn = qqexpiresIn;
    }
    public Integer getQqtokenUpdateTime() {
        return qqtokenUpdateTime;
    }

    public void setQqtokenUpdateTime(Integer qqtokenUpdateTime) {
        this.qqtokenUpdateTime = qqtokenUpdateTime;
    }
    public String getQqnumUid() {
        return qqnumUid;
    }

    public void setQqnumUid(String qqnumUid) {
        this.qqnumUid = qqnumUid;
    }
    public String getQqnumUsername() {
        return qqnumUsername;
    }

    public void setQqnumUsername(String qqnumUsername) {
        this.qqnumUsername = qqnumUsername;
    }
    public String getQqnumToken() {
        return qqnumToken;
    }

    public void setQqnumToken(String qqnumToken) {
        this.qqnumToken = qqnumToken;
    }
    public Integer getQqnumexpiresIn() {
        return qqnumexpiresIn;
    }

    public void setQqnumexpiresIn(Integer qqnumexpiresIn) {
        this.qqnumexpiresIn = qqnumexpiresIn;
    }
    public Integer getQqnumtokenUpdateTime() {
        return qqnumtokenUpdateTime;
    }

    public void setQqnumtokenUpdateTime(Integer qqnumtokenUpdateTime) {
        this.qqnumtokenUpdateTime = qqnumtokenUpdateTime;
    }
    public String getWeixinUsername() {
        return weixinUsername;
    }

    public void setWeixinUsername(String weixinUsername) {
        this.weixinUsername = weixinUsername;
    }
    public String getWeixinToken() {
        return weixinToken;
    }

    public void setWeixinToken(String weixinToken) {
        this.weixinToken = weixinToken;
    }
    public String getWeixinRefreshToken() {
        return weixinRefreshToken;
    }

    public void setWeixinRefreshToken(String weixinRefreshToken) {
        this.weixinRefreshToken = weixinRefreshToken;
    }
    public String getWeixinUid() {
        return weixinUid;
    }

    public void setWeixinUid(String weixinUid) {
        this.weixinUid = weixinUid;
    }
    public String getWeixinUnionid() {
        return weixinUnionid;
    }

    public void setWeixinUnionid(String weixinUnionid) {
        this.weixinUnionid = weixinUnionid;
    }
    public Integer getWeixinExpiresIn() {
        return weixinExpiresIn;
    }

    public void setWeixinExpiresIn(Integer weixinExpiresIn) {
        this.weixinExpiresIn = weixinExpiresIn;
    }
    public Integer getWeixinTokenUpdateTime() {
        return weixinTokenUpdateTime;
    }

    public void setWeixinTokenUpdateTime(Integer weixinTokenUpdateTime) {
        this.weixinTokenUpdateTime = weixinTokenUpdateTime;
    }
    public String getAppleUid() {
        return appleUid;
    }

    public void setAppleUid(String appleUid) {
        this.appleUid = appleUid;
    }
    public String getAppleFullname() {
        return appleFullname;
    }

    public void setAppleFullname(String appleFullname) {
        this.appleFullname = appleFullname;
    }
    public String getAppleCode() {
        return appleCode;
    }

    public void setAppleCode(String appleCode) {
        this.appleCode = appleCode;
    }
    public String getAppname() {
        return appname;
    }

    public void setAppname(String appname) {
        this.appname = appname;
    }
    public String getTiktokOpenid() {
        return tiktokOpenid;
    }

    public void setTiktokOpenid(String tiktokOpenid) {
        this.tiktokOpenid = tiktokOpenid;
    }
    public String getTiktokUnionid() {
        return tiktokUnionid;
    }

    public void setTiktokUnionid(String tiktokUnionid) {
        this.tiktokUnionid = tiktokUnionid;
    }
    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    @Override
    public String toString() {
        return "FmbUsers{" +
            "uid=" + uid +
            ", email=" + email +
            ", phoneNumber=" + phoneNumber +
            ", username=" + username +
            ", realname=" + realname +
            ", qqnum=" + qqnum +
            ", password=" + password +
            ", salt=" + salt +
            ", regdate=" + regdate +
            ", gender=" + gender +
            ", childGender=" + childGender +
            ", type=" + type +
            ", lat=" + lat +
            ", lng=" + lng +
            ", edc=" + edc +
            ", childBirthday=" + childBirthday +
            ", newer=" + newer +
            ", sinaweiboUid=" + sinaweiboUid +
            ", sinaweiboUsername=" + sinaweiboUsername +
            ", qqweiboUid=" + qqweiboUid +
            ", loginLastTime=" + loginLastTime +
            ", hasSon=" + hasSon +
            ", accessToken=" + accessToken +
            ", expiresIn=" + expiresIn +
            ", tokenUpdateTime=" + tokenUpdateTime +
            ", departmentId=" + departmentId +
            ", employeeId=" + employeeId +
            ", userType=" + userType +
            ", activeNum=" + activeNum +
            ", recTopicNum=" + recTopicNum +
            ", topicNum=" + topicNum +
            ", repTopicNum=" + repTopicNum +
            ", arriveRatio=" + arriveRatio +
            ", activityApplyNum=" + activityApplyNum +
            ", isChangeEmail=" + isChangeEmail +
            ", isCheckEmail=" + isCheckEmail +
            ", qqweiboUsername=" + qqweiboUsername +
            ", qqweiboToken=" + qqweiboToken +
            ", qqexpiresIn=" + qqexpiresIn +
            ", qqtokenUpdateTime=" + qqtokenUpdateTime +
            ", qqnumUid=" + qqnumUid +
            ", qqnumUsername=" + qqnumUsername +
            ", qqnumToken=" + qqnumToken +
            ", qqnumexpiresIn=" + qqnumexpiresIn +
            ", qqnumtokenUpdateTime=" + qqnumtokenUpdateTime +
            ", weixinUsername=" + weixinUsername +
            ", weixinToken=" + weixinToken +
            ", weixinRefreshToken=" + weixinRefreshToken +
            ", weixinUid=" + weixinUid +
            ", weixinUnionid=" + weixinUnionid +
            ", weixinExpiresIn=" + weixinExpiresIn +
            ", weixinTokenUpdateTime=" + weixinTokenUpdateTime +
            ", appleUid=" + appleUid +
            ", appleFullname=" + appleFullname +
            ", appleCode=" + appleCode +
            ", appname=" + appname +
            ", tiktokOpenid=" + tiktokOpenid +
            ", tiktokUnionid=" + tiktokUnionid +
            ", referer=" + referer +
        "}";
    }
}
