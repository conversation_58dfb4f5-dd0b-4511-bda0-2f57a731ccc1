package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 优惠券生成码表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_user_coupon")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbUserCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠券生成码
     */
    @TableId(value = "coupon_sn")
    private String couponSn;

    /**
     * 优惠券关联ID
     */
    private Integer couponId;

    /**
     * 优惠券金额
     */
    private Integer couponMoney;

    /**
     * 单个优惠券订单最小金额
     */
    private BigDecimal minAmount;

    /**
     * 折扣率
     */
    private Integer discountRate;

    /**
     * 用户UID
     */
    private Integer uid;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 从哪个订单中获得的优惠券
     */
    private String fromOrderSn;

    /**
     * 剩余的使用次数
     */
    private Integer remainTimes;

    /**
     * 发送渠道
     */
    private String source;

    /**
     * 后台派发优惠券的用户UID
     */
    private Integer adminUid;

    /**
     * 派发状态 0为未发送 1为已发送
     */
    private Integer sendStatus;

    /**
     * 使用状态 0为未使用 1为使用中 2为已使用 3是已冻结
     */
    private Integer useStatus;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime useTime;

    /**
     * 派发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime distributeTime;

    /**
     * 单个优惠券开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponStartTime;

    /**
     * 单个优惠券结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponEndTime;

    /**
     * 单个优惠券创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponCreateTime;

    /**
     * 派发分销商ID
     */
    private Integer mid;

    public String getCouponSn() {
        return couponSn;
    }

    public void setCouponSn(String couponSn) {
        this.couponSn = couponSn;
    }
    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }
    public Integer getCouponMoney() {
        return couponMoney;
    }

    public void setCouponMoney(Integer couponMoney) {
        this.couponMoney = couponMoney;
    }
    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }
    public Integer getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Integer discountRate) {
        this.discountRate = discountRate;
    }
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getFromOrderSn() {
        return fromOrderSn;
    }

    public void setFromOrderSn(String fromOrderSn) {
        this.fromOrderSn = fromOrderSn;
    }
    public Integer getRemainTimes() {
        return remainTimes;
    }

    public void setRemainTimes(Integer remainTimes) {
        this.remainTimes = remainTimes;
    }
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }
    public Integer getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }
    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }
    public LocalDateTime getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(LocalDateTime distributeTime) {
        this.distributeTime = distributeTime;
    }
    public LocalDateTime getCouponStartTime() {
        return couponStartTime;
    }

    public void setCouponStartTime(LocalDateTime couponStartTime) {
        this.couponStartTime = couponStartTime;
    }
    public LocalDateTime getCouponEndTime() {
        return couponEndTime;
    }

    public void setCouponEndTime(LocalDateTime couponEndTime) {
        this.couponEndTime = couponEndTime;
    }
    public LocalDateTime getCouponCreateTime() {
        return couponCreateTime;
    }

    public void setCouponCreateTime(LocalDateTime couponCreateTime) {
        this.couponCreateTime = couponCreateTime;
    }
    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }

    @Override
    public String toString() {
        return "FmbUserCoupon{" +
            "couponSn=" + couponSn +
            ", couponId=" + couponId +
            ", couponMoney=" + couponMoney +
            ", minAmount=" + minAmount +
            ", discountRate=" + discountRate +
            ", uid=" + uid +
            ", orderSn=" + orderSn +
            ", fromOrderSn=" + fromOrderSn +
            ", remainTimes=" + remainTimes +
            ", source=" + source +
            ", adminUid=" + adminUid +
            ", sendStatus=" + sendStatus +
            ", useStatus=" + useStatus +
            ", useTime=" + useTime +
            ", distributeTime=" + distributeTime +
            ", couponStartTime=" + couponStartTime +
            ", couponEndTime=" + couponEndTime +
            ", couponCreateTime=" + couponCreateTime +
            ", mid=" + mid +
        "}";
    }
}
