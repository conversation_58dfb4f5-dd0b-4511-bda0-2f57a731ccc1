package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbUserCoupon;
import com.fmb.server2022.fmbx.mapper.FmbUserCouponMapper;
import com.fmb.server2022.fmbx.service.IFmbUserCouponService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 优惠券生成码表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class FmbUserCouponServiceImpl extends ServiceImpl<FmbUserCouponMapper, FmbUserCoupon> implements IFmbUserCouponService {

}
