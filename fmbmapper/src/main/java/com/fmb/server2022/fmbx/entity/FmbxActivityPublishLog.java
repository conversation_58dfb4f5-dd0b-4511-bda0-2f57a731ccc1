package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 活动上下架操作记录
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_activity_publish_log")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxActivityPublishLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "publish_log_id", type = IdType.AUTO)
    private Integer publishLogId;

    /**
     * 活动id
     */
    private Integer xaid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 操作结果
     */
    private String result;

    /**
     * 操作类型:1-上架,2-下架
     */
    private Integer opType;

    /**
     * 创建人的uid
     */
    private Integer createUid;

    /**
     * 创建人的名字(冗余)
     */
    private String createUidName;

    public Integer getPublishLogId() {
        return publishLogId;
    }

    public void setPublishLogId(Integer publishLogId) {
        this.publishLogId = publishLogId;
    }
    public Integer getXaid() {
        return xaid;
    }

    public void setXaid(Integer xaid) {
        this.xaid = xaid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
    public Integer getOpType() {
        return opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }
    public Integer getCreateUid() {
        return createUid;
    }

    public void setCreateUid(Integer createUid) {
        this.createUid = createUid;
    }
    public String getCreateUidName() {
        return createUidName;
    }

    public void setCreateUidName(String createUidName) {
        this.createUidName = createUidName;
    }

    @Override
    public String toString() {
        return "FmbxActivityPublishLog{" +
            "publishLogId=" + publishLogId +
            ", xaid=" + xaid +
            ", ctime=" + ctime +
            ", result=" + result +
            ", opType=" + opType +
            ", createUid=" + createUid +
            ", createUidName=" + createUidName +
        "}";
    }
}
