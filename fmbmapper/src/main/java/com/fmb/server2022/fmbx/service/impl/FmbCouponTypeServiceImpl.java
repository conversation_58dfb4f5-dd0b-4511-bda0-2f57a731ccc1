package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbCouponType;
import com.fmb.server2022.fmbx.mapper.FmbCouponTypeMapper;
import com.fmb.server2022.fmbx.service.IFmbCouponTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 优惠券主表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class FmbCouponTypeServiceImpl extends ServiceImpl<FmbCouponTypeMapper, FmbCouponType> implements IFmbCouponTypeService {

}
