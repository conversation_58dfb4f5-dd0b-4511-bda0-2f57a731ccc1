package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderHotel;
import com.fmb.server2022.fmbx.mapper.FmbOrderHotelMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderHotelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店预订表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-24
 */
@Service
public class FmbOrderHotelServiceImpl extends ServiceImpl<FmbOrderHotelMapper, FmbOrderHotel> implements IFmbOrderHotelService {

}
