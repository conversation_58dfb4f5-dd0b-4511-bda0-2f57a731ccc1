package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderGoodsExtend;
import com.fmb.server2022.fmbx.mapper.FmbOrderGoodsExtendMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsExtendService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 订单order_goods扩展表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class FmbOrderGoodsExtendServiceImpl extends ServiceImpl<FmbOrderGoodsExtendMapper, FmbOrderGoodsExtend> implements IFmbOrderGoodsExtendService {

}
