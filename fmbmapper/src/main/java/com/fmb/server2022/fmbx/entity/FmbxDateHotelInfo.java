package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 按日期存储酒店日历房信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_date_hotel_info")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxDateHotelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @TableId
    private LocalDate datecol;

    /**
     * 活动#skuid,多个以逗号分隔
     */
    private String activitySkuInfo;

    public LocalDate getDatecol() {
        return datecol;
    }

    public void setDatecol(LocalDate datecol) {
        this.datecol = datecol;
    }
    public String getActivitySkuInfo() {
        return activitySkuInfo;
    }

    public void setActivitySkuInfo(String activitySkuInfo) {
        this.activitySkuInfo = activitySkuInfo;
    }

    @Override
    public String toString() {
        return "FmbxDateHotelInfo{" +
            "datecol=" + datecol +
            ", activitySkuInfo=" + activitySkuInfo +
        "}";
    }
}
