package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 订单取消表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_order_cancel")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbOrderCancel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 1,超时关闭，2，退货完成，3，我不想买了，4，重复下单，5，信息有误新下单，6，无法满足特别需求，7，预订流程不方便、8，支付流程不方便、9，价格原因、10，选择其他途径购买、11，其他，12，改签
     */
    private Integer cancelReason;

    /**
     * 取消订单详细说明
     */
    private String cancelDetail;

    /**
     * 后台操作用户UID，前台用户-1，系统0
     */
    private Integer adminUid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public Integer getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(Integer cancelReason) {
        this.cancelReason = cancelReason;
    }
    public String getCancelDetail() {
        return cancelDetail;
    }

    public void setCancelDetail(String cancelDetail) {
        this.cancelDetail = cancelDetail;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "FmbOrderCancel{" +
            "id=" + id +
            ", orderSn=" + orderSn +
            ", cancelReason=" + cancelReason +
            ", cancelDetail=" + cancelDetail +
            ", adminUid=" + adminUid +
            ", ctime=" + ctime +
        "}";
    }
}
