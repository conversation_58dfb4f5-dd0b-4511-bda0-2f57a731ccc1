package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 酒店套餐和房型绑定,相当于票种表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_room_sku")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuiteRoomSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.AUTO)
    private Integer skuId;

    /**
     * 套餐加房型名称
     */
    private String skuName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 套餐id
     */
    private Integer suiteId;

    /**
     * 商户房型id
     */
    private Integer roomId;

    /**
     * 售买状态:0-无效（无可售日期）,1-有效
     */
    private Integer flagSell;

    /**
     * 上下架状态:0-已删除,1-已上架,2-已下架
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 最后加价操作id
     */
    private Integer lastAddpriceOperateId;

    /**
     * 最后加价操作id
     */
    private Integer lastFullpriceOperateId;

    /**
     * 排序值,值越大越靠前
     */
    private Integer sortValue;

    /**
     * 销售量
     */
    private Integer sellNum;

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }
    public Integer getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(Integer suiteId) {
        this.suiteId = suiteId;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public Integer getFlagSell() {
        return flagSell;
    }

    public void setFlagSell(Integer flagSell) {
        this.flagSell = flagSell;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(Integer versionNum) {
        this.versionNum = versionNum;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public Integer getLastAddpriceOperateId() {
        return lastAddpriceOperateId;
    }

    public void setLastAddpriceOperateId(Integer lastAddpriceOperateId) {
        this.lastAddpriceOperateId = lastAddpriceOperateId;
    }
    public Integer getLastFullpriceOperateId() {
        return lastFullpriceOperateId;
    }

    public void setLastFullpriceOperateId(Integer lastFullpriceOperateId) {
        this.lastFullpriceOperateId = lastFullpriceOperateId;
    }
    public Integer getSortValue() {
        return sortValue;
    }

    public void setSortValue(Integer sortValue) {
        this.sortValue = sortValue;
    }
    public Integer getSellNum() {
        return sellNum;
    }

    public void setSellNum(Integer sellNum) {
        this.sellNum = sellNum;
    }

    @Override
    public String toString() {
        return "FmbxSuiteRoomSku{" +
            "skuId=" + skuId +
            ", skuName=" + skuName +
            ", roomName=" + roomName +
            ", suiteId=" + suiteId +
            ", roomId=" + roomId +
            ", flagSell=" + flagSell +
            ", status=" + status +
            ", versionNum=" + versionNum +
            ", ctime=" + ctime +
            ", utime=" + utime +
            ", lastAddpriceOperateId=" + lastAddpriceOperateId +
            ", lastFullpriceOperateId=" + lastFullpriceOperateId +
            ", sortValue=" + sortValue +
            ", sellNum=" + sellNum +
        "}";
    }
}
