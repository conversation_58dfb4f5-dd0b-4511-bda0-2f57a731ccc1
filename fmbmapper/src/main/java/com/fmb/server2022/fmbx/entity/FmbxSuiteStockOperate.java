package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 套餐修改日志
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_suite_stock_operate")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxSuiteStockOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "stock_operate_id", type = IdType.AUTO)
    private Integer stockOperateId;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 商户名称(冗余)
     */
    private String bpsName;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称(冗余)
     */
    private String roomName;

    /**
     * 操作数据(json格式)
     */
    private String operateData;

    /**
     * 修改类型:1-连续日期,2-指定日期
     */
    private Integer changeType;

    /**
     * 后台用户类型:1-父母邦后台,2-供应商后台
     */
    private Integer adminType;

    /**
     * 用户uid
     */
    private Integer adminUid;

    /**
     * 后台用户名字
     */
    private String adminName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 房态变更类型:0-不变,1-打开,2-关闭
     */
    private Integer statusChangeType;

    public Integer getStockOperateId() {
        return stockOperateId;
    }

    public void setStockOperateId(Integer stockOperateId) {
        this.stockOperateId = stockOperateId;
    }
    public Integer getBpsId() {
        return bpsId;
    }

    public void setBpsId(Integer bpsId) {
        this.bpsId = bpsId;
    }
    public String getBpsName() {
        return bpsName;
    }

    public void setBpsName(String bpsName) {
        this.bpsName = bpsName;
    }
    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }
    public String getOperateData() {
        return operateData;
    }

    public void setOperateData(String operateData) {
        this.operateData = operateData;
    }
    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }
    public Integer getAdminType() {
        return adminType;
    }

    public void setAdminType(Integer adminType) {
        this.adminType = adminType;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getStatusChangeType() {
        return statusChangeType;
    }

    public void setStatusChangeType(Integer statusChangeType) {
        this.statusChangeType = statusChangeType;
    }

    @Override
    public String toString() {
        return "FmbxSuiteStockOperate{" +
            "stockOperateId=" + stockOperateId +
            ", bpsId=" + bpsId +
            ", bpsName=" + bpsName +
            ", roomId=" + roomId +
            ", roomName=" + roomName +
            ", operateData=" + operateData +
            ", changeType=" + changeType +
            ", adminType=" + adminType +
            ", adminUid=" + adminUid +
            ", adminName=" + adminName +
            ", ctime=" + ctime +
            ", statusChangeType=" + statusChangeType +
        "}";
    }
}
