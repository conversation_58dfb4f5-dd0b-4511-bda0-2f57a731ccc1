package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 节假日信息
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmbx_date_table")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbxDateTable implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate datecol;

    /**
     * 星期几:1-星期1,2-星期2,3-星期3,4-星期4,5-星期5,6-星期6,7-星期日
     */
    private Integer weeknum;

    /**
     * 节日类型:0-非节日,1-阳历假日,2-农历假日
     */
    private Integer holidayType;

    /**
     * 节假日信息
     */
    private String holiday;

    /**
     * 年和月份
     */
    private String yearMonthStr;

    public LocalDate getDatecol() {
        return datecol;
    }

    public void setDatecol(LocalDate datecol) {
        this.datecol = datecol;
    }
    public Integer getWeeknum() {
        return weeknum;
    }

    public void setWeeknum(Integer weeknum) {
        this.weeknum = weeknum;
    }
    public Integer getHolidayType() {
        return holidayType;
    }

    public void setHolidayType(Integer holidayType) {
        this.holidayType = holidayType;
    }
    public String getHoliday() {
        return holiday;
    }

    public void setHoliday(String holiday) {
        this.holiday = holiday;
    }
    public String getYearMonthStr() {
        return yearMonthStr;
    }

    public void setYearMonthStr(String yearMonthStr) {
        this.yearMonthStr = yearMonthStr;
    }

    @Override
    public String toString() {
        return "FmbxDateTable{" +
            "datecol=" + datecol +
            ", weeknum=" + weeknum +
            ", holidayType=" + holidayType +
            ", holiday=" + holiday +
            ", yearMonthStr=" + yearMonthStr +
        "}";
    }
}
