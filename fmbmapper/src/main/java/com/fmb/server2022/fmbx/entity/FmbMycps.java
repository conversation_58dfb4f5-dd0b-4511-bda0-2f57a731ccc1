package com.fmb.server2022.fmbx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 分销商信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_mycps")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbMycps implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "mid", type = IdType.AUTO)
    private Integer mid;

    /**
     * 分销商名称
     */
    private String mname;

    /**
     * 状态：0：未审核，1：正常，2：禁用
     */
    private Integer status;

    /**
     * 分销类型：1:B端(机构)，2:C端(个人)
     */
    private Integer mType;

    /**
     * 分销模式：1:返佣模式，2:分销价模式
     */
    private Integer mMode;

    /**
     * 分销商等级
     */
    private Integer mLevel;

    /**
     * 账户类型：1:普通账户(无账户)2:预付款账户;3:信用账户
     */
    private Integer accountType;

    /**
     * 额度提醒
     */
    private Integer minNotice;

    /**
     * 分销商所属分部
     */
    private Integer cityId;

    /**
     * 我司市场对接人(后台uid)
     */
    private Integer marketUid;

    /**
     * 分销商对接人
     */
    private String agencyName;

    /**
     * 分销商对接人手机
     */
    private String agencyMobile;

    /**
     * 分销商对接人邮箱
     */
    private String agencyEmail;

    /**
     * 分销渠道(多种类型逗号分隔保存)：weixin:微信朋友圈/gongzhonghao:公众号/weibo:微博/toutiao:头条/zhibo:直播/boke:博客
     */
    private String channel;

    /**
     * 分销商财务对接人
     */
    private String financeName;

    /**
     * 分销商财务对接人手机
     */
    private String financeMobile;

    /**
     * 分销商财务对接人邮箱
     */
    private String financeEmail;

    /**
     * 结算方式(alipay:支付宝/public_bank:对公银行/private_bank:对私银行)
     */
    private String settleMode;

    /**
     * 银行名:ICBC:工行/ABC:农行/BOC:中行/CBC:建行/CMB:招行/CTB:交通……
     */
    private String bankName;

    /**
     * 开户行
     */
    private String depositBank;

    /**
     * 账户名称(结算方式为支付宝时，此字段为支付宝名称)
     */
    private String accountName;

    /**
     * 账户账号(结算方式为支付宝时，此字段为支付宝帐号)
     */
    private String accountNumber;

    /**
     * 提现门槛
     */
    private Integer thresholdMoney;

    /**
     * 后台编辑用户ID
     */
    private Integer adminUid;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 结算类型：1:每月
     */
    private Integer settleType;

    /**
     * 结算日期(多日期逗号分隔)
     */
    private String settleDates;

    /**
     * 结算提醒：0:屏蔽(不提醒)，1:开启(提醒)
     */
    private Integer settleNotice;

    /**
     * 预付款余额
     */
    private BigDecimal prepayBalance;

    /**
     * 可用信用额度
     */
    private BigDecimal creditBalance;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 备注信息
     */
    private String note;

    /**
     * 身份证背面图片ID
     */
    private String cardReverse;

    /**
     * 身份证正面图片ID
     */
    private String cardFront;

    /**
     * 邀请人的邀请码
     */
    private String inviteCode;

    /**
     * 邀请人mid
     */
    private Integer inviteMid;

    /**
     * 我的邀请码
     */
    private String code;

    /**
     * 邀请人uid
     */
    private Integer inviteUid;

    public Integer getMid() {
        return mid;
    }

    public void setMid(Integer mid) {
        this.mid = mid;
    }
    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Integer getmType() {
        return mType;
    }

    public void setmType(Integer mType) {
        this.mType = mType;
    }
    public Integer getmMode() {
        return mMode;
    }

    public void setmMode(Integer mMode) {
        this.mMode = mMode;
    }
    public Integer getmLevel() {
        return mLevel;
    }

    public void setmLevel(Integer mLevel) {
        this.mLevel = mLevel;
    }
    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }
    public Integer getMinNotice() {
        return minNotice;
    }

    public void setMinNotice(Integer minNotice) {
        this.minNotice = minNotice;
    }
    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    public Integer getMarketUid() {
        return marketUid;
    }

    public void setMarketUid(Integer marketUid) {
        this.marketUid = marketUid;
    }
    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }
    public String getAgencyMobile() {
        return agencyMobile;
    }

    public void setAgencyMobile(String agencyMobile) {
        this.agencyMobile = agencyMobile;
    }
    public String getAgencyEmail() {
        return agencyEmail;
    }

    public void setAgencyEmail(String agencyEmail) {
        this.agencyEmail = agencyEmail;
    }
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getFinanceName() {
        return financeName;
    }

    public void setFinanceName(String financeName) {
        this.financeName = financeName;
    }
    public String getFinanceMobile() {
        return financeMobile;
    }

    public void setFinanceMobile(String financeMobile) {
        this.financeMobile = financeMobile;
    }
    public String getFinanceEmail() {
        return financeEmail;
    }

    public void setFinanceEmail(String financeEmail) {
        this.financeEmail = financeEmail;
    }
    public String getSettleMode() {
        return settleMode;
    }

    public void setSettleMode(String settleMode) {
        this.settleMode = settleMode;
    }
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    public String getDepositBank() {
        return depositBank;
    }

    public void setDepositBank(String depositBank) {
        this.depositBank = depositBank;
    }
    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
    public Integer getThresholdMoney() {
        return thresholdMoney;
    }

    public void setThresholdMoney(Integer thresholdMoney) {
        this.thresholdMoney = thresholdMoney;
    }
    public Integer getAdminUid() {
        return adminUid;
    }

    public void setAdminUid(Integer adminUid) {
        this.adminUid = adminUid;
    }
    public LocalDateTime getUtime() {
        return utime;
    }

    public void setUtime(LocalDateTime utime) {
        this.utime = utime;
    }
    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }
    public Integer getSettleType() {
        return settleType;
    }

    public void setSettleType(Integer settleType) {
        this.settleType = settleType;
    }
    public String getSettleDates() {
        return settleDates;
    }

    public void setSettleDates(String settleDates) {
        this.settleDates = settleDates;
    }
    public Integer getSettleNotice() {
        return settleNotice;
    }

    public void setSettleNotice(Integer settleNotice) {
        this.settleNotice = settleNotice;
    }
    public BigDecimal getPrepayBalance() {
        return prepayBalance;
    }

    public void setPrepayBalance(BigDecimal prepayBalance) {
        this.prepayBalance = prepayBalance;
    }
    public BigDecimal getCreditBalance() {
        return creditBalance;
    }

    public void setCreditBalance(BigDecimal creditBalance) {
        this.creditBalance = creditBalance;
    }
    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    public String getCardReverse() {
        return cardReverse;
    }

    public void setCardReverse(String cardReverse) {
        this.cardReverse = cardReverse;
    }
    public String getCardFront() {
        return cardFront;
    }

    public void setCardFront(String cardFront) {
        this.cardFront = cardFront;
    }
    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }
    public Integer getInviteMid() {
        return inviteMid;
    }

    public void setInviteMid(Integer inviteMid) {
        this.inviteMid = inviteMid;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public Integer getInviteUid() {
        return inviteUid;
    }

    public void setInviteUid(Integer inviteUid) {
        this.inviteUid = inviteUid;
    }

    @Override
    public String toString() {
        return "FmbMycps{" +
            "mid=" + mid +
            ", mname=" + mname +
            ", status=" + status +
            ", mType=" + mType +
            ", mMode=" + mMode +
            ", mLevel=" + mLevel +
            ", accountType=" + accountType +
            ", minNotice=" + minNotice +
            ", cityId=" + cityId +
            ", marketUid=" + marketUid +
            ", agencyName=" + agencyName +
            ", agencyMobile=" + agencyMobile +
            ", agencyEmail=" + agencyEmail +
            ", channel=" + channel +
            ", financeName=" + financeName +
            ", financeMobile=" + financeMobile +
            ", financeEmail=" + financeEmail +
            ", settleMode=" + settleMode +
            ", bankName=" + bankName +
            ", depositBank=" + depositBank +
            ", accountName=" + accountName +
            ", accountNumber=" + accountNumber +
            ", thresholdMoney=" + thresholdMoney +
            ", adminUid=" + adminUid +
            ", utime=" + utime +
            ", ctime=" + ctime +
            ", settleType=" + settleType +
            ", settleDates=" + settleDates +
            ", settleNotice=" + settleNotice +
            ", prepayBalance=" + prepayBalance +
            ", creditBalance=" + creditBalance +
            ", deposit=" + deposit +
            ", note=" + note +
            ", cardReverse=" + cardReverse +
            ", cardFront=" + cardFront +
            ", inviteCode=" + inviteCode +
            ", inviteMid=" + inviteMid +
            ", code=" + code +
            ", inviteUid=" + inviteUid +
        "}";
    }
}
