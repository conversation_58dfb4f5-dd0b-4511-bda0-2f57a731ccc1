package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbShopInterfacePerson;
import com.fmb.server2022.fmbx.mapper.FmbShopInterfacePersonMapper;
import com.fmb.server2022.fmbx.service.IFmbShopInterfacePersonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 对接人信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-07-26
 */
@Service
public class FmbShopInterfacePersonServiceImpl extends ServiceImpl<FmbShopInterfacePersonMapper, FmbShopInterfacePerson> implements IFmbShopInterfacePersonService {

}
