package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderExtInfo;
import com.fmb.server2022.fmbx.mapper.FmbOrderExtInfoMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderExtInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 *  服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-03-28
 */
@Service
public class FmbOrderExtInfoServiceImpl extends ServiceImpl<FmbOrderExtInfoMapper, FmbOrderExtInfo> implements IFmbOrderExtInfoService {

}
