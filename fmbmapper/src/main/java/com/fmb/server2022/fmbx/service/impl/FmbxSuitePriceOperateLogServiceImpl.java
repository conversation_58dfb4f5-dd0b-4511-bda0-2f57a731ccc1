package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbxSuitePriceOperateLog;
import com.fmb.server2022.fmbx.mapper.FmbxSuitePriceOperateLogMapper;
import com.fmb.server2022.fmbx.service.IFmbxSuitePriceOperateLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * sku具体到天的价格修改日志 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-14
 */
@Service
public class FmbxSuitePriceOperateLogServiceImpl extends ServiceImpl<FmbxSuitePriceOperateLogMapper, FmbxSuitePriceOperateLog> implements IFmbxSuitePriceOperateLogService {

}
