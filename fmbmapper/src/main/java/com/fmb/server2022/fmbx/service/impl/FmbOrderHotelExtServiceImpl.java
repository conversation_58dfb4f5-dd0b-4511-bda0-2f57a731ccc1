package com.fmb.server2022.fmbx.service.impl;

import com.fmb.server2022.fmbx.entity.FmbOrderHotelExt;
import com.fmb.server2022.fmbx.mapper.FmbOrderHotelExtMapper;
import com.fmb.server2022.fmbx.service.IFmbOrderHotelExtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 酒店订单扩展信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-01-10
 */
@Service
public class FmbOrderHotelExtServiceImpl extends ServiceImpl<FmbOrderHotelExtMapper, FmbOrderHotelExt> implements IFmbOrderHotelExtService {

}
