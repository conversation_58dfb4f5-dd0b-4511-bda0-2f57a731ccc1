package com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import com.fasterxml.jackson.databind.annotation.* ;

import com.fasterxml.jackson.annotation.JsonInclude ;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@TableName("jishigou_common_district")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JishigouCommonDistrict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String name;

    /**
     * 拼音
     */
    private String pinyinName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 从属于
     */
    private Integer upid;

    /**
     * 排序
     */
    private Integer list;

    /**
     * 地区编号
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getPinyinName() {
        return pinyinName;
    }

    public void setPinyinName(String pinyinName) {
        this.pinyinName = pinyinName;
    }
    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
    public Integer getUpid() {
        return upid;
    }

    public void setUpid(Integer upid) {
        this.upid = upid;
    }
    public Integer getList() {
        return list;
    }

    public void setList(Integer list) {
        this.list = list;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        return "JishigouCommonDistrict{" +
            "id=" + id +
            ", name=" + name +
            ", pinyinName=" + pinyinName +
            ", englishName=" + englishName +
            ", level=" + level +
            ", upid=" + upid +
            ", list=" + list +
            ", code=" + code +
            ", sort=" + sort +
        "}";
    }
}
