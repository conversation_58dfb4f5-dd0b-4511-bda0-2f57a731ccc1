package com.fmb.server2022.fmb_new.service.impl;

import com.fmb.server2022.fmb_new.entity.FmbActivityTagList;
import com.fmb.server2022.fmb_new.mapper.FmbActivityTagListMapper;
import com.fmb.server2022.fmb_new.service.IFmbActivityTagListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 *  服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-03
 */
@Service
public class FmbActivityTagListServiceImpl extends ServiceImpl<FmbActivityTagListMapper, FmbActivityTagList> implements IFmbActivityTagListService {

}
