package com.fmb.server2022.fmb_new.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 后台用户信息表
 * </p>
 *
 * <AUTHOR>
 */
@TableName("fmb_admin")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FmbAdmin implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "uid", type = IdType.AUTO)

    private Integer uid;


    private String name;

    /**
     * 真实姓名
     */

    private String realname;

    /**
     * 邮箱地址
     */

    private String email;

    /**
     * 电话
     */

    private String phone;

    /**
     * 职位
     */

    private String offer;

    /**
     * 是否禁用
     */

    private Integer disabled;


    private String password;


    private String salt;


    private String token;


    private Integer tokenExpires;

    /**
     * 密码过期时间点（默认半年内过期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime expireTime;


    private String role;

    /**
     * 1,上海。2，全国
     */

    private Integer area;

    /**
     * 部门ID，此用户属于哪个部门
     */

    private Integer departmentId;


    /**
     * 'base地 用来 标记这个用户的办公城市 0未知 1北京 2上海',
     */
    private Integer baseCityId;



    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOffer() {
        return offer;
    }

    public void setOffer(String offer) {
        this.offer = offer;
    }

    public Integer getDisabled() {
        return disabled;
    }

    public void setDisabled(Integer disabled) {
        this.disabled = disabled;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getTokenExpires() {
        return tokenExpires;
    }

    public void setTokenExpires(Integer tokenExpires) {
        this.tokenExpires = tokenExpires;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getBaseCityId() {
        return baseCityId;
    }

    public void setBaseCityId(Integer baseCityId) {
        this.baseCityId = baseCityId;
    }

    @Override
    public String toString() {
        return "FmbAdmin{" +
                "uid=" + uid +
                ", name=" + name +
                ", realname=" + realname +
                ", email=" + email +
                ", phone=" + phone +
                ", offer=" + offer +
                ", disabled=" + disabled +
                ", password=" + password +
                ", salt=" + salt +
                ", token=" + token +
                ", tokenExpires=" + tokenExpires +
                ", expireTime=" + expireTime +
                ", role=" + role +
                ", area=" + area +
                ", departmentId=" + departmentId +
                ", baseCityId=" + baseCityId +
                "}";
    }
}
