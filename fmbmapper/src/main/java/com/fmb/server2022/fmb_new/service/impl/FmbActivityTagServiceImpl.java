package com.fmb.server2022.fmb_new.service.impl;

import com.fmb.server2022.fmb_new.entity.FmbActivityTag;
import com.fmb.server2022.fmb_new.mapper.FmbActivityTagMapper;
import com.fmb.server2022.fmb_new.service.IFmbActivityTagService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <p>
 * 活动标签表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-04-03
 */
@Service
public class FmbActivityTagServiceImpl extends ServiceImpl<FmbActivityTagMapper, FmbActivityTag> implements IFmbActivityTagService {

}
