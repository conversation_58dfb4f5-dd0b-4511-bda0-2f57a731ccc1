#!/bin/sh
source ~/.bash_profile


nowdatestr=`date "+%Y%m%d_%H%M%S"`

FMBX_RUN_PATH=/home/<USER>/javaproject

FMBX_BASE_DIR=$FMBX_RUN_PATH/fmbx
FMBX_JAR_FILE=server2022-1.0.jar
JAR_FILE_RUN=server2022_${nowdatestr}.jar

SHUTDOWN_CODE=0ff425b4a15d7809839dd9d35e8b3298
FMBXRUN_EVN=dev

RSYNC_NGINGX_IP=**************

#启动新服务
function startFmbx(){


nohup java -Dlog4j2.formatMsgNoLookups=true -jar $FMBX_RUN_PATH/${JAR_FILE_RUN} --server.port=$1 --fmb.shutdowncode=${SHUTDOWN_CODE} --spring.profiles.active=${FMBXRUN_EVN} > $FMBX_RUN_PATH/server2022_$1.log 2>&1 &


}


function checkStartResult() {

#循环检查应用是否启动成功  因为启动需要时间
for ((integer = 1; integer <= 15; integer++))
do
    echo "$integer  check start up $1"

    rs=`curl --connect-timeout 1  http://127.0.0.1:$1/fmbx/dbtest01?name=test_start_ok_fmbx  `

    checktag="test_start_ok_fmbx"
    if [[ $rs =~ $checktag ]]
    then
        echo "$rs" ;
        echo "$1 start check ok"
curl -X PUT "http://${RSYNC_NGINGX_IP}:8500/v1/kv/upstreams/fmbx_api/${RSYNC_NGINGX_IP}:$1"
        break ;
    fi
    sleep 2

done


#检查端口启动是否成功
checkUp="`/usr/sbin/lsof -i:$1 | grep LISTEN | wc -l`"
if [ ${checkUp} == "1" ]; then
    # 成功启动数量加1
    return 1
fi

    return 0

}

# 停止服务
function stopFmbx(){


#先从nginx 组里把应用摘下来
curl  -X DELETE "http://${RSYNC_NGINGX_IP}:8500/v1/kv/upstreams/fmbx_api/${RSYNC_NGINGX_IP}:$1"
#调用接口 停止所有的kafka消费者
rs=`curl --connect-timeout 5  http://127.0.0.1:$1/fmbx/stopAllKafkaConsumer/${SHUTDOWN_CODE}`

    checktag2="stopAllKafkaConsumer"
    if [[ $rs =~ $checktag2 ]]
    then
        echo "$1 stop kafka consumer ok"
        break ;
    fi
#休眠6秒 等待可能耗时比较长的kafka异步任务完成
sleep 10

PID="`ps -ef | grep server2022  | grep -v 'grep' | grep $1 | awk '{print $2}'`"
if [ "$PID" = "" ]
then
   echo "No port=$1 server2022 pid alive!"
   break ;
else
{
   kill  $PID
   echo "KILL $PID  ,port=$1"
}

echo "stop port $1 over"

done

}


#cd ${FMBX_BASE_DIR}
#mvn clean install  -Dmaven.test.skip=true

cd ${FMBX_BASE_DIR}
mvn clean install  -Dmaven.test.skip=true

#cd ${FMBX_BASE_DIR}/server2022
#mvn clean package -Dmaven.test.skip=true
cp ${FMBX_BASE_DIR}/server2022/target/${FMBX_JAR_FILE} $FMBX_RUN_PATH/${JAR_FILE_RUN}


# 一共4个端口
jports=("8070" "8071" "8072" "8073")
# 记录对应jports 端口现在是否有服务
s=("0" "0" "0" "0")

startPort=("" "")
#启动计数
nstart=0
#检查是否启动成功
nstart_ok=0

for i in ${!jports[@]}
do

    #记录哪些端口已经启用
    s[$i]="`/usr/sbin/lsof -i:${jports[$i]} | grep LISTEN | wc -l`"
    if  [ ${s[$i]} == "1" ]
    then
        echo "${jports[$i]}  exist service"
    else

        # 最多启动2个
        if [ $nstart -lt 2 ]
        then
            #这里可以进行启动操作
            startFmbx ${jports[$i]}

            #记录启动端口
            startPort[$nstart]=${jports[$i]}
            let    nstart++

        fi
    fi
done

#检查端启动情况
for i in ${!startPort[@]}
do
    checkStartResult ${startPort[$i]}
    if [ $? == "1" ]; then
                # 成功启动数量加1
                let nstart_ok++
    fi
done

# 如果成功启动数量大于1
if [ ${nstart_ok} -gt 0 ]; then
       #循环停止之前发现的应用
    for i in ${!s[@]}
        do
        if  [ ${s[$i]} == "1" ]; then
            echo "stop portaim=${jports[$i]}"
            stopFmbx ${jports[$i]}
        fi
    done
fi
