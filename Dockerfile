FROM openjdk:8-jdk-alpine

# 安装必要工具
RUN apk add --no-cache curl bash

# 设置工作目录
WORKDIR /app

# 设置Java环境变量
ENV JAVA_HOME=/usr/lib/jvm/java-1.8-openjdk
ENV PATH=$PATH:$JAVA_HOME/bin

# 先复制pom文件并下载依赖（利用Docker缓存）
COPY pom.xml .
COPY common/pom.xml ./common/
COPY fmbmapper/pom.xml ./fmbmapper/
COPY server2022/pom.xml ./server2022/

# 安装Maven
ENV MAVEN_VERSION 3.6.3
RUN mkdir -p /usr/share/maven /usr/share/maven/ref \
  && curl -fsSL -o /tmp/apache-maven.tar.gz https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz \
  && tar -xzf /tmp/apache-maven.tar.gz -C /usr/share/maven --strip-components=1 \
  && rm -f /tmp/apache-maven.tar.gz \
  && ln -s /usr/share/maven/bin/mvn /usr/bin/mvn

# 下载依赖
RUN mvn dependency:go-offline -B

# 复制源代码
COPY . .

# 编译项目（跳过测试）
RUN mvn clean package -DskipTests

# 暴露端口
EXPOSE 8070

# 启动应用
CMD ["java", "-jar", "/app/server2022/target/server2022-1.0.jar"]