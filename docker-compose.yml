version: '3.8'

services:
  # Java应用
  fmb-java:
    build: .
    ports:
      - "8070:8070"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_DYNAMIC_DATASOURCE_MASTER_URL=***********************************************************************************************************************************************************
      - SPRING_DATASOURCE_DYNAMIC_DATASOURCE_SLAVE_URL=***********************************************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=
    depends_on:
      - mysql
      - redis
    networks:
      - fmb-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: fmb_new
      MYSQL_USER: fmb
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - fmb-network

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fmb-network

volumes:
  mysql_data:
  redis_data:

networks:
  fmb-network:
    driver: bridge
