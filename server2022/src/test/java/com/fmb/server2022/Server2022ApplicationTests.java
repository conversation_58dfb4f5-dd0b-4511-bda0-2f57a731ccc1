package com.fmb.server2022;

import com.fmb.util.RegStringUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.File;
import java.io.IOException;
import java.util.List;

class Server2022ApplicationTests {

    @Test
    void spelTest() {


        ExpressionParser parser = new SpelExpressionParser();
// 定义解析表达式
        Expression expression = parser.parseExpression("#result == null or #result=='' ");
// 构造上下文
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("result", "1");
// 求值
        System.out.println(expression.getValue(context));

    }



    @Test
    public void excelColModify() {

        try {
            File inputFile = new File("/Users/<USER>/workspace/fmbx/server2022/src/main/java/com/fmb/server2022/domain/HotelPriceChangeDataDomain.java");
            List<String> strings = FileUtils.readLines(inputFile,"UTF-8");


            String startMofiy = "@ExcelProperty(value = \"套餐房型名字\",index = 2)" ;

            StringBuffer stringBuffer = new StringBuffer();
            boolean beginModify = false ;

            int nstart = 0 ;
            for (String line : strings) {


                String trim = line.trim();
                String endline = "\r\n";
                if (trim.startsWith("@ExcelProperty")) {
                    System.out.println(line);

                    if (trim.equals(startMofiy)) {
                        beginModify = true ;

                        String s = StringUtils.substringAfterLast(trim, "=");

                        String snum = RegStringUtil.regSlice(trim, "(index\\s*=\\s*\\d+)");
                        nstart = Integer.parseInt(StringUtils.substringAfterLast(snum,"=").trim()) ;

                    }
                    if (beginModify) {


                        String snum = RegStringUtil.regSlice(trim, "(index\\s*=\\s*\\d+)");

                        String s1 = StringUtils.substringBeforeLast(snum, "=");

                        String pre = StringUtils.substringBefore(line,snum);
                        String la = StringUtils.substringAfter(line,snum);

                        nstart ++ ;
                        stringBuffer.append(pre);
                        stringBuffer.append(s1);
                        stringBuffer.append("= ");
                        stringBuffer.append(nstart);
                        stringBuffer.append(la) ;

                        stringBuffer.append(endline) ;


                    }else {
                        stringBuffer.append(line) ;
                        stringBuffer.append(endline) ;
                    }



                }else {
                    stringBuffer.append(line) ;
                    stringBuffer.append(endline) ;
                }
            }

            System.out.println("-----------------");
            System.out.println(stringBuffer.toString());


            FileUtils.writeStringToFile(inputFile,stringBuffer.toString(),"UTF-8");


        } catch (IOException e) {
            e.printStackTrace();
        }

        //ExcelProperty

    }

}
