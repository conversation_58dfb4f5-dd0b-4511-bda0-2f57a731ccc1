package com.fmb.server2022;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.fmb.util.FmbDateUtil;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @Date: 2023/3/24 2:59 下午
 */
public class DateTest {

    @Test
    public void date01() {

//        LocalDate now = LocalDate.now() ;
//        System.out.println(now);
//        now = now.plus(2, ChronoUnit.DAYS) ;
//        System.out.println(now.toString());
//
//        final int dayDiff = FmbDateUtil.dayDiff("2023-04-12","2023-04-11") ;
//        System.out.println(dayDiff);

        System.out.println(new LocalDate("2023-08-04").minus(Days.days(1)).toString());

    }


}
