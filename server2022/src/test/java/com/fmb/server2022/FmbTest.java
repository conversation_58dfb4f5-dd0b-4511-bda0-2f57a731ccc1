package com.fmb.server2022;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;

import static com.fmb.basic.FmbConstants.PHP_MD5_SALT_KEY;

//@SpringBootTest
public class FmbTest {




    @Autowired
    JedisPool jedisPool ;


    @Test
    public void sign230804() {
        HashMap<String, Object> map = new HashMap<>();
        final long l = System.currentTimeMillis();
        map.put("mill", l);
        map.put("days", 5);

        final String sign = MD5Util.md5("" + l + PHP_MD5_SALT_KEY);
        map.put("sign", sign);


        map.put("aid", 212307);
        map.put("dateBegin", "2023-07-01");
        map.put("dateEnd", "2023-09-01");
        map.put("second", 3600);
        map.put("downExcel", 1);



        System.out.println(JSON.toJSONString(map,true));

    }


    @Test
    public void k01() {
        System.out.println((SnowflakeIdWorker.getInstance().nextId()+"").length());
    }

    @Test
    public void k02() {
        System.out.println(System.currentTimeMillis() - 3 * 60 * 1000);
    }


    @Test
    public void json01() {

        String jtxt = "{\n" +
                "    \"suite\": {\n" +
                "        \"autoCloseOrderSecond\": 1800,\n" +
                "        \"buyLimit\": 1,\n" +
                "        \"certFlag\": 1,\n" +
                "        \"certTip\": \"\",\n" +
                "        \"flagHotelReserve\": 1,\n" +
                "        \"flagRiskWarning\": 0,\n" +
                "        \"flagStandardHotel\": 1,\n" +
                "        \"isInvoice\": 1,\n" +
                "        \"isReturn\": 1,\n" +
                "        \"nightMin\": 1,\n" +
                "        \"preReserveDay\": 2,\n" +
                "        \"preReserveTime\": \"12:30\",\n" +
                "        \"returnPolicy\": \"\",\n" +
                "        \"returnValidDay\": 2,\n" +
                "        \"returnValidSecond\": 127800,\n" +
                "        \"returnValidTime\": \"12:30\",\n" +
                "        \"riskWarningTip\": \"\",\n" +
                "        \"suiteContent\": \"[{\\\"type\\\":\\\"食\\\",\\\"intro\\\":\\\"仅早餐\\\",\\\"content\\\":\\\"仅早餐\\\"},{\\\"type\\\":\\\"享\\\",\\\"intro\\\":\\\"免费泳池\\\",\\\"content\\\":\\\"免费泳池\\\"}]\",\n" +
                "        \"suiteId\": 855,\n" +
                "        \"totalNeight\": 1,\n" +
                "        \"versionNum\": 0,\n" +
                "        \"xaid\": 105761\n" +
                "    },\n" +
                "    \"skuDetail\": {\n" +
                "        \"fmbxSku\": {\n" +
                "            \"bpId\": 45,\n" +
                "            \"bpsId\": 245,\n" +
                "            \"skuId\": 359,\n" +
                "            \"skuName\": \"普通套餐房劵\",\n" +
                "            \"skuType\": 2,\n" +
                "            \"xaid\": 105761\n" +
                "        },\n" +
                "        \"goodsPrice\": 10,\n" +
                "        \"goodsPriceAll\": 20,\n" +
                "        \"marketPrice\": 20,\n" +
                "        \"marketPriceAll\": 40,\n" +
                "        \"reqGenSku\": {\n" +
                "            \"skuId\": 359,\n" +
                "            \"skuNumber\": 2\n" +
                "        },\n" +
                "        \"reserveSku\": {\n" +
                "            \"canDivide\": 1,\n" +
                "            \"eachOrderMaxNum\": 5,\n" +
                "            \"eachOrderMinNum\": 2,\n" +
                "            \"flagBuyLimit\": 1,\n" +
                "            \"flagSell\": 1,\n" +
                "            \"goodsPrice\": 10,\n" +
                "            \"isReturn\": 0,\n" +
                "            \"marketPrice\": 20,\n" +
                "            \"maxBuyAllNum\": 8,\n" +
                "            \"sellEndTime\": \"2023-02-28 00:00:00\",\n" +
                "            \"sellNum\": 49,\n" +
                "            \"sellStartTime\": \"2023-01-10 00:00:00\",\n" +
                "            \"settlePrice\": 10,\n" +
                "            \"skuId\": 359,\n" +
                "            \"skuName\": \"普通套餐房劵\",\n" +
                "            \"status\": 1,\n" +
                "            \"stockNum\": 80,\n" +
                "            \"suiteId\": 855,\n" +
                "            \"versionNum\": 22\n" +
                "        },\n" +
                "        \"settlePrice\": 10,\n" +
                "        \"settlePriceAll\": 20,\n" +
                "        \"skuNumber\": 2,\n" +
                "        \"skuType\": 2,\n" +
                "        \"suite\": {\n" +
                "            \"$ref\": \"$.suite\"\n" +
                "        }\n" +
                "    }\n" +
                "}" ;


            final SkuDetail skuDetail = JSONUtil.parse(jtxt).getByPath("skuDetail", SkuDetail.class);

        System.out.println(skuDetail);

//        final JSONObject jsonObject = JSON.parseObject(jtxt);
//
//        FmbxSuite suite =   jsonObject.getObject("suite",FmbxSuite.class) ;
//        SkuDetail skuDetail =   jsonObject.getObject("skuDetail",SkuDetail.class) ;


    }

    /**
     * 测试jedis 生成10万个codeid
     */
    @Test
    public void codeGentest() {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String setName = "codeset";

            //生成  1000*100个随机码 批量插入redis的集合
            for (int i = 0; i < 100; i++) {

                ArrayList<String> strings = new ArrayList<>();
                for (int j = 0; j < 1000; j++) {
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append( SnowflakeIdWorker.getInstance().nextId()) ;
                    strings.add( stringBuffer.reverse().subSequence(0, 10).toString());
                }

                Long codeset = jedis.sadd(setName, strings.toArray(new String[]{}));
            }

            Long scard = jedis.scard(setName);

            Assert.isTrue(scard.intValue()==100*1000,"没有重复的");
            System.out.println(scard);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }



}
