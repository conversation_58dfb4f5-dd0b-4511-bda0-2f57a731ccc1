<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FmbxBpDao" >
    <resultMap id="FmbxBpResultMap" type="com.fmb.server2022.domain.FmbxBpListDomain" >
        <id column="bp_id" property="bpId" jdbcType="INTEGER" />
        <collection column="{bpId=bp_id}" property="contractList" ofType="com.fmb.server2022.fmbx.entity.FmbxBpContract" select="getContractList"></collection>
        <collection column="{bpId=bp_id}" property="shopList" ofType="com.fmb.server2022.fmbx.entity.FmbxBps" select="getShopList"></collection>
    </resultMap>
    <resultMap id="FmbxBpContractResultMap" type="com.fmb.server2022.fmbx.entity.FmbxBpContract" >
        <id property="bpcId" column="bpc_id" jdbcType="INTEGER"/>
        <result property="bpcName" column="bpc_name" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="FmbxBpsResultMap" type="com.fmb.server2022.fmbx.entity.FmbxBps" >
        <id property="bpsId" column="bps_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getContractList"  resultMap="FmbxBpContractResultMap">
        select bpc_id,bpc_name
        from fmbx_bp_contract
        where  bp_id = #{bpId,jdbcType=INTEGER};
    </select>
    <select id="getShopList"  resultMap="FmbxBpsResultMap">
        select bps.bps_id,bps.bps_type,concat(bps.name, '(', if(bps.bpid_of_edit = bb.bp_id, '可编辑', '不可编辑'), ')') name
        from fmbx_bps bps,fmbx_bp_bps_bind bb
        where bps.bps_id = bb.bps_id and bb.bp_id = #{bpId,jdbcType=INTEGER};
    </select>
    <select id="selectFmbxBpList" resultMap="FmbxBpResultMap" parameterType="com.fmb.server2022.reqdomain.ReqFmbxBpList"  >
        select m.bp_id,m.account_name,m.parter_type,m.ctime,pi.partner_province,pi.partner_city,pi.partner_name,pi.partner_contacts_name,pi.partner_contacts_mobile,
        m.admin_uid,a.realname as admin_name, ac.realname as create_name,a.phone,s.login_stat_count,s.login_stat_lasttime,s.sale_order_count,s.sale_goods_count,s.sale_order_money,
        m.pid,m.account_type,m.account_status,m.check_status from fmbx_bp_main m
        LEFT JOIN fmbx_bp_parter_info pi on m.bp_id=pi.bp_id
        LEFT JOIN fmbx_bp_stat s on m.bp_id=s.bp_id
        LEFT JOIN fmb_admin a on m.admin_uid=a.uid
        LEFT JOIN fmb_admin ac on m.create_uid=ac.uid
        <where>
            <if test="reqPar.queryKeyword!=null">
                and ( m.bp_id = #{reqPar.queryKeyword}
                or m.account_name like concat('%',#{reqPar.queryKeyword},'%')
                or pi.partner_name like concat('%',#{reqPar.queryKeyword},'%')
                or pi.partner_contacts_name like concat('%',#{reqPar.queryKeyword},'%')
                or pi.partner_contacts_mobile like concat('%',#{reqPar.queryKeyword},'%') )
            </if>
            <if test="reqPar.partnerProvince!=null">
                and pi.partner_province = #{reqPar.partnerProvince}
            </if>
            <if test="reqPar.partnerCity!=null">
                and pi.partner_city = #{reqPar.partnerCity}
            </if>
            <if test="reqPar.realname!=null">
                and a.realname = #{reqPar.realname}
            </if>
            <if test="reqPar.parterType!=null">
                and m.parter_type = #{reqPar.parterType}
            </if>
            <if test="reqPar.accountType!=null">
                and m.account_type = #{reqPar.accountType}
            </if>
            <if test="reqPar.accountStatus!=null">
                and m.account_status = #{reqPar.accountStatus}
            </if>
            <if test="reqPar.checkStatus!=null">
                and m.check_status = #{reqPar.checkStatus}
            </if>
        </where>
        <if test="reqPar.orderby == null or reqPar.orderby == 1">
            order by m.ctime desc
        </if>
        <if test="reqPar.orderby != null and reqPar.orderby == 2">
            order by s.sale_order_count desc ,m.ctime desc
        </if>
        <if test="reqPar.orderby != null and reqPar.orderby == 3">
            order by s.sale_goods_count desc ,m.ctime desc
        </if>
        <if test="reqPar.orderby != null and reqPar.orderby == 4">
            order by s.sale_order_money desc ,m.ctime desc
        </if>
    </select>


<!--供应商审核列表 -->
    <select id="selectFmbxBpReviewList" resultType="com.fmb.server2022.domain.FmbxBpReviewListDomain" parameterType="com.fmb.server2022.reqdomain.ReqFmbxBpReviewList"  >
        select action_id,m.account_name,h.check_result,h.ctime,h.checktime,h.status,h.reject_reason from fmbx_bp_review_his h
        LEFT JOIN fmbx_bp_main m on h.bp_id=m.bp_id
        WHERE h.bp_id=#{reqPar.bpId}
        order by ctime desc
    </select>

    <select id="listShopUsers" resultType="integer">
        select distinct s.shop_user_id
        from fmb_order_info o,
             fmb_shop_users s
        where o.create_time
            > date_sub(now()
                  , interval 2  YEAR )
          and o.order_status = 5
          and s.shop_user_id = o.shop_user_id
          and s.type = 3
    </select>
        
</mapper>