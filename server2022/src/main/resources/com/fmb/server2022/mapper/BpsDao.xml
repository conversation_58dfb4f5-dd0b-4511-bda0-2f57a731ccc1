<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.BpsDao" >


<!--供应商查询 -->
    
    <sql id="BpsInfoWhere">

        <where>


            <if test="reqPar.bpsIds!=null and reqPar.bpsIds.size()!=0 ">
                and fmbx_bps.bps_id in
                <foreach collection="reqPar.bpsIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="reqPar.key!=null">
                and ( fmbx_bps.bps_id = #{reqPar.key} or fmbx_bps.name like  CONCAT('%',#{reqPar.key},'%') )
            </if>

            <if test="reqPar.bps_type!=null">
                and bps_type = #{reqPar.bps_type}
            </if>

            <if test="reqPar.phone!=null">
                and phone = #{reqPar.phone}
            </if>

            <if test="reqPar.provinces!=null">
                and provinces = #{reqPar.provinces}
            </if>

            <if test="reqPar.area!=null">
                and area = #{reqPar.area}
            </if>

            <if test="reqPar.address!=null">
                and address = #{reqPar.address}
            </if>

            <if test="reqPar.status!=null">
                and status = #{reqPar.status}
            </if>

            <if test="reqPar.admin_uid!=null">
                and fmbx_bps.admin_uid = #{reqPar.admin_uid}
            </if>

            <if test="reqPar.bpId!=null">
                and fbm.bp_id = #{reqPar.bpId}
            </if>


        </where>
    </sql>

<select id="selectBpsInfo" resultType="int"  parameterType="com.fmb.server2022.reqdomain.ReqBpsInfo"  >


    select  fmbx_bps.bps_id

    from fmbx_bps
    left join fmbx_bp_bps_bind b on b.bps_id = fmbx_bps.bps_id
    left join fmbx_bp_main fbm on b.bp_id = fbm.bp_id
    left join fmbx_bp_parter_info pi on pi.bp_id = fbm.bp_id

    <include refid="BpsInfoWhere"></include>

    group by fmbx_bps.bps_id

</select>



    <select id="selectBpListByCpsId" resultType="Map" parameterType="integer">
        select i.bp_id,i.partner_name,b.bp_id from fmbx_bp_bps_bind as b left join fmbx_bp_parter_info as i on b.bp_id=i.bp_id where bps_id= #{bpsId}
    </select>


    <select id="selectBpsInfoStr" resultMap="rmBpsInfo" parameterType="list"  >

        select
        fmbx_bps.*
        , pi.bp_id
        , concat(pi.partner_name, '(', if(fmbx_bps.bpid_of_edit = pi.bp_id, '可编辑', '不可编辑'), ')') as edinfo
        from fmbx_bps
        left join fmbx_bp_bps_bind b on b.bps_id = fmbx_bps.bps_id
        left join fmbx_bp_main fbm on b.bp_id = fbm.bp_id
        left join fmbx_bp_parter_info pi on pi.bp_id = fbm.bp_id

        where fmbx_bps.bps_id in

        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="selectBpsInfoR" resultMap="rmBpsInfo"   >

        select
        fmbx_bps.*
        , pi.bp_id
        , concat(pi.partner_name, '(', if(fmbx_bps.bpid_of_edit = pi.bp_id, '可编辑', '不可编辑'), ')') as edinfo
        from fmbx_bps
        left join fmbx_bp_bps_bind b on b.bps_id = fmbx_bps.bps_id
        left join fmbx_bp_main fbm on b.bp_id = fbm.bp_id
        left join fmbx_bp_parter_info pi on pi.bp_id = fbm.bp_id

        <include refid="BpsInfoWhere"></include>


    </select>



    <resultMap id="rmBpsInfo" type="com.fmb.server2022.domain.BpsInfoDomain">
        <id column="bps_id" property="bpsId"></id>
        <result column="name" property="name"></result>
        <result column="bps_type" property="bpsType"></result>
        <result column="phone" property="phone"></result>
        <result column="provinces" property="provinces"></result>
        <result column="area" property="area"></result>
        <result column="address" property="address"></result>
        <result column="longitude" property="longitude"></result>
        <result column="latitude" property="latitude"></result>
        <result column="open_time" property="openTime"></result>
        <result column="decorate_time" property="decorateTime"></result>
        <result column="house_num" property="houseNum"></result>
        <result column="star_level" property="starLevel"></result>
        <result column="play_time" property="playTime"></result>
        <result column="leave_time" property="leaveTime"></result>
        <result column="status" property="status"></result>
        <result column="ctime" property="ctime"></result>
        <result column="create_uid" property="createUid"></result>
        <result column="utime" property="utime"></result>
        <result column="admin_uid" property="adminUid"></result>
        <result column="bpid_of_edit" property="bpidOfEdit"></result>
        <result column="flag_basic" property="flagBasic"></result>
        <result column="flag_room" property="flagRoom"></result>
        <result column="flag_pic" property="flagPic"></result>
        <result column="flag_video" property="flagVideo"></result>

        <!--这种不需要新类-->
        <!--private ArrayList<HashMap<String,Object>> listBp-->
        <collection property="listBp" ofType="java.util.HashMap">
            <result column="bp_id" property="bpId"></result>
            <result column="edinfo" property="edinfo"></result>
        </collection>

        <!--这种需要新类   -->
        <!--private ArrayList<BpInfo> listBp-->
        <!--<collection property="listBp" ofType="com.fmb.server2022.domain.BpInfo" javaType="list">-->
            <!--<id column="bp_id" property="bpId"></id>-->
            <!--<result column="edinfo" property="edinfo"></result>-->
        <!--</collection>-->


    </resultMap>

        


<!--商户media查询 -->

<select id="selectBpsMediaInfo" resultType="com.fmb.server2022.domain.BpsMediaInfoDomain" parameterType="com.fmb.server2022.reqdomain.ReqBpsMediaInfo"  >
    
select bm.id,m.media_id,m.url,bm.groupval,bm.description,b.name,b.bps_type,   bm.admin_type,bm.admin_name,bm.ctime,bm.status,bm.rankval
from fmbx_bps_media bm
left join  fmbx_bps b on bm.bps_id = b.bps_id
left join fmbx_media m on m.media_id = bm.media_id

where   bm.bps_id = #{reqPar.bps_id}
        <if test="reqPar.media_id!=null">
            and bm.media_id = #{reqPar.media_id}
        </if>
        <if test="reqPar.id!=null">
            and bm.id = #{reqPar.id}
        </if>

        <if test="reqPar.roomId!=null">
          and bm.room_id = #{reqPar.roomId}
        </if>

        <if test="reqPar.type!=null">
            and bm.type = #{reqPar.type}
        </if>

        <if test="reqPar.groupval!=null">
            and bm.groupval = #{reqPar.groupval}
        </if>
        <if test="reqPar.description!=null and reqPar.description!='' ">
            and bm.description like CONCAT('%',#{reqPar.description},'%')
        </if>


        <if test="reqPar.admin_type!=null">
            and bm.admin_type = #{reqPar.admin_type}
        </if>


        <if test="reqPar.admin_uid!=null">
            and bm.admin_uid = #{reqPar.admin_uid}
        </if>


    <if test="reqPar.from==1 ">
        order by bm.rankval asc
    </if>

    <if test="reqPar.from==2 ">
        order by bm.id desc
    </if>


</select>

    <select id="selectCreateInfo" parameterType="int" resultType="com.fmb.server2022.domain.BpsMediaFilterInfo">



 select   admin_type ,admin_uid, max(  admin_name ) as admin_name from fmbx_bps_media b where b.bps_id = #{bps_id}
and b.status= 1
 group by admin_type,admin_uid

    </select>
        


        

<!--商户管理房型管理 -->

<select id="selectBpsRoomInfo" resultMap="rmBpsRoomInfo"
        parameterType="com.fmb.server2022.reqdomain.ReqBpsRoomInfo"  >
    

select r.* ,m.media_id,m.url from fmbx_bps_room r
left join fmbx_bps_media bm on bm.room_id = r.room_id and bm.groupval = 10
left join fmbx_media m on m.media_id = bm.media_id

    where r.bps_id = #{reqPar.bpsId}

        <if test="reqPar.room_name!=null">
            and room_name  like  CONCAT('%',#{reqPar.room_name},'%')
        </if>

        <if test="reqPar.room_status!=null">
            and room_status = #{reqPar.room_status}
        </if>
    order by r.room_id asc , bm.rankval asc

</select>

<resultMap id="rmBpsRoomInfo" type="com.fmb.server2022.domain.BpsRoomInfoDomain">
    <id column="room_id" property="roomId"></id>
    <result column="bps_id" property="bpsId"></result>
    <result column="room_name" property="roomName"></result>
    <result column="room_introduce" property="roomIntroduce"></result>
    <result column="room_num" property="roomNum"></result>
    <result column="floor_number" property="floorNumber"></result>
    <result column="build_area" property="buildArea"></result>
    <result column="bed_type" property="bedType"></result>
    <result column="bed_type_val" property="bedTypeVal"></result>
    <result column="bed_num" property="bedNum"></result>
    <result column="bed_width" property="bedWidth"></result>
    <result column="add_bed_type" property="addBedType"></result>
    <result column="max_people_num" property="maxPeopleNum"></result>
    <result column="smoke_type" property="smokeType"></result>
    <result column="bathtub_type" property="bathtubType"></result>
    <result column="room_status" property="roomStatus"></result>
    <result column="admin_uid" property="adminUid"></result>
    <result column="ctime" property="ctime"></result>
    <result column="utime" property="utime"></result>
    <result column="is_selected" property="isSelected"></result>

    <collection property="images" ofType="java.util.HashMap">
        <result column="media_id" property="mediaId"></result>
        <result column="url" property="url"></result>
    </collection>


</resultMap>

    <!--查询酒店套餐关联房型 -->

    <select id="selectBpsRoomSku" resultMap="rmBpsRoomInfo"
            parameterType="com.fmb.server2022.reqdomain.ReqBpsRoomSku"  >


        select if(ifnull(sku.suite_id,0)>0,true,false) is_selected, r.room_id, r.room_name, m.media_id, m.url from fmbx_bps_room r
        left join fmbx_suite_room_sku sku on r.room_id = sku.room_id
        left join fmbx_bps_media bm on bm.room_id = r.room_id and bm.groupval = 10
        left join fmbx_media m on m.media_id = bm.media_id

        where r.bps_id = #{reqPar.bpsId}

        <if test="reqPar.room_name!=null">
            and room_name  like  CONCAT('%',#{reqPar.room_name},'%')
        </if>

        <if test="reqPar.room_status!=null">
            and room_status = #{reqPar.room_status}
        </if>
        order by r.room_id asc , bm.rankval asc

    </select>

<!--供应商合同管理 -->

<select id="selectBpContract"   resultMap="rmBpContract" 
 parameterType="com.fmb.server2022.reqdomain.ReqBpContract"  >
    

select c.*,bi.partner_name, bm.account_name ,u.sys_username  from fmbx_bp_contract c
left join fmbx_bp_main bm on bm.bp_id = c.bp_id
left join fmbx_bp_parter_info bi on bi.bp_id = c.bp_id
    left join fmbx_thirdsys_user u on u.third_sys_uid = c.creater_ding_userid
<where>



    <if test="reqPar.bpc_number!=null">
        and bpc_number = #{reqPar.bpc_number}
    </if>


        <if test="reqPar.bpc_runuing_status!=null and reqPar.bpc_runuing_status.size()!=0 ">
            and c.bpc_runuing_status in
            <foreach collection="reqPar.bpc_runuing_status" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reqPar.bp_key!=null">
            and ( c.bp_id = #{reqPar.bp_key} or bm.account_name like CONCAT('%',#{reqPar.bp_key},'%')   )
        </if>

        <if test="reqPar.bpId!=null">
            and  c.bp_id = #{reqPar.bpId}
        </if>

        <if test="reqPar.city_id!=null">
            and c.city_id = #{reqPar.city_id}
        </if>



        <if test="reqPar.business_type!=null">
            and c.business_type = #{reqPar.business_type}
        </if>



        <if test="reqPar.bp_template_type!=null">
            and c.bp_template_type = #{reqPar.bp_template_type}
        </if>



        <if test="reqPar.bp_create_type!=null">
            and c.bp_create_type = #{reqPar.bp_create_type}
        </if>


        <if test="reqPar.bpc_name!=null">
            and bm.account_name like CONCAT('%',#{reqPar.bpc_name},'%')
        </if>


</where>
</select>



<resultMap id="rmBpContract" type="com.fmb.server2022.domain.BpContractDomain">
    <result column="bpc_id" property="bpcId"></result>
    <result column="bp_id" property="bpId"></result>
    <result column="bpc_runuing_status" property="bpcRunuingStatus"></result>
    <result column="bpc_number" property="bpcNumber"></result>
    <result column="city_id" property="cityId"></result>
    <result column="ctime" property="ctime"></result>
    <result column="business_type" property="businessType"></result>
    <result column="bp_template_type" property="bpTemplateType"></result>
    <result column="bp_create_type" property="bpCreateType"></result>
    <result column="bpc_name" property="bpcName"></result>
    <result column="partner_name" property="partnerName"></result>
    <result column="bp_start_date" property="bpStartDate"></result>
    <result column="bp_end_date" property="bpEndDate"></result>
    <result column="account_name" property="accountName"></result>
    <result column="sys_username" property="accountName"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

        

<!--供应商证照 开始-->



<select id="selectBpMediaInfo"   resultMap="rmBpMediaInfo" parameterType="int">


select bm.bpm_id,m.media_id,m.url,bm.file_type_name,bm.file_type from fmbx_bp_media bm
left join fmbx_media m on m.media_id = bm.media_id
where bm.bp_id = #{bpid}



</select>



<resultMap id="rmBpMediaInfo" type="com.fmb.server2022.domain.BpMediaInfoDomain">
    <result column="bpm_id" property="bpmId"></result>
    <result column="media_id" property="mediaId"></result>
    <result column="url" property="url"></result>
    <result column="file_type_name" property="fileTypeName"></result>
    <result column="file_type" property="fileType"></result>


</resultMap>

    <!--供应商证照 结束-->

        
</mapper>