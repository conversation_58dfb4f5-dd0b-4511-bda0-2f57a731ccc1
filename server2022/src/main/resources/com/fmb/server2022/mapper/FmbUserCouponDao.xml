<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FmbUserCouponDao" >

    <select id="getUsableCouponList" resultType="map">
        SELECT
        (
        CASE
        fct.coupon_type
        WHEN 3 THEN
        2
        WHEN 4 THEN
        1 ELSE 0
        END
        ) AS rank,
        fuc.min_amount,
        fct.min_amount AS u_min_amount,
        fuc.discount_rate,
        fuc.use_status,
        fct.coupon_type,
        fct.coupon_name,
        fct.coupon_id,
        fct.plat_id,
        fct.limit_type,
        fct.limit_date,
        fct.limit_cate,
        DATE_FORMAT(fct.limit_date,'%Y-%m-%d %H:%i:%s') as limit_date,
        fct.activity_id,
        fuc.coupon_sn,
        fct.use_limit,
        fuc.coupon_money,
        DATE_FORMAT(fuc.coupon_start_time,'%Y-%m-%d %H:%i:%s') as coupon_start_time,
        DATE_FORMAT(fuc.coupon_end_time,'%Y-%m-%d %H:%i:%s') as coupon_end_time,
        fuc.order_sn,
        fuc.use_time,
        DATE_FORMAT(unix_timestamp( fuc.use_time ),'%Y-%m-%d %H:%i:%s') AS use_time_stamp,
        fct.coupon_tag,
        DATE_FORMAT(fuc.distribute_time,'%Y-%m-%d %H:%i:%s') as distribute_time,
        fct.stacked
        FROM
        fmb_user_coupon fuc
        LEFT JOIN fmb_coupon_type fct ON fuc.coupon_id = fct.coupon_id
        WHERE
        fct.STATUS = 1
        AND fuc.uid = #{uid}
        AND fuc.use_status = 0
        AND fuc.coupon_end_time > #{toDayDate}
--         AND fuc.coupon_sn = 'aZTDmtBjkyQ8'
        ORDER BY
        rank DESC,
        IF
        (
        fuc.discount_rate > 0,
        fuc.discount_rate,
        10
        ) ASC,
        fct.stacked DESC,
        fuc.coupon_money DESC,
        IF
        (
        fuc.distribute_time > #{toDayDate},
        fuc.distribute_time,
        0
        ) DESC,
        IF
        (
        fuc.coupon_end_time  &lt;  #{toDayDate},
        DATE_ADD( #{toDayDate}, INTERVAL 10 YEAR ),
        fuc.coupon_end_time
        ) ASC,
        IF
        ( fct.limit_type = 2, 0, 1 ) ASC,
        fuc.distribute_time DESC,
        fuc.coupon_sn ASC
    </select>
    <select id="getCouponList" resultType="com.fmb.server2022.fmbx.entity.FmbCouponType">
        SELECT
        *
        FROM
        fmb_coupon_type
        WHERE
        coupon_tag = "领券下单"
        AND STATUS = 1
        AND start_time &lt;= now( ) AND end_time >= DATE_ADD( NOW( ), INTERVAL 2 HOUR )
        ORDER BY
        activity_id DESC,
        limit_cate DESC,
        coupon_money ASC
    </select>
    <select id="getCountNotUseCoupon" resultType="java.lang.Integer">
        SELECT
        count( * ) AS num
        FROM
        fmb_user_coupon
        WHERE
        coupon_id = #{couponId}
        AND send_status = 0
        AND use_status = 0
        AND coupon_start_time &lt;= now( ) AND coupon_end_time >= DATE_ADD( NOW( ), INTERVAL 2 HOUR )     </select>

</mapper>