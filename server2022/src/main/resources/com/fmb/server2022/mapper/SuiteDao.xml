<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.SuiteDao" >


    <select id="selectPriceAddOperation" resultType="java.util.Map">

        select  s.sku_name as skuName ,  p.price_operate_id as priceOperateId  from
            fmbx_suite_room_sku s
                left join fmbx_suite_price_operate p on p.price_operate_id = s.last_addprice_operate_id
        where s.suite_id = #{suiteid}
          and s.last_addprice_operate_id > 0
          and p.price_operate_id > 0
        order by s.utime desc

    </select>

<!--酒店sku信息查询 开始-->



<select id="selectHotelSku"   resultMap="rmHotelSku" 
 parameterType="com.fmb.server2022.reqdomain.ReqHotelSku"  >

    select
    s.suite_id,
    s.name,
    s.total_neight,
    sku.sku_id,
    sku.sku_name,
    sku.flag_sell,
    sku.status ,
    sku.sell_num ,
    s.price_input_type

    from fmbx_suite_room_sku sku
    left join fmbx_suite s on s.suite_id = sku.suite_id
    left join fmbx_bps_room r on r.room_id = sku.room_id
    left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
    WHERE u.xaid = #{reqPar.xaid}

    <if test="reqPar.status!=null  ">
        and sku.status  != #{reqPar.status}
    </if>

    order by s.sort_value desc, s.suite_id desc, sku.sort_value desc, sku.sku_id desc;

</select>

    <select id="selectHotelReserveSkuMenu"   resultType="com.fmb.server2022.domain.HotelSkuDomain"
            parameterType="com.fmb.server2022.reqdomain.ReqHotelSku"  >

        select s.suite_id,
        s.name,
        h.sku_id,
        s.total_neight
        from fmbx_suite s
        LEFT JOIN fmbx_hotel_reserve_sku h on h.suite_id=s.suite_id
        where s.flag_hotel_reserve=1 and h.sku_id>0 and h.status>0
        and s.xaid=#{reqPar.xaid}
        order by s.sort_value desc, s.suite_id desc

    </select>



<resultMap id="rmHotelSku" type="com.fmb.server2022.domain.HotelSkuDomain">
    <id column="suite_id" property="suiteId"></id>
    <result column="name" property="name"></result>
    <result column="total_neight" property="totalNeight"></result>
    <result column="price_input_type" property="priceInputType"></result>


    <collection property="listSku" ofType="java.util.HashMap">
        <result column="sku_id" property="skuId"></result>
        <result column="sku_name" property="skuName"></result>
        <result column="flag_sell" property="flagSell"></result>
        <result column="status" property="status"></result>
        <result column="sell_num" property="sellNum"></result>
    </collection>

</resultMap>


    <select id="selectHotelSkuList"   resultType="com.fmb.server2022.domain.HotelSkuDomain"
            parameterType="com.fmb.server2022.reqdomain.ReqHotelSku"  >


        select
        s.suite_id,
        s.name,
        s.total_neight,
        sku.sku_id,
        sku.sku_name,
        sku.flag_sell,
        sku.status ,
        sku.sell_num ,

        s.price_input_type

        from fmbx_suite_room_sku sku
        left join fmbx_suite s on s.suite_id = sku.suite_id
        left join fmbx_bps_room r on r.room_id = sku.room_id
        left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
        WHERE u.xaid = #{reqPar.xaid}

        <if test="reqPar.status!=null  ">
                    and sku.status  != #{reqPar.status}
        </if>
        order by s.sort_value desc, s.suite_id desc, sku.sort_value desc, sku.sku_id desc;

    </select>


    <!--酒店sku信息查询 结束-->

        

<!--酒店sku日期价格范围查询 开始-->



<select id="selectHotelSkuDatePrice"   resultMap="rmHotelSkuDatePrice" 
 parameterType="com.fmb.server2022.reqdomain.ReqHotelDate"  >


    select d.datecol,
           ifnull(p.price_id,0) as price_id ,
           ifnull( p.market_price  ,0 ) as market_price,
           ifnull( p.goods_price ,0) as goods_price ,
           ifnull( p.settle_price , 0) as settle_price ,
           ifnull( p.room_id,0) as room_id,
           ifnull(p.suite_room_sku_id,0) as sku_id ,
           ifnull(s.stock_num,0) as stock_num,
           ifnull(s.stock_id,0) as stock_id,
           ifnull(s.version_num,0) as version_num,
           ifnull(s.status,0) as stock_status

from fmbx_date_table d
         left join fmbx_suite_roomdate_price p on d.datecol = p.target_date and p.suite_room_sku_id = #{reqPar.sku_id}
         left join fmbx_suite_stock s on s.target_date = d.datecol and s.room_id = p.room_id
where d.datecol between #{reqPar.datecolBegin} and #{reqPar.datecolEnd}
order by d.datecol asc 

</select>

    <select id="selectRoomStockDateInfo" parameterType="com.fmb.server2022.reqdomain.ReqHotelDate"
            resultType="com.fmb.server2022.domain.HotelStockDateDomain">


        select d.datecol,
               ifnull(s.stock_id,0) as stock_id ,
               ifnull(s.stock_num,0) as stock_num,
               ifnull(s.status,0) as stock_status

        from fmbx_date_table d
                 left join fmbx_suite_stock s on s.target_date = d.datecol and s.room_id = #{reqPar.roomId}
        where d.datecol between #{reqPar.datecolBegin} and #{reqPar.datecolEnd}
        order by d.datecol asc

    </select>

    <select id="selectSkuOfAvailableHotel" parameterType="int" resultType="integer">

        select u.sku_id
        from fmbx_suite_roomdate_price p
           , fmbx_suite_stock s
           , fmbx_suite_room_sku u
           , fmbx_suite t

        where t.xaid = #{xaid}
          and p.suite_room_sku_id = u.sku_id
          and p.target_date >= DATE_FORMAT(now(),'%Y-%m-%d')
          and p.target_date = s.target_date
          and s.room_id = p.room_id
          and t.flag_standard_hotel = 1
          and t.suite_id = u.suite_id
          and s.stock_num > 0
          and s.status = 1
          and u.sku_id = p.suite_room_sku_id
          and u.status = 1
limit 1

    </select>

    <select id="selectSkuOfAvailableReserveHotel" parameterType="int" resultType="integer">
        select h.sku_id

        from fmbx_hotel_reserve_sku h,
        fmbx_sku s
        , fmbx_suite t
        where h.sku_id = s.sku_id
        and s.sku_type = 2
        and s.xaid = #{xaid}
        and h.status = 1
        and h.stock_num > 0
        and t.flag_hotel_reserve = 1
        and t.suite_id = h.suite_id
        and h.sell_start_time  &lt;=   now()
        and h.sell_end_time >= now()
        limit 1
    </select>

    <select id="countHotelDeleteSku" resultType="int" parameterType="int">
        select count(m.sku_id)
        from fmbx_sku m
                 left join fmbx_suite_room_sku fsrs on m.sku_id = fsrs.sku_id
        where fsrs.status = 0
          and m.xaid = #{xaid}
          and m.sku_type = 1
    </select>
    <select id="countReserveHotelDeleteSku" resultType="int" parameterType="int">

        select count(m.sku_id)
        from fmbx_sku m
                 left join fmbx_hotel_reserve_sku fhrs on fhrs.sku_id = m.sku_id
        where fhrs.status = 0
          and m.xaid = #{xaid}
          and m.sku_type = 2
    </select>

    <select id="selectValidPriceAndStock" resultType="map" parameterType="map">

        select
            p.target_date,
            p.price_id,
            p.room_id,
            s.stock_id,
            s.stock_num,
            s.status ,
            u.sku_id
        from  fmbx_suite_roomdate_price p
           , fmbx_suite_stock s
           , fmbx_suite_room_sku u

        where
            u.sku_id = #{skuid}
          and p.suite_room_sku_id = u.sku_id
          and p.target_date >= #{dateStart}
          and p.target_date = s.target_date
          and s.room_id = p.room_id
          and s.stock_num > 0
          and s.status = 1
          and u.sku_id = p.suite_room_sku_id

        <if test="_parameter.containsKey('checkPublishStatus') ">
            and u.status = 1
        </if>
            limit 1

    </select>

    <select id="selectValidHotelDatePriceOfSuite" resultType="map" parameterType="map">

        select
        p.target_date,
        p.price_id,
        p.room_id,
        s.stock_id,
        s.stock_num,
        s.status ,
        u.sku_id
        from  fmbx_suite_roomdate_price p
        , fmbx_suite_stock s
        , fmbx_suite_room_sku u

        where
        u.suite_id = #{suiteId}
        and p.suite_room_sku_id = u.sku_id
        and p.target_date >= #{dateStart}
        and p.target_date = s.target_date
        and s.room_id = p.room_id
        and s.stock_num > 0
        and s.status = 1
        and u.sku_id = p.suite_room_sku_id
        and u.status = 1

        limit 1

    </select>






<resultMap id="rmHotelSkuDatePrice" type="com.fmb.server2022.domain.HotelSkuDatePriceDomain">
    <result column="datecol" property="datecol"></result>
    <result column="market_price" property="marketPrice"></result>
    <result column="goods_price" property="goodsPrice"></result>
    <result column="settle_price" property="settlePrice"></result>
    <result column="room_id" property="roomId"></result>
    <result column="sku_id" property="skuId"></result>
    <result column="stock_num" property="stockNum"></result>
    <result column="stock_id" property="stockId"></result>
    <result column="version_num" property="versionNum"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--酒店sku日期价格范围查询 结束-->


    <select id="selectStockRoomList" resultType="com.fmb.server2022.domain.RoomStockInfo">

        select room_id, room_name
        from fmbx_bps_room
        where
                room_id in (
                select distinct(u.room_id)
                from fmbx_suite s
                left join fmbx_suite_room_sku u on u.suite_id = s.suite_id
                where s.xaid = #{xaid} )


    </select>

    <select id="selectStockRoomList2" parameterType="com.fmb.server2022.reqdomain.ReqHotelDate" resultType="com.fmb.server2022.domain.RoomStockInfo">
        select room_id, room_name
        from fmbx_bps_room

        <where>

            <if test="reqPar.xaid!=null  ">
               and room_id in (
                select distinct(u.room_id)
                from fmbx_suite s
                left join fmbx_suite_room_sku u on u.suite_id = s.suite_id
                where s.xaid = #{reqPar.xaid} )
            </if>

            <if test="reqPar.bpsId!=null  ">
                and bps_id = #{reqPar.bpsId}
            </if>

        </where>



    </select>


        

<!--酒店价格变动列表 开始-->



<select id="selectHotelPriceChangeData"   resultMap="rmHotelPriceChangeData" 
 parameterType="com.fmb.server2022.reqdomain.ReqHotelPriceChangeData"  >


select o.price_operate_id,o.xaid, o.sku_name,o.sku_id, o.operate_data, o.change_type, o.admin_name,o.admin_uid, o.ctime ,o.activity_name
from fmbx_suite_price_operate o

<where>
    <if test="reqPar.xaid!=null and reqPar.xaid != '' ">
        and o.xaid = #{reqPar.xaid}
    </if>

    <if test="reqPar.sku_id!=null and reqPar.sku_id != '' ">
        and o.sku_id = #{reqPar.sku_id}
    </if>

    <if test="reqPar.ctime_start!=null">
        and o.ctime &gt;= #{reqPar.ctime_start}
    </if>
    <if test="reqPar.ctime_end!=null">
        and o.ctime &lt;= #{reqPar.ctime_end}
    </if>

    <if test="reqPar.target_date!=null and reqPar.target_date != '' ">
        and exists(
        select 1
        from fmbx_suite_price_operate_log l
        where l.price_operate_id = o.price_operate_id
        and l.target_date = #{reqPar.target_date}
        )
    </if>

    <if test="reqPar.change_type!=null  ">
        and o.change_type = #{reqPar.change_type}
    </if>

    <if test="reqPar.admin_uid!=null and reqPar.admin_uid != '' ">
        and o.admin_uid = #{reqPar.admin_uid}
    </if>

</where>

    order by o.price_operate_id desc

</select>





<resultMap id="rmHotelPriceChangeData" type="com.fmb.server2022.domain.HotelPriceChangeDataDomain">
    <result column="price_operate_id" property="priceOperateId"></result>
    <result column="xaid" property="xaid"></result>
    <result column="activity_name" property="activityName"></result>
    <result column="sku_name" property="skuName"></result>
    <result column="sku_id" property="skuId"></result>
    <result column="operate_data" property="operateData"></result>
    <result column="change_type" property="changeType"></result>
    <result column="admin_name" property="adminName"></result>
    <result column="admin_uid" property="adminUid"></result>
    <result column="ctime" property="ctime"></result>
    <result column="target_date" property="targetDate"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--酒店价格变动列表 结束-->

        

<!--酒店库存变化 开始-->


<sql id="HotelStockChangeDataWhere">


    <where>
        <if test="reqPar.room_id!=null and reqPar.room_id != '' ">
            and o.room_id = #{reqPar.room_id}
        </if>

        <if test="reqPar.change_type!=null  ">
            and o.change_type = #{reqPar.change_type}
        </if>
        <if test="reqPar.bps_id!=null  ">
            and o.bps_id = #{reqPar.bps_id}
        </if>

        <if test="reqPar.status_change_type!=null  ">
            and o.status_change_type = #{reqPar.status_change_type}
        </if>


        <if test="reqPar.admin_uid!=null and reqPar.admin_uid != '' ">
            and o.admin_uid = #{reqPar.admin_uid}
        </if>


        <if test="reqPar.ctime_start!=null">
            and o.ctime &gt;= #{reqPar.ctime_start}
        </if>


        <if test="reqPar.ctime_end!=null">
            and o.ctime &lt;= #{reqPar.ctime_end}
        </if>


        <if test="reqPar.target_date!=null and reqPar.target_date != '' ">
        and exists(select 1
        from fmbx_suite_stock_operate_log l
        where l.stock_operate_id = o.stock_operate_id
        and l.target_date =  #{reqPar.target_date} )
        </if>

    </where>

</sql>

<select id="selectHotelStockChangeData"   resultMap="rmHotelStockChangeData"
 parameterType="com.fmb.server2022.reqdomain.ReqHotelStockChangeData"  >


    select o.stock_operate_id,
       o.room_id,
       o.room_name,
       o.bps_name ,
       o.bps_id ,
       o.operate_data,
       o.change_type,
       o.admin_name,
       o.admin_uid,
           o.status_change_type ,
       o.ctime
from fmbx_suite_stock_operate o

    <include refid="HotelStockChangeDataWhere"></include>

order by o.stock_operate_id desc 


</select>



<resultMap id="rmHotelStockChangeData" type="com.fmb.server2022.domain.HotelStockChangeDataDomain">
    <result column="stock_operate_id" property="stockOperateId"></result>
    <result column="room_id" property="roomId"></result>
    <result column="bps_id" property="bpsId"></result>
    <result column="room_name" property="roomName"></result>
    <result column="bps_name" property="bpsName"></result>
    <result column="operate_data" property="operateData"></result>
    <result column="change_type" property="changeType"></result>
    <result column="status_change_type" property="statusChangeType"></result>
    <result column="admin_name" property="adminName"></result>
    <result column="admin_uid" property="adminUid"></result>
    <result column="ctime" property="ctime"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--酒店库存变化 结束-->

        

<!--活动列表 开始-->


<sql id="ActivityXWhere">

    <where>


        <if test="reqPar.xaid!=null and reqPar.xaid != '' ">
            and a.xaid = #{reqPar.xaid}
        </if>

        <if test="reqPar.title!=null and reqPar.title != '' ">
            and a.title like  CONCAT('%',#{reqPar.title},'%')
        </if>


        <if test="reqPar.business_type!=null  ">
            and a.business_type = #{reqPar.business_type}
        </if>

        <if test="reqPar.category_id!=null  ">
            and a.category_id = #{reqPar.category_id}
        </if>

        <if test="reqPar.bps_id!=null  ">
            and a.bps_id = #{reqPar.bps_id}
        </if>

        <if test="reqPar.bp_id!=null  ">
            and a.bp_id = #{reqPar.bp_id}
        </if>

        <if test="reqPar.admin_uid!=null  ">
            and m.admin_uid = #{reqPar.admin_uid}
        </if>

        <if test="reqPar.is_confirm!=null  ">
            and a.is_confirm = #{reqPar.is_confirm}
        </if>

        <if test="reqPar.create_uid!=null  ">
            and a.create_uid = #{reqPar.create_uid}
        </if>

        <if test="reqPar.last_modify_uid!=null  ">
            and a.last_modify_uid = #{reqPar.last_modify_uid}
        </if>


        <if test="reqPar.flag_publish!=null and reqPar.flag_publish lt 3 ">
            and a.flag_publish = #{reqPar.flag_publish}
        </if>
        <if test="reqPar.flag_publish!=null and reqPar.flag_publish == 3 ">
            and a.deleted_sku_num &gt; 0
        </if>


        <if test="reqPar.flag_have_valid_sku!=null  ">
            and a.flag_have_valid_sku = #{reqPar.flag_have_valid_sku}
        </if>

        <if test="reqPar.provinces!=null and reqPar.provinces != '' ">
            and a.provinces = #{reqPar.provinces}
        </if>



        <if test="reqPar.provinces_sub!=null and reqPar.provinces_sub != '' ">
            and a.provinces_sub = #{reqPar.provinces_sub}
        </if>

        <if test="reqPar.city_id!=null and reqPar.city_id != '' ">
            and a.city_id = #{reqPar.city_id}
        </if>
        <if test="reqPar.ctime_begin!=null and reqPar.ctime_begin != '' ">
            and a.ctime &gt;= #{reqPar.ctime_begin}
        </if>
        <if test="reqPar.ctime_end!=null and reqPar.ctime_end != '' ">
            and a.ctime &lt;= #{reqPar.ctime_end}
        </if>


        <if test="reqPar.ticket_type!=null and reqPar.ticket_type.size()!=0 ">
            and a.ticket_type in
            <foreach collection="reqPar.ticket_type" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </where>

</sql>

<select id="selectActivityX"   resultMap="rmActivityX" 
 parameterType="com.fmb.server2022.reqdomain.ReqActivityX"  >


    select a.xaid,
       a.title,
       a.banner_list_url,
       a.business_type,
       a.category_id,
           g.cate_name ,
       s.name,
       s.bps_id,
       b.partner_name,
       b.bp_id,
       m.admin_mobile,
       m.admin_name,
       m.admin_uid,
       a.is_confirm,
       a.create_uid,
       a.create_uid_name,
       a.last_modify_uid,
       a.last_modify_uid_name,
       a.flag_publish,
       a.flag_have_valid_sku,
       a.provinces,
       a.provinces_sub,
       a.deleted_sku_num ,
       a.city_id,
       a.ctime,
       a.utime
from fmbx_activity a
         left join fmbx_bps s on s.bps_id = a.bps_id
         left join fmbx_bp_parter_info b on b.bp_id = a.bp_id
         left join fmbx_bp_main m on m.bp_id = a.bp_id
         left join fmb_goods_category g on g.cate_id = a.category_id
    <include refid="ActivityXWhere"></include>

order  by a.ctime  desc

</select>





<resultMap id="rmActivityX" type="com.fmb.server2022.domain.ActivityXDomain">
    <result column="xaid" property="xaid"></result>
    <result column="title" property="title"></result>
    <result column="banner_list_url" property="bannerListUrl"></result>
    <result column="business_type" property="businessType"></result>
    <result column="category_id" property="categoryId"></result>
    <result column="name" property="name"></result>
    <result column="bps_id" property="bpsId"></result>
    <result column="partner_name" property="partnerName"></result>
    <result column="bp_id" property="bpId"></result>
    <result column="admin_mobile" property="adminMobile"></result>
    <result column="admin_name" property="adminName"></result>
    <result column="admin_uid" property="adminUid"></result>
    <result column="is_confirm" property="isConfirm"></result>
    <result column="create_uid" property="createUid"></result>
    <result column="create_uid_name" property="createUidName"></result>
    <result column="last_modify_uid" property="lastModifyUid"></result>
    <result column="last_modify_uid_name" property="lastModifyUidName"></result>
    <result column="flag_publish" property="flagPublish"></result>
    <result column="flag_have_valid_sku" property="flagHaveValidSku"></result>
    <result column="provinces" property="provinces"></result>
    <result column="provinces_sub" property="provincesSub"></result>
    <result column="city_id" property="cityId"></result>
    <result column="deleted_sku_num" property="deletedSkuNum"></result>
    <result column="ctime" property="ctime"></result>
    <result column="utime" property="utime"></result>
    <result column="cate_name" property="cateName"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--活动列表 结束-->

        

<!--日历房酒店sku列表 开始-->


<sql id="HotelDateSkuWhere">

    <where>
        <if test="reqPar.xaid!=null  ">
            and u.xaid = #{reqPar.xaid}
        </if>

        <if test="reqPar.sku_id!=null  ">
            and u.sku_id = #{reqPar.sku_id}
        </if>

        <if test="reqPar.suite_id!=null  ">
            and sku.suite_id = #{reqPar.suite_id}
        </if>

        <if test="reqPar.room_id!=null  ">
            and r.room_id = #{reqPar.room_id}
        </if>

        <if test="reqPar.create_uid!=null  ">
            and u.create_uid = #{reqPar.create_uid}
        </if>

        <if test="reqPar.ctime_start!=null">
            and u.ctime &gt; #{reqPar.ctime_start}
        </if>
        <if test="reqPar.ctime_end!=null">
            and u.ctime &lt; #{reqPar.ctime_end}
        </if>

        <if test="reqPar.flag_sell!=null and reqPar.flag_sell.size()!=0 ">
            and sku.flag_sell in
            <foreach collection="reqPar.flag_sell" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="reqPar.status!=null and reqPar.status.size()!=0 ">
            and sku.status in
            <foreach collection="reqPar.status" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="reqPar.suite_name!=null and reqPar.suite_name.length()!=0   ">
            and s.name like CONCAT('%',#{reqPar.suite_name},'%')
        </if>

        <if test="reqPar.suiteIds!=null and reqPar.suiteIds.size()!=0 ">
            and s.suite_id in
            <foreach collection="reqPar.suiteIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </where>

</sql>

<select id="selectHotelDateSku"    resultType="com.fmb.server2022.domain.HotelDateSkuDomain"
 parameterType="com.fmb.server2022.reqdomain.ReqHotelDateSku"  >


    select
       u.xaid,
       sku.sku_id,
       s.suite_id,
       s.name,
       s.sort_value as suite_sort_value ,
       sku.sort_value as  sku_sort_value ,
       r.room_id,
       r.room_name,
       s.total_neight,
       s.night_min,
       s.flag_hotel_reserve,
       s.flag_standard_hotel,
       s.pre_reserve_day,
       s.pre_reserve_time,
       u.create_uid,
       u.create_uid_name,
       u.ctime,
       u.last_update_uid,
       u.last_update_uid_name,
       u.utime,
       sku.flag_sell,
       sku.status

from fmbx_suite_room_sku sku
         left join fmbx_suite s on s.suite_id = sku.suite_id
         left join fmbx_bps_room r on r.room_id = sku.room_id
         left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
    <include refid="HotelDateSkuWhere"></include>

    order by s.sort_value desc , s.suite_id desc ,  sku.sort_value desc , sku.sku_id desc

</select>


<select id="selectHotelDateSkuReturnInt"  resultType="int"
        parameterType="com.fmb.server2022.reqdomain.ReqHotelDateSku"  >

    select
       s.suite_id

from fmbx_suite_room_sku sku
         left join fmbx_suite s on s.suite_id = sku.suite_id
         left join fmbx_bps_room r on r.room_id = sku.room_id
         left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1

    <include refid="HotelDateSkuWhere"></include>

    group by s.suite_id
    order by s.sort_value desc , s.suite_id desc

</select>



<select id="selectHotelDateSkuUserListIds"   resultMap="rmHotelDateSku"
        parameterType="com.fmb.server2022.reqdomain.ReqHotelDateSku"  >

    select
       u.xaid,
       sku.sku_id,
    s.sort_value as suite_sort_value ,
    sku.sort_value as  sku_sort_value ,
    s.suite_id,
       s.name,
       r.room_id,
       r.room_name,
       s.total_neight,
       s.night_min,
       s.flag_hotel_reserve,
       s.flag_standard_hotel,
       s.pre_reserve_day,
       s.pre_reserve_time,
       u.create_uid,
       u.create_uid_name,
       u.ctime,
       u.last_update_uid,
       u.last_update_uid_name,
       u.utime,
       sku.flag_sell,
           sku.sell_num ,
       sku.status

from fmbx_suite_room_sku sku
         left join fmbx_suite s on s.suite_id = sku.suite_id
         left join fmbx_bps_room r on r.room_id = sku.room_id
         left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1

    <include refid="HotelDateSkuWhere"></include>

    order by s.sort_value desc , s.suite_id desc ,  sku.sort_value desc , sku.sku_id desc

</select>

    <select id="selectHotelDateSkuTotalConut" resultType="int"
            parameterType="com.fmb.server2022.reqdomain.ReqHotelDateSku"  >
        select count(distinct sku.sku_id)
        from fmbx_suite_room_sku sku
        left join fmbx_suite s on s.suite_id = sku.suite_id
        left join fmbx_bps_room r on r.room_id = sku.room_id
        left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
        <include refid="HotelDateSkuWhere"></include>
    </select>



<resultMap id="rmHotelDateSku" type="com.fmb.server2022.domain.HotelDateSkuDomainSub">
    <id column="suite_id" property="suiteId"></id>
    <result column="name" property="name"></result>
    <result column="suite_sort_value" property="suiteSortValue"></result>


    <collection property="list" ofType="com.fmb.server2022.domain.HotelDateSkuDomain">
        <result column="suite_id" property="suiteId"></result>
        <result column="name" property="name"></result>
    <result column="sku_id" property="skuId"></result>
    <result column="room_id" property="roomId"></result>
    <result column="room_name" property="roomName"></result>
    <result column="total_neight" property="totalNeight"></result>
    <result column="night_min" property="nightMin"></result>
    <result column="flag_hotel_reserve" property="flagHotelReserve"></result>
    <result column="flag_standard_hotel" property="flagStandardHotel"></result>
    <result column="pre_reserve_day" property="preReserveDay"></result>
    <result column="pre_reserve_time" property="preReserveTime"></result>
    <result column="create_uid" property="createUid"></result>
    <result column="create_uid_name" property="createUidName"></result>
    <result column="ctime" property="ctime"></result>
    <result column="last_update_uid" property="lastUpdateUid"></result>
    <result column="last_update_uid_name" property="lastUpdateUidName"></result>
    <result column="utime" property="utime"></result>
    <result column="flag_sell" property="flagSell"></result>
    <result column="status" property="status"></result>
    <result column="sku_sort_value" property="skuSortValue"></result>
        <result column="suite_sort_value" property="suiteSortValue"></result>
        <result column="sell_num" property="sellNum"></result>
    </collection>

</resultMap>

    <!--日历房酒店sku列表 结束-->

        

<!--房券sku列表 开始-->


<sql id="HotelReserveSkuWhere">

    <where>
        <if test="reqPar.xaid!=null ">
            and u.xaid = #{reqPar.xaid}
        </if>

        <if test="reqPar.sku_id!=null  ">
            and sku.sku_id = #{reqPar.sku_id}
        </if>

        <if test="reqPar.suite_id!=null  ">
            and sku.suite_id = #{reqPar.suite_id}
        </if>

        <if test="reqPar.create_uid!=null  ">
            and u.create_uid = #{reqPar.create_uid}
        </if>


        <if test="reqPar.ctime_start!=null">
            and u.ctime &gt; #{reqPar.ctime_start}
        </if>
        <if test="reqPar.ctime_end!=null">
            and u.ctime &lt; #{reqPar.ctime_end}
        </if>

        <if test="reqPar.suite_name!=null and reqPar.suite_name.length()!=0   ">
            and s.name like CONCAT('%',#{reqPar.suite_name},'%')
        </if>

        <if test="reqPar.flag_sell!=null and reqPar.flag_sell.size()!=0 ">
            and sku.flag_sell in
            <foreach collection="reqPar.flag_sell" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="reqPar.status!=null and reqPar.status.size()!=0 ">
            and sku.status in
            <foreach collection="reqPar.status" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </where>

</sql>

<select id="selectHotelReserveSku"   resultMap="rmHotelReserveSku" 
 parameterType="com.fmb.server2022.reqdomain.ReqHotelReserveSku"  >


    select
       u.xaid,
       sku.sku_id,
       s.suite_id,
           sku.sku_name ,
    sku.sort_value as  sku_sort_value ,
    s.name as suiteName,
       s.total_neight,
       s.flag_hotel_reserve,
       s.flag_standard_hotel,
       sku.reserve_choose_start_time,
       sku.reserve_choose_end_time,
       sku.sell_start_time,
       sku.sell_end_time,

       sku.market_price,
       sku.goods_price,
       sku.settle_price,
       sku.stock_num,

 
       u.create_uid,
       u.create_uid_name,
       u.ctime,
       u.last_update_uid,
       u.last_update_uid_name,
       u.utime,
       sku.flag_sell,
           sku.sell_num ,
       sku.status

from fmbx_hotel_reserve_sku sku
         left join fmbx_suite s on s.suite_id = sku.suite_id
         left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 2
    <include refid="HotelReserveSkuWhere"></include>
    order by   sku.sort_value desc , sku.sku_id desc

</select>




<resultMap id="rmHotelReserveSku" type="com.fmb.server2022.domain.HotelReserveSkuDomain">
    <result column="sku_id" property="skuId"></result>
    <result column="suite_id" property="suiteId"></result>

    <result column="sku_sort_value" property="skuSortValue"></result>

    <result column="suiteName" property="suiteName"></result>
    <result column="sku_name" property="skuName"></result>
    <result column="total_neight" property="totalNeight"></result>
    <result column="flag_hotel_reserve" property="flagHotelReserve"></result>
    <result column="flag_standard_hotel" property="flagStandardHotel"></result>
    <result column="reserve_choose_start_time" property="reserveChooseStartTime"></result>
    <result column="reserve_choose_end_time" property="reserveChooseEndTime"></result>
    <result column="sell_start_time" property="sellStartTime"></result>
    <result column="sell_end_time" property="sellEndTime"></result>
    <result column="market_price" property="marketPrice"></result>
    <result column="goods_price" property="goodsPrice"></result>
    <result column="settle_price" property="settlePrice"></result>
    <result column="stock_num" property="stockNum"></result>
    <result column="create_uid" property="createUid"></result>
    <result column="create_uid_name" property="createUidName"></result>
    <result column="ctime" property="ctime"></result>
    <result column="last_update_uid" property="lastUpdateUid"></result>
    <result column="last_update_uid_name" property="lastUpdateUidName"></result>
    <result column="utime" property="utime"></result>
    <result column="flag_sell" property="flagSell"></result>
    <result column="status" property="status"></result>
    <result column="sell_num" property="sellNum"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--房券sku列表 结束-->

        

<!--活动发布日志 开始-->


<sql id="ActivityPublishLogWhere">

    <where>
        <if test="reqPar.xaid!=null and reqPar.xaid != '' ">
            and xaid = #{reqPar.xaid}
        </if>
        <if test="reqPar.op_type!=null  ">
            and op_type = #{reqPar.op_type}
        </if>

        <if test="reqPar.ctime_start!=null">
            and ctime &gt; #{reqPar.ctime_start}
        </if>
        <if test="reqPar.ctime_end!=null">
            and ctime &lt; #{reqPar.ctime_end}
        </if>
        <if test="reqPar.create_uid!=null and reqPar.create_uid != '' ">
            and create_uid = #{reqPar.create_uid}
        </if>



    </where>

</sql>

<select id="selectActivityPublishLog"   resultMap="rmActivityPublishLog" 
 parameterType="com.fmb.server2022.reqdomain.ReqActivityPublishLog"  >


    select * from fmbx_activity_publish_log
    <include refid="ActivityPublishLogWhere"></include>

</select>


<resultMap id="rmActivityPublishLog" type="com.fmb.server2022.domain.ActivityPublishLogDomain">
    <result column="publish_log_id" property="publishLogId"></result>
    <result column="xaid" property="xaid"></result>
    <result column="ctime" property="ctime"></result>
    <result column="result" property="result"></result>
    <result column="create_uid" property="createUid"></result>
    <result column="op_type" property="opType"></result>
    <result column="create_uid_name" property="createUidName"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--活动发布日志 结束-->


    <select id="selctRoomInfoOfSuite" parameterType="int" resultType="map">
        select room_id,room_name from fmbx_bps_room where room_id in (
            select  distinct  room_id from  fmbx_suite_room_sku s where suite_id = #{suiteId}
        )

    </select>

    <select id="queryCategoryInfo" parameterType="map" resultType="com.fmb.server2022.fmbx.entity.FmbGoodsCategory">

        select cate_id,cate_name from fmb_goods_category
        where fmb_goods_category.cate_id in 

            <foreach collection="inList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        union
        select cate_id,cate_name from fmb_goods_category where parentid in
        <foreach collection="inList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="querySkuBuyNum" parameterType="map" resultType="int">


        select  ifnull(   sum(g.goods_number),0) from fmb_order_goods g left join fmb_order_info o
                                                                     on g.order_sn = o.order_sn
        where g.uid = #{reqPar.uid}
          and g.goods_id = #{reqPar.skuId}
          and o.order_status !=6

    </select>
        
</mapper>