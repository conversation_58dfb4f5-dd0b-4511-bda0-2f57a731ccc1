<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FrontSkuDao" >



<!--用户可用优惠券 开始-->


<sql id="FrontCouponWhere">

    <where>
    </where>

</sql>

<select id="selectFrontCoupon"   resultMap="rmFrontCoupon" 
 parameterType="map"  >


    SELECT
        (
        CASE
        fct.coupon_type
        WHEN 3 THEN
        2
        WHEN 4 THEN
        1 ELSE 0
        END
        )  rank,
        fuc.min_amount,
        fct.min_amount AS u_min_amount,
        fuc.discount_rate,
        fuc.use_status,
        fct.coupon_type,
        fct.coupon_name,
        fct.coupon_id,
        fct.plat_id,
        fct.limit_type,
        fct.limit_date,
        fct.limit_cate,
        
        fct.activity_id,
        fuc.coupon_sn,
        fct.use_limit,
        fuc.coupon_money,
        fuc.coupon_start_time,
        fuc.coupon_end_time,
        fuc.order_sn,
        fuc.use_time,

        fct.coupon_tag,
        fuc.distribute_time,
        fct.stacked
        FROM
        fmb_user_coupon fuc
        LEFT JOIN fmb_coupon_type fct ON fuc.coupon_id = fct.coupon_id
        WHERE
        fct.STATUS = 1
        AND fuc.uid = #{reqPar.uid}
        AND fuc.use_status = 0
        AND fuc.coupon_end_time &gt; now()

    <if test="reqPar.coupon_sn!=null  ">
        and fuc.coupon_sn  = #{reqPar.coupon_sn} limit 1
    </if>

    <if test="reqPar.coupon_sn==null ">
        ORDER BY rank DESC  ,
        IF
        (
        fuc.discount_rate &gt; 0,
        fuc.discount_rate,
        10
        ) ASC,
        fct.stacked DESC,
        fuc.coupon_money DESC,
        IF
        (
        fuc.distribute_time &gt; now(),
        fuc.distribute_time,
        0
        ) DESC,
        IF
        (
        fuc.coupon_end_time  &lt;  now(),
        DATE_ADD( now(), INTERVAL 10 YEAR ),
        fuc.coupon_end_time
        ) ASC,
        IF
        ( fct.limit_type = 2, 0, 1 ) ASC,
        fuc.distribute_time DESC,
        fuc.coupon_sn ASC
    </if>


</select>






<resultMap id="rmFrontCoupon" type="com.fmb.server2022.domain.FrontCouponDomain">
    <result column="rank" property="rank"></result>
    <result column="min_amount" property="minAmount"></result>
    <result column="u_min_amount" property="uMinAmount"></result>
    <result column="discount_rate" property="discountRate"></result>
    <result column="use_status" property="useStatus"></result>
    <result column="coupon_type" property="couponType"></result>
    <result column="coupon_name" property="couponName"></result>
    <result column="coupon_id" property="couponId"></result>
    <result column="plat_id" property="platId"></result>
    <result column="limit_type" property="limitType"></result>
    <result column="limit_date" property="limitDate"></result>
    <result column="limit_cate" property="limitCate"></result>
    <result column="activity_id" property="activityId"></result>
    <result column="coupon_sn" property="couponSn"></result>
    <result column="use_limit" property="useLimit"></result>
    <result column="coupon_money" property="couponMoney"></result>
    <result column="coupon_start_time" property="couponStartTime"></result>
    <result column="coupon_end_time" property="couponEndTime"></result>
    <result column="order_sn" property="orderSn"></result>
    <result column="use_time" property="useTime"></result>
    <result column="coupon_tag" property="couponTag"></result>
    <result column="distribute_time" property="distributeTime"></result>
    <result column="stacked" property="stacked"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--用户可用优惠券 结束-->

        

<!--订单操作记录 开始-->


<select id="selectAdminOrderAction"   resultMap="rmAdminOrderAction" 
 parameterType="map"  >


    select ac.order_sn, ac.order_status, ac.log_time, ac.action_note, ac.admin_uid,  ifnull(a.name,'前台用户') as name , ifnull(a.realname,'前台用户') as realname
from fmb_order_actions ac
         left join fmb_admin a on a.uid = ac.admin_uid 
where ac.order_sn = #{ordersn}
order by ac.action_id asc 

</select>





<resultMap id="rmAdminOrderAction" type="com.fmb.server2022.domain.AdminOrderActionDomain">
    <result column="order_sn" property="orderSn"></result>
    <result column="order_status" property="orderStatus"></result>
    <result column="log_time" property="logTime"></result>
    <result column="action_note" property="actionNote"></result>
    <result column="admin_uid" property="adminUid"></result>
    <result column="name" property="name"></result>
    <result column="realname" property="realname"></result>

    <!--<collection property="images" ofType="java.util.HashMap">-->

    <!--</collection>-->

</resultMap>

    <!--订单操作记录 结束-->

    <select id="queryBpsInfoBySkuId" resultType="com.fmb.server2022.fmbx.entity.FmbxBps">
        select address,phone from fmbx_bps where bps_id = (select bps_id from  fmbx_sku where sku_id = #{id}  )
    </select>


    <select id="queryHotelSellNum" resultType="map">
        select 1 as type , ifnull( sum(room_sku.sell_num),0 ) as sell_num  from fmbx_sku sku left join fmbx_suite_room_sku room_sku on sku.sku_id = room_sku.sku_id
        where sku.xaid = #{aid} and sku.sku_type  =1
        union all
        select 2 as type , ifnull(  sum(reserve_sku.sell_num),0 ) as sell_num  from fmbx_sku sku left join fmbx_hotel_reserve_sku reserve_sku on sku.sku_id = reserve_sku.sku_id
        where sku.xaid = #{aid} and sku.sku_type  =2

    </select>

    <select id="queryActivitySkuDateInfo" resultType="com.fmb.server2022.domain.stock.DatePrimeInfo" parameterType="map">


        select s.sku_id as skuId, date_format(k.target_date, '%Y-%m-%d') as dateCol
        from fmbx_sku s
                 left join fmbx_suite_room_sku r on r.sku_id = s.sku_id
                 left join  fmbx_suite_stock k on k.room_id = r.room_id
                 left join fmbx_suite_roomdate_price p on p.target_date = k.target_date and p.room_id = r.room_id and p.suite_room_sku_id = r.sku_id
        where s.xaid = #{reqPar.aid}
          and s.sku_type = 1
          and r.flag_sell = 1
          and r.status = 1
          and k.status = 1
          and k.stock_num > 0
          and p.price_id > 0
          and r.sku_id = #{reqPar.skuid}
          and k.target_date >= #{reqPar.dateStart}
          order by dateCol asc ,skuId asc

    </select>


    <select id="queryDateHotelInfoOfExists" parameterType="map" resultType="com.fmb.server2022.fmbx.entity.FmbxDateHotelInfo">

        select * from fmbx_date_hotel_info where datecol in


        <if test="reqPar.collect!=null and reqPar.collect.size()!=0 ">
            <foreach collection="reqPar.collect" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryDateHotelInfoOfDel" parameterType="map" resultType="com.fmb.server2022.fmbx.entity.FmbxDateHotelInfo">

        select * from fmbx_date_hotel_info where  datecol >= current_date
        <if test="reqPar.collect!=null and reqPar.collect.size()!=0 ">
           and datecol not in
            <foreach collection="reqPar.collect" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryHotelDateSku" parameterType="map" resultType="integer" >

        select distinct sku.sku_id
        from fmbx_suite_room_sku sku
                 left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
        WHERE u.xaid = #{reqPar.xaid}
          and sku.status = 1

        <if test="reqPar.containsKey('flag_sell')">
            and sku.flag_sell = #{reqPar.flag_sell}
        </if>


    </select>

    <select id="queryHotelDateSkuAndSuite" parameterType="map" resultType="map">
        select distinct sku.sku_id , sku.suite_id
        from fmbx_suite_room_sku sku
        left join fmbx_suite s on s.suite_id = sku.suite_id
        left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 1
        WHERE u.xaid = #{reqPar.xaid}
        and sku.status = 1
        and sku.flag_sell =1
        and s.flag_standard_hotel = 1
    </select>

    <select id="queryHotelReserveSku" parameterType="map" resultType="integer" >

        select distinct sku.sku_id
        from fmbx_hotel_reserve_sku sku
                 left join fmbx_suite s on s.suite_id = sku.suite_id
                 left join fmbx_sku u on u.sku_id = sku.sku_id and u.sku_type = 2
        WHERE u.xaid =  #{reqPar.xaid}
          and sku.status = 1

        <if test="reqPar.containsKey('flag_sell')">
            and sku.flag_sell = #{reqPar.flag_sell}
        </if>

          and s.flag_hotel_reserve = 1

    </select>

</mapper>