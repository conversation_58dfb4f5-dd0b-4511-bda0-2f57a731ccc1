<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FrontHotelDao" >


<!--前台酒店详情 -->
<select id="selectHotelDetail" resultType="com.fmb.server2022.domain.FrontHotelDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select b.name,if(b.open_time&gt;b.decorate_time,open_time,decorate_time) as open_time,if(b.open_time&gt;b.decorate_time,'开业','装修') as open_time_type,
  b.house_num,b.phone,b.star_level,c.introduce,c.policy,b.play_time,b.leave_time,c.food_info,b.take_pet,c.services
  from fmbx_activity a left join fmbx_bps b on a.bps_id=b.bps_id
  left join fmbx_bps_content c on a.bps_id=c.bps_id
  where a.xaid=#{reqPar.xaid} and a.flag_delete=0
</select>


<!--前台房型详情 -->
<select id="selectFrontRoomDetail" resultType="com.fmb.server2022.domain.FrontRoomDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select r.bps_id,r.room_id,sku.sku_id,sku.sku_name,sku.room_name,r.bed_type,s.night_min,r.build_area,r.floor_number,r.max_people_num,r.smoke_type,r.bathtub_type,
  s.suite_id,s.suite_content,e.buy_note
  from fmbx_suite_room_sku sku,fmbx_bps_room r,fmbx_suite s,fmbx_suite_ext e
  where sku.suite_id=s.suite_id and s.suite_id=e.suite_id and sku.room_id=r.room_id
  and sku.sku_id=#{reqPar.skuId}
  <if test="reqPar.type==6">
  and s.flag_standard_hotel=1
  </if>
  and sku.status=1 and sku.flag_sell=1
</select>
<!--房劵房型详情 -->
<select id="selectResRoomDetail" resultType="com.fmb.server2022.domain.FrontRoomDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select r.bps_id,r.room_id,sku.sku_id,sku.sku_name,sku.room_name,r.bed_type,r.build_area,r.floor_number,r.max_people_num,r.smoke_type,r.bathtub_type
  from fmbx_suite_room_sku sku,fmbx_bps_room r,fmbx_suite s
  where sku.suite_id=s.suite_id and sku.room_id=r.room_id
  and sku.suite_id=#{reqPar.suiteId} and s.flag_hotel_reserve=1 and sku.status=1 and sku.flag_sell=1
</select>
<!--房劵房型套餐 -->
<select id="selectResSuite" resultType="com.fmb.server2022.domain.FrontRoomSuiteDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select s.suite_id,s.name as suite_name,rsku.sku_name,s.suite_content,e.buy_note,rsku.goods_price,rsku.stock_num,rsku.each_order_max_num,rsku.each_order_min_num,rsku.flag_buy_limit,
  rsku.max_buy_all_num,date(rsku.reserve_choose_start_time) as reserve_choose_start_time,date(rsku.reserve_choose_end_time) as reserve_choose_end_time,s.night_min,s.total_neight,
  rsku.invalid_reserve_week_days,rsku.invalid_reserve_date,date(rsku.reserve_validity_start_time) as reserve_validity_start_time,date(rsku.reserve_validity_end_time) as reserve_validity_end_time
  from fmbx_hotel_reserve_sku rsku left join fmbx_suite s on rsku.suite_id=s.suite_id
  left join fmbx_hotel_reserve_sku_ext e on rsku.sku_id=e.sku_id
  left join fmbx_suite_room_sku sku on rsku.suite_id=sku.suite_id
  <where>
    <if test="reqPar.skuId!=null">
      and rsku.sku_id=#{reqPar.skuId}
    </if>
    <if test="reqPar.suiteId!=null">
      and rsku.suite_id=#{reqPar.suiteId}
    </if>
    and s.flag_hotel_reserve=1 and rsku.status=1 and rsku.flag_sell=1
    and sku.status=1 and sku.flag_sell=1
  </where>
</select>

<!--房型图片 -->
<select id="selectBpsMediaInfo" resultType="com.fmb.server2022.domain.BpsMediaInfoDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select m.url
  from fmbx_bps_media bm
  left join  fmbx_bps b on bm.bps_id = b.bps_id
  left join fmbx_media m on m.media_id = bm.media_id
  <where>
    <if test="reqPar.bpsId!=null">
      and bm.bps_id = #{reqPar.bpsId}
    </if>
    <if test="reqPar.roomId!=null">
      and bm.room_id = #{reqPar.roomId}
    </if>
    <if test="reqPar.roomIds!=null and reqPar.roomIds.size()!=0 ">
      and bm.room_id in
      <foreach collection="reqPar.roomIds" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and bm.type = 1
    order by bm.rankval asc
  </where>
</select>

<!--日历房日历查询 按sku-->
<select id="selectHotelSkuDatePrice" resultType="com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select d.datecol as date_str,
    d.holiday,
    ifnull(p.goods_price ,0) as goods_price ,
    ifnull(p.suite_id,0) as suite_id,
    ifnull(p.room_id,0) as room_id,
    ifnull(p.suite_room_sku_id,0) as sku_id ,
    ifnull(s.stock_num,0) as stock_num,
    ifnull(s.status,0) as stock_status
  from fmbx_date_table d
    left join fmbx_suite_roomdate_price p on d.datecol = p.target_date and p.suite_room_sku_id = #{reqPar.skuId}
    left join fmbx_suite_stock s on s.target_date = d.datecol and s.room_id = p.room_id
  where d.datecol between #{reqPar.priceStartDate} and #{reqPar.priceEndDate}
  order by d.datecol asc
</select>

<!--日历房日历查询 -->
<select id="selectHotelDatePrice" resultType="com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select d.datecol as date_str,
  d.holiday,
  ifnull(min(p.goods_price),0) as goods_price ,
  ifnull(sum(s.stock_num),0) as stock_num,
  if(ifnull(min(s.status),0)=1 &amp;&amp; ifnull(max(s.status),2)=1,1,2) as stock_status
  from fmbx_date_table d
  left join fmbx_suite_roomdate_price p on d.datecol = p.target_date
  <if test="reqPar.skuIds!=null and reqPar.skuIds.size()!=0 ">
    and p.suite_room_sku_id in
    <foreach collection="reqPar.skuIds" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </if>
  left join fmbx_suite_stock s on s.target_date = d.datecol and s.room_id = p.room_id
  where d.datecol between #{reqPar.priceStartDate} and #{reqPar.priceEndDate}
  group by d.datecol
  order by d.datecol asc
</select>

<select id="selectMinPrice" resultType="com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select min(goods_price) as min_goods_price,min(market_price) as min_market_price from fmbx_suite_roomdate_price
  <where>
    <if test="reqPar.priceStartDate!=null">
      and target_date >= #{reqPar.priceStartDate}
    </if>
    <if test="reqPar.priceEndDate!=null">
      and target_date &lt; #{reqPar.priceEndDate}
    </if>
    <if test="reqPar.skuIds!=null and reqPar.skuIds.size()!=0 ">
      and suite_room_sku_id in
      <foreach collection="reqPar.skuIds" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </where>
</select>

<!--酒店活动详情 -->
<select id="selectFrontActvHotel" resultType="com.fmb.server2022.domain.FrontActvHotelDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select a.xaid,a.bps_id,a.banner_list_url,a.flag_publish as status,b.star_level,if(open_time&gt;decorate_time,concat(open_time,'年开业'),concat(decorate_time,'年装修')) as open_time,
  a.title,a.sub_title,b.address,b.longitude,b.latitude,e.rich_txt as aid_content,a.ctime as start_time,a.ctime as end_time,m.url as video_url,m.video_snap_url as video_snap_url
  from fmbx_activity a left join fmbx_bps b on a.bps_id=b.bps_id
  left join fmbx_activity_ext e on a.xaid=e.xaid
  left join fmbx_media m on a.video_media_id=m.media_id
  where a.xaid=#{reqPar.xaid} and a.flag_delete=0
</select>

<!--预售房劵套餐列表 -->
<select id="selectFrontResSku" resultType="com.fmb.server2022.domain.FrontResSkuDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
    select rsku.sku_id,s.suite_id,rsku.sku_name as suite_name,s.night_min,s.suite_content,rsku.goods_price,rsku.market_price,rsku.sell_num,
    rsku.sell_count_down_show_flag,rsku.sell_start_time,rsku.sell_end_time,rsku.reserve_choose_start_time,rsku.reserve_choose_end_time,
    group_concat(sku.room_id) as room_ids,rsku.sort_value,sku.flag_sell,sku.status
from fmbx_hotel_reserve_sku rsku left join fmbx_suite s on rsku.suite_id=s.suite_id
left join fmbx_suite_room_sku sku on rsku.suite_id=sku.suite_id
where s.xaid=#{reqPar.xaid} and rsku.status=1 and rsku.flag_sell=1 and s.flag_hotel_reserve=1
and now() between rsku.sell_start_time and rsku.sell_end_time
and sku.flag_sell=1 and sku.status=1
group by rsku.sku_id
order by rsku.sort_value desc
</select>

<!--日历房套餐列表 -->
<select id="selectFrontRoomSku" resultType="com.fmb.server2022.domain.FrontRoomSkuDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
    select sku.suite_id,sku.sku_id,s.name as suite_name,s.night_min,sku.room_id,sku.room_name,r.build_area,r.bathtub_type,r.max_people_num,r.bed_type,s.suite_content,sku.sell_num,sku.sort_value
from fmbx_suite_room_sku sku left join fmbx_suite s on sku.suite_id=s.suite_id
left join fmbx_bps_room r on sku.room_id=r.room_id
where s.xaid=#{reqPar.xaid} and sku.status=1 and sku.flag_sell=1
<if test="reqPar.type==6">
and s.flag_standard_hotel=1
</if>
order by s.sort_value desc, sku.sort_value desc
</select>

<!--日历房提交订单sku信息 -->
<select id="selectFrontRoomSkuBySkuId" resultType="com.fmb.server2022.domain.FrontRoomSkuOrderDomain" parameterType="com.fmb.server2022.controller.front.reqdomain.ReqHotel"  >
  select a.xaid,a.title,s.suite_id,sku.sku_id,s.name as suite_name,s.night_min,sku.room_id,sku.room_name,r.build_area,r.bathtub_type,r.max_people_num,r.bed_type,s.suite_content,se.buy_note,bc.policy,s.flag_risk_warning,s.risk_warning_tip,sku.sell_num,sku.sort_value
  from fmbx_suite_room_sku sku left join fmbx_suite s on sku.suite_id=s.suite_id
  left join fmbx_bps_room r on sku.room_id=r.room_id
  left join fmbx_suite_ext se on se.suite_id=s.suite_id
  left join fmbx_activity a on a.xaid=s.xaid
  left join fmbx_bps_content bc on a.bps_id=bc.bps_id
  where sku.sku_id=#{reqPar.skuId} and sku.status=1 and sku.flag_sell=1
</select>




<!--日历房最低价查询 开始-->



<select id="selectHotelDateMinPriceOne"   resultMap="rmHotelDateMinPrice" 
 parameterType="map"  >


    select a.price_id,b.suite_room_sku_id ,b.goods_price,b.market_price from (
select  p.price_id,  min(p.goods_price) as sku_min_price
from fmbx_suite_room_sku sku
         left join fmbx_suite_roomdate_price p on p.suite_room_sku_id = sku.sku_id
         left join fmbx_suite s on   s.suite_id =sku.suite_id
         left join fmbx_bps_room r on sku.room_id = r.room_id

where s.xaid = #{reqPar.aid}
  and sku.status = 1
  and sku.flag_sell = 1
  and s.flag_standard_hotel = 1
group by p.price_id
order by  sku_min_price asc limit 1 ) a left join fmbx_suite_roomdate_price b on b.price_id = a.price_id

</select>

<resultMap id="rmHotelDateMinPrice" type="com.fmb.server2022.domain.HotelDateMinPriceDomain">
    <result column="price_id" property="priceId"></result>
    <result column="suite_room_sku_id" property="suiteRoomSkuId"></result>
    <result column="goods_price" property="goodsPrice"></result>
    <result column="market_price" property="marketPrice"></result>
</resultMap>

    <!--日历房最低价查询 结束-->

        

<!--房券最低价查询 开始-->



<select id="selectHotelReserveMinPriceOne"   resultMap="rmHotelReserveMinPrice" 
 parameterType="map"  >


    select rsku.sku_id,

       rsku.goods_price,
       rsku.market_price

from fmbx_hotel_reserve_sku rsku
         left join fmbx_suite s on rsku.suite_id = s.suite_id
         left join fmbx_suite_room_sku sku on rsku.suite_id = sku.suite_id
where s.xaid = #{reqPar.aid}
  and rsku.status = 1
  and rsku.flag_sell = 1
  and s.flag_hotel_reserve = 1
order by rsku.goods_price asc ,rsku.sku_id limit 1

</select>




<resultMap id="rmHotelReserveMinPrice" type="com.fmb.server2022.domain.HotelReserveMinPriceDomain">
    <result column="sku_id" property="skuId"></result>
    <result column="goods_price" property="goodsPrice"></result>
    <result column="market_price" property="marketPrice"></result>

</resultMap>

    <!--房券最低价查询 结束-->

        
</mapper>