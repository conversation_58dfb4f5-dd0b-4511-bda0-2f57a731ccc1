<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.NewActivityDao" >

    <select id="selectActivityTagList" resultType="com.fmb.server2022.domain.ActivityTagListDomain" >
        select l.id as tag_list_id,l.tag_name,l.cat_id as tag_cat_id,c.cate_name from fmb_activity_tag_list l
        left join fmb_activity_tag_category c on l.cat_id=c.id
        where l.`type`=1 and l.`status`=1
    </select>
</mapper>