<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FmbxBpMediaDao" >


<select id="getGroupNumByBpsId" resultType="Map">
    select count(id) as num,bps_id,groupval from  fmbx_bps_media where bps_id=#{bps_id} and type=1 group by groupval desc
</select>


    <select id="getImgListByBpsIdAndGroupNum" resultType="Map">
        select distinct m.media_id,m.media_id as mediaId,m.url,bm.description,m.file_ext_type
        from  fmbx_bps_media as bm
            left join fmbx_media as m on m.media_id=bm.media_id
        where bm.bps_id=#{bpsId} and groupval=#{groupval} and bm.type=1
    </select>

    <select id="getFrontImgListByBpsId" resultType="com.fmb.server2022.domain.FrontBpsMediaDomain">
        select m.media_id,m.url,m.video_snap_url,m.file_ext_type,bm.description,bm.type,bm.groupval from  fmbx_bps_media as bm
        left join fmbx_media as m on m.media_id=bm.media_id where bm.bps_id=#{bpsId}
        and bm.status = 1
        order by groupval desc,rankval
    </select>

    <select id="queryBpsVideos" resultType="map">

        select m.media_id as mediaId, bp.description, m.video_snap_url as videoSnapUrl,m.url,m.length
        from fmbx_bps_media bp, fmbx_media m
        where bp.media_id = m.media_id
          and bp.bps_id = #{bpsId}
          and bp.type = 2
          and bp.status = 1
          order by m.media_id desc
    </select>

</mapper>