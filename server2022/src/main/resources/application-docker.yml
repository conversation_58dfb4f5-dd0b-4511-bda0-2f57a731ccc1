server:
  port: 8888

spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:cns}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true
          username: ${DB_USER:root}
          password: ${DB_PASSWORD:cns!@#$1235}
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            filters: stat,wall,slf4j
  
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: 
    database: 0
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:rocketmq-namesrv:9876}
    consumer:
      bootstrap-servers: ${KAFKA_SERVERS:rocketmq-namesrv:9876}
      group-id: fmb-consumer-group
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      bootstrap-servers: ${KAFKA_SERVERS:rocketmq-namesrv:9876}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

  session:
    store-type: redis
    redis:
      flush-mode: on_save
      namespace: spring:session

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.fmb.server2022.fmbx.entity,com.fmb.server2022.fmb_new.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.fmb: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# FMB 自定义配置
fmb:
  fmbImgUrl: http://localhost/images/
  fmbImgDir: /var/www/html/images/
  ffmpegPath: /usr/bin/ffmpeg
  uploadPath: /tmp/uploads/
  tempPath: /tmp/
  downPrefix: http://localhost/download/
  serviceFax: ************
  serviceTel: ************
  categoryChild: 1
  categoryLong: 2
  categoryShow: 3
  categoryTravel: 4
  categoryBuy: 5