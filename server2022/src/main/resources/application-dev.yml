spring:
    cache:
          type: redis
    kafka:
        producer:
            bootstrap-servers: *************:9092,*************:9092,*************:9092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
            acks: 1
            properties:
                linger.ms: 10
            batch-size: 16384
        consumer:
            bootstrap-servers: *************:9092,*************:9092,*************:9092
            enable-auto-commit: false
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            fetch-max-wait: 100

    redis:
        host: **************
        port: 6380
        password: gb7MIpgOOP7J
        timeout: 2000
        database: 4
        jedis:
            pool:
                max-active: 30
                max-idle: 2
    datasource:
        druid:
            stat-view-servlet:
                enabled: true
                loginUsername: dradmin
                loginPassword: FmbDr2022
        dynamic:
#            p6spy: true
            primary: master
            druid:
                initial-size: 5
                max-active: 20
                min-idle: 2
                test-on-borrow: true
                validation-query: select 1
            datasource:
                master:
                    username: fmb
                    password: 123456
                    driver-class-name: com.mysql.jdbc.Driver
                    url: ************************************************************************************************************************************************
                slave:
                    username: fmb_r
                    password: 123456
                    driver-class-name: com.mysql.jdbc.Driver
                    url: ************************************************************************************************************************************************
logging:
    config: classpath:logback-spring-dev.xml
    level:
      org.apache.kafka.clients: warn

fmb:
    #excel 下载前缀
    downPrefix: http://**************:7201
    #log 地址
    logPath: /opt/excelOutputDir/log
    fmbImgDir: /home/<USER>/fmb.photo/jupload/
    fmbImgUrl: http://img3.fumubang.net/jupload
    categoryLong: 95
    categoryShow: 27
    categoryChild: 17
    categoryTravel: 45
    categoryBuy: 20
    #ffmpeg路径
    ffmpegPath: /usr/bin/ffmpeg
    serviceTel: ************
    serviceFax: 021-53085657
    fmbKefuUrl: http://192.168.29.211:8181

