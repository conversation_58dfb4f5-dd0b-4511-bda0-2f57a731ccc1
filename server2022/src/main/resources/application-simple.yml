server:
  port: 8070

spring:
  cache:
    type: redis
    
  # 完全禁用Kafka自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
      
  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    database: 0
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      primary: master
      druid:
        initial-size: 5
        max-active: 20
        min-idle: 2
        test-on-borrow: true
        validation-query: SELECT 1
      datasource:
        master:
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************************************
        slave:
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************************************

  # 最小化Kafka配置以满足FmbServerConfig和应用需求
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      bootstrap-servers: localhost:9092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: localhost:9092
      group-id: fmb-simple-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

logging:
  level:
    # 降低Kafka相关日志级别，减少干扰
    org.apache.kafka: ERROR
    org.springframework.kafka: ERROR
    com.fmb: debug
    org.springframework.web: info
    org.mybatis: info  
    root: info

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.fmb.server2022.fmbx.entity,com.fmb.server2022.fmb_new.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# FMB 自定义配置
fmb:
  fmbImgUrl: http://localhost:8070/images/
  fmbImgDir: /tmp/fmb/images/
  uploadPath: /tmp/fmb/uploads/
  tempPath: /tmp/fmb/temp/
  downPrefix: http://localhost:8070/download/
  logPath: /tmp/fmb/logs/
  ffmpegPath: /usr/bin/ffmpeg
  serviceFax: ************
  serviceTel: ************
  categoryChild: 1
  categoryLong: 2
  categoryShow: 3
  categoryTravel: 4
  categoryBuy: 5
  fmbKefuUrl: http://localhost:8070/mock/kefu

# 健康检查
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always