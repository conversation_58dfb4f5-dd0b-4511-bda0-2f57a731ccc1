<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">

    <springProperty scope="context" name="logPath" source="fmb.logPath"/>
    <springProperty scope="context" name="port" source="server.port"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>

    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logPath}/server2022/server2022.%d{yyyy-MM-dd}_${port}.log</FileNamePattern>
            <MaxHistory>100</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %X{traceId} %msg%n</pattern>
        </encoder>

    </appender>


    <appender name="importantInfo"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logPath}/server2022/server2022ImportantInfo.%d{yyyy-MM-dd}_${port}.log</FileNamePattern>
            <MaxHistory>100</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %L %logger{50} - %X{traceId} %msg%n</pattern>
        </encoder>

    </appender>


    <appender name="asyncFileLogger" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="importantInfo" />
    </appender>


    <logger name="importantInfoLogger" level="info">
        <appender-ref ref="asyncFileLogger"></appender-ref>
    </logger>


    <root level="warn">
        <appender-ref ref="FILE" />
        <!--<appender-ref ref="STDOUT" />-->
    </root>
</configuration>
