<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.UserDao">

    <sql id="userlist_where" >
        <where>
            <if test="status !=null">
                and  status = #{status}
            </if>
            <if test=" keyword!='' and keyword  !=null">
                and ( username like  CONCAT('%',#{keyword},'%') or  nickname like  CONCAT('%',#{keyword},'%')  )
            </if>
        </where>
    </sql>
    
    <select id="countUser" resultType="Integer" parameterType="com.fmb.server2022.reqdomain.ReqListUser" >
        SELECT count(*)
        FROM sys_user

        <include refid="userlist_where"></include>

    </select>

    <resultMap id="userMap" type="com.fmb.server2022.domain.One2Many">
        <id column="userId" property="userId"/>
        <result column="username" property="username"/>
        <result column="nickname" property="nickname"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="status" property="status"/>
        <collection property="roles" ofType="com.alibaba.fastjson.JSONObject">
            <id column="roleId" property="roleId"/>
            <result column="roleName" property="roleName"/>
        </collection>
    </resultMap>
    <select id="listUser" resultMap="userMap" parameterType="com.fmb.server2022.reqdomain.ReqListUser">
        SELECT u.*,
               r.id        roleId,
               r.role_name roleName
        FROM (
                 SELECT id                                      userId,
                        username                                username,
                        nickname                                nickname,
                        status                           ,
                        DATE_FORMAT(create_time, '%Y.%m.%d %T') createTime,
                        DATE_FORMAT(update_time, '%Y.%m.%d %T') updateTime
                 FROM sys_user

              <include refid="userlist_where"></include>


                 ORDER BY id
                 LIMIT #{start}, #{size}
             ) u
                 LEFT JOIN sys_user_role ur on ur.user_id = u.userId
                 LEFT JOIN sys_role r ON r.id = ur.role_id
        ORDER BY u.userId, r.id
    </select>

    <delete id="removeUserAllRole">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>

    <insert id="batchAddUserRole">
        insert into sys_user_role
        (user_id, role_id) values
        <foreach collection="roleIds" item="i" separator=",">
            (#{userId},#{i})
        </foreach>
    </insert>

    <select id="getAllRoles" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id        roleId,
               role_name roleName
        FROM sys_role
        WHERE status = 1
    </select>

    <insert id="addUser" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO sys_user
            (username, password, nickname)
        VALUES (#{username}, #{password}, #{nickname})
    </insert>

    <update id="updateUser">
        UPDATE sys_user
        SET
        nickname = #{nickname}
        <if test="password !='' and password !=null">
            , password = #{password}
        </if>
        , status = #{deleteStatus}
        WHERE id = #{userId} and id != 10001
    </update>
    <resultMap id="roleMap" type="com.fmb.server2022.domain.One2Many">
        <id column="roleId" property="roleId"/>
        <result column="roleName" property="roleName"/>
        <collection property="users" ofType="com.alibaba.fastjson.JSONObject">
            <id column="userId" property="userId"/>
            <result column="nickname" property="nickname"/>
        </collection>
        <collection property="menus" ofType="com.fmb.server2022.domain.One2Many">
            <id column="menuCode" property="menuCode"/>
            <result column="menuName" property="menuName"/>
            <collection property="permissions" ofType="com.alibaba.fastjson.JSONObject">
                <id column="permissionId" property="permissionId"/>
                <result column="permissionName" property="permissionName"/>
            </collection>
        </collection>
    </resultMap>
    <select id="listRole" resultMap="roleMap">
        SELECT r.id              roleId,
               r.role_name       roleName,
               u.id              userId,
               u.nickname,
               p.id              permissionId,
               p.menu_code       menuCode,
               p.menu_name       menuName,
               p.permission_name permissionName
        FROM sys_role r
                 LEFT JOIN sys_user_role ur on ur.role_id = r.id
                 LEFT JOIN sys_user u ON u.id = ur.user_id AND u.status = 1
                 LEFT JOIN sys_role_permission rp ON r.id = rp.role_id AND rp.status = 1
                 LEFT JOIN sys_permission p ON rp.permission_id = p.id
        WHERE r.status = 1
        ORDER BY r.id, p.id
    </select>
    <resultMap id="permissionMap" type="com.fmb.server2022.domain.One2Many">
        <id column="menuName" property="menuName"/>
        <collection property="permissions" ofType="com.alibaba.fastjson.JSONObject">
            <id column="id" property="id"/>
            <result column="permissionName" property="permissionName"/>
            <result column="requiredPerm" property="requiredPerm" javaType="Integer"/>
        </collection>
    </resultMap>
    <select id="listAllPermission" resultMap="permissionMap">
        SELECT p.id                  id,
               p.menu_name           menuName,
               p.permission_name     permissionName,
               p.required_permission requiredPerm
        FROM sys_permission p;
    </select>



    <resultMap id="aRole" type="com.fmb.server2022.domain.One2Many">
        <id column="roleId" property="roleId"/>
        <result column="roleName" property="roleName"/>
        <collection property="users" ofType="com.alibaba.fastjson.JSONObject">
            <id column="userId" property="userId"/>
        </collection>
        <collection property="permissionIds" ofType="Integer">
            <id column="permissionId" property="permissionId"/>
        </collection>
    </resultMap>
    <select id="getRoleAllInfo" resultMap="aRole">
        SELECT r.id        roleId,
               r.role_name roleName,
               ur.user_id  userId,
               p.id        permissionId
        FROM sys_role r
                 LEFT JOIN sys_user_role ur ON r.id = ur.role_id
                 LEFT JOIN sys_role_permission rp ON r.id = rp.role_id AND rp.status = 1
                 LEFT JOIN sys_permission p ON rp.permission_id = p.id
        WHERE r.id = #{roleId}
    </select>

    <update id="removeRole">
        UPDATE sys_role
        SET status = 0
        WHERE id = #{roleId}
          and id != 1
    </update>

    <update id="updateRoleAllPermission">
        UPDATE sys_role_permission
        SET status = #{status}
        WHERE role_id = #{roleId}
    </update>

    <update id="removeOldPermission">
        UPDATE sys_role_permission
        SET
        status = 0
        WHERE role_id = #{roleId}
        AND permission_id in (
        <foreach collection="permissions" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>
    <update id="updateRoleName">
        UPDATE sys_role
        SET role_name = #{roleName}
        WHERE id = #{roleId}
    </update>
    <select id="queryExistUsername" resultType="int">
        select count(*)
        from sys_user
        WHERE username = #{username}
          AND status = 1;
    </select>


    <select id="selectHavePermissionUsers" parameterType="int">

select distinct  u.id  from sys_user u ,
              sys_role r,
              sys_user_role ur ,
              sys_role_permission rp
where
      rp.permission_id = #{permissionid}
and rp.status = 1
and rp.role_id =  r.id
and r.id = ur.role_id
and u.id = ur.user_id
and u.status = 1

    </select>
<select id="selectListPermissions" parameterType="int" resultType="integer">
select distinct p.id
from sys_user u,
     sys_role r,
     sys_user_role ur,
     sys_role_permission rp,
     sys_permission p
where rp.status = 1
  and rp.role_id = r.id
  and r.id = ur.role_id
  and u.id = ur.user_id
  and u.status = 1
  and p.id = rp.permission_id
  and p.required_permission = 1
  and u.id = #{userid}

</select>



</mapper>
