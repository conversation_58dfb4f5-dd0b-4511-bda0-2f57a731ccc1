<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fmb.server2022.mapper.FmbLoginDao">


    <select id="selectFmbAdmin" parameterType="string" resultType="map">
          select * from fmb_admin a where a.name = #{arg0} and disabled = 0 ;
    </select>


    <select id="selectUsers" resultType="com.fmb.server2022.domain.FmbAdminUsers" parameterType="map" >
        select
a.uid,a.realname, a.name,a.phone,a.email,d.department_name,a.department_id
from fmb_admin a left join  fmb_department_list  d on a.department_id = d.department_id
where a.disabled = 0
        <if test="pars.containsKey('uid')">
          and a.uid = #{pars.uid}
        </if>

    </select>
</mapper>
