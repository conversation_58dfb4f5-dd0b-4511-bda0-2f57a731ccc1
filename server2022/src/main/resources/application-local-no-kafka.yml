server:
  port: 8070

spring:
  cache:
    type: redis
    
  # 只禁用Kafka自动配置，但保留必要配置以满足FmbKafkaConfig
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
      
  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    database: 0
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      # p6spy: true  # 开启SQL日志（可选）
      primary: master
      druid:
        initial-size: 5
        max-active: 20
        min-idle: 2
        test-on-borrow: true
        validation-query: SELECT 1
      datasource:
        master:
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************************************
        slave:
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************************************

  # Kafka配置（本地调试用mock配置，满足FmbServerConfig和FmbKafkaConfig要求）
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      bootstrap-servers: localhost:9092
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: localhost:9092
      group-id: fmb-local-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

logging:
  level:
    com.fmb: debug
    org.springframework.web: debug
    org.mybatis: debug  
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.fmb.server2022.fmbx.entity,com.fmb.server2022.fmb_new.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# FMB 自定义配置
fmb:
  # 图片相关配置
  fmbImgUrl: http://localhost:8070/images/
  fmbImgDir: /tmp/fmb/images/
  
  # 文件路径配置
  uploadPath: /tmp/fmb/uploads/
  tempPath: /tmp/fmb/temp/
  downPrefix: http://localhost:8070/download/
  logPath: /tmp/fmb/logs/
  
  # 工具配置
  ffmpegPath: /usr/bin/ffmpeg
  
  # 服务联系方式
  serviceFax: ************
  serviceTel: ************
  
  # 业务分类配置
  categoryChild: 1
  categoryLong: 2
  categoryShow: 3
  categoryTravel: 4
  categoryBuy: 5
  
  # 客服系统URL（本地调试使用mock地址）
  fmbKefuUrl: http://localhost:8070/mock/kefu
  
# 开发环境健康检查
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,beans
  endpoint:
    health:
      show-details: always