



LambdaQueryWrapper<HotelDateSku> wraper = new LambdaQueryWrapper<>(HotelDateSku.class)

        // 活动主表id
        
        // 
.eq(null!=par.getInteger("skuId"),  HotelDateSku::getSkuId,par.getInteger("skuId"))
        
        // 套餐id
.eq(null!=par.getInteger("suiteId"),  HotelDateSku::getSuiteId,par.getInteger("suiteId"))
        
        // 商户房型id
.eq(null!=par.getInteger("roomId"),  HotelDateSku::getRoomId,par.getInteger("roomId"))
        
        // 创建人uid
        
        // 创建时间
        
        // 售买状态:0-无效（无可售日期）,1-有效
.eq(null!=par.getInteger("flagSell"),  HotelDateSku::getFlagSell,par.getInteger("flagSell"))
        
        // 上下架状态:0-已删除,1-已上架,2-已下架
.eq(null!=par.getInteger("status"),  HotelDateSku::getStatus,par.getInteger("status"))
        

 ;

############APIPOST请求
{
        //活动主表id
        
        //
        
"skuId":1,
        //套餐id
        
"suiteId":1,
        //商户房型id
        
"roomId":1,
        //创建人uid
        
        //创建时间
        
        //售买状态:0-无效（无可售日期）,1-有效
        
"flagSell":1,
        //上下架状态:0-已删除,1-已上架,2-已下架
        
"status":1,
}


apipost 
{
        //活动主表id

    "xaid":"活动主表id" , 
        //
    "sku_id":1 ,  
        //套餐id
    "suite_id":1 ,  
        //商户房型id
    "room_id":1 ,  
        //创建人uid

    "create_uid":"创建人uid" , 
        //创建时间

    "ctime":"创建时间" , 
        //售买状态:0-无效（无可售日期）,1-有效
    "flag_sell":1 ,  
        //上下架状态:0-已删除,1-已上架,2-已下架
    "status":1 ,  
}





############

@Data
public Class ReqHotelDateSku{
        //活动主表id
        //
    private  Integer skuId;
        //套餐id
    private  Integer suiteId;
        //商户房型id
    private  Integer roomId;
        //创建人uid
        //创建时间
        //售买状态:0-无效（无可售日期）,1-有效
    private  Integer flagSell;
        //上下架状态:0-已删除,1-已上架,2-已下架
    private  Integer status;
}
------------------------controller方法

    @PostMapping(value = "/hotelDateSku")
    public FmbRespBean hotelDateSku(@RequestBody ReqHotelDateSku par) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        xxxService.selectHotelDateSku(par, resultMap);


        return FmbRespBean.success(resultMap);
    }


