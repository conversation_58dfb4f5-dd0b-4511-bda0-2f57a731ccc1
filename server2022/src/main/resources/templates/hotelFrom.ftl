<style type="text/css">
    *{ margin:0; padding:0; }
    .form-preview *{ margin:0; padding:0; }
    .form-preview{ position:relative; width:640px; margin:0 auto!important; font-size:12px; font-family:"Microsoft Yahei"; overflow:hidden; }
    .form-preview .code{ display:block; width:200px; height:200px; }
    .form-preview .code-top{ position:absolute; top:0px; right:0; }
    .form-preview .code-bottom{ position:absolute; bottom:0; left:0; }
    .form-preview h1{ margin:0; padding:0; font-size:20px; line-height:2; margin:20px 0 0 0; font-weight:bold; }
    .form-preview h2{ margin:0; padding:0; font-size:18px; line-height:2; }
    .form-preview h3{ margin:0; padding:0; font-size:14px; line-height:2; font-weight:bold; }
    .form-preview p{ font-size:14px; line-height:1.5; }
    .form-preview table{ border-collapse:collapse; }
    .form-preview th,.form-preview td{ text-align:center; line-height:2; font-weight:bold; vertical-align:top; }
    .form-preview th{ border-bottom:#333 solid 2px; }
    .form-preview td p{ margin:5px 0 0 0; line-height:1.5; }
    .form-preview .seat{ display:inline-block; width:12px; }
    .form-preview .seat24{ display:inline-block; width:24px; }
    .form-preview .seat240{ display:inline-block; width:240px; }
    .form-preview .f20{ font-size:16px; }
    .form-preview .bc{ background:#efefef; }
    .form-preview .tIndet{ margin-left:65px; text-indent:6px; }
    .form-preview .b{font-weight:bold; }
    .form-preview table.line1 th,.form-preview table.line1 td{ line-height:1; }
    .mt60{ margin-top:60px; }
    .mt15{ margin-top:15px; }
    .table{ margin:10px 0; border-collapse:collapse; }
    .table th,.table td{ border:#7d7d7d solid 1px; word-break:break-all; }
    .table th{ line-height:2.6; background:#ddd; }
    .table td{ padding:10px 0; font-size:14px; line-height:1.5; font-weight:normal; vertical-align:middle; }
    .table td .box{ display:inline-block; width:12px; height:12px; min-width:auto!important; border:#6b6b6b solid 1px; position:relative; top:2px; }
    .group{ overflow:hidden; margin-top:5px; border-top:#7d7d7d solid 1px; }
    .group .table{ margin:0; }
    .fl{ float:left; }
    .fr{ float:right; }
    .group > p{ margin-top:5px; text-align:justify; }
    .group .table{ width:100%; margin-top:5px; }
    .group td{ padding:5px 0; border:none; vertical-align:middle; text-align:center; }
    .group td:first-child{ text-align:left; }
    .group td:last-child{ text-align:right; }
    .footer{ overflow:hidden; margin:0 0 5px 0; }
    .footer img{ float:left; }
    .footer .text{ float:left; margin-left:30px; }
    .footer .text p{ font-size:16px; line-height:2; }
    .footer .text p:nth-child(1){ margin-top:20px; }
    .footer .text p:nth-child(3){ margin-top:30px; }
    p.h1{ font-size:16px; line-height:1.8; }
    .margin0{ margin:0!important; }
    .mt5{ margin:5px 0 5px 0; }
    .form-preview p.ft12{ font-size:12px; }
    .form-preview table.ft12 td{ font-size:12px; }
    table td.tl{ text-align:left; }
    .b{ font-weight:bold; }
</style>
<div class="form-preview">
    <!--img src="" class="code code-top" alt="二维码" /-->
    <h1>父母邦订房单</h1>
    <h2>订房单类型：${dfdStatusStr}</h2>
    <p>酒店名称：${hotelBasicInfo.name}</p>
    <p>联系电话：${bpParterHotelInfo.hotelConfirmContractPhone}</p>
    <p>订房传真：${bpParterHotelInfo.hotelConfirmContractFax}</p>
    <#--    {{if(data.is_show_seal)}}-->
    <#--    <div style="position:absolute;top:54px;left:25px;"><img src="<?=$value['img_sign_company']?>" style="width:150px;height: 170px;position: relative;right:20px;top:-56px;"></div>-->
    <#--    {{end}}-->
    <p class="mt15">订单号：${orderInfo.orderSn}</p>
        <p>套餐名：${goodsInfos[0].suiteName}</p>
        <p>入住人：${orderInfo.receiver}</p>
        <p>联系电话：${orderInfo.mobile}</p>
        <table width="100%" class="table mt5 ft12">
            <thead>
            <tr>
                <th>入离日期</th>
                <th>房型床型</th>
                <!--th>套餐份数</th-->
                <th>房间数</th>
                <th>父母邦售价</th>
                <th>结算价</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td width="150">入住：${goodsList[0].playTime?date('yyyy-MM-dd')}<br>离店：${goodsList[0].leaveTime?date('yyyy-MM-dd')}</td>
                <td width="100">${suiteInfo.room.roomName}(${suiteInfo.room.bedTypeStr})</td>
                <!--td width="85">${goodsList[0].goodsNumber}份</td-->
                <td width="85">${goodsInfos[0].roomNum}间房</td>
                <td width="120"><#if goodsList[0].goodsPrice?? && goodsList[0].goodsPrice!=0>${goodsList[0].goodsPrice} X ${goodsInfos[0].roomNum}</#if></td>
                <td width="120">${goodsList[0].settlePrice} X ${goodsInfos[0].roomNum}</td>
            </tr>
            </tbody>
        </table>
        <#if ticketComment?? && ticketComment!="">
        <p class="ft12">套餐描述：${ticketComment}</p>
        </#if>
        <div class="group">
            <p class="ft12"><span class="b">客户备注：</span>${orderInfo.postscript}</p>
            <table class="table">
                <tbody>
                <tr>
                    <td><span class="box"></span> 满足</td>
                    <td><span class="box"></span> 尽量满足</td>
                    <td><span class="box"></span> 无法满足</td>
                </tr>
                </tbody>
            </table>
        </div>
    <div class="group margin0">
        <p class="ft12"><span class="b">父母邦备注：</span>${fmbComment}</p>
        <table class="table">
            <tbody>
            <tr>
                <td><span class="box"></span> 本单确认</td>
                <td class="tl">确认号：</td>
                <td>确认人：</td>
                <td><span class="box"></span> 本单不确认</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="footer">
        <!--img src="" class="code" alt="二维码" /-->
        <div class="text">
            <p>父母邦客服电话：${serviceTel}</p>
            <p>传真号码：${serviceFax}</p>
            <p>工号：${adminName}</p>
            <p>时间：${adminDate}</p>
        </div>
    </div>
</div>