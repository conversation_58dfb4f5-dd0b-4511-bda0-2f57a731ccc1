spring:
    cache:
          type: redis
    kafka:
        producer:
            bootstrap-servers: *************:9092,*************:9092,*************:9092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
            acks: 1
            properties:
                linger.ms: 10
            batch-size: 16384
        consumer:
            bootstrap-servers: *************:9092,*************:9092,*************:9092
            enable-auto-commit: false
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            fetch-max-wait: 100
    redis:
        host: ***********
        port: 6379
        password: gb7MIpgOOP7J
        timeout: 2000
        database: 4
        jedis:
            pool:
                max-active: 30
                max-idle: 2
    datasource:
        druid:
            stat-view-servlet:
                enabled: true
                loginUsername: dradmin
                loginPassword: FmbDr2022
        dynamic:
            p6spy: false
            primary: master
            druid:
                initial-size: 5
                max-active: 40
                min-idle: 5
                test-on-borrow: true
                validation-query: select 1
                keep-alive: true

            datasource:
                master:
                    username: fmb
                    password: 2w@p2
                    driver-class-name: com.mysql.jdbc.Driver
                    url: **************************************************************************************************************************************************
                slave:
                    username: fmb_r
                    password: sdjklsajl$%
                    driver-class-name: com.mysql.jdbc.Driver
                    url: *************************************************************************************************************************************************
logging:
    config: classpath:logback-spring-pro.xml
    level:
      org.apache.kafka.clients: warn

fmb:
    #excel 下载前缀
    downPrefix: http://img3.fumubang.com/xupload
    #log 地址
    logPath: /home/<USER>/javaProject/log
    fmbImgDir: /home/<USER>/fmb.photo/xupload/
    fmbImgUrl: http://img3.fumubang.com/xupload
    categoryLong: 103
    categoryShow: 37
    categoryChild: 36
    categoryTravel: 40
    categoryBuy: 38
    #ffmpeg路径
    ffmpegPath: /usr/bin/ffmpeg
    serviceTel: ************
    serviceFax: 021-53085657
    fmbKefuUrl: http://kefu.fumubang.com

