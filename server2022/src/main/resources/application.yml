server:
    port: 8070
    servlet:
        session:
          cookie:
            name: fmbxuid
        context-path: /fmbx



spring:

    freemarker:
      suffix: .ftl
      charset: utf-8
      request-context-attribute: request
    mvc:
        throw-exception-if-no-handler-found: true
    profiles:
        active: local
    servlet:

      multipart:
        max-request-size: 50MB
        max-file-size: 50MB
    mail:
      host: smtp.exmail.qq.com
      username: <EMAIL>
      password: FMBmob180308
      properties.mail.smtp:
        auth: true
        starttls.enable: true
        starttls.required: true
fmb:
  shutdowncode: 734012b6e3d2f5d354fa11007d763ce6
  node-type: front

#    jackson:
#      default-property-inclusion: non_null
dingding:
     finance: "092311205924241632"
     legal: "06306400291235399"
     ceo: "062904404929312818"
     manager:
          - "manager4802"
          - "045610272032925613"
          - "063249595023341948"
          - "011017315532903910"


async:
  core_pool_size: 8
  max_pool_size: 10
  queue_capacity:  50000


mail:
    configs:
        inc:
            host: smtp.exmail.qq.com
            username: <EMAIL>
            password: FMBmob180308
            properties.mail.smtp:
                auth: true
                starttls.enable: true
                starttls.required: true
        df:
            host: smtp.exmail.qq.com
            username: <EMAIL>
            password: Fmb4006809888
            properties.mail.smtp:
                auth: true
                starttls.enable: true
                starttls.required: true
