spring:
    cache:
      type: redis
    kafka:
        producer:
#            bootstrap-servers: 127.0.0.1:9092
            bootstrap-servers: **************:9092
#            bootstrap-servers: *************:9092,*************:9092,*************:9092
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
            acks: 1
            properties:
                linger.ms: 10
            batch-size: 16384
        consumer:
#            bootstrap-servers: 127.0.0.1:9092
            bootstrap-servers: **************:9092
#            bootstrap-servers: *************:9092,*************:9092,*************:9092
            enable-auto-commit: false
            key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

    redis:
        host: 127.0.0.1
        port: 6379
        password: Fumubang1015
        timeout: 2000
        database: 4
        jedis:
            pool:
                max-active: 20
                max-idle: 1
    datasource:
        druid:
            stat-view-servlet:
                enabled: true
                loginUsername: dradmin
                loginPassword: FmbDr2022
        dynamic:
            p6spy: true
            primary: master
            druid:
                initial-size: 2
                max-active: 10
                min-idle: 2
                test-on-borrow: true
                validation-query: select 1
            datasource:
                master:
                    username: fmb
                    password: 123456
                    driver-class-name: com.mysql.jdbc.Driver
                    url: ********************************************************************************************************************************************************************
                slave:
                    username: fmb_r
                    password: 123456
                    driver-class-name: com.mysql.jdbc.Driver
                    url: ********************************************************************************************************************************************************************
#                master:
#                  username: root
#                  password: 123456
#                  driver-class-name: com.mysql.jdbc.Driver
#                  url: ***************************************************************************************************************************************************************
#                slave:
#                  username: root
#                  password: 123456
#                  driver-class-name: com.mysql.jdbc.Driver
#                  url: ***************************************************************************************************************************************************************
logging:
    config: classpath:logback-spring-local.xml
    level:
         org.apache.kafka.clients: warn
#        com.baomidou: debug
#        org.mybatis: debug
#        org.apache.kafka.clients.consumer.internals.ConsumerCoordinator: debug
fmb:
    #excel 下载前缀
    downPrefix: http://127.0.0.1:8070
    #log 地址
    logPath: /Users/<USER>/temp/log
    fmbImgDir: /Users/<USER>/temp/uploadfile/
    fmbImgUrl: http://127.0.0.1:8070
    categoryLong: 95
    categoryShow: 27
    categoryChild: 17
    categoryTravel: 45
    categoryBuy: 20
    #ffmpeg路径
    ffmpegPath: /usr/local/bin/ffmpeg
    serviceTel: ************
    serviceFax: 021-53085657

management:
    endpoints:
        web:
            exposure:
                include: '*'
