[{"id": 100, "text": "首页", "hasChildren": false, "path": "/indexConfig/systemNotice", "children": []}, {"id": 101, "text": "供应商管理", "hasChildren": true, "path": "", "children": [{"id": 102, "text": "供应商管理", "hasChildren": false, "path": "/supplierManagement", "children": []}, {"id": 103, "text": "供应商合同管理", "hasChildren": false, "path": "/supplierManagement/contractManagement", "children": []}]}, {"id": 104, "text": "商户管理", "hasChildren": false, "path": "/businessManage", "children": []}, {"id": 105, "text": "产品管理", "hasChildren": true, "path": "", "children": [{"id": 106, "text": "产品列表", "hasChildren": false, "path": "/productManage", "children": []}, {"id": 107, "text": "发布新产品", "hasChildren": false, "path": "/productManage/addProduct", "children": []}, {"id": 108, "text": "酒店", "hasChildren": true, "path": "", "children": [{"id": 109, "text": "房态日历", "hasChildren": false, "path": "/productManage/roomStatusCalendar/0", "children": []}, {"id": 110, "text": "房态变更日志", "hasChildren": false, "path": "/productManage/roomStatusLog", "children": []}, {"id": 111, "text": "价格变更日志", "hasChildren": false, "path": "/productManage/priceLog", "children": []}]}]}, {"id": 112, "text": "订单管理", "hasChildren": false, "path": "/orderManage", "children": []}]