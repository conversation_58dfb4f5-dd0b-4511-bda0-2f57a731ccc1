package com.fmb;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@MapperScan("com.fmb.server2022.mapper,com.fmb.server2022.fmbx.mapper,com.fmb.server2022.fmb_new.mapper")
@EnableKafka
@EnableScheduling
public class Server2022Application {

    public static void main(String[] args) {
        SpringApplication.run(Server2022Application.class, args);
    }

}
