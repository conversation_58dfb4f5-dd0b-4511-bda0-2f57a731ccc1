package com.fmb.server2022.mapper;

//NEW_IMPORT_DOMAIN

import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.FrontActvHotelDomain;
import com.fmb.server2022.domain.FrontHotelDomain;
import com.fmb.server2022.domain.FrontResSkuDomain;
import com.fmb.server2022.domain.FrontRoomDomain;
import com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain;
import com.fmb.server2022.domain.FrontRoomSkuDomain;
import com.fmb.server2022.domain.FrontRoomSkuOrderDomain;
import com.fmb.server2022.domain.FrontRoomSuiteDomain;
import com.fmb.server2022.domain.HotelDateMinPriceDomain;
import com.fmb.server2022.domain.HotelReserveMinPriceDomain;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface FrontHotelDao {


    /**
     * 前台酒店详情
     */
    List<FrontHotelDomain> selectHotelDetail(@Param("reqPar") ReqHotel reqPar);


    /**
     * 前台房型详情
     */
    List<FrontRoomDomain> selectFrontRoomDetail(@Param("reqPar") ReqHotel reqPar);

    /**
     * 房型图片
     */
    List<BpsMediaInfoDomain> selectBpsMediaInfo(@Param("reqPar") ReqHotel req);

    /**
     * 日历房低价日历查询
     */
    List<FrontRoomSkuDatePriceDomain> selectHotelDatePrice(@Param("reqPar") ReqHotel reqPar);

    /**
     * 日历房选择日历查询
     */
    List<FrontRoomSkuDatePriceDomain> selectHotelSkuDatePrice(@Param("reqPar") ReqHotel reqPar);

    List<FrontRoomSkuDatePriceDomain> selectMinPrice(@Param("reqPar") ReqHotel reqPar);

    /**
     * 酒店活动详情
     */
    List<FrontActvHotelDomain> selectFrontActvHotel(@Param("reqPar") ReqHotel req);


    /**
     * 预售房劵套餐列表
     */
    List<FrontResSkuDomain> selectFrontResSku(@Param("reqPar") ReqHotel req);


    /**
     * 日历房套餐列表
     */
    List<FrontRoomSkuDomain> selectFrontRoomSku(@Param("reqPar") ReqHotel req);

    /**
     * 房劵房型详情
     */
    List<FrontRoomDomain> selectResRoomDetail(@Param("reqPar") ReqHotel reqPar);

    /**
     * 房劵房型套餐
     */
    List<FrontRoomSuiteDomain> selectResSuite(@Param("reqPar") ReqHotel reqPar);

    /**
     * 日历房提交订单sku信息
     */
    List<FrontRoomSkuOrderDomain> selectFrontRoomSkuBySkuId(@Param("reqPar") ReqHotel req);


/**
 * 日历房最低价查询
 */
    HotelDateMinPriceDomain selectHotelDateMinPriceOne(@Param("reqPar") Map req);



/**
 * 房券最低价查询
 */
    HotelReserveMinPriceDomain selectHotelReserveMinPriceOne(@Param("reqPar") Map req);


}