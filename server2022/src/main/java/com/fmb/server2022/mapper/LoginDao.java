package com.fmb.server2022.mapper;

import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

public interface LoginDao {
    /**
     * 根据用户名和密码查询对应的用户
     */
    Integer checkUser(@Param("username") String username, @Param("password") String password);

    SessionUserInfoOfFmbAdmin getUserInfo(String username);

    Set<String> getAllMenu();

    Set<String> getAllPermissionCode();


}