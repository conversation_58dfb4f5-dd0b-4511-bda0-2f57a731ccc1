package com.fmb.server2022.mapper;



import com.fmb.server2022.domain.FrontBpsMediaDomain;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FmbxBpMediaDao {

    @MapKey("bps_id")
    List<Map<String, Object>> getGroupNumByBpsId(Integer bpsId);

    /**
     *
     * @param bpsId
     * @param groupval
     * @return
     */
    @MapKey("media_id")
    List<Map<String, Object>> getImgListByBpsIdAndGroupNum(@Param("bpsId") Integer bpsId,@Param("groupval") Integer groupval);

    List<FrontBpsMediaDomain> getFrontImgListByBpsId(Integer bpsId);
}
