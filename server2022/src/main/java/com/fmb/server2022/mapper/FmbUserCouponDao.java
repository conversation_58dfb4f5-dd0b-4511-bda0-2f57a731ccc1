package com.fmb.server2022.mapper;

import com.fmb.server2022.fmbx.entity.FmbCouponType;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FmbUserCouponDao {
    /***
      * 获取可用的优惠前列表
      * @param uid
      * @param toDayDate
      * @return
      */
    @MapKey("coupon_sn")
    List<Map<String, Object>> getUsableCouponList(@Param("uid") Integer uid, @Param("toDayDate")  String toDayDate);

    List<FmbCouponType> getCouponList();

    Integer getCountNotUseCoupon(Integer couponId);
}
