package com.fmb.server2022.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fmb.server2022.domain.FmbxBpListDomain;
import com.fmb.server2022.domain.FmbxBpReviewListDomain;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.reqdomain.ReqFmbxBpList;
import com.fmb.server2022.reqdomain.ReqFmbxBpReviewList;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface FmbxBpDao {


    /**
     * 供应商列表
     */

    IPage<FmbxBpListDomain> selectFmbxBpList(IPage<FmbxBpListDomain> page, @Param("reqPar") ReqFmbxBpList req);

    List<FmbxBpListDomain> selectFmbxBpList(@Param("reqPar") ReqFmbxBpList req);

    List<FmbxBps> getShopList(Integer bpId);

    /**
     * 供应商审核列表
     */
    IPage<FmbxBpReviewListDomain> selectFmbxBpReviewList(IPage<FmbxBpReviewListDomain> page, @Param("reqPar") ReqFmbxBpReviewList req);

    List<FmbxBpReviewListDomain> selectFmbxBpReviewList(@Param("reqPar") ReqFmbxBpReviewList req);


    List<Integer> listShopUsers();
}