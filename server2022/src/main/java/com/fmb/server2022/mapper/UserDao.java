package com.fmb.server2022.mapper;

import com.alibaba.fastjson.JSONObject;
import com.fmb.server2022.reqdomain.ReqListUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 用户/角色/权限
 */
public interface UserDao {
    /**
     * 查询用户数量
     * @param jsonObject
     */

    int countUser(ReqListUser jsonObject);

    /**
     * 查询用户列表
     */

    List<JSONObject> listUser(ReqListUser reqList);

    /**
     * 查询所有的角色
     * 在添加/修改用户的时候要使用此方法
     */

    List<JSONObject> getAllRoles();

    /**
     * 校验用户名是否已存在
     */

    int queryExistUsername( @Param("username") String username);

    /**
     * 新增用户
     */

    int addUser(JSONObject jsonObject);

    /**
     * 修改用户
     */

    int updateUser(JSONObject jsonObject);


    int batchAddUserRole(@Param("roleIds") Integer[] roleids,@Param("userId") Integer id);


    int removeUserAllRole(int userId);

    /**
     * 角色列表
     */

    List<JSONObject> listRole();

    /**
     * 查询所有权限, 给角色分配权限时调用
     */

    List<JSONObject> listAllPermission();



    /**
     * 将角色曾经拥有而修改为不再拥有的权限 delete_status改为'2'
     */

    int removeOldPermission(@Param("roleId") String roleId, @Param("permissions") List<Integer> permissions);

    /**
     * 修改角色名称
     */

    int updateRoleName(JSONObject jsonObject);

    /**
     * 查询某角色的全部数据
     * 在删除和修
     * 改角色时调用
     */

    JSONObject getRoleAllInfo(@Param("roleId") Integer roleId);

    /**
     * 删除角色
     */

    int removeRole(JSONObject jsonObject);

    /**
     * 删除本角色全部权限
     */

    int updateRoleAllPermission(@Param("roleId") Integer roleId, @Param("status") Integer status);


    List<Integer> selectHavePermissionUsers(@Param("permissionid")  Integer permissionid);


    /**
     * 找到列表类型的 权限
     * @param userid
     * @return
     */

    List<Integer> selectListPermissions(@Param("userid")  Integer userid);
}
