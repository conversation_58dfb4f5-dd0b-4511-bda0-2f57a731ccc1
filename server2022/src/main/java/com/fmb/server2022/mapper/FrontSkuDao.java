package com.fmb.server2022.mapper;

//NEW_IMPORT_DOMAIN

import com.fmb.server2022.domain.AdminOrderActionDomain;
import com.fmb.server2022.domain.FrontCouponDomain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface FrontSkuDao {


/**
 * 用户可用优惠券
 */
    //单表
    List<FrontCouponDomain> selectFrontCoupon(@Param("reqPar") Map req);
    //联表



/**
 * 订单操作记录
 */
    //单表
    List<AdminOrderActionDomain> selectAdminOrderAction(HashMap map);

}
