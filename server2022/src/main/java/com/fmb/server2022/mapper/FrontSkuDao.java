package com.fmb.server2022.mapper;

//NEW_IMPORT_DOMAIN

import com.fmb.server2022.domain.AdminOrderActionDomain;
import com.fmb.server2022.domain.FrontCouponDomain;
import com.fmb.server2022.domain.stock.DatePrimeInfo;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxDateHotelInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface FrontSkuDao {


/**
 * 用户可用优惠券
 */
    //单表
    List<FrontCouponDomain> selectFrontCoupon(@Param("reqPar") Map req);
    //联表



/**
 * 订单操作记录
 */
    //单表
    List<AdminOrderActionDomain> selectAdminOrderAction(HashMap map);

    FmbxBps queryBpsInfoBySkuId( Integer id);

    List<Map> queryHotelSellNum(Integer aid);

    /**
     * 查询日历房可用sku 信息
     * @param map
     * @return
     */
    List<DatePrimeInfo> queryActivitySkuDateInfo(@Param("reqPar") Map map);

    List<FmbxDateHotelInfo> queryDateHotelInfoOfExists(@Param("reqPar") Map map);
    List<FmbxDateHotelInfo> queryDateHotelInfoOfDel(@Param("reqPar") Map map);


    List<Integer> queryHotelDateSku(@Param("reqPar") HashMap<String, Object> par);

    List<Integer> queryHotelReserveSku(@Param("reqPar") HashMap<String, Object> par);


    List<Map> queryHotelDateSkuAndSuite(@Param("reqPar") HashMap<String, Object> par);
}
