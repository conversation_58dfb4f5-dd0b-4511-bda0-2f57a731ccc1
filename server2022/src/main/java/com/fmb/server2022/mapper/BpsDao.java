package com.fmb.server2022.mapper ;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fmb.server2022.domain.BpContractDomain;
import com.fmb.server2022.domain.BpMediaInfoDomain;
import com.fmb.server2022.domain.BpsInfoDomain;
import com.fmb.server2022.domain.BpsMediaFilterInfo;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.BpsRoomInfoDomain;
import com.fmb.server2022.reqdomain.ReqBpContract;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqBpsMediaInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomSku;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface BpsDao   {


    /**
     * 通过商家ID查询关联的供应商
     * @param cpsId
     * @return
     */
    @MapKey("bp_id")
    List<Map<String, Object>> selectBpListByCpsId(Integer cpsId);

    /**
 * 商户分页查询
 */
    IPage<Integer> selectBpsInfo(IPage<Integer> page,@Param("reqPar") ReqBpsInfo req);
    List<BpsInfoDomain>  selectBpsInfoStr(@Param("ids") List<Integer> ids);

    List<BpsInfoDomain> selectBpsInfoR( @Param("reqPar") ReqBpsInfo req);



/**
 * 商户图片查询
 */
    
    IPage<BpsMediaInfoDomain> selectBpsMediaInfo(IPage<BpsMediaInfoDomain> page, @Param("reqPar") ReqBpsMediaInfo req);
    List<BpsMediaInfoDomain> selectBpsMediaInfo(@Param("reqPar") ReqBpsMediaInfo req);

    List<BpsMediaFilterInfo> selectCreateInfo(Integer bps_id);

/**
 * 商户管理房型管理
 */
    
    IPage<BpsRoomInfoDomain> selectBpsRoomInfo(IPage<BpsRoomInfoDomain> page, @Param("reqPar") ReqBpsRoomInfo req);
    List<BpsRoomInfoDomain> selectBpsRoomInfo(@Param("reqPar") ReqBpsRoomInfo req);
    List<BpsRoomInfoDomain> selectBpsRoomSku(@Param("reqPar") ReqBpsRoomSku req);


/**
 * 供应商合同管理
 */
    
    IPage<BpContractDomain> selectBpContract(IPage<BpContractDomain> page, @Param("reqPar") ReqBpContract req);
    List<BpContractDomain> selectBpContract(@Param("reqPar") ReqBpContract req);




/**
 * 供应商证照
 */
    //单表
    List<BpMediaInfoDomain> selectBpMediaInfo(@Param("bpid") Integer bpid);

    @Select(" select type ,count(id) as cr from fmbx_bps_media where bps_id=#{bpsid}  group by type order by type asc  ")
    List<Map> selectCountType(Integer bpsid);

    List<Map> selectFmbxBpQuery(@Param("key")  String queryKeyword);
}