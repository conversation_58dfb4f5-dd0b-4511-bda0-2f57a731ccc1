package com.fmb.server2022.mapper;

//NEW_IMPORT_DOMAIN

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fmb.server2022.domain.ActivityPublishLogDomain;
import com.fmb.server2022.domain.ActivityXDomain;
import com.fmb.server2022.domain.HotelDateSkuDomain;
import com.fmb.server2022.domain.HotelDateSkuDomainSub;
import com.fmb.server2022.domain.HotelPriceChangeDataDomain;
import com.fmb.server2022.domain.HotelReserveSkuDomain;
import com.fmb.server2022.domain.HotelSkuDatePriceDomain;
import com.fmb.server2022.domain.HotelSkuDomain;
import com.fmb.server2022.domain.HotelStockChangeDataDomain;
import com.fmb.server2022.domain.HotelStockDateDomain;
import com.fmb.server2022.domain.RoomStockInfo;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.reqdomain.ReqActivityPublishLog;
import com.fmb.server2022.reqdomain.ReqActivityX;
import com.fmb.server2022.reqdomain.ReqHotelDate;
import com.fmb.server2022.reqdomain.ReqHotelDateSku;
import com.fmb.server2022.reqdomain.ReqHotelPriceChangeData;
import com.fmb.server2022.reqdomain.ReqHotelReserveSku;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.reqdomain.ReqHotelStockChangeData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface SuiteDao {
    List<Map<String,Object>> selectPriceAddOperation(Integer suiteid);

/**
 * 酒店sku信息查询 ,根据套餐分组
 */
    List<HotelSkuDomain> selectHotelSku(@Param("reqPar") ReqHotelSku req);

    List<HotelSkuDomain> selectHotelSkuList(@Param("reqPar") ReqHotelSku req);


/**
 * 酒店sku日期价格范围查询
 *
 * 查询里 一定有日期信息 但如果没有对应的库存和价格
 * 对应的 价格id 和 库存id 为 0
 */
    List<HotelSkuDatePriceDomain> selectHotelSkuDatePrice(@Param("reqPar") ReqHotelDate req);

    List<RoomStockInfo> selectStockRoomList(Integer xaid);
    List<RoomStockInfo> selectStockRoomList2(@Param("reqPar")  ReqHotelDate xaid);


/**
 * 酒店价格变动列表
 */
    //单表
    IPage<HotelPriceChangeDataDomain> selectHotelPriceChangeData(IPage<HotelPriceChangeDataDomain> page,@Param("reqPar") ReqHotelPriceChangeData req);
    List<HotelPriceChangeDataDomain> selectHotelPriceChangeData(@Param("reqPar") ReqHotelPriceChangeData req);


/**
 * 酒店库存变化
 */
    //单表
    IPage<HotelStockChangeDataDomain> selectHotelStockChangeData(IPage<HotelStockChangeDataDomain> page,@Param("reqPar") ReqHotelStockChangeData req);
    List<HotelStockChangeDataDomain> selectHotelStockChangeData(@Param("reqPar") ReqHotelStockChangeData req);


    Map selectValidPriceAndStock(Map<String, Object> par);

    /**
     * 查询一个套餐下是否有有效的日历房sku
     * @param par
     * @return
     */
    Map selectValidHotelDatePriceOfSuite(Map<String, Object> par);

    List<HotelStockDateDomain> selectRoomStockDateInfo(@Param("reqPar") ReqHotelDate req);

    Integer selectSkuOfAvailableHotel(Integer xaid);

    Integer selectSkuOfAvailableReserveHotel(Integer xaid);

    int countHotelDeleteSku(int xaid);

    int countReserveHotelDeleteSku(int xaid);

/**
 * 活动列表
 */
    //单表
    IPage<ActivityXDomain> selectActivityX(IPage<ActivityXDomain> page,@Param("reqPar") ReqActivityX req);
    List<ActivityXDomain> selectActivityX(@Param("reqPar") ReqActivityX req);


/**
 * 日历房酒店sku列表
 */
    //单表
    IPage<HotelDateSkuDomain> selectHotelDateSku(IPage<HotelDateSkuDomain> page,@Param("reqPar") ReqHotelDateSku req);
    List<HotelDateSkuDomain> selectHotelDateSku(@Param("reqPar") ReqHotelDateSku req);
    //联表
    IPage<Integer> selectHotelDateSkuReturnInt(IPage<Integer> page,@Param("reqPar") ReqHotelDateSku req);
    List<HotelDateSkuDomainSub> selectHotelDateSkuUserListIds(@Param("reqPar") ReqHotelDateSku req);
    int selectHotelDateSkuTotalConut(@Param("reqPar") ReqHotelDateSku req);


    /**
 * 房券sku列表
 */
    //单表
    IPage<HotelReserveSkuDomain> selectHotelReserveSku(IPage<HotelReserveSkuDomain> page,@Param("reqPar") ReqHotelReserveSku req);
    List<HotelReserveSkuDomain> selectHotelReserveSku(@Param("reqPar") ReqHotelReserveSku req);
    List<HotelReserveSkuDomain> selectHotelReserveSkuMenu(@Param("reqPar") ReqHotelSku req);



/**
 * 活动发布日志
 */
    //单表
    IPage<ActivityPublishLogDomain> selectActivityPublishLog(IPage<ActivityPublishLogDomain> page,@Param("reqPar") ReqActivityPublishLog req);
    List<ActivityPublishLogDomain> selectActivityPublishLog(@Param("reqPar") ReqActivityPublishLog req);


    List<Map> selctRoomInfoOfSuite(Integer suiteId);


    List<FmbGoodsCategory> queryCategoryInfo(@Param("inList") List<Integer> collect);

    Integer querySkuBuyNum(@Param("reqPar") HashMap<String, Object> map);
}
