package com.fmb.server2022.reqdomain;

import com.fmb.server2022.domain.ActivityTagListDomain;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class ReqaddNewActivity {



    private Integer xaid;

    /**
     * 商户id
     */
    private Integer bpsId;

    /**
     * 供应商id
     */
    private Integer bpId;

    /**
     * 合同id
     */
    private ArrayList<Integer> bpcId;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
     */
    private Integer ticketType;

    /**
     * 售卖状态:0-未知或非卖品,1-关闭,2-无库存,4-已过期,8-已屏蔽,16-在售中
     */
    private Integer sellStat;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动副标题
     */
    private String subTitle;

    /**
     * 显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
     */
    private String platId;

    /**
     * 产品所属分站
     */
    private Integer cityId;

    /**
     * 可售卖分站(find_in_set查询)
     */
    private String salesCityIds;

    /**
     * 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
     */
    private Integer businessType;

    /**
     * 二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
     */
    private Integer isConfirm;

    /**
     * 活动banner多图
     */
    private String bannerList;

    /**
     * 视频id
     */
    private Integer videoMediaId;


    //活动详情 富文本
    private String richTxt ;

    private List<ActivityTagListDomain> tagList;

    private Integer editorUid;
    private String editorMobile;
    private String editorName;
    private String editorEmail;
    private Integer marketUid;
    private String marketMobile;
    private String marketName;
    private String marketEmail;

}
