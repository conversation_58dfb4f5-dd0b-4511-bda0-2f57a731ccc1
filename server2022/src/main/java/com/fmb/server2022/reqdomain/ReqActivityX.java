package com.fmb.server2022.reqdomain ;

import lombok.Data;

import java.util.ArrayList;

@Data
public class ReqActivityX extends  BaseReq{

    //    "keyword":"产品名称或id",
//     "ticket_type":[6],
    private String keyword ;

    private ArrayList<Integer> ticket_type ;



    //活动id
    private  Integer xaid;
        //活动标题
    private  String title;
        //所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
    private  Integer business_type;
        //分类id
    private  Integer category_id;
        //商户id
    private  Integer bps_id;
        //供应商id
    private  Integer bp_id;
        //我司对接人台后uid
    private  Integer admin_uid;
        //二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
    private  Integer is_confirm;
        //创建人的uid
    private  Integer create_uid;
        //最后操作人uid
    private  Integer last_modify_uid;
        //上下架状态:0-待发布,1-已上架,2-已下架
    private  Integer flag_publish;
        //售卖状态:0-未售(sku无效),1-在售
    private  Integer flag_have_valid_sku;
        //省份
    private  String provinces;
        //省份下的区
    private  String provinces_sub;
        //产品所属分站
    private  Integer city_id;




    //创建开始日期
    private String ctime_begin ;

    //创建结束日期
    private String ctime_end ;


}