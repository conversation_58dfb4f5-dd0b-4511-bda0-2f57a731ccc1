package com.fmb.server2022.reqdomain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.*;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReqHotelMail {
    public static final Map<Integer, String> confirmStatusMap;
    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(-1,"不变");
            map.put(0,"待处理");
            map.put(1,"处理中");
            map.put(2,"有货-保留");
            map.put(3,"无货-待处理");
            map.put(4,"无货-退款");
            map.put(5,"处理中-预订");
            map.put(6,"处理中-更改");
            map.put(7,"处理中-取消");
            map.put(8,"处理中-预订催单");
            map.put(9,"处理中-更改催单");
            map.put(10,"处理中-取消催单");
            confirmStatusMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer id;
    private String setTo;
    private String setCc;
    private String subject;
    private String textContent;
    private String htmlContent;
    private Integer confirmStatus;
    private List<ConfirmStatusBean> confirmStatusList;
    public List<ConfirmStatusBean> getConfirmStatusList(){
        return this.getAllConfirmStatusList();
    }
    private Integer confirmStatusSelectedId;
    private String confirmComment;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConfirmStatusBean {
        private int confirmId;
        private String confirmStr;
        private String selected;

        public int getConfirmId() {
            return confirmId;
        }

        public void setConfirmId(int confirmId) {
            this.confirmId = confirmId;
        }

        public String getConfirmStr() {
            return confirmStr;
        }

        public void setConfirmStr(String confirmStr) {
            this.confirmStr = confirmStr;
        }

        public String getSelected() {
            return selected;
        }

        public void setSelected(String selected) {
            this.selected = selected;
        }
    }

    public static List<ConfirmStatusBean> getAllConfirmStatusList(){
        List<ConfirmStatusBean> reList = new ArrayList<>();
        ConfirmStatusBean b = new ConfirmStatusBean();
        b.setConfirmStr("不变");
        b.setConfirmId(-1);
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(0);
        b.setConfirmStr("待处理");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(1);
        b.setConfirmStr("处理中");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(5);
        b.setConfirmStr("处理中-预订");
        //b.setSelected("selected");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(8);
        b.setConfirmStr("处理中–预订催单");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(6);
        b.setConfirmStr("处理中-更改");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(9);
        b.setConfirmStr("处理中-更改催单");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(7);
        b.setConfirmStr("处理中-取消");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(10);
        b.setConfirmStr("处理中-取消催单");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(2);
        b.setConfirmStr("有货-保留");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(3);
        b.setConfirmStr("无货-待处理");
        reList.add(b);
        b = new ConfirmStatusBean();
        b.setConfirmId(4);
        b.setConfirmStr("无货-退款");
        reList.add(b);
        return reList;
    }
}
