package com.fmb.server2022.reqdomain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ReqUpdateHotelDateSkuSortValueList {


    @JsonProperty("data")
    private List<DataDTO> data;

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataDTO {
        @JsonProperty("suiteId")
        private Integer suiteId;
        @JsonProperty("suiteSortValue")
        private Integer suiteSortValue;
        @JsonProperty("list")
        private List<ListDTO> list;

        public Integer getSuiteId() {
            return suiteId;
        }

        public void setSuiteId(Integer suiteId) {
            this.suiteId = suiteId;
        }

        public Integer getSuiteSortValue() {
            return suiteSortValue;
        }

        public void setSuiteSortValue(Integer suiteSortValue) {
            this.suiteSortValue = suiteSortValue;
        }

        public List<ListDTO> getList() {
            return list;
        }

        public void setList(List<ListDTO> list) {
            this.list = list;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ListDTO {
            @JsonProperty("skuId")
            private Integer skuId;
            @JsonProperty("skuSortValue")
            private Integer skuSortValue;

            public Integer getSkuId() {
                return skuId;
            }

            public void setSkuId(Integer skuId) {
                this.skuId = skuId;
            }

            public Integer getSkuSortValue() {
                return skuSortValue;
            }

            public void setSkuSortValue(Integer skuSortValue) {
                this.skuSortValue = skuSortValue;
            }
        }
    }
}
