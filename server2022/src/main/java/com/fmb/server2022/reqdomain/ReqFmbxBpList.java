package com.fmb.server2022.reqdomain ;

import lombok.Data;

import java.util.List;

@Data
public class ReqFmbxBpList extends  BaseReq{

        //关键字
    private  String queryKeyword;
        //商家类型:1-酒店,2-其他
    private  Integer parterType;
        //省份
    private  String partnerProvince;
        //市
    private  String partnerCity;
        //真实姓名
    private  String realname;
        //账号类型:1-主账号,2-子账号
    private  Integer accountType;
        //账号类型:1-正常,0-禁用
    private  Integer accountStatus;
        //审核状态:1-待供应商完善,2-待商务审核,3-审核通过
    private  Integer checkStatus;
        //排序方式
    private  Integer orderby;

    /**
     * 老活动的id 批量导入原始房型数据时
     */
    private Integer aid ;

    /**
     * 是否导入老活动的图片
     */
    private Integer loadImg ;
    /**
     * 商户id
     */
    private Integer bpsId ;

    private List<ReqImgInfo> imagesInfo ;

    private List<String> imageUrls ;

}