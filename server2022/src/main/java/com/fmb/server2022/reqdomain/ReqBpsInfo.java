package com.fmb.server2022.reqdomain ;

import lombok.Data;

import java.util.ArrayList;

@Data
public class ReqBpsInfo extends  BaseReq{
        //商户名称或者id
    private  String key;
        //商户类型:1-酒店,2-其他
    private  Integer bps_type;
        //联系号码
    private  String phone;
        //省份
    private  String provinces;
        //区
    private  String area;
        //酒店地址
    private  String address;
        //状态:0-屏蔽,1-正常 
    private  Integer status;
        //台后操作用户
    private  Integer admin_uid;

    private Integer bpId ;

    private ArrayList<Integer> bpsIds ;


    private Integer reqSource = 1 ;


}