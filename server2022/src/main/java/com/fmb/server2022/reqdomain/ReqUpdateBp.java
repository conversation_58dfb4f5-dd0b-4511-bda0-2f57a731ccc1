package com.fmb.server2022.reqdomain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class ReqUpdateBp {
    //   //  dbField: bp_id
    @NotNull
    private Integer updateType;

//    @NotNull
    private Integer bpId;


    //   商家名称//  dbField: partner_name
    @NotBlank
    private String partnerName;

    //   联系人//  dbField: partner_contacts_name
    @NotBlank
    private String partnerContactsName;

    //   联系电话//  dbField: partner_contacts_phone
//    @NotBlank
    private String partnerContactsPhone;

    //   联系手机号//  dbField: partner_contacts_mobile
    @NotBlank
    private String partnerContactsMobile;

    //   邮箱//  dbField: partner_contacts_email
//    @NotBlank
    private String partnerContactsEmail;

    //   省份//  dbField: partner_province
//    @NotBlank
    private String partnerProvince;

    //   市//  dbField: partner_city
//    @NotBlank
    private String partnerCity;

    //   地址//  dbField: partner_address
    private String partnerAddress;


//    酒店预订信息开始---------

    //   预订部工作时间//  dbField: hotel_work_time
    @NotBlank
    private String hotelWorkTime;

    //   订房联系人//  dbField: hotel_confirm_contract_name
    @NotBlank
    private String hotelConfirmContractName;

    //   订房电话//  dbField: hotel_confirm_contract_phone
    @NotBlank
    private String hotelConfirmContractPhone;

    //   订房传真//  dbField: hotel_confirm_contract_fax
//    @NotBlank
    private String hotelConfirmContractFax;

    //   订房邮箱//  dbField: hotel_confirm_contract_email
//    @NotBlank
    private String hotelConfirmContractEmail;

    //   订房备注//  dbField: hotel_confirm_contract_note
//    @NotBlank
    private String hotelConfirmContractNote;


//    酒店预订信息结束---------



    //   营业执照文件id//  dbField: license_file_id
    @NotNull
    private Integer licenseMediaid;

    //永久有效
    private Integer noend ;


    //   营业执照过期时间//  dbField: expireDate

    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    LocalDate expireDate ;




    //   特种行业许可证//  dbField: special_bp_file_id
    private  Integer[]  specialBpMediaid ;



    //   结算账户名称//  dbField: settle_account_name
    @NotBlank
    private String settleAccountName;
    @NotBlank
    private String settleAccountNo;


    //   结算账户开户行//  dbField: settle_account_bank
    @NotBlank
    private String settleAccountBank;

    //   结算财务联系人//  dbField: settle_account_username
    @NotBlank
    private String settleAccountUsername;

    //   结算财务联系人电话//  dbField: settle_account_user_mobile
    @NotBlank
    private String settleAccountUserMobile;

    //   结算财务联系人邮箱//  dbField: settle_account_user_email
    @NotBlank
    private String settleAccountUserEmail;


}
