package com.fmb.server2022.reqdomain ;

import lombok.Data;

import java.util.ArrayList;

@Data
public class ReqHotelReserveSku extends  BaseReq{

        //活动id
    private  Integer xaid;
        //skuid
    private  Integer sku_id;
        //套餐id
    private  Integer suite_id;

    private  String suite_name;
        //创建人uid
    private  Integer create_uid;
        //创建时间
    private  String ctime_start;
    private  String ctime_end;
        //售买状态(库存和当前时间在售卖时间内的结果):0-无效,1-有效
    private ArrayList< Integer> flag_sell;
        //上下架状态:0-已删除,1-已上架,2-已下架,3-待完善
    private  ArrayList<Integer>  status;


}