package com.fmb.server2022.reqdomain ;

import lombok.Data;

import java.util.ArrayList;

@Data
public class ReqBpContract extends  BaseReq{

        //审批中合同状态:0-发起申请,1-直属经理审批,2-法务审批,3-上海财务审批,4-CEO审批,5-合同归档提交,6-合同确认无误,7-拒绝
    private ArrayList bpc_runuing_status;
        //合同编号
    private  String bpc_number;
        //分站,1-北京,2-上海
    private  Integer city_id;
        //所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线,20-运营
    private  Integer business_type;
        //合同形式:1-标准合同,2-非标准合同
    private  Integer bp_template_type;
        //合同类型:1-新建,2-续签,3-补充协议
    private  Integer bp_create_type;
        //合同名称
    private  String bpc_name;

    //关联供应商/ID：
    private  String bp_key;


    // 供应商ID
    private  Integer bpId;


}