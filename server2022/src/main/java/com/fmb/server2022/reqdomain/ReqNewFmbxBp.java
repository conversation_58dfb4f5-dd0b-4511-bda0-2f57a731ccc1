package com.fmb.server2022.reqdomain;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ReqNewFmbxBp  extends  BaseReq{
    public  static final Map<Integer, String> parterTypeMap;
    public  static final Map<Integer, String> settleGenTypeMap;
    public  static final Map<Integer, String> settleTypeMap;
    public  static final Map<Integer, String> invoiceTypeMap;

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"酒店");
            map.put(2,"其他");
            parterTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"系统自动发起");
            map.put(2,"结算人员手动发起");
            settleGenTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"下单时间");
            map.put(2,"消费时间");
            map.put(3,"入住时间");
            settleTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"结算发票");
            map.put(2,"佣金发票");
            invoiceTypeMap = Collections.unmodifiableMap(map);
        }
    }

    @NotEmpty
    private String accountName;
    @NotEmpty
    private String inputPass;
    @NotEmpty
    private String passwdConfirm;
    @NotNull
    private Integer parterType;
    @NotNull
    private Integer adminUid;
    @NotNull
    private String adminMobile;

    private String adminName;
    private String adminEmail;

    private Integer settleEachMonthday;
    private Integer settleGenType;
    private Integer settleType;
    private Integer invoiceType;

    private Integer accountType;

    private Integer accountStatus;

    private Integer checkStatus;

    private Integer bpId;

    private List<Map<String,Object>> bpsIds;

    private String bpParterEmail;

    private String bpParterMobile;

    private String tipsInfo;


    /**
     * 商家后台展示用户手机号 1:显示 0：不显示
     */
    private Integer isShowMobile;
    /**
     * 商家后台展示用户姓名 1:不展示 0：展示
     */
    private Integer isShowCustomerName;

    /**
     * 展示销售数据 0否 1是 默认0
     */
    private Integer showOrderStat;


    /**
     * 老的商家id
     */
    private Integer shopUserId ;

}
