package com.fmb.server2022.reqdomain;

import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import lombok.Data;

import java.util.List;

@Data
public class ReqFmbxBpsRoom extends FmbxBpsRoom {


    private List<RoomImagesBean> roomImages;

    public List<RoomImagesBean> getRoomImages() {
        return roomImages;
    }

    public void setRoomImages(List<RoomImagesBean> roomImages) {
        this.roomImages = roomImages;
    }

    public static class RoomImagesBean {
        /**
         * mediaId : 123
         * url : xxx
         */

        private int mediaId;
        private String url;

        public int getMediaId() {
            return mediaId;
        }

        public void setMediaId(int mediaId) {
            this.mediaId = mediaId;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
