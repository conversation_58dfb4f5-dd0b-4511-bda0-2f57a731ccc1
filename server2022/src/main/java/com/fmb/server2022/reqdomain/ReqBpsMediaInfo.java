package com.fmb.server2022.reqdomain ;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ReqBpsMediaInfo extends  BaseReq{

        //文件id
    private  Integer media_id;
        //类型:1-图片,2-视频
    private  Integer type;

    //商家id
    @NotNull
    private  Integer bps_id;
        //图片分组:1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
    private  Integer groupval;
        //后台用户类型:1-父母邦后台,2-供应商后台
    private  Integer admin_type;

    private  Integer admin_uid;
        //后台用户名字
    private  String admin_name;

    //关键字
    private  String description;

    private Integer id ;

    private Integer roomId ;


    //1- 从房型详情来  2-从商户media 列表来
    // 根据这个值不一样 设置排序条件
    private Integer from  ;


}