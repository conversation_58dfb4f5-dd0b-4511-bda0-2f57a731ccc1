package com.fmb.server2022.reqdomain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class ReqFmbOrderConfirm {
    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 后台操作uid
     */
    private Integer adminUid;

    private String adminName;

    /**
     * 二次确认处理状态(1:未处理，2:处理中，3:处理完)
     */
    private Integer confirmStatus;

    private String confirmStatusStr;

    public String getConfirmStatusStr(){
        return ReqHotelMail.confirmStatusMap.get(confirmStatus);
    }

    /**
     * 二次确认具体说明
     */
    private String postscript;

    /**
     * 附件
     */
    private String uploadFile;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    private Boolean hasSms;

    private String smsContent;

}
