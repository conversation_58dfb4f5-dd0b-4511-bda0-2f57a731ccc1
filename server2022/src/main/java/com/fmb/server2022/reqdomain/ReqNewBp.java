package com.fmb.server2022.reqdomain;



import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class ReqNewBp {


    @NotEmpty
    private String accountName;
    @NotEmpty
    private String inputPass;
    @NotEmpty
    private String passwdConfirm;
    @NotNull
    private int invoiceType;
    @NotNull
    private int settleEachMonthday;
    @NotNull
    private int settleType;
    @NotNull
    private int hotel_confirm_type;

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getInputPass() {
        return inputPass;
    }

    public void setInputPass(String inputPass) {
        this.inputPass = inputPass;
    }

    public String getPasswdConfirm() {
        return passwdConfirm;
    }

    public void setPasswdConfirm(String passwdConfirm) {
        this.passwdConfirm = passwdConfirm;
    }

    public int getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(int invoiceType) {
        this.invoiceType = invoiceType;
    }

    public int getSettleEachMonthday() {
        return settleEachMonthday;
    }

    public void setSettleEachMonthday(int settleEachMonthday) {
        this.settleEachMonthday = settleEachMonthday;
    }

    public int getSettleType() {
        return settleType;
    }

    public void setSettleType(int settleType) {
        this.settleType = settleType;
    }

    public int getHotel_confirm_type() {
        return hotel_confirm_type;
    }

    public void setHotel_confirm_type(int hotel_confirm_type) {
        this.hotel_confirm_type = hotel_confirm_type;
    }
}
