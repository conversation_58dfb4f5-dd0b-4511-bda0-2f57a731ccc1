package com.fmb.server2022.reqdomain ;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class ReqHotelDateSku extends  BaseReq{

        //活动主表id
    @NotNull
    private  Integer xaid;
        //
    private  Integer sku_id;
        //套餐id
    private  Integer suite_id;

    private  String suite_name;

    //商户房型id
    private  Integer room_id;
        //创建人uid
    private  Integer create_uid;
        //售买状态:0-无效（无可售日期）,1-有效
    private ArrayList<Integer>  flag_sell;
        //上下架状态:0-已删除,1-已上架,2-已下架
    private  ArrayList<Integer> status;

    private String ctime_start ;
    private String ctime_end;

    List<Integer> suiteIds ;

    public ReqHotelDateSku() {
        this.suiteIds = new ArrayList<>() ;
    }
}