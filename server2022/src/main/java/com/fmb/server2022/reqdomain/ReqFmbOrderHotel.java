package com.fmb.server2022.reqdomain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fmb.server2022.fmbx.entity.FmbxHotelGoodsDetail;
import com.fmb.util.FmbDateUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ReqFmbOrderHotel {

    public static final Map<Integer, String> formTypeMap;

    public static final Map<String, Integer> typeMap;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "未知");
            map.put(1, "新订");
            map.put(2, "改签");
            map.put(3, "变更");
            map.put(4, "取消");
            map.put(5, "其他");
            formTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, Integer> map = new HashMap<>();
            map.put("未知", 0);
            map.put("新订", 1);
            map.put("改签", 2);
            map.put("变更", 3);
            map.put("取消", 4);
            map.put("其他", 5);
            typeMap = Collections.unmodifiableMap(map);
        }
    }

    private String title;

    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 保存之前的订单号
     */
    private String oldOrderSn;

    /**
     * 商品ID
     */
    private Integer recId;

    /**
     * 改签前的rec_id
     */
    private Integer lastRecId;

    /**
     * 票种ID
     */
    private Integer goodsId;

    /**
     * 表单样式0未知1新建2改签3变更4取消5其他
     */
    private Integer formType;

    private String type;
    /**
     * 票种名称
     */
    private String ticketName;

    /**
     * 结算价
     */
    private String settlePrice;

    /**
     * 销售价
     */
    private String goodsPrice;

    /**
     * 房数
     */
    private Integer goodsNumber;

    /**
     * 房间数量
     */
    private Integer roomNum;

    /**
     * 房型
     */
    private String hotelType;

    /**
     * 用户姓名
     */
    private String receiver;

    /**
     * 用户联系方式
     */
    private String mobile;

    /**
     * 证件类型
     */
    private String cardType;

    /**
     * 证件号码
     */
    private String cardSn;

    private String postscript;

    /**
     * 父母邦备注
     */
    private String fmbComment;

    /**
     * 套餐备注
     */
    private String ticketComment;

    /**
     * 酒店名称
     */
    private String shopName;

    /**
     * 酒店联系电话
     */
    private String shopTel;

    /**
     * 酒店传真号码
     */
    private String shopFax;

    /**
     * 酒店确认人
     */
    private String confirmPeople;

    /**
     * 酒店确认号
     */
    private String confirmSn;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime playTime;

    /**
     * 离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime leaveTime;


    private String playDate;
    private String leaveDate;
    public String getPlayDate() {
        if (playTime != null){
            return playTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return "";
    }

    public String getLeaveDate() {
        if (leaveTime != null){
            return leaveTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return "";
    }

    /**
     * 后台操作uid
     */
    private Integer adminUid;

    private String kefuInfo = "电话：400-680-9888 传真号码：021-53085657";

    private String adminName = "zengfulong";

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public String getNowTimeStr(){
        return FmbDateUtil.FORMATTER_FULL.format(LocalDateTime.now());
    }

    private Integer confirmRecheckStatus;

    private String confirmRecheckStatusStr;

    private List<FmbxHotelGoodsDetail> hotelGoodsDetail;
}
