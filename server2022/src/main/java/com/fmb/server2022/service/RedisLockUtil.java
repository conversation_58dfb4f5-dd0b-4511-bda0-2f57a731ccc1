package com.fmb.server2022.service;

import com.fmb.basic.FmbConstants;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 * @Date: 2023/9/13 10:06 上午
 */
public class RedisLockUtil {

    private static Logger logger = LoggerFactory.getLogger(RedisLockUtil.class);

    private static JedisPool jedisPool ;

    @Autowired
    public RedisLockUtil(JedisPool jedisPool) {
        RedisLockUtil.jedisPool = jedisPool ;
    }


    /**
     * 防止一个方法执行过于频繁
     * @param key
     * @param millisecondsToExpire
     * @return
     */
    public static boolean testIsRunTooMuchInShotTime(  String key,long millisecondsToExpire) {

        boolean flag = false ;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String eval = jedis.set(key, "", new SetParams().nx().px(millisecondsToExpire));
            flag = "OK".equals(eval) ;
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return flag ;
    }

}
