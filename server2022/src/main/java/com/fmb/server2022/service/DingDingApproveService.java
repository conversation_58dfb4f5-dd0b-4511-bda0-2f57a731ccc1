package com.fmb.server2022.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiCspaceAddRequest;
import com.dingtalk.api.request.OapiFileUploadSingleRequest;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiProcessGetByNameRequest;
import com.dingtalk.api.request.OapiProcessinstanceCreateRequest;
import com.dingtalk.api.request.OapiProcessinstanceCspaceInfoRequest;
import com.dingtalk.api.request.OapiProcessinstanceFileUrlGetRequest;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.request.OapiProcessinstanceListidsRequest;
import com.dingtalk.api.request.OapiSmartworkHrmEmployeeQueryonjobRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiCspaceAddResponse;
import com.dingtalk.api.response.OapiFileUploadSingleResponse;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiProcessGetByNameResponse;
import com.dingtalk.api.response.OapiProcessinstanceCreateResponse;
import com.dingtalk.api.response.OapiProcessinstanceCspaceInfoResponse;
import com.dingtalk.api.response.OapiProcessinstanceFileUrlGetResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.api.response.OapiProcessinstanceListidsResponse;
import com.dingtalk.api.response.OapiSmartworkHrmEmployeeQueryonjobResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.fmb.server2022.config.DingDingConfig;
import com.fmb.server2022.domain.DingdingFileInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpContract;
import com.fmb.server2022.fmbx.entity.FmbxThirdsysUser;
import com.fmb.server2022.fmbx.service.IFmbxBpContractService;
import com.fmb.server2022.fmbx.service.IFmbxThirdsysUserService;
import com.fmb.util.SnowflakeIdWorker;
import com.taobao.api.ApiException;
import com.taobao.api.FileItem;
import com.taobao.api.internal.util.WebUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fmb.server2022.service.DingDingTokenService.DINGDING_AGENTID;
import static com.fmb.server2022.service.DingDingTokenService.DINGDING_OA_FORMTEST_PROCID;

@Service
public class DingDingApproveService {

    @Autowired
    DingDingTokenService dingDingTokenService;


    @Autowired
    DingdingFileService uploadService ;

    @Autowired
    IFmbxThirdsysUserService thirdsysUserService ;

    @Autowired
    IFmbxBpContractService contractService ;

    @Autowired
    DingDingApproveService self;

    @Autowired
    DingDingConfig dingDingConfig ;



    private static Logger logger = LoggerFactory.getLogger(DingDingApproveService.class);


    public int updateThirdUserInfo () {

        int count = 0 ;
        List<OapiV2UserGetResponse.UserGetResponse> fmbDingUsers = queryAllDingDingUser();

        LocalDateTime now = LocalDateTime.now();
        for (OapiV2UserGetResponse.UserGetResponse user : fmbDingUsers) {

            LambdaUpdateWrapper<FmbxThirdsysUser> updateWrapper = new LambdaUpdateWrapper<>();

            updateWrapper.eq(FmbxThirdsysUser::getThirdSysUid,user.getUserid())
                    .set(FmbxThirdsysUser::getThirdSysDepid,user.getDeptIdList().get(user.getDeptIdList().size()-1))
                    .set(FmbxThirdsysUser::getMobile,user.getMobile())
                    .set(FmbxThirdsysUser::getSysUsername,user.getName())
                    .set(FmbxThirdsysUser::getStatus,1)
                    .set(FmbxThirdsysUser::getEmail,user.getEmail())
                    .set(FmbxThirdsysUser::getUtime, now)
            ;

            boolean update = thirdsysUserService.update(null, updateWrapper);
            if (update) {
                count++ ;
            }

        }


        //更新 就的用户的信息
        LambdaUpdateWrapper<FmbxThirdsysUser> oldUserUpdateWrapper = new LambdaUpdateWrapper<>();

        oldUserUpdateWrapper.lt(FmbxThirdsysUser::getUtime,now)
                .set(FmbxThirdsysUser::getStatus,0)
        ;

        thirdsysUserService.update(null,oldUserUpdateWrapper) ;
        return count ;

    }


    /**
     * 读取 钉钉通讯录  ,读取试用期和正式员工数据
     *
     * @return
     */
    public List<OapiV2UserGetResponse.UserGetResponse> queryAllDingDingUser() {

        List<String> userList = new ArrayList<>();
        List<OapiV2UserGetResponse.UserGetResponse> rList = new ArrayList<>();

        try {

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/queryonjob");
            //查询所有在职员工的id
            OapiSmartworkHrmEmployeeQueryonjobRequest req = new OapiSmartworkHrmEmployeeQueryonjobRequest();

//        在职员工子状态筛选，可以查询多个状态。不同状态之间使用英文逗号分隔。
//        2：试用期
//        3：正式
//        5：待离职
//       -1：无状态
            req.setStatusList("2,3");
            req.setOffset(0L);
            req.setSize(50L);

            OapiSmartworkHrmEmployeeQueryonjobResponse rsp = null;

            //请求第一页
            rsp = client.execute(req, dingDingTokenService.getDingDingToken());

            userList = rsp.getResult().getDataList();

            //如果还有下一页
            while (rsp.getResult().getNextCursor() != null && rsp.getResult().getNextCursor() > 0) {

                req.setOffset(rsp.getResult().getNextCursor());
                rsp = client.execute(req, dingDingTokenService.getDingDingToken());
                userList.addAll(rsp.getResult().getDataList());

            }

        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        //根据id查询具体信息
        for (String uid : userList) {

            OapiV2UserGetResponse.UserGetResponse userGetResponse = readUserInfo(uid);


            rList.add(userGetResponse);

//            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/list");
//
//            OapiSmartworkHrmEmployeeListRequest req = new OapiSmartworkHrmEmployeeListRequest();
//            req.setUseridList(uid);
//            req.setAgentid(DINGDING_AGENTID);
//            OapiSmartworkHrmEmployeeListResponse rsp = null;
//            try {
//                rsp = client.execute(req, dingDingTokenService.getDingDingToken());
//
////                System.out.println(JSON.toJSONString(  rsp.getResult()));
//
//
//                rsp.getResult().stream().forEach(vo -> {
//
//                    FmbDingUser fmbDingUser = new FmbDingUser();
//                    rList.add(fmbDingUser);
//                    fmbDingUser.setUserid(uid);
//                    for (OapiSmartworkHrmEmployeeListResponse.EmpFieldVO fieldVO : vo.getFieldList()) {
//
//                        if ("sys00-name".equals(fieldVO.getFieldCode())) {
//                            fmbDingUser.setName(fieldVO.getValue());
//                        } else
//                        if ("sys00-mobile".equals(fieldVO.getFieldCode())) {
//                            String value = fieldVO.getValue();
//
//                            value = StringUtils.replace(value,"+86-","");
//                            fmbDingUser.setMobile(value);
//                        }
//                        else
//                        if ("sys00-deptIds".equals(fieldVO.getFieldCode())) {
//
//
//                            String value = fieldVO.getValue();
//                            //一个人可能多个部门id , 以 | 分隔, 取最后一个
//                            String[] split = StringUtils.split(value, '|');
//                            fmbDingUser.setDepId(split[split.length-1]);
//                        }
//                        else
//                        if ("sys00-position".equals(fieldVO.getFieldCode())) {
//                            fmbDingUser.setPosition(fieldVO.getValue());
//                        }
//
//                    }
//
//                    logger.info("fmbDingUser={}", fmbDingUser.toString());
//
//                });
//
//            } catch (ApiException ex) {
//                long errorid = SnowflakeIdWorker.getInstance().nextId();
//                logger.error("errorid " + errorid, ex);
//            }

        }

        return rList;

    }

    private OapiV2UserGetResponse.UserGetResponse readUserInfo(String uid) {


        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
            req.setUserid(uid);
            req.setLanguage("zh_CN");
            OapiV2UserGetResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());
            return rsp.getResult();
        } catch (ApiException ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        return null ;

    }


    /**
     * 批量发送订单通知
     *
     * @param users
     * @param text
     * @return
     */
    public boolean sendMsg(List<String> users, String text) {
        String collect = users.stream().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(collect)) {
            return sendMsg(collect, text);
        }
        return false;

    }

    /**
     * 发送钉钉通知
     *
     * @param userid
     * @param text
     * @return
     */
    public boolean sendMsg(String userid, String text) {

        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(DINGDING_AGENTID);
        request.setUseridList(userid);
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("text");
        msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
        msg.getText().setContent(text);
        request.setMsg(msg);

        OapiMessageCorpconversationAsyncsendV2Response rsp = null;
        try {
            rsp = client.execute(request, dingDingTokenService.getDingDingToken());

            return rsp.getErrcode() == 0L;
        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        return false;
    }

    /**
     * 获取钉钉应用信息
     *
     * @param name
     */
    public String processByName(String name) {

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/get_by_name");
            OapiProcessGetByNameRequest req = new OapiProcessGetByNameRequest();
            req.setName(name);
            OapiProcessGetByNameResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());

            return rsp.getBody();
        } catch (ApiException ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        return name;
    }


    /**
     * 创建审批表单  表单并提交
     *
     * @param userid  钉钉userid
     * @param userDepid 钉钉部门id
     * @param bpcid  合同id
     * @return
     */
    public String createOAForm(String userid, Long userDepid,Integer bpcid) {

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
            OapiProcessinstanceCreateRequest req = new OapiProcessinstanceCreateRequest();
            req.setProcessCode(DINGDING_OA_FORMTEST_PROCID);
            req.setOriginatorUserId(userid);
            req.setDeptId(userDepid);

            List<OapiProcessinstanceCreateRequest.FormComponentValueVo> list3 = new ArrayList<>();

            OapiProcessinstanceCreateRequest.FormComponentValueVo v_bangong = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            v_bangong.setName("办公地");
            v_bangong.setValue("上海");

            OapiProcessinstanceCreateRequest.FormComponentValueVo v_lcname = new OapiProcessinstanceCreateRequest.FormComponentValueVo();

            v_lcname.setName("流程名称");
            v_lcname.setValue("测试728");

            OapiProcessinstanceCreateRequest.FormComponentValueVo v_pic = new OapiProcessinstanceCreateRequest.FormComponentValueVo();

            v_pic.setName("图片");
            v_pic.setValue(JSON.toJSONString(new String[]{"http://a.fmbimg.com/static/images/revision/xin2.png"}));

            OapiProcessinstanceCreateRequest.FormComponentValueVo v_note = new OapiProcessinstanceCreateRequest.FormComponentValueVo();

            v_note.setName("说明");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            v_note.setValue("来自API调用" + simpleDateFormat.format(Calendar.getInstance().getTime()));

            //上传审批附件
//            OapiProcessinstanceCreateRequest.FormComponentValueVo formComponentValueVo10 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("spaceId", "163xxxx658");
//            jsonObject.put("fileName", "IMG_2322.PNG");
//            jsonObject.put("fileSize", "276297");
//            jsonObject.put("fileType", "png");
//            jsonObject.put("fileId", "405xxxxx777");
//            Object o[] = new Object[]{jsonObject};
//            String s = JSON.toJSONString(o);
//            formComponentValueVo10.setName("附件");
//            formComponentValueVo10.setValue(s);
//
//            list3.add(formComponentValueVo10);

            list3.add(v_bangong);
            list3.add(v_lcname);
            list3.add(v_note);
            list3.add(v_pic);

            req.setFormComponentValues(list3);

            OapiProcessinstanceCreateResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());


//            返回类似 "{"errcode":0,"errmsg":"ok","process_instance_id":"KkMy4cRHR3K-9p879r_jGw00681661147761","request_id":"16kd1xoretsq9"}"
//            System.out.println(rsp.getBody());
            if (rsp.getErrcode()==0) {
                String processInstanceId = rsp.getProcessInstanceId();

                LambdaUpdateWrapper<FmbxBpContract> contractLambdaQueryWrapper = new LambdaUpdateWrapper<>();

                contractLambdaQueryWrapper.eq(FmbxBpContract::getBpcId,bpcid)
                            .set(FmbxBpContract::getBpDingProcessid,processInstanceId)
                 ;
                boolean update = contractService.update(null, contractLambdaQueryWrapper);


                return processInstanceId;
            }

        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        return "";
    }


    /**
     * 读取审批详情
     * 并把相关数据写入数据库
     *
     * @param id 审批实例的id
     */
    public OapiProcessinstanceGetResponse.ProcessInstanceTopVo readOaApproceDetail(String id) {

        OapiProcessinstanceGetResponse rsp = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");

            OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
            req.setProcessInstanceId(id);

            int counter = 0 ;
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo rspProcessInstance = null ;
            while (counter<3){


                rsp = client.execute(req, dingDingTokenService.getDingDingToken());

                rspProcessInstance = rsp.getProcessInstance();

                if (rspProcessInstance != null) {
                    buildDbContractWithResponse(rspProcessInstance,id) ;
                    break;
                }

                counter ++ ;

            }


            return rspProcessInstance;
        } catch (ApiException ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        return null;
    }


    /**
     *
     * @param dingResponse  钉钉原始返回的 审批实例对象
     * @param instanceId  审批实例的id
     * @return
     */
    private FmbxBpContract buildDbContractWithResponse(OapiProcessinstanceGetResponse.ProcessInstanceTopVo dingResponse, String
            instanceId) {

//        System.out.println("-----result--1");
//        System.out.println( JSON.toJSONString( dingResponse));
//        System.out.println("-----result--2");

        FmbxBpContract partnerContract = new FmbxBpContract();
        partnerContract.setBpDingProcessid(instanceId);


        //查询看该合同
        LambdaQueryWrapper<FmbxBpContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FmbxBpContract::getBpDingProcessid,instanceId) ;
        FmbxBpContract checkExist = contractService.getOne(wrapper);

        ArrayList<HashMap> records = new ArrayList<>();
        for (OapiProcessinstanceGetResponse.OperationRecordsVo operationRecordsVo : dingResponse.getOperationRecords()) {

            HashMap<String, Object> recs = new HashMap<>();
            recs.put("operator", getDingdingUserRealName( operationRecordsVo.getUserid()));
            recs.put("dingUserid", operationRecordsVo.getUserid());
            recs.put("ctime", DateUtil.formatDateTime(operationRecordsVo.getDate()));
            recs.put("note", operationRecordsVo.getRemark());
            recs.put("type", operationRecordsVo.getOperationType());
            recs.put("result", operationRecordsVo.getOperationResult());

            records.add(recs) ;

        }



        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = dingResponse.getFormComponentValues();

        ArrayList<DingdingFileInfo> allFileInfo = new ArrayList<>();


        // 冗长的解析...
        //下面 的代码来自 respGenCode 生成.因为钉钉返回的数据结构表单项 有30多项...
        int cou = 0 ;
        for (OapiProcessinstanceGetResponse.FormComponentValueVo form : formComponentValues) {


            String name = form.getName();
            String value = form.getValue() ;

//            System.out.println("--------"+name+"--------"+form.getComponentType());
//            System.out.println(name);
//            System.out.println(value);

            if ("合同号".equals(name)) {
                partnerContract.setBpcNumber(value);
            }
            else if ("地区".equals(name)) {
                // 选择类型
                if ("上海".equals(value)) {
                    partnerContract.setCityId(2);
                }
                if ("北京".equals(value)) {
                    partnerContract.setCityId(1);
                }
            }

            else if ("业务线".equals(name)) {
                // 选择类型
                if ("酒店".equals(value)) {
                    partnerContract.setBusinessType(2);
                }
                if ("北京活动票务".equals(value)) {
                    partnerContract.setBusinessType(3);
                }
                if ("上海活动票务".equals(value)) {
                    partnerContract.setBusinessType(5);
                }
                if ("运营".equals(value)) {
                    partnerContract.setBusinessType(20);
                }
            }

            else if ("合同类型".equals(name)) {
                // 选择类型
                if ("非标准合同".equals(value)) {
                    partnerContract.setBpTemplateType(2);
                }
                if ("标准合同".equals(value)) {
                    partnerContract.setBpTemplateType(1);
                }
            }

            else if ("合同签署方式".equals(name)) {
                // 选择类型
                if ("新签合同".equals(value)) {
                    partnerContract.setBpCreateType(1);
                }
                if ("续签合同".equals(value)) {
                    partnerContract.setBpCreateType(2);
                }
                if ("补充协议".equals(value)) {
                    partnerContract.setBpCreateType(3);
                }
            }

            else if ("合同名称".equals(name)) {
                partnerContract.setBpcName(value);
            }

            else if ("合同甲方".equals(name)) {
                partnerContract.setBpUserA(value);
            }

            else if ("合同乙方".equals(name)) {
                partnerContract.setBpUserB(value);
            }

            else if ("合同丙方".equals(name)) {
                partnerContract.setBpUserC("null".equals(value)?"":value);
            }
            else if ("合同开始日期".equals(name)) {
                partnerContract.setBpStartDate(DateUtil.parseDate( value).toLocalDateTime().toLocalDate());
            }
            else if ("合同结束日期".equals(name)) {
                partnerContract.setBpEndDate(DateUtil.parseDate( value).toLocalDateTime().toLocalDate());
            }

            else if ("合同期限".equals(name)) {
                partnerContract.setBpcDateNote(value);
            }

            else if ("合同总金额（元）".equals(name)) {
                partnerContract.setBpcAllMoney(value);
            }

            else if ("具体售卖商品".equals(name)) {
                partnerContract.setProductDetail(value);
            }

            else if ("历史预付款保证金（元）".equals(name)) {
                partnerContract.setHistoryPrePayInfo("null".equals(value)?0: Integer.parseInt(value));
            }

            else if ("预付款保证金（元）".equals(name)) {
                partnerContract.setPrePay(Integer.parseInt(value));
            }

            else if ("预付款保证金说明".equals(name)) {
                partnerContract.setPrePayNote(value);
            }

            else if ("毛利率（%）".equals(name)) {
                partnerContract.setGrossRate(new BigDecimal(value));
            }

            else if ("是否提供发票".equals(name)) {
                partnerContract.setInvoiceInfo(value);
            }

            else if ("项目负责人".equals(name)) {
                partnerContract.setProductLeader(value);
            }

            else if ("合同简介".equals(name)) {
                partnerContract.setBpDesc(value);
            }


            else if ("证照上传".equals(name)) {
                // 附件操作

                collectFileNode(allFileInfo, value,"证照上传");



            }

            else if ("合同上传".equals(name)) {
                // 附件操作
                collectFileNode(allFileInfo, value,"合同上传");
            }

            else if ("其他附件上传".equals(name)) {
                // 附件操作
                collectFileNode(allFileInfo, value,"其他附件上传");
            }

            else if ("公共附件上传".equals(name)) {
                // 附件操作
                collectFileNode(allFileInfo, value,"公共附件上传");
            }

            else if ("申请用印".equals(name)) {
                // 选择类型
                partnerContract.setReqSeal(value);
            }

            else if ("用印状态".equals(name)) {
                // 选择类型
                if ("对方先盖章".equals(value)) {
                    partnerContract.setBpSealType(2);
                }
                if ("我方先盖章".equals(value)) {
                    partnerContract.setBpSealType(1);
                }
            }

            else if ("合同回寄及相关信息备注".equals(name)) {
                partnerContract.setBpReciverNote(value);
            }
            else if ("拒绝意见列表".equals(name)) {
                partnerContract.setRejectTableInfo(value);
            }

            else if ("快递单号".equals(name)) {
                partnerContract.setBpReciverExpressNo("null".equals(value)?"":value);
            }

            else if ("快递公司".equals(name)) {
                // 选择类型
                partnerContract.setBpReciverExpressCompany("null".equals(value)?"":value);
            }

            else if ("用印情况".equals(name)) {
                // 选择类型
                // 对方竟然返回的null字符串.....
                partnerContract.setBpSealUseInfo("null".equals(value)?"":value);
            }

            else if ("不予处理说明".equals(name)) {
                partnerContract.setBpSealNoNote("null".equals(value)?"":value);
            }

            else if ("备注".equals(name)) {
                partnerContract.setBpNote("null".equals(value)?"":value);
            }

            else if ("盖章合同上传".equals(name)) {
                // 附件操作
                collectFileNode(allFileInfo, value,"盖章合同上传");
            }

            else if ("业务备注".equals(name)) {
                partnerContract.setBpBusinessNote("null".equals(value)?"":value);
            }

//            System.out.println(cou+"--- done ");
//            cou ++ ;
        }

        LocalDateTime now = LocalDateTime.now();

        //设置操作详情
        partnerContract.setDingdingOpDetail(JSON.toJSONString(records));
//        String dingStatusStr = dingResponse.getStatus();


        partnerContract.setBpcRunuingStatus(getContractStatusByActionList(records));

        //默认有效
        if (partnerContract.getBpEndDate().isAfter(now.toLocalDate())) {
            partnerContract.setBpcAllStatus(1);
        }else {
            partnerContract.setBpcAllStatus(2);
        }

//        partnerContract.setBpcAllStatus(getAllStatus(dingResponse.getStatus()));

        if(StringUtils.isBlank(partnerContract.getRejectTableInfo())){
            partnerContract.setRejectTableInfo("{}");
        }

        partnerContract.setCreaterDingUserid(dingResponse.getOriginatorUserid());

        if (checkExist != null) {
            partnerContract.setBpcId(checkExist.getBpcId());
            partnerContract.setUtime(now);
            contractService.updateById(partnerContract) ;
        }else {
            //新增合同
            partnerContract.setBpDingCheckTime(now);
            partnerContract.setCtime(now);
            partnerContract.setUtime(now);
            contractService.save(partnerContract) ;
        }


        processContractFile(partnerContract,allFileInfo ) ;

        return partnerContract ;
    }

    private Integer getAllStatus(String status) {
//        整体状态:1-新创建,2-审批中,3-被终止,4-完成,5-取消


        switch (status){

            case "NEW" :
                return 1 ;
            case "RUNNING" :
                return 2 ;
            case "TERMINATED" :
                return 3 ;
            case "COMPLETED" :
                return 4 ;
            case "CANCELED" :
                return 5 ;
        }
        return 0 ;

    }

    private String getDingdingUserRealName(String userid) {

        LambdaQueryWrapper<FmbxThirdsysUser> wraper = new LambdaQueryWrapper<>();
        wraper.eq(FmbxThirdsysUser::getThirdSysUid,userid) ;

        FmbxThirdsysUser one = thirdsysUserService.getOne(wraper);

        if (one != null) {
            return one.getSysUsername();
        }
        return  "未知" ;

    }

    /**
     * 存储所有的合同附件到文件表
     * @param partnerContract
     * @param allFileInfo
     */
    private void processContractFile(FmbxBpContract partnerContract, ArrayList<DingdingFileInfo> allFileInfo) {

        for (DingdingFileInfo fileInfo : allFileInfo) {
//            System.out.println(fileInfo);

            String fileUrl = queryDownLoadInstanceFileUrl(partnerContract.getBpDingProcessid(), fileInfo.getFileId());

            uploadService.downLoadDingdingFile(fileUrl,fileInfo,partnerContract.getBpcId());

        }


    }

    /**
     * 把钉钉附件信息 存入 allFileInfo
     * @param allFileInfo
     * @param value
     * @param formname
     */
    private void collectFileNode(ArrayList<DingdingFileInfo> allFileInfo, String value,String formname) {
        if (value != null) {

            List<DingdingFileInfo> dingdingFileInfos = JSONArray.parseArray(value, DingdingFileInfo.class);

            if (dingdingFileInfos != null) {
                for (DingdingFileInfo fileInfo : dingdingFileInfos) {
                    fileInfo.setFormName(formname);
                }
                allFileInfo.addAll(dingdingFileInfos);
            }


        }

    }

    /**
     * 根据返回的 字段输出一些代码 ....要不太费劲了 .
     * @param form
     */
    private void respGenCode(OapiProcessinstanceGetResponse.FormComponentValueVo form) {

        System.out.println(JSON.toJSONString(form));

        if ("DDSelectField".equals( form.getComponentType())) {

            System.out.println("else if (\""+form.getName()+"\".equals(form.getName())) {\n" +
                    "                // 选择类型  \n" +
                    "                if (\""+form.getValue()+"\".equals(value)) {\n" +
                    "                    partnerContract.setCityId(1);\n" +
                    "                }\n" +
                    "            }\n");

        }
        if ("TextField".equals( form.getComponentType())
                || "TextareaField".equals( form.getComponentType())
                || "MoneyField".equals( form.getComponentType())
                || "TextNote".equals( form.getComponentType())
                || "TextNote".equals( form.getComponentType())

                ) {

            System.out.println("else if (\""+form.getName()+"\".equals(form.getName())) {\n" +
                    "                    partnerContract.setCityId(1);\n" +
                    "            }\n");

        }
        if ("DDDateField".equals( form.getComponentType())


                ) {

            System.out.println("else if (\""+form.getName()+"\".equals(form.getName())) {\n" +
                    "                    partnerContract.setCityId(1);\n" +
                    "            }\n");

        }
        if ("DDAttachment".equals( form.getComponentType())


                ) {

            System.out.println("else if (\""+form.getName()+"\".equals(form.getName())) {\n" +
                    "                    // 附件操作\n" +
                    "                    partnerContract.setCityId(1);\n" +
                    "            }\n");

        }
    }


    /**
     * 目前测试没成功
     * 上传文件到 钉盘   需要前端code....
     * 钉钉 不提供直接上传到钉盘的 服务器API 鄙视一下~~~
     */
    public void uploadFileToDingpan() {

        try {
            Long spaceId = null;
            //获取spaceid
            {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/cspace/info");
                OapiProcessinstanceCspaceInfoRequest req = new OapiProcessinstanceCspaceInfoRequest();
                req.setUserId("4827080520296281");
                req.setAgentId(DINGDING_AGENTID);
                OapiProcessinstanceCspaceInfoResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());

                spaceId = rsp.getResult().getSpaceId();
                System.out.println(spaceId);
                System.out.println(rsp.getBody());
            }

            if (spaceId == null) {
                return;
            }

            File fileUp = new File("/Users/<USER>/Downloads/WX20220727.png");
            String mediaId = null;
            {

                try {

                    OapiFileUploadSingleRequest request = new OapiFileUploadSingleRequest();
                    request.setFileSize(fileUp.length());
                    request.setAgentId(DINGDING_AGENTID.toString());

                    DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/file/upload/single?" + WebUtils.buildQuery(request.getTextParams(), "utf-8"));

                    request = new OapiFileUploadSingleRequest();
                    request.setFile(new FileItem(fileUp));
                    OapiFileUploadSingleResponse response = client.execute(request, dingDingTokenService.getDingDingToken());
                    System.out.println(response.getBody());

                    mediaId = response.getMediaId();
                } catch (IOException e) {
                    e.printStackTrace();
                }
// 必须重新new一个请求

            }

            //上传附件到流程
            {

                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/cspace/add");
                OapiCspaceAddRequest req = new OapiCspaceAddRequest();
                req.setAgentId(DINGDING_AGENTID.toString());
                req.setCode("PROC-A057AAB1-A778-4DE2-9C1E-D8DEF8A82E5E");
                req.setMediaId(mediaId);
                req.setSpaceId(spaceId + "");
                req.setName(fileUp.getName());
                req.setOverwrite(true);
                req.setHttpMethod("GET");
                OapiCspaceAddResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());
                System.out.println(rsp.getBody());

            }

        } catch (ApiException e) {
            e.printStackTrace();
        }

    }

    /**
     * 流程被拒绝
     *
     * @param processInstanceId
     * @param type  agree-同意    refuse-拒绝  start-实例开始
     */
    public void processFinish(String processInstanceId,String type) {

        LambdaQueryWrapper<FmbxBpContract> wrappter = new LambdaQueryWrapper<>();
        wrappter.eq(FmbxBpContract::getBpDingProcessid,processInstanceId);

        FmbxBpContract contract = contractService.getOne(wrappter);

        logger.info("processInstanceId={},type={}",processInstanceId,type);
        if (contract != null) {

            if ("refuse".equals(type)) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo instanceTopVo = readOaApproceDetail(processInstanceId);

                readRespAndSet(contract, instanceTopVo);

                contractService.updateById(contract) ;

            }
            else if ("agree".equals(type)) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo instanceTopVo = readOaApproceDetail(processInstanceId);

                readRespAndSet(contract, instanceTopVo);

                contractService.updateById(contract) ;

            }


        }else if ("start".equals(type)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo instanceTopVo = readOaApproceDetail(processInstanceId);

        }

    }

    /**
     * 抽取 返回信息 并设置合同
     * @param contract
     * @param instanceTopVo
     */
    private void readRespAndSet(FmbxBpContract contract, OapiProcessinstanceGetResponse.ProcessInstanceTopVo instanceTopVo) {
        List<OapiProcessinstanceGetResponse.OperationRecordsVo> records = instanceTopVo.getOperationRecords();

        //读取最后一个操作里的审批说明内容
        String remark = records.get(records.size() - 1).getRemark();

        contract.setBpDingRejectInfo(remark);

        contract.setBpDingCheckTime(LocalDateTimeUtil.of(instanceTopVo.getFinishTime().getTime()));
    }

    /**
     * 下载钉钉OA审批里的附件附件
     * @param instanceid
     * @param fileId
     */
    public String queryDownLoadInstanceFileUrl (String instanceid, String fileId) {


        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/file/url/get");
            OapiProcessinstanceFileUrlGetRequest req = new OapiProcessinstanceFileUrlGetRequest();
            OapiProcessinstanceFileUrlGetRequest.GrantCspaceRequest obj1 = new OapiProcessinstanceFileUrlGetRequest.GrantCspaceRequest();
            obj1.setProcessInstanceId(instanceid);
            obj1.setFileId(fileId);
            req.setRequest(obj1);
            OapiProcessinstanceFileUrlGetResponse rsp = client.execute(req, dingDingTokenService.getDingDingToken());

            String downloadUri = rsp.getResult().getDownloadUri();
//            System.out.println(rsp.getBody());

            return downloadUri ;
        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        return "";
    }


    /**
     * 循环读取 审批实例
     * @param processCode
     */
    public ArrayList<String> instanceListids(String processCode){

        ArrayList<String> ids = new ArrayList<>();
        try {

            Long nextCursor = 0L;
//            int count = 0 ;
            long now = System.currentTimeMillis();
            while (nextCursor != null) {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/listids");
                OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
                req.setProcessCode(processCode);
                req.setStartTime(now -20*60*60*24*1000L);
                req.setEndTime(now -15*60*60*24*1000L);
                req.setSize(20L);
                req.setCursor(nextCursor);
//        req.setUseridList("manager1,manager2");
                OapiProcessinstanceListidsResponse rsp = client.execute(req,  dingDingTokenService.getDingDingToken());

                ids.addAll( rsp.getResult().getList());
//                System.out.println(rsp.getBody());
                nextCursor = rsp.getResult().getNextCursor();

//                count ++ ;
//                if (count>10) {
//                    break;
//                }
            }

        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        for (String id : ids) {

            readOaApproceDetail(id) ;

        }

        return ids ;

    }




    /**
     * 根据 operationRecords 返回合同状态
     * @return 合同状态:0-发起申请,1-直属经理审批,2-法务审批,3-上海财务审批,4-CEO审批,5-合同归档提交,6-合同确认无误,7-拒绝
     * @param records
     */
    public int getContractStatusByActionList(ArrayList<HashMap> records){
        //只有1条 一定是刚开始
        if (records.size()==1) {
            return  0 ;
        }

        //最后一条操作结果是 REFUSE 直接返回7
        HashMap lastRecord = records.get(records.size() - 1);
        if ("REFUSE".equals( lastRecord.get("result").toString())) {
            return  7 ;
        }


        String dingUserid1 = "dingUserid";

        String lastUserId = lastRecord.get(dingUserid1).toString();

        //所有的操作者
        Set<String> dingUserid = records.stream().map(x -> {
            return x.get(dingUserid1).toString();
        }).collect(Collectors.toSet());


        //主管审过
        boolean flagZhuguan = false ;

        for (String s : dingUserid) {
            if (dingDingConfig.getManager().contains(s)) {
                flagZhuguan = true;
                break;
            }
        }


        //财务审过
        boolean flagCaiwu = dingUserid.contains(dingDingConfig.getFinance());
        // 是否有韩璐 法务审过 ,因为流程里 财务可能出现多次
        boolean flagFawu = dingUserid.contains(dingDingConfig.getLegal());

        boolean flagCEO = dingUserid.contains(dingDingConfig.getCeo());

        if(flagZhuguan&&!flagFawu){
            return 1 ;
        }

        if(flagZhuguan&&flagFawu && !flagCaiwu){
            return 2 ;
        }

        if(flagCaiwu&& lastUserId.equals(dingDingConfig.getFinance())){
            return 3 ;
        }
        if(flagCEO&& lastUserId.equals(dingDingConfig.getCeo())){
            return 4 ;
        }

        int leagalCounter = 0 ;
        for (HashMap record : records) {
            if (record.get(dingUserid1).equals(dingDingConfig.getLegal())) {
                leagalCounter ++ ;
            }
        }

        //ceo 批完 韩璐再完成1次操作是 合同归档提交
        if(flagCEO&& leagalCounter==2){
            return 5 ;
        }

        //ceo 批完 韩璐再完成2次操作是 合同确认无误
        if(flagCEO&& leagalCounter==3){
            return 6 ;
        }


        return 0 ;
    }


}
