package com.fmb.server2022.service.schedule.impel;

import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_CANCELORDERJOB;

@Service(value = JOB_NAME_CANCELORDERJOB)
public class CancelOrderJob implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(CancelOrderJob.class);

    @Autowired
    OrderService orderService ;

    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {
            orderService.cancelOrder(job.getJobFmbxId(),"auto_cancel");
        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
