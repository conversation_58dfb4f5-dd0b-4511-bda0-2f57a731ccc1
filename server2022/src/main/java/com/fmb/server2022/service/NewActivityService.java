package com.fmb.server2022.service;

//NEW_IMPORT_DOMAIN

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.domain.ActivityTagCateDomain;
import com.fmb.server2022.domain.ActivityTagListDomain;
import com.fmb.server2022.domain.ActivityXDomain;
import com.fmb.server2022.fmb_new.entity.FmbActivityTag;
import com.fmb.server2022.fmb_new.entity.FmbActivityTagList;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.entity.FmbNewActivity;
import com.fmb.server2022.fmb_new.entity.FmbProductIndex;
import com.fmb.server2022.fmb_new.service.IFmbActivityTagListService;
import com.fmb.server2022.fmb_new.service.IFmbActivityTagService;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmb_new.service.IFmbNewActivityService;
import com.fmb.server2022.fmb_new.service.IFmbProductIndexService;
import com.fmb.server2022.fmbx.entity.*;
import com.fmb.server2022.fmbx.service.IFmbxActivityExtService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpContractService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxMediaService;
import com.fmb.server2022.mapper.NewActivityDao;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqActivityTag;
import com.fmb.server2022.reqdomain.ReqActivityX;
import com.fmb.server2022.reqdomain.ReqaddNewActivity;
import com.fmb.util.upload.UploadFileOutInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NewActivityService {


    @Autowired
    IFmbAdminService adminService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IFmbxBpMainService bpMainService ;

    @Autowired
    IFmbxBpContractService contractService ;


    @Autowired
    IFmbNewActivityService fmbNewActivityService ;

    @Autowired
    IFmbProductIndexService productIndexService ;


    @Autowired
    NewActivityService self ;

    @Autowired
    IFmbxActivityService fmbxActivityService ;

    @Autowired
    IFmbxActivityExtService extService ;

    @Autowired
    IFmbxMediaService mediaService ;

    @Autowired
    IFmbxBpParterInfoService bpParterInfoService;

    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    SuiteService suiteService ;

    @Autowired
    IFmbActivityTagListService activityTagListService;

    @Autowired
    IFmbActivityTagService activityTagService;

    @Autowired
    NewActivityDao newActivityDao ;

    /**
     * 首次新增活动  大部分内容为空. 占位
     * @param input
     * @param resultMap
     */
    public void addActivity(ReqaddNewActivity input, HashMap<String,Object> resultMap) {

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        resultMap.put("result",0) ;
        LocalDateTime now = LocalDateTime.now();
        Integer aid = saveActivity2OldSystem(input,now);

        FmbxActivity xAct = new FmbxActivity();

        xAct.setXaid(aid);
        xAct.setBpsId(input.getBpsId());
        xAct.setBpId(input.getBpId());
        xAct.setBpcId("") ;
        //允许合同传空
        if (input.getBpcId()!=null && !input.getBpcId().isEmpty()) {
            xAct.setBpcId(input.getBpcId().stream().map(x->{ return x+"";}).filter(s->StringUtils.isNotBlank(s)).collect(Collectors.joining(",")));
        }

        xAct.setTicketType(6);
        //FmbxBpParterInfo bpInfo = bpParterInfoService.getById(input.getBpId());
        FmbxBps bps = bpsService.getById(input.getBpsId());
        xAct.setTitle(bps.getName());
//        xAct.setTicketType(input.getTicketType());

        xAct.setPlatId("9");
        xAct.setCityId(1);


        //根据  当前用户的 base city 确定 当前活动的分站
        Wrapper<FmbAdmin> wrAdmin = new LambdaQueryWrapper<FmbAdmin>()
                .eq(FmbAdmin::getUid,nowUserInfo.getUserId())
                .select(FmbAdmin::getUid,FmbAdmin::getName,FmbAdmin::getRealname,FmbAdmin::getBaseCityId)
                ;
        final FmbAdmin fmbAdmin = adminService.getOne(wrAdmin);
        if (fmbAdmin != null) {
            xAct.setCityId(fmbAdmin.getBaseCityId());
        }

        xAct.setSalesCityIds("1,2,3,4,5,6");
        xAct.setBusinessType(2);
        xAct.setCtime(now);
        xAct.setUtime(now);

        xAct.setCreateUid(nowUserInfo.getUserId());
        xAct.setCreateUidName(nowUserInfo.getUsername());



        boolean save = fmbxActivityService.save(xAct);
        if (save) {

            //默认添加 对应的富文本内容
            FmbxActivityExt extDB = new FmbxActivityExt();
            extDB.setXaid(xAct.getXaid());
            extDB.setRichTxt("");
            extDB.setCtime(now);
            extDB.setUtime(now);

            extService.save(extDB);

            resultMap.put("result",1) ;
            resultMap.put("xaid",xAct.getXaid()) ;
        }

    }

    /**
     * 同步新增活动信息
     * @param input
     * @param now
     * @return
     */
    public Integer saveActivity2OldSystem(ReqaddNewActivity input, LocalDateTime now) {
        FmbNewActivity atemp = new FmbNewActivity();
        atemp.setTitle(input.getTitle());

        //设置必须的默认值
        atemp.setActivityGroupType(4 );


        atemp.setStartTime(now);
        atemp.setEndTime(now);
        atemp.setApplyStartTime(now);
        atemp.setApplyEndTime(now);

        atemp.setLinkUtime(now);
        atemp.setUtime(now);
        atemp.setPublishTime(now);
        atemp.setCtime(now);

        fmbNewActivityService.save(atemp) ;

        FmbProductIndex pidx = new FmbProductIndex();
        pidx.setApId(atemp.getAid());
        pidx.setTitle(atemp.getTitle());

        BeanUtils.copyProperties(atemp,pidx);

        productIndexService.save(pidx) ;

        return atemp.getAid() ;
    }

    /**
     * 更新活动
     * @param input
     * @param resultMap
     */
    public void updateActivity(ReqaddNewActivity input, HashMap<String,Object> resultMap) {

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        Integer xaid = input.getXaid();

        resultMap.put("xaid",xaid) ;

        if (xaid ==null) {
            throw new BadLogicException("活动ID位空");
        }
        resultMap.put("result",0) ;

        checkActivityExist(xaid);

        FmbxActivity upDb = new FmbxActivity();
        BeanUtils.copyProperties(input,upDb);

        if (input.getBpcId()!=null && !input.getBpcId().isEmpty()) {
            upDb.setBpcId(input.getBpcId().stream().map(x->{ return x+"";}).filter(s->StringUtils.isNotBlank(s)).collect(Collectors.joining(",")));
        }else if (input.getBpcId()!=null &&input.getBpcId().isEmpty()){
            upDb.setBpcId("");
        }
        if (input.getVideoMediaId()==null){
            upDb.setVideoMediaId(0);
        }

        if (input.getBannerList()!=null ) {
            upDb.setBannerList(  input.getBannerList());

            final ArrayList<Integer> integers = new ArrayList<>();
            final String[] split = input.getBannerList().split(",");
            for (String s : split) {
                integers.add(Integer.parseInt(s)) ;
            }

            if (!integers.isEmpty()) {
                //final List<String> collect = mediaService.listByIds(integers).stream().map(b -> b.getUrl()).collect(Collectors.toList());
                final List<String> collect = new ArrayList<>();
                for (Integer id : integers){
                    FmbxMedia media = mediaService.getById(id);
                    if (media!=null && media.getUrl()!=null) {
                        collect.add(media.getUrl());
                    }
                }
                upDb.setBannerListUrl(JSON.toJSONString(collect));
            }else {
                upDb.setBannerListUrl("");
            }


        }

        if (input.getTagList()!=null) {
            Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                    .eq(FmbActivityTag::getAid, input.getXaid());
            List<FmbActivityTag> tagdbList = activityTagService.list(wrTag);
            for (FmbActivityTag tag : tagdbList) {
                activityTagService.removeById(tag.getId());
            }
            for (ActivityTagListDomain tag : input.getTagList()) {
                FmbActivityTag fmbActivityTag = new FmbActivityTag();
                if (tag.getTagListId()==null){
                    tag.setTagListId(0);
                }
                if (tag.getTagCatId()==null){
                    tag.setTagCatId(0);
                }
                BeanUtils.copyProperties(tag, fmbActivityTag);

                fmbActivityTag.setId(null);
                fmbActivityTag.setAid(input.getXaid());
                activityTagService.save(fmbActivityTag);
            }
        }

        LocalDateTime now = LocalDateTime.now();
        upDb.setUtime(now);

        upDb.setLastModifyUid(nowUserInfo.getUserId());
        upDb.setLastModifyUidName(nowUserInfo.getUsername());

        boolean b = fmbxActivityService.updateById(upDb);

        if (input.getRichTxt() != null) {
            FmbxActivityExt extDB = new FmbxActivityExt();
            extDB.setRichTxt(input.getRichTxt());
            extDB.setXaid(xaid);
            extDB.setUtime(now);
            extService.update(extDB, new LambdaUpdateWrapper<FmbxActivityExt>().eq(FmbxActivityExt::getXaid,input.getXaid()));
        }
        //FmbNewActivity
        Wrapper<FmbNewActivity> wrNewactivity = new LambdaQueryWrapper<FmbNewActivity>()
                .eq(FmbNewActivity::getAid, input.getXaid());
        FmbNewActivity fmbNewActivity = fmbNewActivityService.getOne(wrNewactivity);
        if(fmbNewActivity != null) {
            fmbNewActivity.setEditorUid(upDb.getEditorUid());
            fmbNewActivity.setMarketUid(upDb.getMarketUid());
            fmbNewActivityService.updateById(fmbNewActivity);
        }

        SuiteService.sendActivityChangeNotify(xaid);

        if (b) {
            resultMap.put("result",1) ;
        }

    }

    public void checkActivityExist(Integer xaid) {
        Wrapper<FmbxActivity> wtest = new LambdaQueryWrapper<FmbxActivity>().eq(FmbxActivity::getXaid, xaid);
        long count = fmbxActivityService.count(wtest);
        if (count == 0) {
            throw new BadLogicException("活动不存在");
        }
    }


    @DS(FmbConstants.DB_slave)
    public void activityDetail(Integer xaid, HashMap<String,Object> resultMap) {

        checkActivityExist(xaid);

        FmbxActivity activity = fmbxActivityService.getById(xaid);

        resultMap.put("activity",activity) ;

        List<FmbxMedia> mediaList = new ArrayList<>() ;


        //相关图片查询
        if (StringUtils.isNotBlank(activity.getBannerList())) {
            ArrayList<Integer> ids = new ArrayList<>();

            String[] split = activity.getBannerList().split(",");
            for (String s : split) {
                ids.add(Integer.parseInt(s)) ;
            }
            if (ids.size()>0) {
                Wrapper<FmbxMedia> wrMedia = new LambdaQueryWrapper<FmbxMedia>()
                        .select(FmbxMedia::getUrl,FmbxMedia::getMediaId)
                        .in(FmbxMedia::getMediaId,ids);
                mediaList = mediaService.list(wrMedia);
            }

        }
        resultMap.put("banners",mediaList) ;

        //视频信息
        if (activity.getVideoMediaId()!=null && activity.getVideoMediaId().intValue()!=0) {

            Wrapper<FmbxMedia> wrVideo = new LambdaQueryWrapper<FmbxMedia>()
                    .select(FmbxMedia::getUrl,FmbxMedia::getMediaId)
                    .eq(FmbxMedia::getMediaId,activity.getVideoMediaId());

            FmbxMedia one = mediaService.getOne(wrVideo);
            if (one != null) {
                resultMap.put("video",one) ;
            }

        }

        //富文本信息
        FmbxActivityExt activityExt = extService.getOne(Wrappers.lambdaQuery(FmbxActivityExt.class).eq(FmbxActivityExt::getXaid, xaid));

        resultMap.put("activityExt",activityExt) ;

        getActivityTag(xaid,resultMap);
        getActivityTagByCate(xaid,resultMap);

    }

/**
 * 活动列表
 */
   
public void selectActivityX(HashMap<String,Object> resultMap, ReqActivityX req) {


    // 将keyword 换为 xaid 和 title搜素
    if (StringUtils.isNotBlank(req.getKeyword())) {
        if (NumberUtil.isNumber(req.getKeyword())) {
            req.setXaid(NumberUtil.parseInt(req.getKeyword()));
        }else {
            req.setTitle(req.getKeyword());
        }
    }

      if (req.getDoExport().intValue()==0) {

          //单表查询
          IPage<ActivityXDomain> page = new Page<>(req.getPageno(), req.getPagesize());
          IPage<ActivityXDomain> rlist = suiteDao.selectActivityX(page, req);

          for (ActivityXDomain record : rlist.getRecords()) {

              record.setBanners(new JSONArray());

              final JSONArray jsonArray = JSONArray.parseArray(record.getBannerListUrl());
              if (jsonArray != null) {
                  record.setBanners(jsonArray);
                  record.setBannerListUrl(null);
              }

              record.setPublishLog(suiteService.queryLastPublishLog(record.getXaid()));

              if (6== record.getTicketType()) {
                  record.setGoodsType(6);
                  if (record.getFlagHotelDate()==0 && record.getFlagHotelReserve()==1) {
                      record.setGoodsType(7);
                  }
              }

          }

          resultMap.put("list", rlist.getRecords());
          resultMap.put("total", rlist.getTotal());
          resultMap.put("pages", rlist.getPages());
          resultMap.put("size", rlist.getSize());
          resultMap.put("current", rlist.getCurrent());

      }else{

          List<ActivityXDomain> lists = suiteDao.selectActivityX(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,ActivityXDomain.class, "活动列表", "selectActivityX");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }

    public void getActivityTagByCate(Integer xaid,HashMap<String,Object>resultMap){
        if (xaid==null){
            throw new BadLogicException("活动ID为空");
        }
        //公共标签
        List<ActivityTagListDomain> activityTagList = newActivityDao.selectActivityTagList();
        Map<Integer, List<ActivityTagListDomain>> groupBy = activityTagList.stream().collect(Collectors.groupingBy(ActivityTagListDomain::getTagCatId, HashMap::new,Collectors.toList()));
        List<ActivityTagCateDomain> cateDomainList = new ArrayList<>();
        for(Map.Entry<Integer,List<ActivityTagListDomain>> ele : groupBy.entrySet()){
            ActivityTagCateDomain c = new ActivityTagCateDomain();
            c.setCatId(ele.getKey());
            List<ActivityTagListDomain> tagList = ele.getValue().stream().sorted(Comparator.comparing(ActivityTagListDomain::getTagListId))
                    .collect(Collectors.toList());
            for (ActivityTagListDomain tag : tagList){
                Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                        .select(FmbActivityTag::getId)
                        .eq(FmbActivityTag::getAid,xaid)
                        .eq(FmbActivityTag::getTagListId,tag.getTagListId());
                long tagcount = activityTagService.count(wrTag);
                if (tagcount == 0) {
                    tag.setIsSelected(false);
                } else {
                    tag.setIsSelected(true);
                    tag.setId(activityTagService.list(wrTag).get(0).getId());
                }
            }
            c.setTagList(tagList);
            c.setCateName(ele.getValue().get(0).getCateName());
            cateDomainList.add(c);
        }
        resultMap.put("activityTagByCate",cateDomainList) ;
        Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                .select(FmbActivityTag::getId,FmbActivityTag::getAid,FmbActivityTag::getTagName)
                .eq(FmbActivityTag::getAid,xaid)
                .eq(FmbActivityTag::getTagListId,0);
        //活动自定义标签
        List<FmbActivityTag> tagList = activityTagService.list(wrTag);
        resultMap.put("activitySelfTag",tagList) ;
    }

    public void getActivityTagByName(ReqActivityTag input, HashMap<String,Object>resultMap){
        Wrapper<FmbActivityTagList> wrTag = new LambdaQueryWrapper<FmbActivityTagList>()
                .select(FmbActivityTagList::getId,FmbActivityTagList::getCatId,FmbActivityTagList::getTagName)
                .like(FmbActivityTagList::getTagName,input.getTagName());
        resultMap.put("activityTagList",activityTagListService.list(wrTag)) ;
    }

    public void chooseActivityTag(ReqActivityTag input, HashMap<String,Object>resultMap){
        if (input.getAid()==null){
            throw new BadLogicException("活动ID为空");
        }
        Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                .eq(FmbActivityTag::getAid, input.getAid());
        List<FmbActivityTag> tagdbList = activityTagService.list(wrTag);
        List<ActivityTagListDomain> taginList = input.getTagList();
        for (ActivityTagListDomain tag : taginList) {
            tag.setAid(input.getAid());
        }
        List<Integer> dbList = tagdbList.stream().map(FmbActivityTag::getId).collect(Collectors.toList());
        List<Integer> inList = taginList.stream().map(ActivityTagListDomain::getId).collect(Collectors.toList());

        //删除tagdbList-taginList
        List<Integer> rmList = dbList.stream().filter(entity -> !inList.contains(entity)).collect(Collectors.toList());
        if (rmList != null && rmList.size() > 0) {
            for (FmbActivityTag tag : tagdbList) {
                if(rmList.contains(tag.getId())){
                    activityTagService.removeById(tag.getId());
                }
            }
        }
        //修改taginList-tagdbList
        List<Integer> updList = inList.stream().filter(entity -> dbList.contains(entity)).collect(Collectors.toList());
        if (updList != null && updList.size() > 0) {
            for (ActivityTagListDomain tag : taginList) {
                if (tag.getId()!=null && updList.contains(tag.getId())) {
                    for (FmbActivityTag db : tagdbList) {
                        BeanUtils.copyProperties(tag, db);
                        activityTagService.updateById(db);
                    }
                }
            }
        }
        //新增 没有id的tag
        for (ActivityTagListDomain tag : taginList) {
            if (tag.getId()==null) {
                FmbActivityTag db = new FmbActivityTag();
                if (tag.getTagListId()==null){
                    tag.setTagListId(0);
                }
                if (tag.getTagCatId()==null){
                    tag.setTagCatId(0);
                }
                BeanUtils.copyProperties(tag, db);
                activityTagService.save(db);
            }
        }
        resultMap.put("result", 1);
    }

    public void getActivityTag(Integer xaid, HashMap<String,Object>resultMap){
        Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                .eq(FmbActivityTag::getAid,xaid)
                .orderByAsc(FmbActivityTag::getId);
        resultMap.put("activityTag",activityTagService.list(wrTag)) ;
    }

}
