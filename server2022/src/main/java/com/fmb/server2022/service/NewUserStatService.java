package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.fmb.server2022.domain.NewUserOrderStatDomain;
import com.fmb.server2022.mapper.SuiteDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.DB_slave;

/**
 * <AUTHOR>
 * @Date: 2023/8/3 1:37 下午
 */
@Service
public class NewUserStatService {

    private final SuiteDao suiteDao ;


    @Autowired
    public NewUserStatService(SuiteDao suiteDao) {
        this.suiteDao = suiteDao;
    }


    public NewUserOrderStatDomain newUserStat(Integer aid, String dateBegin, String dateEnd, Integer second) {

        DynamicDataSourceContextHolder.push(DB_slave);
        HashMap<String, Object> par = new HashMap<>();
        par.put("aid", aid);
        par.put("dateBegin", dateBegin);
        par.put("dateEnd", dateEnd);
        par.put("second", second);

        final NewUserOrderStatDomain newUserOrderStatDomain = suiteDao.selectNewUserOrderStatOne(par);

        final NewUserOrderStatDomain newUserOrderStatDomain2 = suiteDao.selectNewUserOrderStatOne2(par);

//        BeanUtils.copyProperties(newUserOrderStatDomain2,newUserOrderStatDomain);

        newUserOrderStatDomain.setMoney(newUserOrderStatDomain2.getMoney());
        newUserOrderStatDomain.setOrdercount(newUserOrderStatDomain2.getOrdercount());
        newUserOrderStatDomain.setDistorderusercount(newUserOrderStatDomain2.getDistorderusercount());


        final List<NewUserOrderStatDomain> newUserOrderStatDomain3 = suiteDao.selectNewUserOrderStat3(par);

        newUserOrderStatDomain.setUserCountNew(newUserOrderStatDomain3.size());

        final HashSet<Integer> subUsers = new HashSet<>();

        final List<NewUserOrderStatDomain> newUserOrderStatDomain4 = suiteDao.selectNewUserOrderStat4(par);
        newUserOrderStatDomain.setUserCountMycps(newUserOrderStatDomain4.size());

        final List<NewUserOrderStatDomain> newUserOrderStatDomain5 = suiteDao.selectNewUserOrderStat5(par);
        newUserOrderStatDomain.setUserCountShequn(newUserOrderStatDomain5.size());
        final List<NewUserOrderStatDomain> newUserOrderStatDomain6 = suiteDao.selectNewUserOrderStat6(par);
        newUserOrderStatDomain.setUserCountWechat(newUserOrderStatDomain6.size());

        subUsers.addAll(newUserOrderStatDomain4.stream().map(NewUserOrderStatDomain::getUid).collect(Collectors.toList()));
        subUsers.addAll(newUserOrderStatDomain5.stream().map(NewUserOrderStatDomain::getUid).collect(Collectors.toList()));
        subUsers.addAll(newUserOrderStatDomain6.stream().map(NewUserOrderStatDomain::getUid).collect(Collectors.toList()));

        newUserOrderStatDomain.setUserCountOld(subUsers.size());

        newUserOrderStatDomain.setUserCountFinal(newUserOrderStatDomain.getUserCountNew()-newUserOrderStatDomain.getUserCountOld());


        return newUserOrderStatDomain;

    }
}
