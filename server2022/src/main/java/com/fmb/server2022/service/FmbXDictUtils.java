package com.fmb.server2022.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FmbXDictUtils {

    /**
     * 查询数据库 字典类型数据 返回结果
     * @param key  表名__字段名   表名+双划线+字段名
     *
     * 例如 : 证件资料:0-不需要,1-需提供一位用户身份信息,2-需提供所有用户身份信息
     * fmbx_suite__cert_flag
     *
     *             FmbXDictUtils.queryDict("fmbx_suite__cert_flag") 返回list数据 json格式化后就是
     *             [
     *            {
     * 				"value": "不需要",
     * 				"key": "0"
     *            },
     *            {
     * 				"value": "需提供一位用户身份信息",
     * 				"key": "1"
     *            },
     *            {
     * 				"value": "需提供所有用户身份信息",
     * 				"key": "2"
     *            }
     *             ]
     *
     *
     * @return 找不到就返回空数组
     */
    public static List<Map<String, String>> queryDict(String key) {
        List<Map<String, String>>  xx= new ArrayList<>();
        if (FmbDBDictInfo.dict.containsKey(key)) {
            final Map<String, String> stringStringMap = FmbDBDictInfo.dict.get(key);

            final List<Map<String, String>> objectList = stringStringMap.entrySet().stream().map(en -> {
                HashMap<String, String> x = new HashMap<>();
                x.put("key", en.getKey().trim());
                x.put("value", en.getValue().trim());
                return  x ;
            }).collect(Collectors.toList());
            return objectList;
        } else {
            return xx;
        }
    }

    /**
     * 把 key 对应的信息写入map ,并返回字段说明
     * @param key
     * @param map
     * @param aimKeyName
     */
    public static void queryDictAndSetInMap(String key,Map map,String aimKeyName) {
        final List<Map<String, String>> list = queryDict(key);
        if (!list.isEmpty()){
            map.put(aimKeyName,list) ;
            map.put(aimKeyName+"_note",FmbDBDictInfo.dictOfColumnNote.get(key)) ;
        }

    }



}
