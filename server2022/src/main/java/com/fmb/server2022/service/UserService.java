package com.fmb.server2022.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.domain.FmbTreeInfo;
import com.fmb.server2022.fmbx.entity.SysPermission;
import com.fmb.server2022.fmbx.entity.SysRole;
import com.fmb.server2022.fmbx.entity.SysRolePermission;
import com.fmb.server2022.fmbx.entity.SysUser;
import com.fmb.server2022.fmbx.entity.SysUserRole;
import com.fmb.server2022.fmbx.mapper.SysUserMapper;
import com.fmb.server2022.fmbx.service.ISysMenuService;
import com.fmb.server2022.fmbx.service.ISysPermissionService;
import com.fmb.server2022.fmbx.service.ISysRolePermissionService;
import com.fmb.server2022.fmbx.service.ISysRoleService;
import com.fmb.server2022.fmbx.service.ISysUserRoleService;
import com.fmb.server2022.fmbx.service.ISysUserService;
import com.fmb.server2022.mapper.LoginDao;
import com.fmb.server2022.mapper.UserDao;
import com.fmb.server2022.reqdomain.ReqListUser;
import com.fmb.server2022.reqdomain.ReqNewRole;
import com.fmb.server2022.reqdomain.ReqNewUser;
import com.fmb.server2022.service.kafka.FmbKafkaTopic;
import com.fmb.util.MD5Util;
import com.fmb.util.PageUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.TOKENSETS_PRE;
import static com.fmb.basic.FmbConstants.TOKEN_PRE_OF_FMBADMIN;

/**
 * @description: 用户/角色/权限
 */
@Service
public class UserService {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserDao userDao;

    @Autowired
    SysUserMapper userMapper ;

    @Autowired
    ISysUserService sysUserService ;

    @Autowired
    ISysRoleService roleService ;

    @Autowired
    ISysRolePermissionService rolePermissionService ;

    @Autowired
    LoginDao loginDao ;

    @Autowired
    ISysUserRoleService userRoleService ;

    @Autowired
    JedisPool jedisPool  ;


    @Autowired
    ISysPermissionService permissionService ;

    @Autowired
    private KafkaTemplate<String, String> template;

    @Autowired
    SysTokenService tokenService ;


    @Autowired
    ISysMenuService menuService ;



    @Autowired
    MenuRedisService menuRedisService ;

    /**
     * 用户列表
     */

    public FmbRespBean listUser(ReqListUser reqList) {

        HashMap<String, Object> map = new HashMap<>();

        //查询总条数
        int count = userDao.countUser(reqList);
        //计算页码信息
        PageUtil pager = new PageUtil(reqList.getPageno(), reqList.getPagesize(), count);

        //设置分页查询参数
        reqList.setStart(pager.getStartIdx());
        reqList.setSize(pager.getPageSize());

        //查询分页数据
        List<JSONObject> list = userDao.listUser(reqList);

        map.put("total",count) ;
        map.put("pagesize",pager.getPageSize()) ;
        map.put("pageno",pager.getNowPageNo()) ;
        map.put("list",list) ;

        return FmbRespBean.success(map);

    }



    /**
     * 添加用户
     */
    public FmbRespBean addUser(ReqNewUser req ) {

        HashMap<String, Object> map = new HashMap<>();


        int exist = userDao.queryExistUsername(req.getUsername());
        if (exist > 0) {
            throw  new BadLogicException("用户名已经存在:"+req.getUsername());
        }

        if(StringUtils.isBlank( req.getPasswd())){
            throw  new BadLogicException("密码为空");
        }
        if(!req.getPasswd().equals(req.getPasswdConfirm())){
            throw  new BadLogicException("两次密码输入不一致");
        }

        if ( !req.getUsername().matches("^[0-9a-zA-Z_]{1,40}$")) {
            throw new BadLogicException("用户名只能是字母数字下划线,最长40");
        }
        if ( StringUtils.isNotBlank(req.getMobile()) &&  ( req.getMobile().length()!=11  || !req.getMobile().trim().matches("\\d{11}") ) ) {
            throw new BadLogicException("手机号码错误");
        }



        SysUser newUser = new SysUser();

        newUser.setUsername(req.getUsername());
        String salt = MD5Util.md5("" + SnowflakeIdWorker.getInstance().nextId());
        newUser.setSalt(salt);
        //用户的密码 数据库存储规则  md5( md5(原始密码)+salt)
        newUser.setPassword(MD5Util.md5( MD5Util.md5(req.getPasswd())+salt));
        newUser.setNickname(req.getNickName());
        newUser.setStatus(1);
        newUser.setMobile(req.getMobile());
        newUser.setEmail(req.getEmail());


        boolean save = sysUserService.save(newUser);
        if (save) {
            map.put("userid",newUser.getId()) ;
        }

        map.put("roleadd",0) ;
        if (req.getRoleid().length>0) {
            int i = userDao.batchAddUserRole(req.getRoleid(), newUser.getId());
            map.put("roleadd",i) ;
        }

        return FmbRespBean.success(map);

    }

    /**
     * 查询所有的角色
     * 在添加/修改用户的时候要使用此方法
     * @param resultMap
     */
    public void getAllRoles(HashMap<String, Object> resultMap) {
        List<JSONObject> roles = userDao.getAllRoles();

        resultMap.put("list",roles) ;

    }

    /**
     * 修改用户
     */

    @DSTransactional
    public FmbRespBean updateUser(ReqNewUser req) {

        HashMap<String, Object> map = new HashMap<>();

        if (req.getUserId()==null) {
            throw  new BadLogicException("用户ID为空");
        }


        if ( !req.getUsername().matches("^[0-9a-zA-Z_]{1,40}$")) {
            throw new BadLogicException("用户名只能是字母数字下划线,最长40");
        }
        if ( StringUtils.isNotBlank(req.getMobile()) &&  ( req.getMobile().length()!=11  || !req.getMobile().trim().matches("\\d{11}") ) ) {
            throw new BadLogicException("手机号码错误");
        }


        if(StringUtils.isNotBlank( req.getPasswd()) && !req.getPasswd().equals(req.getPasswdConfirm())){
            throw  new BadLogicException("两次密码输入不一致");
        }
//        if(StringUtils.isBlank( req.getPasswd()) || StringUtils.isBlank(req.getPasswdConfirm())){
//            throw  new BadLogicException("两次密码不能为空");
//        }

        SysUser userDB = sysUserService.getById(req.getUserId());
        if(userDB==null){
            throw  new BadLogicException("用户不存在:"+req.getUserId());
        }


        //更新用户基础信息
        {

            LambdaUpdateWrapper<SysUser> userWrapper = new LambdaUpdateWrapper<>();

            userWrapper.eq(SysUser::getId,req.getUserId())
                    .set(SysUser::getUsername,req.getUsername())
                    .set(SysUser::getNickname,req.getNickName())
                    .set(SysUser::getMobile,req.getMobile())
                    .set(SysUser::getEmail,req.getEmail())
            ;



            //密码更新 密码不为空 且相等
            if(StringUtils.isNotBlank( req.getPasswd()) && req.getPasswd().equals(req.getPasswdConfirm())){
                String salt = MD5Util.md5("" + SnowflakeIdWorker.getInstance().nextId());

                userWrapper.set(SysUser::getSalt,salt);
                userWrapper.set(SysUser::getPassword,MD5Util.md5( MD5Util.md5(req.getPasswd())+salt));

            }

            if (req.getStatus() != null ) {
                userWrapper.set(SysUser::getStatus,req.getStatus());
            }

            boolean updateById = sysUserService.update(null, userWrapper);


            if (updateById) {
                map.put("userUpdate",updateById?1:0) ;
            }
        }


        SessionUserInfoOfFmbAdmin userInfo = loginDao.getUserInfo(req.getUsername());

        //增加角色
        int roleAdd = 0 ;
        //减少角色
        int roleDel = 0 ;
        if (userInfo != null) {

            Set<Integer> haveRoles = userInfo.getRoleIds().stream().collect(Collectors.toSet());
            Set<Integer> inputRoleids = Arrays.asList(req.getRoleid()).stream().collect(Collectors.toSet());
            {
                //添加新的角色id

                for (Integer nRoleld : req.getRoleid()) {
                    if (!haveRoles.contains(nRoleld)) {

                        SysUserRole nur = new SysUserRole();
                        nur.setUserId(req.getUserId());
                        nur.setRoleId(nRoleld);
                        boolean save = userRoleService.save(nur);
                        if (save) {
                            roleAdd ++ ;
                        }
                    }
                }
                map.put("roleAdd",roleAdd) ;
            }

            {
                //删除 不再有的.

                for (Integer nowHaveRole : userInfo.getRoleIds()) {

                    if(!inputRoleids.contains(nowHaveRole)){

                        LambdaUpdateWrapper<SysUserRole> eq = new LambdaUpdateWrapper<SysUserRole>()
                                .eq(SysUserRole::getRoleId, nowHaveRole)
                                .eq(SysUserRole::getUserId,req.getUserId());
                        boolean delFlag = userRoleService.remove(eq) ;
                        if (delFlag) {
                            roleDel ++ ;
                        }
                    }
                }

                map.put("roleDel",roleDel) ;
            }
        }

        //如果是禁用用户 需要将把用户 从线上踢下线
        if (req.getStatus()==0) {
            kickOnlineUserOut(req.getUserId()) ;
        }else
        {
            //打开状态下需要看用户的角色是否发生变化
            if(roleAdd>0 || roleDel>0){
                userInfoChangeNeedUpdateRedis(req.getUserId()) ;
            }
        }

        return FmbRespBean.success(map);
    }

    /**
     * 用户信息变了 需要更新用户存储在 redis 里的信息
     * @param userId
     */
    public void userInfoChangeNeedUpdateRedis(Integer userId) {
        HashMap<String, Object> pars = new HashMap<>();
        pars.put("userId", userId);
        template.send(FmbKafkaTopic.ADMIN_USER_CHNAGE,JSON.toJSONString(pars));
        logger.info("userInfoChangeNeedUpdateRedis userid={}",userId);
    }

    /**
     * 将用户存储的 redis token 删除
     * @param userId
     */
    public void kickOnlineUserOut(Integer userId) {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            Set<String> smembers = jedis.smembers(TOKENSETS_PRE + userId);
            for (String token : smembers) {
                jedis.del(TOKEN_PRE_OF_FMBADMIN +token) ;
            }
            jedis.del(TOKENSETS_PRE + userId) ;

            logger.info("kickout userid={}",userId);
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    /**
     * 角色列表
     */
    public FmbRespBean listRole() {
        HashMap<String, Object>  map  = new HashMap<>();
        List<JSONObject> roles = userDao.listRole();
        map.put("roles",roles) ;
        return FmbRespBean.success(map);
    }

    /**
     * 查询所有权限, 给角色分配权限时调用
     */

    public FmbRespBean listAllPermission() {

        HashMap<String, Object>  map  = new HashMap<>();
        List<JSONObject> permissions = userDao.listAllPermission();
        map.put("lists",permissions) ;
        return FmbRespBean.success(map);
    }

    /**
     * 添加角色
     */


    @DSTransactional
    public FmbRespBean addRole(ReqNewRole nrole) {


        if(nrole.getPermissions()==null){
           throw new BadLogicException("权限id数组为空");
        }

        HashMap<String, Object> map = new HashMap<>();


        SysRole entry = new SysRole();
        entry.setRoleName(nrole.getRoleName());
        LocalDateTime now = LocalDateTime.now();
        entry.setCreateTime(now);
        entry.setUpdateTime(now);
        entry.setStatus(1);

        boolean save = roleService.save(entry);

        int addpcounter = 0 ;
        for (Integer pid : nrole.getPermissions()) {
            SysRolePermission rp = new SysRolePermission();
            rp.setRoleId(entry.getId());
            rp.setPermissionId(pid);
            rp.setCreateTime(now);
            rp.setUpdateTime(now);
            rp.setStatus(1);
            addpcounter += rolePermissionService.save(rp)?1:0;
        }

        map.put("result",save?1:0);
        map.put("roleid",entry.getId());
        map.put("addrolepermisson_count",addpcounter);
        return FmbRespBean.success(map);
    }

    /**
     * 修改角色
     */

    @DSTransactional
    public FmbRespBean updateRole(ReqNewRole nrole) {

        HashMap<String, Object> map = new HashMap<>();



        if (nrole.getRoleId()==null) {
            throw new BadLogicException("角色id不能为空");
        }

        if(nrole.getPermissions()==null){
            throw new BadLogicException("权限id数组为空");
        }


        SysRole roleAim = roleService.getOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getId, nrole.getRoleId()));
        if (roleAim==null) {
            throw new BadLogicException("角色id"+nrole.getRoleId()+"找不到");
        }

        //操作更新角色名字
        map.put("updateRoleName",0) ;
        if (!nrole.getRoleName().equals(roleAim.getRoleName())) {

            SysRole nupdate = new SysRole();
            nupdate.setId(roleAim.getId());
            nupdate.setRoleName(nrole.getRoleName());
            nupdate.setUpdateTime(LocalDateTime.now());

            boolean uflag = roleService.updateById(nupdate);

            map.put("updateRoleName",uflag?1:0) ;

        }


        {
            //查出原有信息
            JSONObject roleInfo = userDao.getRoleAllInfo(nrole.getRoleId());
            Set<Integer> oldPerms = (Set<Integer>) roleInfo.get("permissionIds");
            //保存新增的
            map.put("addCounter", saveNewPermission(nrole.getRoleId(), nrole.getPermissions(), oldPerms));
            //删除不再拥有的
            map.put("delCounter",removeOldPermission(nrole.getRoleId(), nrole.getPermissions(), oldPerms));
        }

        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRole::getRoleId,roleAim.getId());

        //更新含有该 角色的所有用户信息
        userRoleService.list(queryWrapper).stream().map(SysUserRole::getUserId).distinct().forEach( userid ->{
            userInfoChangeNeedUpdateRedis(userid) ;
        });


        return FmbRespBean.success(map);
    }

    /**
     * 为角色添加新权限
     */
    private int saveNewPermission(Integer roleId, Integer[] newPerms, Collection<Integer> oldPerms) {

        int counter = 0 ;
        List<Integer> waitInsert = new ArrayList<>();
        for (Integer newPerm : newPerms) {
            if (!oldPerms.contains(newPerm)) {
                waitInsert.add(newPerm);
            }
        }

        if (waitInsert.size() > 0) {
            LocalDateTime now = LocalDateTime.now();

            for (Integer p : waitInsert) {
                SysRolePermission new_rp = new SysRolePermission();

                new_rp.setRoleId(roleId);
                new_rp.setPermissionId(p);

                new_rp.setCreateTime(now);
                new_rp.setUpdateTime(now);
                new_rp.setStatus(1);

                boolean save = rolePermissionService.save(new_rp);
                if (save) {
                    counter ++ ;
                }
            }
        }

        return counter ;
    }

    /**
     * 删除角色 旧的 不再拥有的权限
     */
    private int removeOldPermission(Integer roleId, Integer[] newPerms, Collection<Integer> oldPerms) {

        int counter = 0 ;

        HashSet<Integer> osets = new HashSet<>() ;
        for (Integer newPerm : newPerms) {
            osets.add(newPerm) ;
        }

        List<Integer> waitRemove = new ArrayList<>();
        for (Integer oldPerm : oldPerms) {
            if (!osets.contains(oldPerm)) {
                waitRemove.add(oldPerm);
            }
        }
        if (waitRemove.size() > 0) {
            for (Integer p : waitRemove) {
                Map<String, Object> rmap = new HashMap<>();
                rmap.put("role_id",roleId) ;
                rmap.put("permission_id",p) ;

                boolean b = rolePermissionService.removeByMap(rmap);

                if (b) {
                    counter ++ ;
                }
            }
        }

        return counter;
    }

    /**
     * 删除角色
     */

    @DSTransactional
    public FmbRespBean roleUpdate(Integer roleid, Integer status) {

        HashMap<String, Object> map = new HashMap<>();

        SysRole roledb = roleService.getById(roleid);
        if (roledb == null) {
            throw new BadLogicException("角色不存在,roleid="+roleid);
        }

        if (status.intValue()!=0 && status.intValue()!=1) {
            throw new BadLogicException("错误的状态值:"+status);
        }


        roledb.setStatus(status);
        roledb.setUpdateTime(LocalDateTime.now());

        boolean b = roleService.updateById(roledb);
        int i = userDao.updateRoleAllPermission(roleid,status);

        map.put("updateRole",b?1:0);
        map.put("updateRolePermission",i);


        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRole::getRoleId,roledb.getId());
        //更新含有该 角色的所有用户信息
        userRoleService.list(queryWrapper).stream().map(SysUserRole::getUserId).distinct().forEach( userid ->{
            userInfoChangeNeedUpdateRedis(userid) ;
        });

        return FmbRespBean.success(map);
    }

    public FmbRespBean addPermission(SysPermission permission) {

        HashMap<String, Object> map = new HashMap<>();

        map.put("saveResult",false) ;
        map.put("updateResult",false) ;

        if (permission.getId() != null) {

            boolean re = permissionService.updateById(permission);

            map.put("updateResult",re) ;
            if(re){

                //找到和这个权限相关的用户 然后更新用户的信息
                userDao.selectHavePermissionUsers(permission.getId()).stream().forEach(userid->{
                    userInfoChangeNeedUpdateRedis(userid);
                });
            }


        }else {
            boolean re = permissionService.save(permission);

            map.put("saveResult",re) ;

        }


        return FmbRespBean.success(map);
    }

    public FmbRespBean genUserTree() {

        HashMap<String, Object> map = new HashMap<>();


        String token = MDC.get(FmbConstants.TOKEN);
        SessionUserInfoOfFmbAdmin userInfo = tokenService.getUserInfoBytoken(token);

        if (userInfo == null) {
            throw new BadLogicException("用户token找不到") ;
        }

        //找出用户的所有的权限id
        Set<String> collect = userDao.selectListPermissions(userInfo.getUserId()).stream().map(x -> x.intValue() + "").collect(Collectors.toSet());
        List<FmbTreeInfo> treeInfos = menuRedisService.createUserTree(collect,false) ;

        map.put("tree",treeInfos.get(0).getChildren()) ;

        return FmbRespBean.success(map);

    }


    /**
     * 获取完整菜单树
     * @return
     */
    public FmbRespBean genFullTree() {

        HashMap<String, Object> map = new HashMap<>();

        List<FmbTreeInfo> treeInfos = menuRedisService.createUserTree(new HashSet<>(),true) ;

        map.put("tree",treeInfos.get(0).getChildren()) ;

        return FmbRespBean.success(map);

    }


    /**
     * 在完整菜单树上 增加展示叶子节点同页面的权限
     * @return
     */
    public FmbRespBean treeRoleInfo() {
        HashMap<String, Object> map = new HashMap<>();

        List<SysPermission> list = permissionService.list();
        Map<String, List<SysPermission>> groupByMenuCode = list.stream().collect(Collectors.groupingBy(SysPermission::getMenuCode));
        HashMap<String, SysPermission> stringSysPermissionHashMap = new HashMap<>();
        for (SysPermission sysPermission : list) {
            stringSysPermissionHashMap.put(sysPermission.getId().toString(),sysPermission) ;
        }

        List<FmbTreeInfo> treeInfos = menuRedisService.createUserTree(new HashSet<>(),true) ;

        loopTree(treeInfos,groupByMenuCode,stringSysPermissionHashMap) ;


        map.put("tree",treeInfos.get(0).getChildren()) ;

        return FmbRespBean.success(map);
    }


    public FmbRespBean treeRoleInfo(Integer menuid,Integer roleid) {
        HashMap<String, Object> map = new HashMap<>();

//        List<SysPermission> list = permissionService.list();

        List<SysPermission> list = permissionService.listByRoleid(roleid) ;

        Map<String, List<SysPermission>> groupByMenuCode = list.stream().collect(Collectors.groupingBy(SysPermission::getMenuCode));
        HashMap<String, SysPermission> stringSysPermissionHashMap = new HashMap<>();
        for (SysPermission sysPermission : list) {
            stringSysPermissionHashMap.put(sysPermission.getId().toString(),sysPermission) ;
        }

        List<FmbTreeInfo> treeInfos = menuRedisService.createUserTree(new HashSet<>(),true) ;

        List<FmbTreeInfo> children = (List<FmbTreeInfo>) treeInfos.get(0).getChildren();

        List<FmbTreeInfo> collect = children.stream().filter(tree -> tree.getMenuId().intValue() == menuid.intValue()).collect(Collectors.toList());


        loopTree(collect,groupByMenuCode,stringSysPermissionHashMap) ;

        map.put("tree",collect) ;

        return FmbRespBean.success(map);
    }

    /**
     * 遍历菜单树时完成 叶子节点页面权限添加操作
     * @param treeInfos
     * @param groupByMenuCode
     * @param map
     */
    private void loopTree(List<FmbTreeInfo> treeInfos, Map<String,List<SysPermission>> groupByMenuCode, HashMap<String,SysPermission> map) {

        for (FmbTreeInfo treeInfo : treeInfos) {

            //看看菜单id 是否为空  -1是默认值
            Integer relatedPermissionid = treeInfo.getMenu().getRelatedPermissionid();
            if (relatedPermissionid != null &&  relatedPermissionid.intValue()!=-1) {
                if (map.containsKey(relatedPermissionid.toString())) {
                    List<SysPermission> sysPermissions = groupByMenuCode.get(map.get(relatedPermissionid.toString()).getMenuCode());
                    if (sysPermissions != null) {
                        treeInfo.getSamePagePermission().addAll(sysPermissions) ;
                    }
                }

            }
            if (!treeInfo.getChildren().isEmpty()) {
                loopTree((List<FmbTreeInfo>) treeInfo.getChildren(),groupByMenuCode,map  ) ;
            }

        }


    }
}
