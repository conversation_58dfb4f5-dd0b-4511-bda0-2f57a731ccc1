package com.fmb.server2022.service;

//NEW_IMPORT_DOMAIN

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.controller.admin.ReqUpdateReserveHotelSkuSortValueList;
import com.fmb.server2022.controller.demo.FmbKafkaWorkerAutoAck;
import com.fmb.server2022.domain.ActivityPublishLogDomain;
import com.fmb.server2022.domain.ActivityXDomain;
import com.fmb.server2022.domain.HotelDateMinPriceDomain;
import com.fmb.server2022.domain.HotelDateSkuDomain;
import com.fmb.server2022.domain.HotelDateSkuDomainSub;
import com.fmb.server2022.domain.HotelPriceChangeDataDomain;
import com.fmb.server2022.domain.HotelReserveMinPriceDomain;
import com.fmb.server2022.domain.HotelReserveSkuDomain;
import com.fmb.server2022.domain.HotelReserveSkuDomainGroup;
import com.fmb.server2022.domain.HotelSkuDatePriceDomain;
import com.fmb.server2022.domain.HotelSkuDomain;
import com.fmb.server2022.domain.HotelStockChangeDataDomain;
import com.fmb.server2022.domain.HotelStockDateDomain;
import com.fmb.server2022.domain.ReserveSkuInfoDomain;
import com.fmb.server2022.domain.ReserveSkuMinMaxInfoDomain;
import com.fmb.server2022.domain.RoomStockInfo;
import com.fmb.server2022.domain.stock.ReqroomStockSet;
import com.fmb.server2022.domain.stock.StockUpdateResultInfo;
import com.fmb.server2022.domain.suite.AddDateInfo;
import com.fmb.server2022.domain.suite.ExcelPriceAllInfo;
import com.fmb.server2022.domain.suite.FmbxSuiteRoomdatePriceExt;
import com.fmb.server2022.domain.suite.PriceAddInfo;
import com.fmb.server2022.domain.suite.SuitePriceOperation;
import com.fmb.server2022.domain.suite.SuitePriceOperationAdd;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxActivityPublishLog;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuitePriceOperate;
import com.fmb.server2022.fmbx.entity.FmbxSuitePriceOperateLog;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomdatePrice;
import com.fmb.server2022.fmbx.entity.FmbxSuiteStock;
import com.fmb.server2022.fmbx.entity.FmbxSuiteStockOperate;
import com.fmb.server2022.fmbx.entity.FmbxSuiteStockOperateLog;
import com.fmb.server2022.fmbx.service.IFmbxActivityPublishLogService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteExtService;
import com.fmb.server2022.fmbx.service.IFmbxSuitePriceOperateLogService;
import com.fmb.server2022.fmbx.service.IFmbxSuitePriceOperateService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockOperateLogService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockOperateService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqActivityPublishLog;
import com.fmb.server2022.reqdomain.ReqHotelDate;
import com.fmb.server2022.reqdomain.ReqHotelDateSku;
import com.fmb.server2022.reqdomain.ReqHotelPriceChangeData;
import com.fmb.server2022.reqdomain.ReqHotelReserveSku;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.reqdomain.ReqHotelStockChangeData;
import com.fmb.server2022.reqdomain.ReqUpdateHotelDateSkuSortValueList;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.server2022.service.order.OrderStatService;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.server2022.domain.stock.ReqroomStockSet.*;
import static com.fmb.server2022.domain.suite.SuitePriceOperation.*;
import static com.fmb.server2022.service.kafka.FmbKafkaProducerService.BINFO;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.*;
import static com.fmb.util.FmbCommonUtil.addLogTraceId;
import static com.fmb.util.FmbNumberUtil.double2String;

@Service
public class SuiteService {

    //酒店sku 不可售卖值 
    public static final  int SKU_FLAG_SELL_NOT_VALID = 0  ;
    public static final  int SKU_FLAG_SELL_VALID = 1  ;

    public static final String DATE_FORMAT10 = "yyyy-MM-dd";
    public static final DateTimeFormatter formater = DateTimeFormatter.ofPattern(DATE_FORMAT10) ;
    public static final String TOUCH_ACTIVITY = "touchActivity";
    public static final String SKUID = "skuid";

    private static Logger logger = LoggerFactory.getLogger(SuiteService.class);
    private static Logger loggerImportant = LoggerFactory.getLogger(IMPORTANT_INFO_LOGGER);


    @Autowired
    IFmbxSuiteRoomdatePriceService roomdatePriceService ;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService ;
    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;
    @Autowired
    IFmbxSkuService skuService ;

    @Autowired
    OrderStatService statService ;

    @Autowired
    IFmbxSuiteService suiteService ;

    @Autowired
    IFmbxSuiteExtService suiteExtService ;

    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbxSuitePriceOperateService priceOperateService;

    @Autowired
    IFmbxSuitePriceOperateLogService priceOperateLogService;

    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    SkuService fmbSkuService ;

    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    FrontSkuDao frontSkuDao ;

    @Autowired
    IFmbxSuiteStockService stockService ;

    @Autowired
    IFmbxSuiteStockOperateService stockOperateService ;
    @Autowired
    IFmbxSuiteStockOperateLogService stockOperateLogService ;

    @Autowired
    IFmbxBpsRoomService roomService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    RedissonClient redissonClient ;

    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;

    @Autowired
    IFmbxActivityPublishLogService publishLogService ;


    private static JedisPool jedisPool ;

    @Autowired
    public SuiteService(JedisPool jedisPool) {
        SuiteService.jedisPool = jedisPool ;
    }

    /**
     *  生成价格日历
     *
     * @param inputOperate
     * @param resultMap
     * @param type 1-加价模式
     * @return
     */
    public void priceOperate(SuitePriceOperation inputOperate, HashMap<String, Object> resultMap, int type) {


        resultMap.put("result",0) ;
        //最开始使用的是 roomIds 加  suiteId 组合方式
        // 后来改为直接使用 skuId
        // 20221205 所以转换一下
        // 实际底层保留了  roomIds 加  suiteId 组合方式 ,只有这种组合没有数据的时候才走下面的代码
        if (  inputOperate.getRoomIds().isEmpty() ){

            if (inputOperate.getSkuId()==null) {
                throw new BadLogicException("skuid为空");
            }

            inputOperate.getRoomIds().clear();
            for (Integer skuid : inputOperate.getSkuId()) {
                final FmbxSuiteRoomSku roomsku = roomSkuService.getById(skuid);
                if (roomsku == null) {
                    throw new BadLogicException("skuid不合法"+inputOperate.getSkuId());
                }
                inputOperate.setSuiteId(roomsku.getSuiteId());
                inputOperate.getRoomIds().add(roomsku.getRoomId()) ;
            }

        }

        //加价模式 开始时间必须小于结束时间 否则无效
        if (TYPE_ADD_MODE==type &&   inputOperate.getDateBegin().getTime() > inputOperate.getDateEnd().getTime()) {
            throw new BadLogicException("起始时间需小于结束时间");
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getName,FmbxSuite::getXaid,FmbxSuite::getTotalNeight)
                .eq(FmbxSuite::getSuiteId,inputOperate.getSuiteId())
                ;
        //查询套餐信息
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);
        if (suiteInfo == null) {
            throw new BadLogicException("套餐信息不存在"+inputOperate.getSuiteId());
        }

        //查询活动信息
        Integer xaid = suiteInfo.getXaid();
        FmbxActivity activityInfo = queryFmbxActivity(xaid);

        //将套餐间夜数据写入对象 加价模式有用
        inputOperate.setTotalNeight(suiteInfo.getTotalNeight());

        //读取用户数据 构造价格日历数据
        //不管哪种修改 价格方式
        // 最终都要变成价格日历 roomPriceInfo 就是存储这些入库前的价格日历的地方
        final ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo = new ArrayList<>();
        //加价模式
        if (TYPE_ADD_MODE==type) {
            BigDecimal divide = inputOperate.getGoodsPrice().divide(new BigDecimal(inputOperate.getTotalNeight()),2, RoundingMode.HALF_UP);
//        System.out.println(divide.doubleValue());
            if (!FmbNumberUtil.canDivide(inputOperate.getGoodsPrice(),inputOperate.getTotalNeight())) {
                throw new BadLogicException("基准价/间夜数结果无法整除，请重新输入");
            }

            buildDatePriceInfo(inputOperate, divide, roomPriceInfo);
        }

        //整价模式
        if (TYPE_FULL_RANGE_MODE==type) {
            //整价模式录入的就是单间夜的价格
            inputOperate.setTotalNeight(null);
            buildDatePriceWithWholePrice(inputOperate,  roomPriceInfo);
        }
        //整价模式(非连续时间)
        if (TYPE_FULL_FIXED_DATE_MODE ==type) {
            //整价模式录入的就是单间夜的价格
            inputOperate.setTotalNeight(null);
            buildDatePriceWithFixDate(inputOperate,  roomPriceInfo);
        }

        final LocalDateTime now = LocalDateTime.now();

        for (Integer roomId : inputOperate.getRoomIds()) {
            createPriceCalendar(inputOperate, type, nowUserInfo, suiteInfo, activityInfo, roomPriceInfo, now, roomId);
        }

        for (Integer skuId : inputOperate.getSkuId()) {
            sendHotelSkuChangeNotify(skuId);
        }


        resultMap.put("result",1) ;
//        resultMap.put("message","新增价格日历"+insertCount+"条,更新价格日历"+updateCount+"条") ;

    }

    public FmbxActivity queryFmbxActivity(Integer xaid) {
        Wrapper<FmbxActivity> wrActivity =
                new LambdaQueryWrapper<FmbxActivity>()
                        .select(
                                FmbxActivity::getXaid,
                                FmbxActivity::getTitle,
                                FmbxActivity::getDeletedSkuNum,
                                FmbxActivity::getFlagPublish,
                                FmbxActivity::getTicketType,
                                FmbxActivity::getCategoryId
                        )
                        .eq(FmbxActivity::getXaid, xaid);
        FmbxActivity activityInfo = activityService.getOne(wrActivity);
        return activityInfo;
    }

    /**
     * 做3个 数据库操作
     * 1.将价格数据 写入数据库(fmbx_suite_roomdate_price)
     * 2.记录操作记录(fmbx_suite_price_operate)
     * 3. 每一个sku对应日期的变化也写入数据库 (fmbx_suite_price_operate_log)
     * @param inputOperate
     * @param type
     * @param nowUserInfo
     * @param suiteInfo
     * @param activityInfo
     * @param roomPriceInfo
     * @param now
     * @param roomid
     */
    void createPriceCalendar(SuitePriceOperation inputOperate, int type, SessionUser nowUserInfo, FmbxSuite suiteInfo, FmbxActivity activityInfo, ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo, LocalDateTime now, Integer roomid) {
        Wrapper<FmbxSuiteRoomSku> wr1  = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getRoomId, roomid)
                .eq(FmbxSuiteRoomSku::getSuiteId, suiteInfo.getSuiteId()) ;
        final FmbxSuiteRoomSku roomSku = roomSkuService.getOne(wr1);
        if (roomSku == null) {
            throw new BadLogicException("sku信息异常");
        }


        int updateCount = 0 ;
        int insertCount = 0 ;
        ArrayList<FmbxSuitePriceOperateLog> operateLogs = new ArrayList<>();
        //循环价格信息 变更或者查询价格日历数据
        for (FmbxSuiteRoomdatePriceExt priceExtIn : roomPriceInfo) {

            //从原有 priceExtIn 里复制数据, 因为本方法可能循环调用.
            //priceExtIn 如果被修改了. 多次循环时就出错了 
            FmbxSuiteRoomdatePriceExt priceExt = new FmbxSuiteRoomdatePriceExt() ;
            BeanUtils.copyProperties(priceExtIn,priceExt);


            priceExt.setSuiteRoomSkuId(roomSku.getSkuId());
            priceExt.setSuiteId(roomSku.getSuiteId());
            priceExt.setRoomId(roomSku.getRoomId());

            Wrapper<FmbxSuiteRoomdatePrice> wr2 = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                    .eq(FmbxSuiteRoomdatePrice::getSuiteRoomSkuId,roomSku.getSkuId())
                    .eq(FmbxSuiteRoomdatePrice::getTargetDate,priceExt.getTargetDate())
                    .select(FmbxSuiteRoomdatePrice::getPriceId,FmbxSuiteRoomdatePrice::getVersionNum)
                    ;

            FmbxSuiteRoomdatePrice priceTest = roomdatePriceService.getOne(wr2);

            //细致到天的 sku 操作日志
            FmbxSuitePriceOperateLog oplog = new FmbxSuitePriceOperateLog();
            oplog.setSkuId(roomSku.getSkuId());
            oplog.setTargetDate(priceExt.getTargetDate());
            oplog.setCtime(now);

            //更新日历价格
            if (priceTest != null) {
                priceExt.setPriceId(priceTest.getPriceId());
                priceExt.setUtime(now);
                priceExt.setVersionNum(priceTest.getVersionNum()+1);

                Wrapper<FmbxSuiteRoomdatePrice> upWra = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                        .eq(FmbxSuiteRoomdatePrice::getPriceId,priceTest.getPriceId())
                        .eq(FmbxSuiteRoomdatePrice::getVersionNum,priceTest.getVersionNum())
                        ;
                boolean b =  roomdatePriceService.update(priceExt,upWra) ;
                if (b) {
                    updateCount ++ ;
                    oplog.setOperateInfo("更新价格,原始价格"+genInfo(priceTest)+"变为"+genInfo(priceExt));
                }

            }else {
                //新增日历价格
                priceExt.setCtime(now);
//                logger.info(" skuid={},targetDate={} ",priceExt.getSuiteRoomSkuId(),priceExt.getTargetDate().toString());
                final boolean save = roomdatePriceService.save(priceExt);
                if (save) {
                    insertCount++ ;
                    oplog.setOperateInfo("新增价格"+genInfo(priceExt));
                }
            }
            operateLogs.add(oplog) ;

        }

        inputOperate.getResult().put("insertCount",insertCount) ;
        inputOperate.getResult().put("updateCount",updateCount) ;

        FmbxSuitePriceOperate operate = new FmbxSuitePriceOperate() ;
        operate.setXaid(suiteInfo.getXaid());
        operate.setActivityName(activityInfo.getTitle());

        operate.setSkuId(roomSku.getSkuId());
        operate.setSkuName(roomSku.getSkuName());
        operate.setSuiteId(suiteInfo.getSuiteId());

        operate.setOperateData(JSON.toJSONString(inputOperate));
        operate.setChangeType(type);
        operate.setAdminType(nowUserInfo.getUserType());
        operate.setAdminUid(nowUserInfo.getUserId());
        operate.setAdminName(nowUserInfo.myInfo());
        operate.setCtime(now);

        final boolean save2 = priceOperateService.save(operate);
        sendHotelSkuChangeNotify(roomSku.getSkuId());

        if (save2) {

            //更新是否有库存标志
            FmbxSuiteRoomSku updateSku = new FmbxSuiteRoomSku();
            updateSku.setSkuId(roomSku.getSkuId());
            updateSku.setUtime(now);

//            if ((insertCount > 0 || updateCount > 0)) {
//                updateSku.setFlagSetPrice(1);
//            }

            if (TYPE_ADD_MODE == type) {
                updateSku.setLastAddpriceOperateId(operate.getPriceOperateId());
            }
            if (TYPE_FULL_RANGE_MODE == type) {
                updateSku.setLastFullpriceOperateId(operate.getPriceOperateId());
            }
            roomSkuService.updateById(updateSku);

            for (FmbxSuitePriceOperateLog log : operateLogs) {
                log.setPriceOperateId(operate.getPriceOperateId());
            }
            priceOperateLogService.saveBatch(operateLogs);
        }

        //加价或整价模式需要更新 到套餐的 PriceInputType列
        // type 1-4 里, 1是加价模式  ,2,3,4都是整价的各种情况
        // 修改类型:1-加价模式,2-整价模式,3-整价指定日期,4-excel上传
        if (type<TYPE_SWITCH_ADDMODE_REMOVE_ALL) {
            FmbxSuite upSuite = new FmbxSuite();
            upSuite.setSuiteId(suiteInfo.getSuiteId());
            // 套餐里 加价模式包含简化为 加价和整价 2个种类
            if (type==TYPE_ADD_MODE) {
                upSuite.setPriceInputType(1);
            }else {
                upSuite.setPriceInputType(2);
            }

            suiteService.updateById(upSuite) ;
        }

    }

    /**
     * 将用户输入的整价录入信息 变为价格日历信息
     * @param operation
     * @param roomPriceInfo
     */
    public void buildDatePriceWithWholePrice(SuitePriceOperation operation, ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo) {


        //遍历修改日期信息 生成对应日期的价格信息
        LinkedHashMap<String, FmbxSuiteRoomdatePriceExt> addInfoHashMap = new LinkedHashMap<>();

        for (SuitePriceOperationAdd operationAdd : operation.getAdds()) {

            for (AddDateInfo dateInfo : operationAdd.getDateInfo()) {

                DateTime dateTest = DateUtil.date(dateInfo.getAddDateBegin().getTime());
                while (dateTest.getTime() < ( dateInfo.getAddDateEnd().getTime()+1)  ) {
                    //hutool 周一是2 转换为  周一就是1
                    final int dayOfWeek = rebuildDayOfWeek(dateTest);
                    //不限制星期几 或者正好是要设定的星期
                    if(dateInfo.getWeeks().isEmpty() || containWeekInfo(dayOfWeek,dateInfo.getWeeks()) ){

                        FmbxSuiteRoomdatePriceExt info = new FmbxSuiteRoomdatePriceExt();

                        info.setTargetDate(dateTest.toSqlDate().toLocalDate());
                        info.setMarketPrice(operationAdd.getMarketPrice());
                        info.setGoodsPrice(operationAdd.getGoodsPrice());
                        info.setSettlePrice(operationAdd.getSettlePrice());

                        addInfoHashMap.put(dateTest.toString(DATE_FORMAT10), info ) ;
                    }
                    dateTest = dateTest.offset(DateField.DAY_OF_YEAR, 1);
                }
            }
        }
        roomPriceInfo.addAll(addInfoHashMap.values()) ;

    }

    /**
     * hutool datetime 星期数转换
     * @param dateTest
     * @return
     */
    private int rebuildDayOfWeek(DateTime dateTest) {
        final int i = (dateTest.dayOfWeek() + 6) % 7;
        return i==0?7:i;
    }

    /**
     * 整价模式指定日期
     * @param operation
     * @param roomPriceInfo
     */
    public void buildDatePriceWithFixDate(SuitePriceOperation operation, ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo) {


        for (String dateInput : operation.getFixedDate()) {
            FmbxSuiteRoomdatePriceExt info = new FmbxSuiteRoomdatePriceExt();
            info.setTargetDate(LocalDate.parse(dateInput));
            info.setMarketPrice(operation.getMarketPrice());
            info.setGoodsPrice(operation.getGoodsPrice());
            info.setSettlePrice(operation.getSettlePrice());
            roomPriceInfo.add(info) ;
        }


    }

    private String genInfo(FmbxSuiteRoomdatePrice priceTest) {
        return "["+priceTest.getMarketPrice()+"/"+priceTest.getGoodsPrice()+"/"+priceTest.getSettlePrice()+"]" ;
    }

    /**
     * 通过输入 创建 价格日历信息
     * @param operation
     * @param divide
     * @param roomPriceInfo
     */
    private void buildDatePriceInfo(SuitePriceOperation operation, BigDecimal divide, ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo) {
        //日期循环变量
        DateTime date = DateUtil.date(operation.getDateBegin());

        //比结束时间多1毫秒
        // 要不有临界值问题
        while (date.getTime()< (operation.getDateEnd().getTime()+1)) {
            FmbxSuiteRoomdatePriceExt price = new FmbxSuiteRoomdatePriceExt();
            price.setTargetDate(date.toSqlDate().toLocalDate());
            price.setGoodsPrice(divide);

            if (operation.getMarketPrice() != null) {
                price.setMarketPrice(operation.getMarketPrice().divide(new BigDecimal(operation.getTotalNeight()),2,RoundingMode.HALF_UP));
            }else {
                price.setMarketPrice(new BigDecimal("0"));
            }

            price.setSettlePrice(operation.getSettlePrice().divide(new BigDecimal(operation.getTotalNeight()),2,RoundingMode.HALF_UP));

            roomPriceInfo.add(price);
            //下一天
            date = date.offset(DateField.DAY_OF_YEAR, 1);
        }

        //遍历修改日期信息 生成对应日期的加价信息
        //因为一个修改里有多个时间段并指定了特定的星期数 可能有相互覆盖的问题 ,后来的覆盖新的 ,以最后添加的为准
        // 所以要存储到addInfoHashMap里
        // 最后和 roomPriceInfo 的价格合并
        HashMap<String, PriceAddInfo> addInfoHashMap = new HashMap<>();
        for (SuitePriceOperationAdd operationAdd : operation.getAdds()) {

            for (AddDateInfo dateInfo : operationAdd.getDateInfo()) {

                if (logger.isInfoEnabled()) {
                    logger.info("dateInfo.getAddDateBegin()={}",dateInfo);
                }
                if (null ==dateInfo.getAddDateEnd()) {
                    continue;
                }

                DateTime dateTest = DateUtil.date(dateInfo.getAddDateBegin().getTime());
                while (dateTest.getTime() < ( dateInfo.getAddDateEnd().getTime()+1)  ) {
                    //hutool 周一是2 转换为  周一就是1
                    final int dayOfWeek = rebuildDayOfWeek(dateTest);
                    //不限制星期几 或者正好是要设定的星期
                    if(dateInfo.getWeeks().isEmpty() || containWeekInfo(dayOfWeek,dateInfo.getWeeks()) ){
                        addInfoHashMap.put(dateTest.toString(DATE_FORMAT10),new PriceAddInfo(operationAdd.getGoodsPriceAdd(),
                                operationAdd.getSettlePriceAdd())) ;
                    }
                    dateTest = dateTest.offset(DateField.DAY_OF_YEAR, 1);
                }
            }
        }

        //开始价格合并
        for (FmbxSuiteRoomdatePriceExt roomdatePrice : roomPriceInfo) {

            final String dateAim = roomdatePrice.getTargetDate().format(formater);
            if(addInfoHashMap.containsKey(dateAim)){
                roomdatePrice.setGoodsPrice(roomdatePrice.getGoodsPrice().add(addInfoHashMap.get(dateAim).getGoodsPriceAdd()));
                roomdatePrice.setSettlePrice(roomdatePrice.getSettlePrice().add(addInfoHashMap.get(dateAim).getSettlePriceAdd()));
            }
        }
    }

    private boolean containWeekInfo(int dayOfWeek, ArrayList<Integer> weeks) {
        for (Integer week : weeks) {
            if (dayOfWeek==week.intValue()){
                return true ;
            }
        }
        return false ;
    }

    /**
     * 查询最后一次的 加价模式操作
     * @param skuid
     * @return
     */
    public FmbxSuitePriceOperate queryLastPriceAddInfo(Integer skuid,int type) {

        Wrapper<FmbxSuitePriceOperate> wr = new LambdaQueryWrapper<FmbxSuitePriceOperate>()
                .eq(FmbxSuitePriceOperate::getSkuId,skuid)
                .eq(FmbxSuitePriceOperate::getChangeType,type)
                .select(FmbxSuitePriceOperate::getOperateData)
                .orderByDesc(FmbxSuitePriceOperate::getPriceOperateId)
                .last(" limit 1 ")
                ;
        final List<FmbxSuitePriceOperate> list = priceOperateService.list(wr);
        if (!list.isEmpty()) {
            return list.get(0);
        }
        return null ;

    }

    /**
     * 根据套餐查询 加价模式的操作信息
     * @param suiteid
     * @param resultMap
     */
    public void queryPriceAddOperation(Integer suiteid, HashMap<String, Object> resultMap) {

        List<Map<String, Object>> maps = suiteDao.selectPriceAddOperation(suiteid);

        resultMap.put("operates",maps) ;

    }

    public void queryOperateInfoOfAddMode(Integer skuid, HashMap<String, Object> resultMap) {

        final FmbxSuiteRoomSku sku = roomSkuService.getById(skuid);

        if (sku == null) {
            throw new BadLogicException("sku信息错误");
        }

        Wrapper<FmbxSuite> wr = new LambdaQueryWrapper<FmbxSuite>()
                .eq(FmbxSuite::getSuiteId,sku.getSuiteId())
                .select(FmbxSuite::getName,FmbxSuite::getTotalNeight)
                ;
        final FmbxSuite suite = suiteService.getOne(wr);

        if (suite == null) {
            throw new BadLogicException("套餐信息错误");
        }
        resultMap.put("skuName",sku.getSkuName());
        resultMap.put("totalNeight",suite.getTotalNeight());

        if (sku.getLastAddpriceOperateId()!=0) {
            queryOperateInfo(sku.getLastAddpriceOperateId(),resultMap) ;
        }

    }

    /**
     * 加价模式操作 信息返回
     * @param operateId
     * @param resultMap
     */
    public void queryOperateInfo(Integer operateId, HashMap<String, Object> resultMap) {

        final FmbxSuitePriceOperate operate = priceOperateService.getById(operateId);

        if (operate != null&& operate.getChangeType()==1 && StringUtils.isNoneBlank(operate.getOperateData())) {

            try {

                JSONObject jsonObject = JSON.parseObject(operate.getOperateData());
                //页面不需要携带roomid信息
                if (jsonObject != null) {
                    if (jsonObject.containsKey("roomid")) {
                        //删除roomid
                        jsonObject.remove("roomid") ;
                    }


                    resultMap.put("operateData",jsonObject) ;
                }
            }catch (Exception ex){
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            }
        }
//        else {
//            throw new BadLogicException("没有找到相关加价操作信息");
//        }

    }


    /**
     *
     * @param skuid
     * @param resultMap
     * @param addData 是否携带数据
     */
    public void genDatePriceExcel(Integer skuid, HashMap<String, Object> resultMap, boolean addData) {

        resultMap.put("result",0) ;
        final FmbxSuiteRoomSku sku = roomSkuService.getById(skuid);

        if (sku == null) {
            throw new BadLogicException("sku信息错误");
        }

        final List<ExcelPriceAllInfo> allInfos = new ArrayList<>();

        //如果需要返回数据
        if (addData) {
            Wrapper<FmbxSuiteRoomdatePrice> wrapPrice = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                    .select(FmbxSuiteRoomdatePrice::getTargetDate,FmbxSuiteRoomdatePrice::getMarketPrice,
                            FmbxSuiteRoomdatePrice::getGoodsPrice,FmbxSuiteRoomdatePrice::getSettlePrice)
                    .eq(FmbxSuiteRoomdatePrice::getSuiteRoomSkuId,skuid)
                    .ge(FmbxSuiteRoomdatePrice::getTargetDate,LocalDate.now())
                    .orderByAsc(FmbxSuiteRoomdatePrice::getTargetDate)
                    ;
            final List<FmbxSuiteRoomdatePrice> list = roomdatePriceService.list(wrapPrice);


            for (FmbxSuiteRoomdatePrice price : list) {
                final ExcelPriceAllInfo addInfo = new ExcelPriceAllInfo();

                final String[] split = price.getTargetDate().toString().split("-");
                if (split.length!=3) {
                    throw new BadLogicException("sku="+skuid +",错误的日期信息"+price.getTargetDate().toString());
                }

                addInfo.setYear(split[0]);
                addInfo.setMonth(split[1]);
                addInfo.setDay(split[2]);
                addInfo.setMarketPrice(price.getMarketPrice().doubleValue());
                addInfo.setGoodsPrice(price.getGoodsPrice().doubleValue());
                addInfo.setSettlePrice(price.getSettlePrice().doubleValue());

                allInfos.add(addInfo) ;
            }
        }

        String sheetName = sku.getSkuId() +"#"+sku.getSkuName();
        UploadFileOutInfo outInfo = ExcelService.genExcel(allInfos, ExcelPriceAllInfo.class, sheetName, "hotelSkuPrice");

        resultMap.put("fileInfo",outInfo) ;
        resultMap.put("result",1) ;

    }


    @DSTransactional
    public void copyHotelRoomSkuPrice(Integer sourceSkuId, Integer aimSkuId, HashMap<String, Object> resultMap){

        final FmbxSku sku =  skuService.getById(aimSkuId);
        final FmbxSku skuSource =  skuService.getById(sourceSkuId);
        if (sku == null) {

            resultMap.put("message","目标sku信息异常") ;
            resultMap.put("result",0) ;
            return  ;

        }
        if (skuSource == null) {
            resultMap.put("message","源sku信息异常") ;
            resultMap.put("result",0) ;
            return  ;
        }


        final FmbxActivity activity = queryFmbxActivity(sku.getXaid());
        final FmbxSuiteRoomSku suiteRoomSku = roomSkuService.getById(aimSkuId);

        Wrapper<FmbxSuiteRoomdatePrice> wrPrice = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                .eq(FmbxSuiteRoomdatePrice::getSuiteRoomSkuId,sourceSkuId)
                .select(FmbxSuiteRoomdatePrice::getTargetDate,FmbxSuiteRoomdatePrice::getMarketPrice,
                        FmbxSuiteRoomdatePrice::getGoodsPrice,FmbxSuiteRoomdatePrice::getSettlePrice)
                ;
        final List<FmbxSuiteRoomdatePrice> copyPrice = roomdatePriceService.list(wrPrice);
        final LocalDateTime now = LocalDateTime.now();
        for (FmbxSuiteRoomdatePrice price : copyPrice) {
            price.setSuiteRoomSkuId(aimSkuId);
            price.setSuiteId(suiteRoomSku.getSuiteId());
            price.setRoomId(suiteRoomSku.getRoomId());
            price.setVersionNum(0);
            price.setCtime(now);
        }

        if (!copyPrice.isEmpty()) {

            Wrapper<FmbxSuiteRoomdatePrice> wrDel = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                    .eq(FmbxSuiteRoomdatePrice::getSuiteRoomSkuId,aimSkuId);
            roomdatePriceService.remove(wrDel) ;

            roomdatePriceService.saveBatch(copyPrice) ;

            SessionUser nowUserInfo = userUnionService.nowUserInfo();

            FmbxSuitePriceOperate operate = new FmbxSuitePriceOperate();
            operate.setXaid(sku.getXaid());
            operate.setActivityName(activity.getTitle());
            operate.setSkuId(aimSkuId);
            operate.setSkuName(suiteRoomSku.getSkuName());
            operate.setSuiteId(suiteRoomSku.getSuiteId());

            HashMap<String, Object> hash = new HashMap<>();
            hash.put("sourceSkuId", sourceSkuId);
            hash.put("aimSkuId", aimSkuId);
            hash.put("message", "sku价格复制"+copyPrice.size());

            operate.setOperateData(JSON.toJSONString(hash));
            operate.setChangeType(6);

            operate.setAdminType(nowUserInfo.getUserType());
            operate.setAdminUid(nowUserInfo.getUserId());
            operate.setAdminName(nowUserInfo.myInfo());
            operate.setCtime(now);

            priceOperateService.save(operate);
            resultMap.put("message","OK") ;
            resultMap.put("result",1) ;
        }else {
            resultMap.put("message","源sku没有价格信息") ;
            resultMap.put("result",0) ;
            return  ;
        }

    }


    /**
     * 读取sku的 价格日历 excel 并设置到数据库的价格日历里
     * @param uploadFile
     * @param resultMap
     */
    public void importPriceExcel(MultipartFile uploadFile, HashMap<String, Object> resultMap) {

        resultMap.put("result",0) ;
        try {

            SessionUser nowUserInfo = userUnionService.nowUserInfo();

            final UploadFileOutInfo outInfo = UploadFileUtil.preparePath("skuprice", "xlsx");

            final FileOutputStream outputStream = new FileOutputStream(outInfo.getFilePath());
            final InputStream inputStream = uploadFile.getInputStream();
            IOUtils.copy(inputStream, outputStream);
            IOUtils.closeQuietly(outputStream);
            IOUtils.closeQuietly(inputStream);

            final FileInputStream bookStream = new FileInputStream(new File(outInfo.getFilePath()));
            final ExcelReader reader = ExcelUtil.getReader(bookStream);

            //读取sheet的名字获取skuid
            String sheetName = reader.getSheetNames().get(0);
            final String[] split = sheetName.split("#", 2);

            if (split.length!=2) {
                throw new BadLogicException("excel工作表名称异常");
            }
            if (!NumberUtil.isNumber(split[0])) {
                throw new BadLogicException("excel工作表名称异常,skuId异常");
            }

            //判断skuid是否合法
            final FmbxSuiteRoomSku sku = roomSkuService.getById(Integer.parseInt(split[0]));
            if (sku == null) {
                throw new BadLogicException("sku信息错误");
            }
            //查询套餐信息
            FmbxSuite suiteInfo = suiteService.getById(sku.getSuiteId());
            if (suiteInfo == null) {
                throw new BadLogicException("套餐信息不存在"+sku.getSuiteId());
            }

            //读取excel 数据
            final List<Map<String, Object>> maps = reader.readAll();
            reader.close();
            IOUtils.closeQuietly(bookStream);
            //把文件存储一下



            resultMap.put("sheetName",sheetName) ;
//            resultMap.put("filePath",outInfo.getFilePath()) ;

            ArrayList<FmbxSuiteRoomdatePriceExt> roomPriceInfo = new ArrayList<>() ;

            for (Map<String, Object> map : maps) {
                //excel 表头信息如下
                // 年	月	日	划线价	售价	结算价
                final String dateStr =
                        map.get("年").toString() + "-" + StringUtils.leftPad(map.get("月").toString(),2,"0")   + "-" + StringUtils.leftPad(map.get("日").toString(),2,"0");

                FmbxSuiteRoomdatePriceExt info = new FmbxSuiteRoomdatePriceExt();
                info.setSuiteRoomSkuId(sku.getSkuId());
                info.setSuiteId(sku.getSuiteId());
                info.setRoomId(sku.getRoomId());
                info.setTargetDate(LocalDate.parse(dateStr,formater));

                info.setMarketPrice(new BigDecimal("0"));
                if ( StringUtils.isNotBlank( map.get("划线价").toString())){
                    info.setMarketPrice(new BigDecimal(map.get("划线价").toString()));
                }

                info.setGoodsPrice(new BigDecimal(map.get("售价").toString()));
                info.setSettlePrice(new BigDecimal(map.get("结算价").toString()));

                roomPriceInfo.add(info) ;
            }

            final LocalDateTime now = LocalDateTime.now();
            final SuitePriceOperation suitePriceOperationExcel = new SuitePriceOperation();

            suitePriceOperationExcel.getResult().put("OriginalFilename",uploadFile.getOriginalFilename()) ;
            suitePriceOperationExcel.getResult().put("OriginalFileSize",uploadFile.getSize()) ;
            suitePriceOperationExcel.getResult().put("excelLines",maps.size()) ;
            suitePriceOperationExcel.getResult().put("sheetName",sheetName) ;
            suitePriceOperationExcel.getResult().put("inputExcelLocalFilePath",outInfo.getFilePath()) ;

            createPriceCalendar(suitePriceOperationExcel, TYPE_FULL_BY_EXCEL, nowUserInfo, suiteInfo, queryFmbxActivity(suiteInfo.getXaid()),
                    roomPriceInfo,
                    now, sku.getRoomId());


            resultMap.put("result",1) ;

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            throw new BadLogicException("价格excel读取错误");
        }

    }

    /**
     * 酒店sku信息查询 根据套餐分组
     */

    public void selectHotelSku(HashMap<String,Object> resultMap, ReqHotelSku req) {

        List<HotelSkuDomain> lists = suiteDao.selectHotelSku(req);

        final DateTime now = DateTime.now();
        for (HotelSkuDomain list : lists) {
            String sellTypeStr = "";
            if(list.getFlagStandardHotel().equals(1)){
                sellTypeStr += "日历房";
            }
            if(list.getFlagStandardHotel().equals(1) && list.getFlagHotelReserve().equals(1)){
                sellTypeStr += "，";
            }
            if(list.getFlagHotelReserve().equals(1)){
                sellTypeStr += "预约房券";
                Wrapper<FmbxHotelReserveSku> resWrapper = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                        .select(FmbxHotelReserveSku::getCanDivide)
                        .eq(FmbxHotelReserveSku::getSuiteId,list.getSuiteId())
                        .eq(FmbxHotelReserveSku::getStatus,1)
                        .eq(FmbxHotelReserveSku::getHotelDateSkuId,0)
                        ;
                FmbxHotelReserveSku hotelReserveSku = reserveSkuService.getOne(resWrapper);
                if (hotelReserveSku != null){
                    if (hotelReserveSku.getCanDivide().equals(1)){
                        sellTypeStr += "(可以拆分)";
                    }else{
                        sellTypeStr += "(不可拆分)";
                    }
                }else{
                    sellTypeStr += "(未配置)";
                }
            }
            list.setSellTypeStr(sellTypeStr);
            for (HashMap<String, Object> stringObjectHashMap : list.getListSku()) {
                stringObjectHashMap.put("statusStr",HotelSkuDomain.statusMap.get(stringObjectHashMap.get("status").toString())) ;

                if ("price".equals(req.getTab())) {
                    HashMap<String, Object> par = new HashMap<>();
                    par.put(SKUID, stringObjectHashMap.get("skuId"));
                    par.put("dateStart", now.toString()) ;

                    logger.info("stringObjectHashMap.get(\"skuId\")={}",stringObjectHashMap.get("skuId"));
                    stringObjectHashMap.put("priceFlag" ,   suiteDao.selectValidPrice(par)!=null?1:0) ;
                }
            }
        }

        resultMap.put("skuInfo",lists) ;

    }
    /**
     * 酒店sku信息查询 不分组
     */
    public void selectHotelSkuList(HashMap<String,Object> resultMap, ReqHotelSku req) {

//        req.setIs_delete(0);
        List<HotelSkuDomain> lists = suiteDao.selectHotelSkuList(req);
        resultMap.put("skuInfo",lists) ;
    }



    /**
     * 根据skuid 查询特定时间段范围内的 价格信息
     * @param resultMap
     * @param req
     */
    public void selectHotelSkuDatePrice(HashMap<String, Object> resultMap, ReqHotelDate req) {
        List<HotelSkuDatePriceDomain> pricees = suiteDao.selectHotelSkuDatePrice(req);
        resultMap.put("pricees", pricees);
    }


    /**
     * 根据活动id查询 sku列表,并查询对应sku 特定时间段的价格数据
     * @param resultMap
     * @param xaidPar
     */
    public void selecthotelSkuDatePriceAll(HashMap<String, Object> resultMap, @Valid ReqHotelDate xaidPar) {

        ReqHotelSku reqHotelSku = new ReqHotelSku();
        reqHotelSku.setXaid(xaidPar.getXaid());
        reqHotelSku.setStatus(0);
        List<HotelSkuDomain> skulists = suiteDao.selectHotelSkuList(reqHotelSku);

        final Iterator<HotelSkuDomain> iterator = skulists.iterator();
        ReqHotelDate req = new ReqHotelDate();
        while (iterator.hasNext()) {
            HotelSkuDomain sku = iterator.next() ;
//            System.out.println(sku.toString());
            //指定sku查询
            if(sku.getSkuId()==null  || ( xaidPar.getInputSkuId()!=null && xaidPar.getInputSkuId()!= sku.getSkuId().intValue() ) ){
                iterator.remove();
                continue;
            }
            req.setDatecolBegin(xaidPar.getDatecolBegin());
            req.setDatecolEnd(xaidPar.getDatecolEnd());
            req.setSku_id(sku.getSkuId());
            List<HotelSkuDatePriceDomain> pricees = suiteDao.selectHotelSkuDatePrice(req);

            for (HotelSkuDatePriceDomain pricee : pricees) {
                pricee.setPriceUniq( double2String( pricee.getMarketPrice())+"/"+double2String(pricee.getGoodsPrice())+"/"+double2String(pricee.getSettlePrice()));
                pricee.setYearMonth(FmbDateUtil.toDate10Str(pricee.getDatecol()).substring(0,7));
            }

            sku.setPriceList(pricees);

            final Set<String> uniqPrice = pricees.stream().map(HotelSkuDatePriceDomain::getPriceUniq).collect(Collectors.toSet());
            final List<String> months = pricees.stream().map(HotelSkuDatePriceDomain::getYearMonth).collect(Collectors.toSet()).stream().sorted().collect(Collectors.toList());


            sku.setUniqPrice(uniqPrice);
            sku.setMonths(months);

//            resultMap.put("uniqPrice",uniqPrice) ;
//            resultMap.put("months",months) ;

            final ArrayList<HashMap<String, Object>> monthList = new ArrayList<>();
            for (String month : months) {
                HashMap<String, Object> monthGroup = new HashMap<>();
                monthGroup.put("month", month);
                monthGroup.put("data", pricees.stream().filter(s->month.equals(s.getYearMonth())).collect(Collectors.toList()));
                monthList.add(monthGroup);
            }

            sku.setMonthList(monthList);
//            resultMap.put("monthList",monthList) ;
        }

        if (xaidPar.getDoExport()==1) {

            if (!skulists.isEmpty()) {
                final HotelSkuDomain hotelSkuDomain = skulists.get(0);
                UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(hotelSkuDomain.getPriceList(), HotelSkuDatePriceDomain.class,
                        hotelSkuDomain.getName(),
                        "selecthotelSkuDatePriceAll");
                resultMap.put("excelInfo", uploadFileOutInfo);
            }

        }else{
            resultMap.put("skulists",skulists) ;
        }



    }


    /**
     * 根据活动查询 库存房态信息
     * @param resultMap
     * @param req
     */
    public void hotelStockDateList(HashMap<String, Object> resultMap, ReqHotelDate req) {

        //活动房型列表
        List<RoomStockInfo>  rooms =  suiteDao.selectStockRoomList2(req) ;

        //循环读取
        final Iterator<RoomStockInfo> iterator = rooms.iterator();

        while (iterator.hasNext()) {
            RoomStockInfo room = iterator.next() ;

            //如果指定了 房型id
            if (req.getInputRoomId() != null  && room.getRoomId().intValue()!=req.getInputRoomId().intValue() ) {
                iterator.remove();
                continue;
            }

            room.setDatesInfo(new ArrayList());

            req.setRoomId(room.getRoomId());
            final List<HotelStockDateDomain> hotelStockDateDomains = suiteDao.selectRoomStockDateInfo(req);
            room.setDatesInfo(hotelStockDateDomains) ;

        }

        resultMap.put("roomStocks" , rooms) ;

    }


    /**
     * 加价和整价 模式切换
     * @param suiteId 套餐id
     * @param resultMap
     */
    public void priceAddModeSwitch(Integer suiteId, Integer aimPriceInputType , HashMap<String, Object> resultMap) {


        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        //查询套餐的状态
        Wrapper<FmbxSuite> wr = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getName,FmbxSuite::getXaid,FmbxSuite::getPriceInputType,FmbxSuite::getSuiteId)
                .eq(FmbxSuite::getSuiteId,suiteId)
                ;
        final FmbxSuite suite = suiteService.getOne(wr) ;
        if (suite == null) {
            throw new BadLogicException("套餐信息异常 suiteId="+suiteId);
        }

        Wrapper<FmbxActivity> wrActicity = new LambdaQueryWrapper<FmbxActivity>()
                .select(FmbxActivity::getXaid,FmbxActivity::getTitle)
                .eq(FmbxActivity::getXaid,suite.getXaid())
                ;
        final FmbxActivity activity = activityService.getOne(wrActicity);
        if (activity == null) {
            throw new BadLogicException("活动信息异常xaid="+suite.getXaid());
        }


        //查询套餐下的所有已经上架的sku 个数
        Wrapper<FmbxSuiteRoomSku> roomSkuCheck = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId,suiteId)
                .eq(FmbxSuiteRoomSku::getStatus,1)
                .select(FmbxSuiteRoomSku::getSkuId)
//                .eq(FmbxSuiteRoomSku::getFlagSell,1)
                ;
        final long count = roomSkuService.count(roomSkuCheck);
        if (count >0) {
            throw new BadLogicException("目前套餐下还有"+count+"个SKU已经上架,请先屏蔽所有sku后再操作");
        }

        //预计删除的 价格数据
        Wrapper<FmbxSuiteRoomdatePrice> removeWrList = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                .eq(FmbxSuiteRoomdatePrice::getSuiteId,suiteId)
                .select(FmbxSuiteRoomdatePrice::getRoomId)

                ;

        final Set<Integer> collectRoom = roomdatePriceService.list(removeWrList).stream().map(FmbxSuiteRoomdatePrice::getRoomId).collect(Collectors.toSet());

        //根据 套餐查询
        Wrapper<FmbxSuiteRoomdatePrice> removeWr = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                .eq(FmbxSuiteRoomdatePrice::getSuiteId,suiteId)
                ;
        //要删多少条


        final long delCount = roomdatePriceService.count(removeWr);
        final boolean removeResult = roomdatePriceService.remove(removeWr);

        final LocalDateTime now = LocalDateTime.now();
        if (removeResult) {

            //删除成功后记录操作
            FmbxSuitePriceOperate op = new FmbxSuitePriceOperate();

            op.setXaid(suite.getXaid());
            op.setActivityName(activity.getTitle());
            op.setSkuId(0);
            op.setSkuName("");
            op.setSuiteId(suiteId);
            HashMap<String, Object> jData = new HashMap<>();
            jData.put("suiteId", suiteId);
            jData.put("delCount",delCount) ;

            op.setOperateData(JSON.toJSONString(jData));
            op.setChangeType(TYPE_SWITCH_ADDMODE_REMOVE_ALL);
            op.setAdminType(nowUserInfo.getUserType());
            op.setAdminUid(nowUserInfo.getUserId());
            op.setAdminName(nowUserInfo.myInfo());


            op.setCtime(now);

            priceOperateService.save(op);

        }

        //清空加价规则
//        cleanPriceOperinfoOfSuite(suiteId, now);

        //更新套餐录入状态为目标价格录入模式
        FmbxSuite upSuite = new FmbxSuite();
        upSuite.setSuiteId(suite.getSuiteId());

        if (aimPriceInputType.intValue() ==1 || aimPriceInputType.intValue()==2) {
            upSuite.setPriceInputType(aimPriceInputType.intValue());
        }else {
            upSuite.setPriceInputType(0);
        }
        suiteService.updateById(upSuite) ;

        for (Integer room : collectRoom) {
            sendHotelStockChangeNotify(room);
        }

        //返回操作结果
        resultMap.put("result",1) ;
        resultMap.put("delCount",delCount) ;

    }

    /**
     * 清空套餐下 所有sku的加价规则
     * @param suiteId
     * @param now
     */
    void cleanPriceOperinfoOfSuite(Integer suiteId, LocalDateTime now) {
        //清空加价规则 开始
        Wrapper<FmbxSuiteRoomSku> skusWr = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suiteId)
                .select(FmbxSuiteRoomSku::getSkuId)
                ;
        //删除原有sku里记录的 加价和整价操作信息
        final List<FmbxSuiteRoomSku> list = roomSkuService.list(skusWr);
        for (FmbxSuiteRoomSku fmbxSuiteRoomSku : list) {
            fmbxSuiteRoomSku.setLastFullpriceOperateId(0);
            fmbxSuiteRoomSku.setLastAddpriceOperateId(0);
            fmbxSuiteRoomSku.setUtime(now);
        }
        roomSkuService.updateBatchById(list) ;
    }

    /**
     *
     * @param xaid
     * @param resultMap
     */
    public void stockRoomList(Integer xaid, HashMap<String, Object> resultMap) {

        //活动房型列表
       List<RoomStockInfo>  rooms =  suiteDao.selectStockRoomList(xaid) ;

        for (RoomStockInfo room : rooms) {

            // 看房型 是否有有效库存
            // 日期大于等于今天 , 库存>0 ,状态是开启
            // 有一条就算ok
            final Integer roomId = room.getRoomId();
            final boolean haveStock = isHaveStock(roomId);

            room.setStatus(haveStock ?1:0);
        }


       resultMap.put("rooms",rooms) ;

    }

    /**
     * 根据 房型id 判断当前是否有 有效库存
     * @param roomId
     * @return
     */
    boolean isHaveStock(Integer roomId) {
        Wrapper<FmbxSuiteStock> wr = new LambdaQueryWrapper<FmbxSuiteStock>()
                .select(FmbxSuiteStock::getStockId)
                .eq(FmbxSuiteStock::getRoomId, roomId)
                .ge(FmbxSuiteStock::getTargetDate,LocalDate.now())
                .gt(FmbxSuiteStock::getStockNum,0)
                .eq(FmbxSuiteStock::getStatus,1)
                .last( " limit 1 ") ;
        final FmbxSuiteStock one = stockService.getOne(wr);
        final boolean haveStock = one != null;
        return haveStock;
    }



    /**
     * 根据 skuid 判断一段时间内一个sku 是否有有效的购买价格和库存
     * 方便在价格和库存变化时 级联更新 套餐和sku的状态
     *
     *
     *
     * @param skuid
     * @param checkPublishStatus 是否检查sku的上下架状态
     * @param preDay
     * @return
     */
    public boolean isSKUHaveValidPriceAndStock(Integer skuid, int checkPublishStatus, int preDay ) {

        HashMap<String, Object> par = new HashMap<>();
        par.put(SKUID, skuid);
        //根据这个条件判断是否检查 上下架状态
        if (checkPublishStatus==1) {
            par.put("checkPublishStatus", checkPublishStatus);
        }


        LocalDate now1 = LocalDate.now() ;
        now1 = now1.plus(preDay, ChronoUnit.DAYS) ;

        par.put("dateStart", now1.toString()) ;

        Map one =  suiteDao.selectValidPriceAndStock(par) ;

        return one != null;
    }

    /**
     * 检查一个套餐下是否有 有效的日历房sku   ,不考虑日历房的 对应的 套餐上的发布状态
     * @param suiteId
     * @return
     */
    public boolean isSuiteHaveValidHotelDateSku(Integer suiteId,int preDay) {
        return isSuiteHaveValidHotelDateSku(suiteId,preDay,null) ;
    }
    public boolean isSuiteHaveValidHotelDateSku(Integer suiteId,int preDay,Integer skuId) {
        HashMap<String, Object> par = new HashMap<>();
        par.put("suiteId", suiteId);
        if (skuId != null) {
            par.put("skuId",skuId) ;
        }

        LocalDate now = LocalDate.now() ;
        now = now.plus(preDay, ChronoUnit.DAYS) ;

        par.put("dateStart", now.toString()) ;

        Map one =  suiteDao.selectValidHotelDatePriceOfSuite(par) ;
        return one != null;
    }



//    public void skuStatusCheck(HashMap<String, Object> resultMap, Integer skuid) {
//        resultMap.put("result" , isSKUHaveValidPriceAndStock(skuid)?1:0 ) ;
//    }

    /**
     * 酒店库存 操作入口
     * @param resultMap
     * @param input
     */
    public void roomStockSet(HashMap<String, Object> resultMap, ReqroomStockSet input) {


        resultMap.put("result",0);
        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        final ArrayList<StockUpdateResultInfo> stockUpdateResultInfos = new ArrayList<>();
        resultMap.put("resultInfos",stockUpdateResultInfos) ;

        if (input.getStockChangeNumber()==null && input.getStatusChangeType()== ReqroomStockSet.STATUS_CHANGE_TYPE_KEEP_NOW) {
            throw new BadLogicException("房态不变,库存也不变操作没有意义!");
        }
        if (input.getStockChangeNumber()!=null && input.getStockChangeNumber()<0) {
            throw new BadLogicException("输入的库存最小值是0!");
        }

        if (input.getRoomIds().isEmpty()) {
            throw new BadLogicException("房型数据为空!");
        }



        final LocalDateTime now = LocalDateTime.now();
        final LinkedHashSet<String> dateSet = new LinkedHashSet<>();

        if (input.getFormType()==FORM_TYPE_DATE_BEGIN_END) {
            //解析日期 放入dateSet
            for (AddDateInfo dateInfo : input.getDateInfo()) {
                if (dateInfo.isValid()) {
                    DateTime dateTest = DateUtil.date(dateInfo.getAddDateBegin().getTime());
                    while (dateTest.getTime() < ( dateInfo.getAddDateEnd().getTime()+1)  ) {
                        //hutool 周一是2 转换为  周一就是1
                        final int dayOfWeek = rebuildDayOfWeek(dateTest);
                        //不限制星期几 或者正好是要设定的星期
                        if(dateInfo.getWeeks().isEmpty() || containWeekInfo(dayOfWeek,dateInfo.getWeeks()) ){
                            dateSet.add(dateTest.toString(DATE_FORMAT10)) ;
                        }
                        dateTest = dateTest.offset(DateField.DAY_OF_YEAR, 1);
                    }
                }
            }
        }

        if (input.getFormType()==FORM_TYPE_FIXED_DATE) {
            dateSet.addAll(input.getFixedDate()) ;
        }


        int successCount = 0 ;
        //构造 即将入库的 FmbxSuiteStock
        // 循环房型 分别操作库存
        for (Integer roomId : input.getRoomIds()) {
            ArrayList<FmbxSuiteStock> stocks = new ArrayList<>();
            dateSet.stream().forEach(dateStr -> {
                final FmbxSuiteStock stock = new FmbxSuiteStock();
                stock.setRoomId(roomId);
                stock.setTargetDate(LocalDate.parse(dateStr));
                stocks.add(stock);
            });


            //操作结果 初始化
            final StockUpdateResultInfo resultInfo = new StockUpdateResultInfo();
            resultInfo.setRoomid(roomId);
            stockUpdateResultInfos.add(resultInfo) ;


            //批量更新库存的数据
            final ArrayList<FmbxSuiteStock> updateStock = new ArrayList<>();
            //批量插入库存的数据
            final ArrayList<FmbxSuiteStock> insertStock = new ArrayList<>();

            //根据roomid 开启redis锁
            RLock stockLock = redissonClient.getLock(HOTEL_ROOM_SKU_NUMBER+roomId);

            try {
                //操作库存 请求上锁 ,最长等2秒
                boolean getLockOk = stockLock.tryLock(2000, TimeUnit.MILLISECONDS);
                if (getLockOk) {
                    try {
                        stockOperate(input, now, stocks, updateStock, insertStock, resultInfo ,nowUserInfo,roomId);
                        resultInfo.setInfo(roomId +"OK");
                        successCount ++ ;
                    } catch (Exception ex) {
                        long errorid = SnowflakeIdWorker.getInstance().nextId();
                        logger.error("errorid " + errorid, ex);
                    } finally {
                        stockLock.unlock();
                    }
                } else {
                    resultInfo.setInfo(roomId +"未能获取到库存锁,操作失败");
                }

            } catch (InterruptedException ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
                resultInfo.setInfo(roomId +" stockOperate执行异常");
            }
        }

        if (successCount==input.getRoomIds().size()) {
            resultMap.put("result",1);
            resultMap.put("message","库存设置成功");
        }else if (successCount==0){
            resultMap.put("result",0);
            resultMap.put("message","库存设置失败");
        }else if (successCount>0  &&  successCount< input.getRoomIds().size()){
            resultMap.put("result",2);
            resultMap.put("message","库存设置部分成功");
        }

    }

    /**
     *  开始操作酒店库存
     * @param input
     * @param now
     * @param stocks
     * @param updateStock
     * @param insertStock
     * @param resultInfo
     * @param nowUserInfo
     * @param roomId
     */
    void stockOperate(ReqroomStockSet input, LocalDateTime now, ArrayList<FmbxSuiteStock> stocks, ArrayList<FmbxSuiteStock> updateStock, ArrayList<FmbxSuiteStock> insertStock, StockUpdateResultInfo resultInfo, SessionUser nowUserInfo, Integer roomId) {

        ArrayList<FmbxSuiteStockOperateLog> operateLogs = new ArrayList<>();
        //把库存操作 收集到 批量更新 updateStock 或者批量插入 insertStock 数组里
        for (FmbxSuiteStock stock : stocks) {
            //保持房态
            if (ReqroomStockSet.STATUS_CHANGE_TYPE_KEEP_NOW == input.getStatusChangeType()) {
                roomStatusKeep(input, now, updateStock, stock,operateLogs);
            }
            //打开或者关闭房态
            else if (ReqroomStockSet.STATUS_CHANGE_TYPE_OPEN == input.getStatusChangeType()  || ReqroomStockSet.STATUS_CHANGE_TYPE_CLOSE == input.getStatusChangeType()) {
                roomStatusOpenOrClose(input, now, updateStock, insertStock, stock,operateLogs);
            }
        }

        //批量更新
        if (!updateStock.isEmpty()) {
            final boolean b = stockService.updateBatchById(updateStock);
            if (b) {
                resultInfo.setUpdateCount(updateStock.size());
            }
        }
        //批量插入
        if (!insertStock.isEmpty()) {
            final boolean b = stockService.saveBatch(insertStock);
            if (b) {
                resultInfo.setInsertCount(insertStock.size());
            }
        }

        //记录 库存操作 
        saveStockOperate(input, now, resultInfo, nowUserInfo, roomId,operateLogs);

    }

    /**
     * 保存库存更新操作
     * @param input
     * @param now
     * @param resultInfo
     * @param nowUserInfo
     * @param roomId
     * @param operateLogs
     */
    void saveStockOperate(ReqroomStockSet input, LocalDateTime now, StockUpdateResultInfo resultInfo, SessionUser nowUserInfo, Integer roomId, ArrayList<FmbxSuiteStockOperateLog> operateLogs) {
        FmbxSuiteStockOperate stockOperate = new FmbxSuiteStockOperate();

        Wrapper<FmbxBpsRoom> roomWra = new LambdaQueryWrapper<FmbxBpsRoom>()
                .eq(FmbxBpsRoom::getRoomId, roomId)
                .select(FmbxBpsRoom::getRoomName,FmbxBpsRoom::getBpsId)
                ;
        final FmbxBpsRoom roomDBInfo = roomService.getOne(roomWra);

        stockOperate.setBpsName("");
        stockOperate.setRoomId(roomId);
        if (roomDBInfo != null) {
            stockOperate.setBpsId(roomDBInfo.getBpsId());
            stockOperate.setRoomName(roomDBInfo.getRoomName());

            Wrapper<FmbxBps> bpsWrapper = new LambdaQueryWrapper<FmbxBps>()
                    .eq(FmbxBps::getBpsId,roomDBInfo.getBpsId())
                    .select(FmbxBps::getName)
                    ;
            final FmbxBps bpsName = bpsService.getOne(bpsWrapper);
            if (bpsName != null) {
                stockOperate.setBpsName(bpsName.getName());
            }
        }

        HashMap<String, Object> map = new HashMap<>();
        map.put("input", input);
        map.put("resultInfo", resultInfo);
        stockOperate.setOperateData(JSON.toJSONString(map));


        stockOperate.setChangeType(input.getFormType());
        stockOperate.setAdminType(nowUserInfo.getUserType());
        stockOperate.setAdminUid(nowUserInfo.getUserId());
        stockOperate.setAdminName(nowUserInfo.myInfo());
        stockOperate.setStatusChangeType(input.getStatusChangeType());
        stockOperate.setCtime(now);

        final boolean save = stockOperateService.save(stockOperate);
        if (save) {
            for (FmbxSuiteStockOperateLog o : operateLogs) {
                o.setStockOperateId(stockOperate.getStockOperateId());
            }
            stockOperateLogService.saveBatch(operateLogs) ;
        }
        sendHotelStockChangeNotify(roomId);
    }

    /**
     * 房态 打开或者关闭 ,同时修改库存数量
     * @param input
     * @param now
     * @param updateStock
     * @param insertStock
     * @param stock
     * @param operateLogs
     */
    void roomStatusOpenOrClose(ReqroomStockSet input, LocalDateTime now, ArrayList<FmbxSuiteStock> updateStock, ArrayList<FmbxSuiteStock> insertStock, FmbxSuiteStock stock, ArrayList<FmbxSuiteStockOperateLog> operateLogs) {
        int typeUpdate = 0 ;

        if (ReqroomStockSet.STATUS_CHANGE_TYPE_OPEN == input.getStatusChangeType()) {
            typeUpdate = ReqroomStockSet.STATUS_OPEN ;
        }
        if (ReqroomStockSet.STATUS_CHANGE_TYPE_CLOSE == input.getStatusChangeType()) {
            typeUpdate = STATUS_CLOSE ;
        }

        Wrapper<FmbxSuiteStock> wr = new LambdaQueryWrapper<FmbxSuiteStock>()
                .eq(FmbxSuiteStock::getRoomId, stock.getRoomId())
                .eq(FmbxSuiteStock::getTargetDate, stock.getTargetDate())

                ;
        FmbxSuiteStock test = stockService.getOne(wr);

        //需要新增
        if (test == null) {

            FmbxSuiteStock stockInsert = new FmbxSuiteStock();

            stockInsert.setRoomId(stock.getRoomId());
            stockInsert.setTargetDate(stock.getTargetDate());
            stockInsert.setStatus(typeUpdate);
            stockInsert.setVersionNum(0);
            stockInsert.setCtime(now);

            roomStockSet(input, test, stockInsert);
            insertStock.add(stockInsert) ;


            //详细操作日志构建
            FmbxSuiteStockOperateLog log = new FmbxSuiteStockOperateLog();
            log.setRoomId(stock.getRoomId());
            log.setTargetDate(stock.getTargetDate());
            log.setOperateInfo( "新增:"+stockInfo(stockInsert));
            log.setCtime(now);
            operateLogs.add(log) ;


        }else {

            FmbxSuiteStock stockUpdate = new FmbxSuiteStock();

            stockUpdate.setStatus(typeUpdate);

            stockUpdate.setStockId(test.getStockId());
            roomStockSet(input, test, stockUpdate);
            stockUpdate.setVersionNum(test.getVersionNum()+1);
            stockUpdate.setUtime(now);

            updateStock.add(stockUpdate) ;


            //详细操作日志构建
            FmbxSuiteStockOperateLog log = new FmbxSuiteStockOperateLog();
            log.setRoomId(stock.getRoomId());
            log.setTargetDate(stock.getTargetDate());
            log.setOperateInfo( "修改:"+ stockInfo(test)+ "->" +stockInfo(stockUpdate) );
            log.setCtime(now);
            operateLogs.add(log) ;


        }
    }

    /**
     * 设置库存数量
     * @param input  用户变库存操作
     * @param oldData  数据库里原有的库存信息 可能为空
     * @param stock   即将入库或者更新库存信息
     */
    void roomStockSet(ReqroomStockSet input, FmbxSuiteStock oldData, FmbxSuiteStock stock) {
        //关房时不做 库存变更
        if (input.getStockChangeNumber() != null   && STATUS_CHANGE_TYPE_CLOSE!=input.getStatusChangeType()  ) {
            if (input.getStockChangeType() == ReqroomStockSet.STOCK_CHANGE_TYPE_EQUAL) {
                stock.setStockNum(input.getStockChangeNumber());
            }
            int stockNumOld = 0 ;
            if (oldData != null) {
                stockNumOld = oldData.getStockNum();
            }
            if (input.getStockChangeType() == ReqroomStockSet.STOCK_CHANGE_TYPE_ADD) {
                stock.setStockNum(input.getStockChangeNumber() + stockNumOld);
            }
            if (input.getStockChangeType() == ReqroomStockSet.STOCK_CHANGE_TYPE_SUB) {
                final int stockNum = stockNumOld - input.getStockChangeNumber();
                stock.setStockNum(stockNum < 0 ? 0 : stockNum);
            }
        }
    }

    /**
     * 保持 原房态  修改库存
     * @param input
     * @param now
     * @param updateStock
     * @param stock
     * @param operateLogs
     */
    void roomStatusKeep(ReqroomStockSet input, LocalDateTime now, ArrayList<FmbxSuiteStock> updateStock, FmbxSuiteStock stock, ArrayList<FmbxSuiteStockOperateLog> operateLogs) {
        Wrapper<FmbxSuiteStock> wr = new LambdaQueryWrapper<FmbxSuiteStock>()
            .eq(FmbxSuiteStock::getRoomId, stock.getRoomId())
            .eq(FmbxSuiteStock::getTargetDate, stock.getTargetDate())
                ;
        FmbxSuiteStock test = stockService.getOne(wr);
        if (test == null) {
            return;
        }

        FmbxSuiteStock stockUpdate = new FmbxSuiteStock();
        stockUpdate.setStockId(test.getStockId());
        roomStockSet(input, test, stockUpdate);
        stockUpdate.setVersionNum(test.getVersionNum()+1);
        stockUpdate.setUtime(now);
        updateStock.add(stockUpdate) ;

        //详细操作日志构建
        FmbxSuiteStockOperateLog log = new FmbxSuiteStockOperateLog();
        log.setRoomId(stock.getRoomId());
        log.setTargetDate(stock.getTargetDate());
        log.setOperateInfo( "修改:"+ stockInfo(test)+ "->" +stockInfo(stockUpdate) );
        log.setCtime(now);
        operateLogs.add(log) ;

    }

    /**
     * 简化库存信息返回
     * @param test
     * @return
     */
    static String stockInfo(FmbxSuiteStock test) {

        String statusStr = "" ;

        if (test.getStatus() == null) {
            return "[库存:"+test.getStockNum()+"]" ;
        }

        switch (test.getStatus()) {
            case 1 :
                statusStr = "开" ;
               break;
            case 2 :
                statusStr = "关" ;
               break;
            case 0 :
                statusStr = "异常" ;
               break;
        }

        return "[库存:"+test.getStockNum()+",房态:"+statusStr+"]" ;
    }


    /**
     * 发异步消息 通知某房型的库存发生变化
     * @param roomId 房间id
     */
    public  void sendHotelStockChangeNotify(int roomId){

        logger.info("roomStockChange roomId={} sendHotelStockChangeNotify",roomId);
        final boolean haveStock = isHaveStock(roomId);
        logger.info( "haveStock ={} roomId={} ",haveStock,roomId );
        //有库存找到所有未开放的sku.看看是否可以打开
        //无库存找到所有 开放的sku.看看是否需要关闭
        Wrapper<FmbxSuiteRoomSku> roomWr = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getRoomId, roomId)
                .ne(FmbxSuiteRoomSku::getStatus, 0)
//                .eq(FmbxSuiteRoomSku::getFlagSell, haveStock ? 0 : 1)
                .select(FmbxSuiteRoomSku::getSkuId);
        List<FmbxSuiteRoomSku> list = roomSkuService.list(roomWr);

        list.forEach(sku -> sendHotelSkuChangeNotify(sku.getSkuId()));

    }




    /**
     * 发异步消息 通知skuid 发生变化
     * @param skuid
     */
    public static void sendHotelSkuChangeNotify(int skuid){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            //为了防止重复调用 2秒内的调用只进行一次
            SetParams parR = new SetParams().nx().px(2000);

            final String set = jedis.set("fmbx:sendHotelSkuChangeNotify:" + skuid, "", parR);
            if ("OK".equals(set)) {
                logger.info("sendHotelSkuChangeNotify_skuid_"+skuid);
                HashMap<String, Object> par = new HashMap<>();
                par.put(SKUID, skuid);
                FmbKafkaProducerService.send(HOTEL_SKU_CHANGE,skuid+"",par);
            }

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    /**
     * 发异步消息 通知skuid 发生变化
     * @param skuid
     */
    public static void sendHotelSkuChangeNotifyNotTouchActivity(int skuid){
        logger.info("sendHotelSkuChangeNotify_skuid_"+skuid);
        HashMap<String, Object> par = new HashMap<>();
        par.put(SKUID, skuid);

        par.put(TOUCH_ACTIVITY, 0);
        FmbKafkaProducerService.send(HOTEL_SKU_CHANGE,skuid+"",par);
    }


    /**
     * 发异步消息 通知skuid 发生变化
     * @param skuid
     */
    public static void sendHotelSkuDelete(int skuid){

        logger.info("sendHotelSkuDelete_skuid_"+skuid);
        HashMap<String, Object> par = new HashMap<>();
        par.put(SKUID, skuid);
        FmbKafkaProducerService.send(HOTEL_SKU_DELETE,skuid+"",par);
    }


    /**
     * 发异步消息 通知skuid 发生变化
     * @param aid
     */
    public static void sendActivityChangeNotify(int aid){
        logger.info("sendActivityChangeNotify_aid"+aid);
        HashMap<String, Object> par = new HashMap<>();
        par.put("aid", aid);
        FmbKafkaProducerService.send(AID_CHANGE,aid+"",par);
    }


    @KafkaListener(topics = {HOTEL_SKU_CHANGE,HOTEL_SKU_DELETE}, groupId = "cousumerBatchAutoAck", containerFactory = "autoBatchAck")
    public void cousumerBatchAutoAck(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    handleHotelSkuChange(record);
                }
            });
        }
    }


    @KafkaListener(topics = {AID_CHANGE}, groupId = "cousumerBatchAutoAck", containerFactory = "autoBatchAck")
    public void aidChange(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    handleAidChange(record);
                }
            });
        }
    }




    /**
     * 处理 aid发生变化
     */
    public void handleAidChange(ConsumerRecord<String, String> record){

        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
        if (!kafkaMessage.isPresent()) {
            return;
        }

        logger.info("receive message [{}]",kafkaMessage.get());
        JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;
        Integer aid = data.getInteger("aid");

        touchActivity(aid);

    }

    /**
     * 处理 某sku发生变化
     */
    public void handleHotelSkuChange(ConsumerRecord<String, String> record){

        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
        if (!kafkaMessage.isPresent()) {
            return;
        }

        logger.info("receive message [{}]",kafkaMessage.get());
        JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;

        int skuid = data.getInteger(SKUID);

        if (logger.isInfoEnabled()) {
            logger.info("handleHotelSkuChange{}",skuid);
        }

        int touchActivity = 1 ;
        if (data.containsKey(TOUCH_ACTIVITY)) {
            touchActivity = data.getInteger(TOUCH_ACTIVITY);
        }

        FmbxSku skuBaseDB = querySkuBase(skuid);

        if (skuBaseDB != null) {

            switch (skuBaseDB.getSkuType().intValue())
            {
                //酒店日历房
                case 1:
                    checkHotelDateSku(skuid,skuBaseDB,touchActivity==1);
                    break;
                //房券
                case 2:
                    checkHotelReserveSku(skuid,skuBaseDB,touchActivity==1);
                    break;
                default:
            }

            //如果是删除sku
            if (HOTEL_SKU_DELETE.equals(  record.topic())) {
                updateActivitySkuDeleteNumber(skuBaseDB.getXaid());
            }

        }else {
            logger.info("bad skuid={}",skuid);
        }

    }

    public FmbxSku querySkuBase(int skuid) {
        Wrapper<FmbxSku> oneWra = new LambdaQueryWrapper<FmbxSku>()
                .eq(FmbxSku::getSkuId, skuid)
                .select(FmbxSku::getSkuType,FmbxSku::getXaid,FmbxSku::getSkuId)
                ;
        FmbxSku skuBaseDB = skuService.getOne(oneWra) ;
        return skuBaseDB;
    }



    /**
     * 检查房券 sku
     * @param skuid
     * @param skuBaseDB
     */
    public void checkHotelReserveSku(int skuid, FmbxSku skuBaseDB,boolean checkActivity) {

//        final FmbxHotelReserveSku hotelReserveSku = reserveSkuService.getById(skuid);

        if (logger.isInfoEnabled()) {
            logger.info("checkHotelReserveSku_{}_{}",skuid,checkActivity);
        }

        Wrapper<FmbxHotelReserveSku> wr = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId,skuid)
                .select(FmbxHotelReserveSku::getFlagSell
                        ,FmbxHotelReserveSku::getSkuId
                        ,FmbxHotelReserveSku::getSuiteId
                        ,FmbxHotelReserveSku::getHotelDateSkuId,FmbxHotelReserveSku::getRoomId
                        ,FmbxHotelReserveSku::getStockNum,FmbxHotelReserveSku::getSellStartTime,FmbxHotelReserveSku::getSellEndTime)
                ;
        FmbxHotelReserveSku hotelReserveSku =  reserveSkuService.getOne(wr) ;

        if (logger.isInfoEnabled()) {
            logger.info("checkHotelReserveSkuhotelReserveSku={}",JSON.toJSONString(hotelReserveSku));
        }
//        Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
//                .eq(FmbxSuite::getSuiteId,hotelReserveSku.getSuiteId())
//
//                ;
//        final FmbxSuite suiteInfo = suiteService.getOne(wrSuite);

        if(hotelReserveSku!=null){

            final LocalDateTime now = LocalDateTime.now();

            final int preDay = querySuitePreDay(hotelReserveSku.getSuiteId());

            boolean suiteHaveValidHotelDateSku =  false;
            if (hotelReserveSku.getHotelDateSkuId()>0) {
                suiteHaveValidHotelDateSku =  isSuiteHaveValidHotelDateSku(hotelReserveSku.getSuiteId(),preDay,hotelReserveSku.getHotelDateSkuId());
            }else{
                suiteHaveValidHotelDateSku =  isSuiteHaveValidHotelDateSku(hotelReserveSku.getSuiteId(),preDay);
            }

            logger.info("checkHotelReserveSku suiteHaveValidHotelDateSku={}  reserveSkuId={} HotelDateSkuId={}",suiteHaveValidHotelDateSku,
                    skuid,
                    hotelReserveSku.getHotelDateSkuId());

            fmbSkuService.addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSku.getSkuId(),hotelReserveSku.getSellStartTime(),
                    hotelReserveSku.getSellEndTime());


            //售卖状态为无效  但库存和售卖时间满足 需要打开
            final boolean haveValidReserveSku = hotelReserveSku.getStockNum() > 0 && now.isAfter(hotelReserveSku.getSellStartTime()) && now.isBefore(hotelReserveSku.getSellEndTime()) && suiteHaveValidHotelDateSku;

            if (logger.isInfoEnabled()) {
                logger.info("checkHotelReserveSku haveValidReserveSku_{}_{}_{}",hotelReserveSku.getSkuId(),haveValidReserveSku,
                        hotelReserveSku.getStockNum());
            }

            if (hotelReserveSku.getFlagSell().intValue()==0
                    && haveValidReserveSku) {

                FmbxHotelReserveSku updateSku = new FmbxHotelReserveSku();
                updateSku.setSkuId(hotelReserveSku.getSkuId());
                updateSku.setFlagSell(1);
                final boolean b = reserveSkuService.updateById(updateSku);

                if (logger.isInfoEnabled()) {
                    logger.info("checkHotelReserveSku updateById_1_{}_{}",hotelReserveSku.getSkuId(),b);
                }

            } else if (hotelReserveSku.getFlagSell().intValue()==1 &&  !haveValidReserveSku) {
                //售卖状态为有效  但 库存和售卖时间满足有任意条件不满足 需要关闭

                FmbxHotelReserveSku updateSku = new FmbxHotelReserveSku();
                updateSku.setSkuId(hotelReserveSku.getSkuId());
                updateSku.setFlagSell(0);
                final boolean b = reserveSkuService.updateById(updateSku);
                if (logger.isInfoEnabled()) {
                    logger.info("checkHotelReserveSku updateById_0_{}_{}",hotelReserveSku.getSkuId(),b);
                }
            }

//            checkSkuOfHotelActivityAndUpdateActivity(skuBaseDB.getXaid());

        }

        if (checkActivity) {

            HashMap<String, Object> par = new HashMap<>();
            par.put("aid", skuBaseDB.getXaid());

            /**
             ******************************************************************
             *     找到活动所有在售 房券对应的 日历房套餐和skuid
             ******************************************************************
             **/
            final List<ReserveSkuInfoDomain> reserveSkuInfoDomains = suiteDao.selectReserveSkuInfo(par);

            final List<ReserveSkuMinMaxInfoDomain> reserveSkuMinMaxInfoDomains = new ArrayList<ReserveSkuMinMaxInfoDomain>();

            /**
             ******************************************************************
             *     循环查询 每一个 日历房sku 找出对应的最高最低价
             ******************************************************************
             **/
            for (ReserveSkuInfoDomain reserveSkuInfoDomain : reserveSkuInfoDomains) {

                final int preDay = FmbDateUtil.suiteDayNeedPreDay(reserveSkuInfoDomain.getPreReserveDay(), reserveSkuInfoDomain.getPreReserveTime());
                LocalDate now1 = LocalDate.now() ;
                now1 = now1.plus(preDay, ChronoUnit.DAYS) ;
                final String targetDateBegin = now1.toString();

                HashMap<String, Object> par2 = new HashMap<>();
                par2.put("targetDateBegin", targetDateBegin);
                par2.put("skuId", reserveSkuInfoDomain.getSkuId());

                final ReserveSkuMinMaxInfoDomain reserveSkuMinMaxInfoDomain = suiteDao.selectReserveSkuMinMaxInfoOne(par2);

                if (reserveSkuMinMaxInfoDomain != null) {
                    par2.put("price",reserveSkuMinMaxInfoDomain.getMinPrice()) ;
                    final ReserveSkuMinMaxInfoDomain reserveSkuMinMaxInfoDomainMin = suiteDao.selectReserveSkuMinMaxInfoOneMinMax(par2);
                    if (reserveSkuMinMaxInfoDomainMin != null) {
                        reserveSkuMinMaxInfoDomain.setMinMarketPrice(reserveSkuMinMaxInfoDomainMin.getMaxMarketPrice());
                    }
                    par2.put("price",reserveSkuMinMaxInfoDomain.getMaxPrice()) ;
                    final ReserveSkuMinMaxInfoDomain reserveSkuMinMaxInfoDomainMax = suiteDao.selectReserveSkuMinMaxInfoOneMinMax(par2);
                    if (reserveSkuMinMaxInfoDomainMax != null) {
                        reserveSkuMinMaxInfoDomain.setMaxMarketPrice(reserveSkuMinMaxInfoDomainMax.getMaxMarketPrice());
                    }
                    par2.remove("price") ;
                    reserveSkuMinMaxInfoDomains.add(reserveSkuMinMaxInfoDomain);
                    if (logger.isInfoEnabled()) {
                        logger.info("reserveSkuMinMaxInfoDomain={}",reserveSkuMinMaxInfoDomain);
                    }
                }

            }

            final Optional<BigDecimal> minGoods = reserveSkuMinMaxInfoDomains.stream().map(ReserveSkuMinMaxInfoDomain::getMinPrice)
//                    .filter(s -> s.compareTo(BigDecimal.ZERO) == 1)
                    .sorted().findFirst();
            final Optional<BigDecimal> maxGoods =
                    reserveSkuMinMaxInfoDomains.stream().map(ReserveSkuMinMaxInfoDomain::getMaxPrice)
//                            .filter(s -> s.compareTo(BigDecimal.ZERO) == 1)
                            .sorted(Comparator.reverseOrder()).findFirst();
            final Optional<BigDecimal> minGoodsMarket =
                    reserveSkuMinMaxInfoDomains.stream().map(ReserveSkuMinMaxInfoDomain::getMinMarketPrice)
//                            .filter(s -> s.compareTo(BigDecimal.ZERO) == 1)
                            .sorted().findFirst();
            final Optional<BigDecimal> maxGoodsMarket =
                    reserveSkuMinMaxInfoDomains.stream().map(ReserveSkuMinMaxInfoDomain::getMaxMarketPrice)
//                            .filter(s -> s.compareTo(BigDecimal.ZERO) == 1)
                            .sorted(Comparator.reverseOrder()).findFirst();

            if (minGoods.isPresent()|| maxGoods.isPresent()|| minGoodsMarket.isPresent() || maxGoodsMarket.isPresent()) {


                final BigDecimal val = minGoods.orElse(null) ;
                final BigDecimal val1 = minGoodsMarket.orElse(null) ;
                final BigDecimal val2 = maxGoods.orElse(null) ;
                final BigDecimal val3 = maxGoodsMarket.orElse(null) ;
                logger.info("priceMinMax {}_{}_{}_{}",val,val1,val2,val3);
                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
                        .eq(FmbxActivity::getXaid, skuBaseDB.getXaid())
                        .set(minGoods.isPresent(), FmbxActivity::getMinGoodsPriceHotelReserve, val)
                        .set(minGoodsMarket.isPresent(), FmbxActivity::getMinMarketPriceHotelReserve, val1)
                        .set(maxGoods.isPresent(), FmbxActivity::getMaxGoodsPriceHotelReserve, val2)
                        .set(maxGoodsMarket.isPresent(), FmbxActivity::getMaxMarketPriceHotelReserve, val3)
                        .set(FmbxActivity::getFlagHotelReserve,1)
                        ;
                final boolean update = activityService.update(acUp);
                logger.info("update_reserve_date1_{}",update);
            }else {
                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
                        .eq(FmbxActivity::getXaid, skuBaseDB.getXaid())
//                        .set( FmbxActivity::getMinGoodsPriceHotelReserve,  BigDecimal.ZERO  )
//                        .set( FmbxActivity::getMinMarketPriceHotelReserve, BigDecimal.ZERO  )
//                        .set( FmbxActivity::getMaxGoodsPriceHotelReserve,  BigDecimal.ZERO  )
//                        .set( FmbxActivity::getMaxMarketPriceHotelReserve, BigDecimal.ZERO  )
                        .set(FmbxActivity::getFlagHotelReserve,0)
                        ;
                final boolean update =activityService.update(acUp);

                logger.info("update_reserve_date0_{}",update);
            }

            /**
             ******************************************************************
             *      房券的价格不再参与 最低最高价格计算
             ******************************************************************
             **/
            //更新 房券最低售价
//            final HotelReserveMinPriceDomain hotelReserveMinPriceDomain = statService.queryHotelReserveMinPrice(skuBaseDB.getXaid());
//            final HotelReserveMinPriceDomain hotelReserveMaxPriceDomain = statService.queryHotelReserveMinPrice(skuBaseDB.getXaid(),2);
//            if (hotelReserveMinPriceDomain != null  || hotelReserveMaxPriceDomain !=null) {
//                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
//                        .eq(FmbxActivity::getXaid, skuBaseDB.getXaid())
//                        .set(hotelReserveMinPriceDomain!=null, FmbxActivity::getMinGoodsPriceHotelReserve,hotelReserveMinPriceDomain.getGoodsPrice())
//                        .set(hotelReserveMinPriceDomain!=null, FmbxActivity::getMinMarketPriceHotelReserve, hotelReserveMinPriceDomain.getMarketPrice())
//                        .set(hotelReserveMaxPriceDomain!=null, FmbxActivity::getMaxGoodsPriceHotelReserve, hotelReserveMaxPriceDomain.getGoodsPrice())
//                        .set(hotelReserveMaxPriceDomain!=null, FmbxActivity::getMaxMarketPriceHotelReserve, hotelReserveMaxPriceDomain.getMarketPrice())
//                        .set(FmbxActivity::getFlagHotelReserve,1)
//                        ;
//                activityService.update(acUp);
//
//            }else {
//                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
//                        .eq(FmbxActivity::getXaid, skuBaseDB.getXaid())
////                        .set( FmbxActivity::getMinGoodsPriceHotelReserve,  BigDecimal.ZERO  )
////                        .set( FmbxActivity::getMinMarketPriceHotelReserve, BigDecimal.ZERO  )
////                        .set( FmbxActivity::getMaxGoodsPriceHotelReserve,  BigDecimal.ZERO  )
////                        .set( FmbxActivity::getMaxMarketPriceHotelReserve, BigDecimal.ZERO  )
//                        .set(FmbxActivity::getFlagHotelReserve,0)
//                        ;
//                activityService.update(acUp);
//            }
            statService.syncActivityInfo2ProductIndex(skuBaseDB.getXaid(),"suiteTouchActivity");
        }

    }

    public void touchActivity(Integer xaid) {
        touchActivity(xaid,true,true,true,true) ;
    }

    /**
     *
     * @param xaid
     * @param touchSku  更新sku
     * @param touchReserve  更新房券
     * @param updateDateSkuPrice  更新日历房最高最低价格
     * @param statSyscActivity    调用  statService.syncActivityInfo2ProductIndex(xaid);
     */
    public void touchActivity(Integer xaid,boolean touchSku,boolean touchReserve,boolean updateDateSkuPrice,boolean statSyscActivity) {

        addLogTraceId() ;
        loggerImportant.info("touchActivity{}_begin {}_{}_{}_{}",xaid,touchSku,touchReserve,updateDateSkuPrice,statSyscActivity);

        HashMap<String, Object> par = new HashMap<>();
        par.put("xaid", xaid);
        // 如果需要只查看 有效的sku 就注释掉下面的语句
        // par.put("flag_sell", 1);

        List<Integer>  hotelDateSku =   frontSkuDao.queryHotelDateSku(par) ;
        List<Integer>  reserveSku =   frontSkuDao.queryHotelReserveSku(par) ;

        if (touchSku) {

            for (Integer id : hotelDateSku) {

                FmbxSku skuBaseDB = querySkuBase(id);
                checkHotelDateSku(id,skuBaseDB,false) ;
            }
            for (Integer id : reserveSku) {
                FmbxSku skuBaseDB = querySkuBase(id);
                checkHotelReserveSku(id,skuBaseDB,false);
            }

        }

        if (!hotelDateSku.isEmpty()) {

            final ArrayList<HotelDateMinPriceDomain> min = new ArrayList<>();
            final ArrayList<HotelDateMinPriceDomain> max = new ArrayList<>();
            for (Integer skuid : hotelDateSku) {
                //找到该 酒店日历房 sku的 最低和最高价
                final ArrayList<HotelDateMinPriceDomain> hotelDateMinPriceDomains = hotelDateMinPrice(skuid);
                if (!hotelDateMinPriceDomains.isEmpty()&& hotelDateMinPriceDomains.size()>1) {
                    min.add(hotelDateMinPriceDomains.get(0));
                    max.add(hotelDateMinPriceDomains.get(1));

                    if (updateDateSkuPrice) {
                        Wrapper<FmbxSuiteRoomSku> wrUpdatePrice = new LambdaUpdateWrapper<FmbxSuiteRoomSku>()
                                .eq(FmbxSuiteRoomSku::getSkuId,skuid)
                                .set(FmbxSuiteRoomSku::getMinGoodsPrice,hotelDateMinPriceDomains.get(0).getGoodsPrice())
                                .set(FmbxSuiteRoomSku::getMaxGoodsPrice,hotelDateMinPriceDomains.get(1).getGoodsPrice())
                                ;
                        roomSkuService.update(wrUpdatePrice) ;
                    }
                }
            }

            HotelDateMinPriceDomain hotelDateMinPriceDomain = null ;
            HotelDateMinPriceDomain hotelDateMaxPriceDomain = null ;
            if (!min.isEmpty()) {
                hotelDateMinPriceDomain = min.stream().sorted(Comparator.comparing(HotelDateMinPriceDomain::getGoodsPrice)).findFirst().get();
                hotelDateMaxPriceDomain = max.stream().sorted(Comparator.comparing(HotelDateMinPriceDomain::getGoodsPrice).reversed()).findFirst().get();

            }

            if (hotelDateMinPriceDomain != null || hotelDateMaxPriceDomain!=null) {
                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
                        .eq(FmbxActivity::getXaid, xaid)
                        .set(hotelDateMinPriceDomain!=null, FmbxActivity::getMinGoodsPrice, hotelDateMinPriceDomain.getGoodsPrice())
                        .set(hotelDateMinPriceDomain!=null, FmbxActivity::getMinMarketPrice, hotelDateMinPriceDomain.getMarketPrice())
                        .set(hotelDateMaxPriceDomain!=null, FmbxActivity::getMaxGoodsPrice, hotelDateMaxPriceDomain.getGoodsPrice())
                        .set(hotelDateMaxPriceDomain!=null, FmbxActivity::getMaxMarketPrice, hotelDateMaxPriceDomain.getMarketPrice())
                        .set(FmbxActivity::getFlagHotelDate,1)

                        ;
                activityService.update(acUp);

            }else {
//                Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
//                        .eq(FmbxActivity::getXaid, xaid)
//                        .set( FmbxActivity::getMinGoodsPrice, BigDecimal.ZERO)
//                        .set( FmbxActivity::getMinMarketPrice,BigDecimal.ZERO)
//                        .set( FmbxActivity::getMaxGoodsPrice, BigDecimal.ZERO)
//                        .set( FmbxActivity::getMaxMarketPrice, BigDecimal.ZERO)
//
//                        ;
//                activityService.update(acUp);

                //最低价找不到 说明 酒店日历房已经没有可售的了
                Wrapper<FmbxActivity> upAWr = new LambdaUpdateWrapper<FmbxActivity>()
                        .eq(FmbxActivity::getXaid,xaid)
                        .set(FmbxActivity::getFlagHotelDate,0)
                        ;
                activityService.update(upAWr) ;

            }

            if (statSyscActivity) {
                statService.syncActivityInfo2ProductIndex(xaid,"suiteTouchActivity");
            }

        }

        if(hotelDateSku.isEmpty() && reserveSku.isEmpty()){
            Wrapper<FmbxActivity> upAWr = new LambdaUpdateWrapper<FmbxActivity>()
                    .eq(FmbxActivity::getXaid,xaid)
                    .set(FmbxActivity::getFlagHotelDate,0)
                    .set(FmbxActivity::getFlagHotelReserve,0)
                    ;
            activityService.update(upAWr) ;
            if (statSyscActivity) {
                statService.syncActivityInfo2ProductIndex(xaid,"suiteTouchActivity");
            }
        }else if(hotelDateSku.isEmpty() ){
            Wrapper<FmbxActivity> upAWr = new LambdaUpdateWrapper<FmbxActivity>()
                    .eq(FmbxActivity::getXaid,xaid)
                    .set(FmbxActivity::getFlagHotelDate,0)
                    ;
            activityService.update(upAWr) ;
            if (statSyscActivity) {
                statService.syncActivityInfo2ProductIndex(xaid,"suiteTouchActivity");
            }
        }else {
            Wrapper<FmbxActivity> upAWr = new LambdaUpdateWrapper<FmbxActivity>()
                    .eq(FmbxActivity::getXaid,xaid)
                    .set(FmbxActivity::getFlagHotelReserve,0)
                    ;
            activityService.update(upAWr) ;
            if (statSyscActivity) {
                statService.syncActivityInfo2ProductIndex(xaid,"suiteTouchActivity");
            }
        }

        if (touchReserve) {
            if (reserveSku.size()>0) {
                final Integer skuid = reserveSku.get(0);
                FmbxSku skuBaseDB = querySkuBase(skuid);
                checkHotelReserveSku(skuid,skuBaseDB,true);
            }
        }

        loggerImportant.info("touchActivity{}_end {}_{}_{}_{}",xaid,touchSku,touchReserve,updateDateSkuPrice,statSyscActivity);
    }


    /**
     * 酒店日历房  sku变化查询
     *
     * 日历房最低价格 更新
     *
     * @param skuid
     * @param skuBaseDB
     */
    public void checkHotelDateSku(int skuid, FmbxSku skuBaseDB,boolean runActivity) {


        if (logger.isInfoEnabled()) {
            logger.info("checkHotelDateSku{}{}",skuid,runActivity);
        }
        //查询sku
        Wrapper<FmbxSuiteRoomSku> wr = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSkuId, skuid)
                .select(FmbxSuiteRoomSku::getStatus, FmbxSuiteRoomSku::getSuiteId, FmbxSuiteRoomSku::getFlagSell);
        final FmbxSuiteRoomSku skuHotelDB = roomSkuService.getOne(wr);

        //查询sku
        final Integer suiteId = skuHotelDB.getSuiteId();

        final int preDay = querySuitePreDay(suiteId);

        LocalDate now1 = LocalDate.now() ;
        now1 = now1.plus(preDay, ChronoUnit.DAYS) ;
        final String targetDateBegin = now1.toString();

        //有价格和库存 (不管是否发布)
        final boolean skuHaveValidPriceAndStockButNotPublish = isSKUHaveValidPriceAndStock(skuid, 0, preDay);



        skuStatusUpdate(skuid, skuHaveValidPriceAndStockButNotPublish? SKU_FLAG_SELL_VALID:SKU_FLAG_SELL_NOT_VALID);


        if (skuHotelDB != null) {

//            final boolean isSuiteHaveValidHotelSku = isSuiteHaveValidHotelDateSku(suiteId,preDay);

//            logger.info("isSuiteHaveValidHotelSku={}", isSuiteHaveValidHotelSku);
            hotelReserveSkuOfSuiteLotOperate(suiteId, 1);


//            checkSkuOfHotelActivityAndUpdateActivity(skuBaseDB.getXaid());

        } else {
            logger.info("bad skuHotelDB skuid={}", skuid);
        }

        if (runActivity) {

            touchActivity(skuBaseDB.getXaid(),false,false,true,true);

        }

    }

    public ArrayList<HotelDateMinPriceDomain> hotelDateMinPrice(int skuId) {
        //查询sku
        Wrapper<FmbxSuiteRoomSku> wr = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSkuId, skuId)
                .select(FmbxSuiteRoomSku::getStatus, FmbxSuiteRoomSku::getSuiteId, FmbxSuiteRoomSku::getFlagSell);
        final FmbxSuiteRoomSku skuHotelDB = roomSkuService.getOne(wr);

        //查询sku
        final Integer suiteId = skuHotelDB.getSuiteId();

        final int preDay = querySuitePreDay(suiteId);

        LocalDate now1 = LocalDate.now() ;
        now1 = now1.plus(preDay, ChronoUnit.DAYS) ;
        final String targetDateBegin = now1.toString();

        final HotelDateMinPriceDomain hotelDateMinPriceDomain =   statService.queryHotelDatePriceBySku(skuId,1,targetDateBegin);
        final HotelDateMinPriceDomain hotelDateMaxPriceDomain = statService.queryHotelDatePriceBySku(skuId,2,targetDateBegin);

        final ArrayList<HotelDateMinPriceDomain> r = new ArrayList<>();

        if (hotelDateMinPriceDomain != null && hotelDateMaxPriceDomain!=null) {
            r.add(hotelDateMinPriceDomain);
            r.add(hotelDateMaxPriceDomain);
        }

        return r ;

    }



    public int querySuitePreDay(Integer suiteId) {
        Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
                .eq(FmbxSuite::getSuiteId, suiteId)
                .select(FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime)
                ;
        final FmbxSuite suitePreInfo = suiteService.getOne(wrSuite);

        //计算需要提前几天
        final int preDay = FmbDateUtil.suiteDayNeedPreDay(suitePreInfo.getPreReserveDay(), suitePreInfo.getPreReserveTime());
        return preDay;
    }

    /**
     * 根据套餐查房券 并做对应的 打开和关闭操作
     * @param suiteId
     * @param operate    0-关闭   1-打开
     */
    public void hotelReserveSkuOfSuiteLotOperate(Integer suiteId, int operate) {
        logger.info("hotelReserveSkuOfSuiteLotOperate run suiteId={} i={}",suiteId , operate);

        Wrapper<FmbxHotelReserveSku> wr = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId,suiteId)
//                .eq(operate==0,FmbxHotelReserveSku::getFlagSell,1)
//                .eq(operate==1,FmbxHotelReserveSku::getFlagSell,0)
//                .eq(FmbxHotelReserveSku::getStatus,1)
                .select(FmbxHotelReserveSku::getSkuId)
                ;
        final List<FmbxHotelReserveSku> list = reserveSkuService.list(wr);

        for (FmbxHotelReserveSku reserveSku : list) {
            sendHotelSkuChangeNotify(reserveSku.getSkuId());
        }

    }

    void updateActivitySkuDeleteNumber(int xaid) {
          int count1 =   suiteDao.countHotelDeleteSku(xaid) ;
          int count2 =   suiteDao.countReserveHotelDeleteSku(xaid) ;

        final FmbxActivity activity = queryFmbxActivity(xaid);

        if(activity!=null  && activity.getDeletedSkuNum().intValue()!=(count1+count2) ) {
            FmbxActivity aup = new FmbxActivity();
            aup.setXaid(xaid);
            aup.setDeletedSkuNum(count1+count2);
            activityService.updateById(aup) ;
        }

    }



    /**
     * 根据酒店活动id 查询是否有 可售的酒店skh 和 房券sku 都没有的情况 就设置活动的 FlagHaveValidSku 为0
     * @param xaid  活动id
     */
//    public void checkSkuOfHotelActivityAndUpdateActivity(Integer xaid) {
//
//        RedissonLockService.doLockJob(FmbConstants.SKU_CHANGE_UPDATE_ACTIVITY, xaid+"", 3000, 1000,
//                () -> checkSkuOfHotelActivityAndUpdateActivityCore(xaid),"系统繁忙");
//    }

//    public void checkSkuOfHotelActivityAndUpdateActivityCore(Integer xaid) {
//        logger.info("checkSkuOfHotelActivityAndUpdateActivity {}",xaid);
//        Wrapper<FmbxActivity> aqr = new LambdaQueryWrapper<FmbxActivity>()
//                .eq(FmbxActivity::getXaid, xaid)
//                .select(FmbxActivity::getFlagDelete,FmbxActivity::getFlagHaveValidSku,FmbxActivity::getFlagPublish,FmbxActivity::getDeletedSkuNum)
//                ;
//        final FmbxActivity fmbxActivity = activityService.getOne(aqr);
//        logger.info("fmbxActivity xaid={} getFlagHaveValidSku={}" ,xaid ,fmbxActivity.getFlagHaveValidSku());
//        //如果在售
//        if (fmbxActivity != null && fmbxActivity.getFlagHaveValidSku()==1) {
//            //找一个 酒店可卖的sku
//            Integer hotelSkuid =   suiteDao.selectSkuOfAvailableHotel(xaid) ;
//
//            if (hotelSkuid != null) {
//                logger.info("{} have_valid_hotel_date",xaid);
//                return;
//            }
//            Integer hotelReserveSkuid =   suiteDao.selectSkuOfAvailableReserveHotel(xaid) ;
//
//            if (hotelReserveSkuid != null) {
//                logger.info("{} have_valid_hotel_reserve",xaid);
//                return;
//            }
//
//            closeActivity(xaid) ;
//
//
//        } else if (fmbxActivity != null && fmbxActivity.getFlagHaveValidSku()==0) {
//            //找一个 酒店可卖的sku
//            Integer hotelSkuid =   suiteDao.selectSkuOfAvailableHotel(xaid) ;
//
//            if (hotelSkuid != null) {
//                logger.info("{} have_valid_hotel_date_open",xaid);
//                openActivity(xaid) ;
//                return;
//            }
//            Integer hotelReserveSkuid =   suiteDao.selectSkuOfAvailableReserveHotel(xaid) ;
//
//            if (hotelReserveSkuid != null) {
//                logger.info("{} have_valid_hotel_reserve_open",xaid);
//                openActivity(xaid) ;
//                return;
//            }
//            logger.info("not_open_aid_FlagHaveValidSku");
//        }
//    }


    void closeActivity(Integer xaid) {
        FmbxActivity activityUpdate = new FmbxActivity();
        activityUpdate.setXaid(xaid);
//                  `sell_stat` tinyint(4) NOT NULL DEFAULT '1' COMMENT '售卖状态:0-未知或非卖品,1-关闭,2-无库存,4-已过期,8-已屏蔽,16-在售中',
        activityUpdate.setFlagHaveValidSku(0);
        activityService.updateById(activityUpdate) ;
    }
    void openActivity(Integer xaid) {
        FmbxActivity activityUpdate = new FmbxActivity();
        activityUpdate.setXaid(xaid);
        activityUpdate.setFlagHaveValidSku(1);
        activityService.updateById(activityUpdate) ;
    }

//    void skuOpen(int skuid){
//        skuStatusUpdate(skuid,1) ;
//    }

    /**
     * 把sku 设置为 对应的  aimStatus
     * @param skuid
     * @param aimStatus  1-有效  0-无效
     * @return
     */
    public boolean skuStatusUpdate(int skuid, int aimStatus) {
        logger.info("skuStatusUpdate {} {}",skuid,aimStatus);
        FmbxSuiteRoomSku skuUpdate = new FmbxSuiteRoomSku();
        skuUpdate.setSkuId(skuid);
        skuUpdate.setFlagSell(aimStatus);
        skuUpdate.setUtime(LocalDateTime.now());
        return roomSkuService.updateById(skuUpdate) ;
    }


    /**
     * 酒店价格变动列表
     */

    public void selectHotelPriceChangeData(HashMap<String, Object> resultMap, ReqHotelPriceChangeData req) {

        if (req.getDoExport().intValue() == 0) {

            //单表查询
            IPage<HotelPriceChangeDataDomain> page = new Page<>(req.getPageno(), req.getPagesize());
            IPage<HotelPriceChangeDataDomain> rlist = suiteDao.selectHotelPriceChangeData(page, req);
            final List<HotelPriceChangeDataDomain> records = rlist.getRecords();

            for (HotelPriceChangeDataDomain record : records) {
                // 修改类型:1-加价模式,2-整价模式,3-整价指定日期,4-excel上传,5-加价模式切换清除数据
                final JSONObject parseObject = JSON.parseObject(record.getOperateData());
                if (record.getChangeType()==1) {
                    SuitePriceOperation operation = parseObject.toJavaObject(SuitePriceOperation.class) ;
                    record.setOperateDateInfo(operation.buildDateInfoOfAddMode());
                    record.setOperatePriceInfo(operation.buildPriceInfoOfAddMode());
                }else if (record.getChangeType()==2) {
                    SuitePriceOperation operation = parseObject.toJavaObject(SuitePriceOperation.class) ;
                    record.setOperateDateInfo(operation.buildDateInfoOfFullMode());
                    record.setOperatePriceInfo("");
                }else if (record.getChangeType()==3) {
                    SuitePriceOperation operation = parseObject.toJavaObject(SuitePriceOperation.class) ;
                    record.setOperateDateInfo(operation.buildDateInfoOfFixDateMode());
                    record.setOperatePriceInfo(operation.buildPriceInfoOfFixDateMode());
                }

            }

            resultMap.put("list", records);
            resultMap.put("total", rlist.getTotal());
            resultMap.put("pages", rlist.getPages());
            resultMap.put("size", rlist.getSize());
            resultMap.put("current", rlist.getCurrent());

        } else {

            List<HotelPriceChangeDataDomain> lists = suiteDao.selectHotelPriceChangeData(req);
            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, HotelPriceChangeDataDomain.class, "酒店价格变动列表", "selectHotelPriceChangeData");
            resultMap.put("excelInfo", uploadFileOutInfo);
        }
    }


/**
 * 酒店库存变化
 */
   
public void selectHotelStockChangeData(HashMap<String,Object> resultMap, ReqHotelStockChangeData req) {

      if (req.getDoExport().intValue()==0) {

        //单表查询
        IPage<HotelStockChangeDataDomain> page = new Page<>(req.getPageno(),req.getPagesize());
        IPage<HotelStockChangeDataDomain> rlist = suiteDao.selectHotelStockChangeData(page, req);
          final List<HotelStockChangeDataDomain> records = rlist.getRecords();

          //通过解析操作信息 把数据反向翻译一下放入字段
          for (HotelStockChangeDataDomain record : records) {
              final JSONObject input = JSON.parseObject(record.getOperateData()).getJSONObject("input");

              final ReqroomStockSet reqroomStockSet =  input.toJavaObject( ReqroomStockSet.class);
              record.setDateInfo(reqroomStockSet.genDateInfo());
              record.setStockInfo(reqroomStockSet.genStockInfo());
          }

          resultMap.put("list", records);
        resultMap.put("total", rlist.getTotal());
        resultMap.put("pages", rlist.getPages());
        resultMap.put("size", rlist.getSize());
        resultMap.put("current", rlist.getCurrent());


       }else{

          List<HotelStockChangeDataDomain> lists = suiteDao.selectHotelStockChangeData(req);
            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,HotelStockChangeDataDomain.class, "酒店库存变化", "selectHotelStockChangeData");
            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }


    public void hotelPriceChangeDetail(HashMap<String, Object> resultMap, Integer priceChangeOperateId) {

        final FmbxSuitePriceOperate operate = priceOperateService.getById(priceChangeOperateId);

        if (operate == null) {
            throw new BadLogicException("改价日志id错误 id="+priceChangeOperateId);
        }

        Wrapper<FmbxSuitePriceOperateLog> wrlog = new LambdaQueryWrapper<FmbxSuitePriceOperateLog>()
                .eq(FmbxSuitePriceOperateLog::getPriceOperateId,priceChangeOperateId)
                .select(FmbxSuitePriceOperateLog::getTargetDate,FmbxSuitePriceOperateLog::getOperateInfo)
                .orderByAsc(FmbxSuitePriceOperateLog::getTargetDate)
                ;
        final List<FmbxSuitePriceOperateLog> listLog = priceOperateLogService.list(wrlog);

        resultMap.put("logs",listLog) ;
        resultMap.put("operate",operate) ;

    }

    public void hotelStockChangeDetail(HashMap<String, Object> resultMap, Integer stockOperateId) {

        final FmbxSuiteStockOperate stockOperate = stockOperateService.getById(stockOperateId);
        if (stockOperate == null) {
            throw new BadLogicException("房态变更日志ID错误 stockOperateId="+stockOperateId);
        }

        Wrapper<FmbxSuiteStockOperateLog> stockWr = new LambdaQueryWrapper<FmbxSuiteStockOperateLog>()
                .eq(FmbxSuiteStockOperateLog::getStockOperateId,stockOperateId)
                .select(FmbxSuiteStockOperateLog::getTargetDate,FmbxSuiteStockOperateLog::getOperateInfo)
                .orderByAsc(FmbxSuiteStockOperateLog::getTargetDate)

                ;
        final List<FmbxSuiteStockOperateLog> list = stockOperateLogService.list(stockWr);
        resultMap.put("logs",list) ;
        resultMap.put("stockOperate",stockOperate) ;

    }


    /**
     * 上架活动
     * @param resultMap
     * @param xaid
     */
    public void activityPublish(HashMap<String, Object> resultMap, Integer xaid) {
        resultMap.put("result",0) ;
        int aimFlag = 1 ;
        String aimResult = "上架" ;
        doPublishJob(resultMap, xaid, aimFlag,aimResult);
    }

    /**
     * 下架活动
     * @param resultMap
     * @param xaid
     */
    public void activityUnPublish(HashMap<String, Object> resultMap, Integer xaid) {

        resultMap.put("result",0) ;
        int aimFlag = 2 ;
        String aimResult = "下架" ;
        doPublishJob(resultMap, xaid, aimFlag,aimResult);

    }

    void doPublishJob(HashMap<String, Object> resultMap, Integer xaid, int aimFlag,String aimResult) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        final FmbxActivity activity = queryFmbxActivity(xaid) ;

        if (activity == null) {
            throw new BadLogicException("活动id不存在"+ xaid);
        }

        if (activity.getFlagPublish().intValue()== aimFlag) {
            throw new BadLogicException("当前活动就是"+aimResult+"状态,无需操作,xaid="+ xaid);
        }


        FmbxActivity activityUpdate = new FmbxActivity();
        activityUpdate.setXaid(xaid);
        activityUpdate.setLastModifyUid(nowUserInfo.getUserId());
        activityUpdate.setLastModifyUidName(nowUserInfo.getUsername());
        final LocalDateTime now = LocalDateTime.now();
        activityUpdate.setUtime(now);
        activityUpdate.setFlagPublish(aimFlag);

        final boolean b = activityService.updateById(activityUpdate);
        if (b) {

            String beforeStr = ActivityXDomain.flagPublishMap.get("" + activity.getFlagPublish());
            FmbxActivityPublishLog plog = new FmbxActivityPublishLog();
            plog.setCtime(now);
            if(2== aimFlag){
                plog.setResult("从"+beforeStr+"->下架");
                plog.setOpType(2);
            }
            if(1== aimFlag){
                plog.setResult("从"+beforeStr+"->上架");
                plog.setOpType(1);
            }
            plog.setCreateUid(nowUserInfo.getUserId());
            plog.setCreateUidName(nowUserInfo.getUsername());
            plog.setXaid(xaid);
            publishLogService.save(plog) ;
            SuiteService.sendActivityChangeNotify(xaid);
        }


        resultMap.put("result" ,1 ) ;
    }

/**
 * 日历房酒店sku列表
 */
public void selectHotelDateSku(HashMap<String,Object> resultMap, ReqHotelDateSku req) {

      if (req.getDoExport().intValue()==0) {

        //联表查询
        IPage<Integer> pageInt = new Page<>(req.getPageno(),req.getPagesize());
        IPage<Integer> rlistInt = suiteDao.selectHotelDateSkuReturnInt(pageInt, req);
        
        List<HotelDateSkuDomainSub> listsDomain = new ArrayList<>() ;

          resultMap.put("pageSkuCount",0);
          resultMap.put("skuTotalCount",0);

        if (!rlistInt.getRecords().isEmpty()) {

            //将分页套餐id参数放入查询条件里
            req.setSuiteIds(rlistInt.getRecords());
            listsDomain = suiteDao.selectHotelDateSkuUserListIds(req);
            int pageSkuCount = 0 ;
            for (HotelDateSkuDomainSub domainSub : listsDomain) {
                pageSkuCount +=domainSub.getList().size() ;
            }

            resultMap.put("pageSkuCount",pageSkuCount);
            //获取符合条件的所有的sku数量 ,先把分页套餐参数清除
            req.setSuiteIds(new ArrayList<>());
            resultMap.put("skuTotalCount",suiteDao.selectHotelDateSkuTotalConut(req));
        }

        resultMap.put("list", listsDomain);
        resultMap.put("total", pageInt.getTotal());
        resultMap.put("pages", pageInt.getPages());
        resultMap.put("size", pageInt.getSize());
        resultMap.put("current", pageInt.getCurrent());

       }else{

          List<HotelDateSkuDomain> lists = suiteDao.selectHotelDateSku(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,HotelDateSkuDomain.class, "日历房酒店sku列表", "selectHotelDateSku");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }



/**
 * 房券sku列表
 */
   
public void selectHotelReserveSku(HashMap<String,Object> resultMap, ReqHotelReserveSku req) {

      if (req.getDoExport().intValue()==0) {



        //单表查询
        IPage<HotelReserveSkuDomain> page = new Page<>(req.getPageno(),req.getPagesize());
          final IPage<Integer> suiteIds = suiteDao.selectHotelReserveSkuSuiteIds(page, req);

          req.setSuiteIds(suiteIds.getRecords());

          List<HotelReserveSkuDomain> rlist = suiteDao.selectHotelReserveSku( req);

          for (HotelReserveSkuDomain record : rlist) {
              record.setRoomInfos(new ArrayList());

              final List<Map> c = suiteDao.selctRoomInfoOfSuiteAll(record.getSuiteId());

              if (record.getHotelReserveSplit()==1 && record.getHotelDateSkuId()>0) {
                  for (Map map : c) {
                      if ((int) map.get("room_id") == record.getRoomId().intValue()) {
                          record.getRoomInfos().add(map);
                          break;
                      }
                  }
              }

              if(record.getHotelReserveSplit()==2 && record.getHotelDateSkuId()==0){
                  record.getRoomInfos().addAll(c);
              }

          }

          final HashMap<Integer, HotelReserveSkuDomainGroup> integerHotelReserveSkuDomainGroupHashMap = new HashMap<>();

          final ArrayList<HotelReserveSkuDomainGroup> hotelReserveSkuDomainGroups = new ArrayList<>();
          for (HotelReserveSkuDomain record : rlist) {

              HotelReserveSkuDomainGroup hotelReserveSkuDomainGroup = null ;
              if (integerHotelReserveSkuDomainGroupHashMap.containsKey(record.getSuiteId())) {
                  hotelReserveSkuDomainGroup = integerHotelReserveSkuDomainGroupHashMap.get(record.getSuiteId()) ;
              }else{
                  hotelReserveSkuDomainGroup = new HotelReserveSkuDomainGroup();
                  hotelReserveSkuDomainGroup.setSuiteId(record.getSuiteId());
                  hotelReserveSkuDomainGroup.setSuiteSortValue(record.getSkuSortValue());
                  hotelReserveSkuDomainGroup.setName(record.getSuiteName());
              }

              //  1-拆分房券,2-单一房券
              if (record.getHotelReserveSplit()==1 && record.getHotelDateSkuId()>0) {
                  hotelReserveSkuDomainGroup.getList().add(record);
                  integerHotelReserveSkuDomainGroupHashMap.put(record.getSuiteId(),hotelReserveSkuDomainGroup);
              }else if(record.getHotelReserveSplit()==2 && record.getHotelDateSkuId()==0) {
                  hotelReserveSkuDomainGroup.getList().add(record);
                  integerHotelReserveSkuDomainGroupHashMap.put(record.getSuiteId(),hotelReserveSkuDomainGroup);
              }

          }

          final List<HotelReserveSkuDomainGroup> collect = integerHotelReserveSkuDomainGroupHashMap.values().stream().sorted(Comparator.comparing(HotelReserveSkuDomainGroup::getSuiteSortValue).reversed()).collect(Collectors.toList());
//          resultMap.put("collect", collect);

          resultMap.put("list",collect);
        resultMap.put("total", suiteIds.getTotal());
        resultMap.put("pages", suiteIds.getPages());
        resultMap.put("size", suiteIds.getSize());
        resultMap.put("current", suiteIds.getCurrent());




       }else{

          List<HotelReserveSkuDomain> lists = suiteDao.selectHotelReserveSku(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,HotelReserveSkuDomain.class, "房券sku列表", "selectHotelReserveSku");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }


    /**
     * 批量更新酒店日历房 排序
     * @param resultMap
     * @param input
     */
    @DSTransactional
    public void updateHotelDateSkuSortValue(HashMap<String, Object> resultMap, ReqUpdateHotelDateSkuSortValueList input) {

        resultMap.put("result", 0);
        for (ReqUpdateHotelDateSkuSortValueList.DataDTO s : input.getData()) {

            if (s.getSuiteSortValue() != null) {
                FmbxSuite suiteUpdate = new FmbxSuite();
                suiteUpdate.setSuiteId(s.getSuiteId());
                suiteUpdate.setSortValue(s.getSuiteSortValue());
                suiteService.updateById(suiteUpdate);
            }

            for (ReqUpdateHotelDateSkuSortValueList.DataDTO.ListDTO dto : s.getList()) {
                if (dto.getSkuSortValue() != null) {
                    FmbxSuiteRoomSku sku = new FmbxSuiteRoomSku();
                    sku.setSkuId(dto.getSkuId());
                    sku.setSortValue(dto.getSkuSortValue());
                    roomSkuService.updateById(sku);
                }
            }

        }
        resultMap.put("result", 1);

    }

    @DSTransactional
    public void updateHotelReserveSkuSortValue(HashMap<String, Object> resultMap, ReqUpdateReserveHotelSkuSortValueList input) {

        resultMap.put("result", 0);
        for (ReqUpdateReserveHotelSkuSortValueList.DataDTO dataDTO : input.getData()) {
            Wrapper<FmbxHotelReserveSku> wr = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .select(FmbxHotelReserveSku::getSuiteId)
                    .eq(FmbxHotelReserveSku::getSkuId,dataDTO.getSkuId());
            FmbxHotelReserveSku fmbxHotelReserveSku = reserveSkuService.getOne(wr);
            if (fmbxHotelReserveSku!=null){
                Wrapper<FmbxHotelReserveSku> wrsku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                        .select(FmbxHotelReserveSku::getSkuId)
                        .eq(FmbxHotelReserveSku::getSuiteId,fmbxHotelReserveSku.getSuiteId());
                List<FmbxHotelReserveSku> list = reserveSkuService.list(wrsku);
                for (FmbxHotelReserveSku updateReserve : list){
                    updateReserve.setSortValue(dataDTO.getSkuSortValue());
                    reserveSkuService.updateById(updateReserve) ;
                }
            }
        }
        resultMap.put("result", 1);
    }

    public FmbxActivityPublishLog queryLastPublishLog(int xaid){

        Wrapper<FmbxActivityPublishLog> wr = new LambdaQueryWrapper<FmbxActivityPublishLog>()
                .eq(FmbxActivityPublishLog::getXaid,xaid)
                .select(FmbxActivityPublishLog::getCreateUid,FmbxActivityPublishLog::getCreateUidName,FmbxActivityPublishLog::getCtime)
                .orderByDesc(FmbxActivityPublishLog::getPublishLogId)
                .last(" limit 1 ")
                ;
        return publishLogService.getOne(wr);

    }


/**
 * 活动发布日志
 */
public void selectActivityPublishLog(HashMap<String,Object> resultMap, ReqActivityPublishLog req) {

    if (req.getDoExport().intValue() == 0) {

        //单表查询
        IPage<ActivityPublishLogDomain> page = new Page<>(req.getPageno(), req.getPagesize());
        IPage<ActivityPublishLogDomain> rlist = suiteDao.selectActivityPublishLog(page, req);
        resultMap.put("list", rlist.getRecords());
        resultMap.put("total", rlist.getTotal());
        resultMap.put("pages", rlist.getPages());
        resultMap.put("size", rlist.getSize());
        resultMap.put("current", rlist.getCurrent());

    } else {

        List<ActivityPublishLogDomain> lists = suiteDao.selectActivityPublishLog(req);

        UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, ActivityPublishLogDomain.class, "活动发布日志", "selectActivityPublishLog");

        resultMap.put("excelInfo", uploadFileOutInfo);
    }

}


}
