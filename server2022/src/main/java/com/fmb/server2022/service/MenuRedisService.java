package com.fmb.server2022.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.server2022.domain.FmbTreeInfo;
import com.fmb.server2022.fmbx.entity.SysMenu;
import com.fmb.server2022.fmbx.service.ISysMenuService;
import com.fmb.util.TreeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class MenuRedisService {


    private static Logger logger = LoggerFactory.getLogger(MenuRedisService.class);

    @Autowired
    ISysMenuService menuService ;


    /**
     * 根据特定用户的 权限返回树状菜单
     *
     * @param collect
     * @param returnAllMenu
     * 当为true时  返回完整树 collect传空的set ,
     * 当为false时 collect需要传入包含权限id 的 set ,
     *
     * @return
     */
//    @Cacheable(value = "ttl2H", keyGenerator = "customKeyGenerator",unless="#result==null or #result==''")
    public List<FmbTreeInfo> createUserTree(Set<String> collect, boolean returnAllMenu) {

        //找出所有的菜单
        LambdaQueryWrapper<SysMenu> wapper = new LambdaQueryWrapper<>();
        wapper.orderByDesc(SysMenu::getSortval).orderByAsc(SysMenu::getMenuId) ;
        List<SysMenu> dbAllMenu = menuService.list(wapper);
        //转换为  FmbTreeInfo list
        List<FmbTreeInfo> infos = new ArrayList<>();
        dbAllMenu.stream().filter( menu-> {
            return returnAllMenu ||  menu.getMenuId().intValue()==1 ||  menu.getIsFolder().intValue()==1 || collect.contains( menu
                    .getRelatedPermissionid()
                    .toString()) ;
        }  ) .forEach(m-> infos.add(new FmbTreeInfo(m))  );
        //生成树结构
        List<FmbTreeInfo> treeInfos = TreeUtils.generateTrees(infos);

        return treeInfos ;
    }

    //  MenuRedisService:createUserTree_[]_true
    @CacheEvict(value = "ttl2H",key = "#name")
    public void clearCache(String name){
        logger.info("clear_infoList_cache");
    }


}
