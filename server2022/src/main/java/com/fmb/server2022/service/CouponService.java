package com.fmb.server2022.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.ReqConfirmOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import com.fmb.server2022.domain.FrontCouponDomain;
import com.fmb.server2022.fmbx.entity.FmbCouponForbidden;
import com.fmb.server2022.fmbx.entity.FmbCouponType;
import com.fmb.server2022.fmbx.entity.FmbCouponUnableActivity;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.entity.FmbTemplate;
import com.fmb.server2022.fmbx.entity.FmbUserCoupon;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomdatePrice;
import com.fmb.server2022.fmbx.service.IFmbCouponForbiddenService;
import com.fmb.server2022.fmbx.service.IFmbCouponTypeService;
import com.fmb.server2022.fmbx.service.IFmbCouponUnableActivityService;
import com.fmb.server2022.fmbx.service.IFmbGoodsCategoryService;
import com.fmb.server2022.fmbx.service.IFmbTemplateService;
import com.fmb.server2022.fmbx.service.IFmbUserCouponService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.fmb.server2022.mapper.FmbUserCouponDao;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.util.FmbDateUtil;
import de.ailis.pherialize.Mixed;
import de.ailis.pherialize.MixedArray;
import de.ailis.pherialize.Pherialize;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class CouponService {

    private static Logger logger = LoggerFactory.getLogger(CouponService.class);
    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;

    @Autowired
    IFmbxSkuService skuService;

    @Autowired
    IFmbTemplateService templateService;

    @Autowired
    IFmbxSuiteRoomdatePriceService suiteRoomdatePriceService;

    @Autowired
    IFmbCouponUnableActivityService couponUnableActivityService;

    @Autowired
    FmbUserCouponDao userCouponDao;

    @Autowired
    IFmbUserCouponService userCouponService;

    @Autowired
    IFmbxActivityService activityService;

    @Autowired
    IFmbCouponForbiddenService couponForbiddenService;

    @Autowired
    IFmbGoodsCategoryService goodsCategoryService;

    @Autowired
    IFmbCouponTypeService couponTypeService;

    @Autowired
    OrderService orderService;

    @Autowired
    CouponService self;

    @Autowired
    SuiteService suiteService;

    public void getCouponBySku1(HashMap<String, Object> resultMap, @Valid ReqConfirmOrder input, NowUser uidInfo) {
        Date date = new Date();
        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        String toDayDate = sdf.format(date);
        Long toDayDateNum = System.currentTimeMillis();
        int uid = uidInfo.getUserId();
        orderService.chooseBestCoupon(uid, input, resultMap, "userChooseCoupon");
        //获取到优惠券列表 处理显示数据
        List<FrontCouponDomain> couponList = (List<FrontCouponDomain>) resultMap.get("couponList");
        Map<String,Object> zhekouquanList = new HashMap<>();
        Iterator<FrontCouponDomain> iterator = couponList.iterator();
        while (iterator.hasNext()) {
            FrontCouponDomain item = iterator.next();
            item.setNumber(1);
            item.setSelected(0);
            item.setCateName("");
            item.setIsUsed(item.isCanUse() == true ? 1 :0);
            if (item.getCouponType() == 4  &&  item.getUseStatus() == 0) {
                String key = "_" + item.getCouponId()+"_"+item.getLimitType()+"_"+item.getLimitCate()+"_"+item.getDiscountRate()+"_"+item.getMinAmount()+"_"+item.getCouponEndTime();
                if(zhekouquanList.containsKey(key)){
                    Map temp = new HashMap();
                    Map temp1 = (Map) zhekouquanList.get(key);
                    temp.put("number" ,(Integer)temp1.get("number") + 1);
                    zhekouquanList.put(key,temp);
                    iterator.remove();
                }else{
                    Map temp = new HashMap();
                    temp.put("number",1);
                    zhekouquanList.put(key,temp);
                }
            }
        }
        for (FrontCouponDomain item : couponList) {
            //折扣券 数量处理
            String key = "_" + item.getCouponId()+"_"+item.getLimitType()+"_"+item.getLimitCate()+"_"+item.getDiscountRate()+"_"+item.getMinAmount()+"_"+item.getCouponEndTime();
            if(zhekouquanList.containsKey(key)){
                Map temp = (Map) zhekouquanList.get(key);
                item.setNumber((Integer) temp.get("number"));
            }
            //限品类
            if(!"".equals(item.getLimitCate()) && (2 == item.getLimitType())) {
                final ArrayList<Integer> cateIdList = new ArrayList<>();
                MixedArray limit_cate = Pherialize.unserialize((String) item.getLimitCate()).toArray();
                ArrayList<Integer> cateIdsArr = new ArrayList<Integer>();
                Integer parentCate = 0, childCate = 0;
                for (int j = 0; j < limit_cate.size(); j++) {
                    Integer cateId = Integer.parseInt(String.valueOf(((Mixed) limit_cate.get(j)).getValue()));
                    FmbGoodsCategory cateData = queryGoodsCate(cateId);
                    cateIdList.add(cateId);
                    if (cateData.getCateId().equals(cateData.getArrchildid())) {
                        cateIdsArr.add(cateData.getCateId());
                        parentCate = cateData.getParentid();
//                        childCate = cateData.getCateId();
                    } else {
                        String[] tmp = cateData.getArrchildid().split(",");
                        for (String s : tmp) {
                            cateIdsArr.add(Integer.valueOf(s));
                        }
                        parentCate = cateData.getCateId();
                    }
                }
                FmbGoodsCategory parentCateData = queryGoodsCate(parentCate);
                String parentCateName = "";
                if (parentCateData.getCateName().contains("旅行")) {
                    parentCateName = "旅行";
                } else if (parentCateData.getCateName().contains("活动") || parentCateData.getCateName().contains("亲子生活")) {
                    parentCateName = "活动";
                } else if (parentCateData.getCateName().contains("演出")) {
                    parentCateName = "演出";
                } else if (parentCateData.getCateName().contains("购物") || parentCateData.getCateName().contains("邦你淘")) {
                    parentCateName = "购物";
                } else if (parentCateData.getCateName().contains("长线游")) {
                    parentCateName = "长线游";
                }
                item.setParentCateName(parentCateName);
                item.setParentCateId(parentCate);
                //查询分类名称
                String cateStr = "";
                List<FmbGoodsCategory> goodsCateArr = getGoodsCateByArr(cateIdList);
                for (FmbGoodsCategory cateItem : goodsCateArr) {
                    cateStr += cateItem.getCateName() + "、";
                }
                cateStr = cateStr.length() > 0 ? cateStr.substring(0, cateStr.length() - 1) : cateStr;
                if (cateStr.length() > 0) {
                    item.setCateName("限" + cateStr + "类商品使用");
                }
            }
            FmbxActivity activity =  suiteService.queryFmbxActivity(input.getXaid());
            //限活动
            if(item.getActivityId() > 0 && !activity.getTitle().equals("")) {
                item.setActivityTitle("限“"+activity.getTitle()+"”使用");
            }else{
                item.setActivityTitle("");
            }
            if(item.getActivityId() > 0){
                if(Arrays.asList(1,2,3,5,6).contains(activity.getTicketType())){
                    item.setTicketType(1);
                }else{
                    if(4 == activity.getTicketType()){
                        item.setTicketType(4);
                    }
                }
            }

            //显示状态处理
            item.setIsKdq(1);
            item.setCouponShowStatus("即将过期");
            if(item.getCouponEndTime().getTime() < toDayDateNum){
                item.setIsKdq(2);
                item.setCouponShowStatus("已过期");
            }else{
                if(item.getCouponEndTime().getTime() - (5 * 24 * 60 * 60 * 1000) > toDayDateNum ){
                    item.setIsKdq(0);
                    item.setCouponShowStatus("");
                }
            }
            if(item.getDistributeTime()!=null && !item.getDistributeTime().equals(FmbConstants.DEFAULT_DATETIME)  && item.getDistributeTime().getTime() > (toDayDateNum - (24 * 60 * 60 * 1000))){
                item.setIsKdq(3);
                item.setCouponShowStatus("新入账");
            }
            if(3 == item.getUseStatus()){
                item.setIsKdq(4);
                item.setCouponShowStatus("已冻结");
            }
            if(1 == item.getUseStatus()){
                item.setIsKdq(4);
                item.setCouponShowStatus("使用中");
            }
            switch (item.getPlatId()){
                case 0:
                    item.setPlatIdStr(" ");
                    break;
                case 1:
                    item.setPlatIdStr("限父母邦PC网站使用");
                    break;
                case 2:
                    item.setPlatIdStr("限父母邦APP使用");
                    break;
                case 3:
                    item.setPlatIdStr("限父母邦微信平台使用");
                    break;
                case 4:
                    item.setPlatIdStr("限父母邦APP、父母邦微信平台使用");
                    break;
                case 5:
                    item.setPlatIdStr("限微信小程序使用");
                    break;
            }

           switch (item.getCouponType()){
                case 1:
                    item.setCouponTypeCn("代金券");
                    break;
                case 2:
                    item.setCouponTypeCn("满减券");
                    break;
                case 3:
                    item.setCouponTypeCn("叠加券");
                    break;
                case 4:
                    item.setCouponTypeCn("折扣券");
                    break;
            }

            item.setMinAmountStr(item.getMinAmount().toString());
            if(item.getMinAmount() > 0){
                item.setMinAmountStr("满"+item.getMinAmount()+"可用");
            }else{
                item.setMinAmountStr("不限金额");
            }
            item.setStartTime(FmbDateUtil.toDate10Str(item.getCouponStartTime()));
            item.setEndTime(FmbDateUtil.toDate10Str(item.getCouponEndTime()));
            Map<String, ArrayList<Integer>> couponBlackWhiteAidListMaps = getCouponBlackWhiteAidList(item.getCouponId());
//            System.out.println(couponBlackWhiteAidListMaps);
            ArrayList<Integer> black_list = couponBlackWhiteAidListMaps.get("black_list");
            if(null != black_list && black_list.size() > 0 ){
//                item.setMinAmountStr(item.getMinAmountStr() + "(部分商品除外)");
                item.setCateName(item.getCateName() + "(部分商品除外)");
            }
        }

        couponList = couponList.stream().sorted(Comparator.comparing(FrontCouponDomain::isCanUse,Comparator.reverseOrder())
                .thenComparing(FrontCouponDomain::getDiscountMoney,Comparator.reverseOrder())
        ).collect(Collectors.toList());

//        Optional<FrontCouponDomain> first = couponList.stream().filter(FrontCouponDomain::isCanUse).sorted((a, b) -> {
//            return b.getDiscountMoney().compareTo(a.getDiscountMoney());
//        }).findFirst();


        double selected_coupon_money = 0;
        Map selectCoupon = new HashMap();
        selectCoupon.put("coupon_type",0);
        //默认选择优惠券
        Map defaultCouponSelect = new HashMap();
        for (int i = 0; i < couponList.size(); i++) {
            FrontCouponDomain item = couponList.get(i);
            //由于折扣券在普通券的前面，故先选一张折扣券，再跟后面的普通券比较价格
            //遇到第一张普通券时候，要比较价格，那个大选哪个
            if(
                    (4 == item.getCouponType()
                            && (0== selected_coupon_money
                            || Arrays.asList(1,2).contains(selectCoupon.get("coupon_type"))))
                            ||
                            (Arrays.asList(1,2).contains(item.getCouponType())
                                    &&
                                    (0== selected_coupon_money
                                            ||
                                            4 == (Integer) selectCoupon.get("coupon_type")))
            ){
                if( (0==selected_coupon_money) || ( selectCoupon.containsKey("key") && selected_coupon_money < item.getCouponMoney () ) ){

                    if(input.getSetDefault() == 1){
                        if(selectCoupon.containsKey("key")){
                            couponList.get((Integer) selectCoupon.get("key")).setSelected(0);
                            couponList.get((Integer) selectCoupon.get("key")).setIsUsed((Integer) selectCoupon.get("is_used"));
                        }
                        selected_coupon_money = Double.parseDouble((item.getCouponMoney().toString()));
                        item.setSelected(1);
                        selectCoupon.put("key",i);
                        selectCoupon.put("coupon_type",item.getCouponType());
                        defaultCouponSelect.put("total_coupon_money",selected_coupon_money);
                        defaultCouponSelect.put("coupon_sns",item.getCouponSn());
                        defaultCouponSelect.put("coupon_num",1);
                        selectCoupon.put("is_used",item.getIsUsed());
                        item.setIsUsed(2);
                    }else{
                        //选中用户选中的优惠券
                        if(input.getSetDefault() == 0 && null!=input.getCouponStr() && input.getCouponStr().length() > 0){
                            //通过码 查询出 coupond_id 防止折扣券 码不同问题
                            Map fmbUserCoupon = getUserCouponByCode(input.getCouponStr(),uid);
                            if((fmbUserCoupon.get("coupon_id") == item.getCouponId() && (Integer) fmbUserCoupon.get("coupon_type") == 4)  || input.getCouponStr().equals(item.getCouponSn())){
                                selected_coupon_money = Double.parseDouble((item.getCouponMoney().toString()));
                                item.setSelected(1);
                                selectCoupon.put("key",i);
                                selectCoupon.put("coupon_type",item.getCouponType());
                                defaultCouponSelect.put("total_coupon_money",selected_coupon_money);
                                defaultCouponSelect.put("coupon_sns",item.getCouponSn());
                                defaultCouponSelect.put("coupon_num",1);
                                selectCoupon.put("is_used",item.getIsUsed());
                                item.setIsUsed(2);
                            }
                        }
                    }
                }else{
                    selectCoupon.clear();
                    selectCoupon.put("coupon_type",0);
                }
            }
        }
        resultMap.put("couponList",couponList);
}



    private Map getUserCouponByCode(String code, Integer uid){
        Wrapper<FmbUserCoupon> wr = new LambdaQueryWrapper<FmbUserCoupon>()
                .eq(FmbUserCoupon::getUid,uid)
                .eq(FmbUserCoupon::getCouponSn,code)
                .select(
                        FmbUserCoupon::getUid,
                        FmbUserCoupon::getCouponId,
                        FmbUserCoupon::getCouponSn,
                        FmbUserCoupon::getCouponMoney
                );
        Map userCoupon = userCouponService.getMap(wr);
        Wrapper<FmbCouponType> wr1 = new LambdaQueryWrapper<FmbCouponType>()
                .eq(FmbCouponType::getCouponId,userCoupon.get("coupon_id"))
                .select(
                        FmbCouponType::getCouponId,
                        FmbCouponType::getCouponType
                );
        FmbCouponType couponType = couponTypeService.getOne(wr1);
        userCoupon.put("coupon_type",couponType.getCouponType());
        return userCoupon;
    }


    private String getCouponeStatusString(Integer status){
        switch (status){
            case 0:
                return  "未使用";
            case 1:
                return  "使用中";
            case 2:
                return  "已使用";
            case 3:
                return  "已冻结";
            default:
                return  "未使用";
        }
    }

    private String getCouponRealName(Integer type){
        switch (type){
            case 1:
                return  "代金券";
            case 2:
                return  "满减券";
            case 3:
                return  "叠加券";
            case 4:
                return  "折扣券";
            default:
                return  "代金券";
        }
    }

    /**
     * 获取分类信息
     * @param cateId
     * @return
     */
    public FmbGoodsCategory queryGoodsCate(Integer cateId){
        Wrapper<FmbGoodsCategory> wr = new LambdaQueryWrapper<FmbGoodsCategory>()
                .eq(FmbGoodsCategory::getCateId, cateId)
                .select(
                        FmbGoodsCategory::getArrchildid,
                        FmbGoodsCategory::getCateId,
                        FmbGoodsCategory::getParentid,
                        FmbGoodsCategory::getCateName
                );
        FmbGoodsCategory parentCateData = goodsCategoryService.getOne(wr);
        return parentCateData;
    }

    /**
     * 获取分类信息 in()  通过int数组
     * @param cateIdList
     * @param cateIdList
     * @return
     */
    private List<FmbGoodsCategory> getGoodsCateByArr(ArrayList<Integer> cateIdList){
        Wrapper<FmbGoodsCategory> wr = new LambdaQueryWrapper<FmbGoodsCategory>()
                .in(FmbGoodsCategory::getCateId, cateIdList)
                .select(
                        FmbGoodsCategory::getArrchildid,
                        FmbGoodsCategory::getCateId,
                        FmbGoodsCategory::getParentid,
                        FmbGoodsCategory::getCateName
                );
        List<FmbGoodsCategory> parentCateData = goodsCategoryService.list(wr);
        return parentCateData;
    }

    /**
     * 获取优惠券黑白名单
     * @return
     */
    public Map<String, ArrayList<Integer>> getCouponBlackWhiteAidList(long couponId){
        Wrapper<FmbCouponForbidden> wr = new LambdaQueryWrapper<FmbCouponForbidden>()
                .eq(FmbCouponForbidden::getCouponId, couponId)
                .select(
                        FmbCouponForbidden::getBanType,
                        FmbCouponForbidden::getCouponId,
                        FmbCouponForbidden::getAid,
                        FmbCouponForbidden::getCityId
                );
        List<FmbCouponForbidden> maps = couponForbiddenService.list(wr);

        ArrayList<Integer> white_list = new ArrayList<>();
        ArrayList<Integer> black_list = new ArrayList<>();


        for (FmbCouponForbidden v : maps) {
            if( 1 == v.getBanType()){
                white_list.add(v.getAid());
            }else if (2 == v.getBanType()){
                black_list.add(v.getAid());
            }
        }
        //排除白名单里的黑名单产品
        if(!black_list.isEmpty() && !white_list.isEmpty()){
            Iterator<Integer> iterator = white_list.iterator();
            while (iterator.hasNext()) {
                if(black_list.contains(iterator.next())){
                    iterator.remove();
                }
            }
        }
        Map<String, ArrayList<Integer>> objectObjectHashMap = new HashMap<>();

        objectObjectHashMap.put("black_list",black_list);
        objectObjectHashMap.put("white_list",white_list);
        return objectObjectHashMap;
    }

    /**
     * 获取活动信息
     * @param aid
     * @return
     */
//    private FmbxActivity getActivityInfo(Integer aid){
//        Wrapper<FmbxActivity> wr = new LambdaQueryWrapper<FmbxActivity>()
//                .eq(FmbxActivity::getXaid, aid)
//                .select(
//                        FmbxActivity::getTitle,
//                        FmbxActivity::getCategoryId,
//                        FmbxActivity::getTicketType
//                );
//        FmbxActivity activity = activityService.getOne(wr);
//        return  activity;
//    }

    /**
     * 获取黑名单活动aid list
     * @return
     */
    private List<Integer> getCouponBlackAidList(){
        Wrapper<FmbCouponUnableActivity> wr = new LambdaQueryWrapper<FmbCouponUnableActivity>()
                .select(
                        FmbCouponUnableActivity::getAid
                );
        List<Map<String, Object>> data = couponUnableActivityService.listMaps(wr);
        List<Integer> aidList=new ArrayList<Integer>();
        for(Map item : data){
            aidList.add((Integer) item.get("aid"));
        }
        return aidList;
    }


    /**
     * 获取年卡 aids
     * @return
     */
    private List<Integer> getNewAllYearCardAids(){
        //cityIds = 1,2,3 //查全国、北京、上海的所有年卡活动
        Wrapper<FmbTemplate> wr = new LambdaQueryWrapper<FmbTemplate>()
                .eq(FmbTemplate::getSectionName,"year_card")
                .eq(FmbTemplate::getModuleName, "new_activity_config")
                .in(FmbTemplate::getCityId, new Integer[]{1, 2, 3})
                .select(
                        FmbTemplate::getContent,
                        FmbTemplate::getSectionName,
                        FmbTemplate::getModuleName,
                        FmbTemplate::getCityId
                );
        List<Map<String, Object>> data = templateService.listMaps(wr);

        Date date = new Date();
        String strDateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
        String toDayDate = sdf.format(date);
        Long toDayDateNum = System.currentTimeMillis();
        List<Integer> aidList=new ArrayList<Integer>();
        //循环处理数据
        for (Map item : data ){
            //反序列化数据
            MixedArray content = Pherialize.unserialize((String) item.get("content")).toArray();
            for (int i = 0; i < content.size(); i++) {
                MixedArray array = (MixedArray) ((Mixed)content.get(i)).getValue();
                Integer aid = Integer.parseInt(String.valueOf(array.get("aid")));
                String  prepare = String.valueOf(array.get("prepare"));
                //判断预发布是否存在
                if( prepare != null && !"".equals(prepare)){
                    //获取当前时间比较大小
                    try {
                        //如果没有到预发布时间 则不计算
                        if(toDayDateNum < (sdf.parse(prepare)).getTime()){
                            continue;
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                //不存在再添加
                if(!aidList.contains(aid)){
                    aidList.add(aid);
                }
            }
        }
        return aidList;
    }



    /**
     * 计算日历房
     * 每个日期的房价格的和 * 数量
     * @return
     */
    private HashMap<String, Double> countCaleOrderMoneyBySkuAndNum(Integer skuId, String checkInDate, Integer num){
        String[] date = checkInDate.split(",");
        date[1] = DateUtil.formatDate(DateUtil.parse(String.valueOf(DateUtil.offset(DateUtil.parse(date[1]), DateField.DAY_OF_MONTH, -1))));
        //查询房价
        HashMap<String, Double> resultMap = new HashMap<>();
        Wrapper<FmbxSuiteRoomdatePrice> wr = new LambdaQueryWrapper<FmbxSuiteRoomdatePrice>()
                .eq(FmbxSuiteRoomdatePrice::getSuiteRoomSkuId,skuId)
                .between(FmbxSuiteRoomdatePrice::getTargetDate, date[0],date[1])
                .select(
                        FmbxSuiteRoomdatePrice::getGoodsPrice,
                        FmbxSuiteRoomdatePrice::getMarketPrice,
                        FmbxSuiteRoomdatePrice::getSettlePrice,
                        FmbxSuiteRoomdatePrice::getTargetDate,
                        FmbxSuiteRoomdatePrice::getSuiteRoomSkuId
                );
        List<FmbxSuiteRoomdatePrice> suiteRoomdatePriceList =  suiteRoomdatePriceService.list(wr);
        double marketPrice = 0, goodsPrice = 0, settlePrice = 0;
        goodsPrice = (suiteRoomdatePriceList.stream().map(FmbxSuiteRoomdatePrice::getGoodsPrice).reduce(BigDecimal.ZERO,BigDecimal::add)).doubleValue();
        marketPrice = (suiteRoomdatePriceList.stream().map(FmbxSuiteRoomdatePrice::getMarketPrice).reduce(BigDecimal.ZERO,BigDecimal::add)).doubleValue();
        settlePrice = (suiteRoomdatePriceList.stream().map(FmbxSuiteRoomdatePrice::getSettlePrice).reduce(BigDecimal.ZERO,BigDecimal::add)).doubleValue();
        resultMap.put("marketPrice",marketPrice);
        resultMap.put("goodsPrice",goodsPrice);
        resultMap.put("settlePrice",settlePrice);
        return  resultMap;
    }


    /**
     * 计算房券
     * 房价 * 数量
     * @return
     */
    private HashMap<String, Double> countReseOrderMoneyBySkuAndNum(Integer skuId, Integer num){
        //查询房价
        Wrapper<FmbxHotelReserveSku> wr = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId,skuId)
                .select(
                        FmbxHotelReserveSku::getGoodsPrice,
                        FmbxHotelReserveSku::getMarketPrice,
                        FmbxHotelReserveSku::getSettlePrice,
                        FmbxHotelReserveSku::getSkuName
                );
        FmbxHotelReserveSku hotelReserveSku =  reserveSkuService.getOne(wr);
        //计算 原价 现价 结算价
        double marketPrice  = 0, goodsPrice  = 0, settlePrice = 0;
        marketPrice = hotelReserveSku.getMarketPrice().doubleValue() * num;
        goodsPrice  = hotelReserveSku.getGoodsPrice().doubleValue() * num;
        settlePrice = hotelReserveSku.getSettlePrice().doubleValue() * num;
        HashMap<String, Double> resultMap = new HashMap<>();
        resultMap.put("marketPrice",marketPrice);
        resultMap.put("goodsPrice",goodsPrice);
        resultMap.put("settlePrice",settlePrice);
        return  resultMap;
    }


    public void getCouponByAid(HashMap<String, Object> resultMap, ReqCoupon input, NowUser uidInfo) {
        int uid = 0;
        if(null != uidInfo){
            uid = uidInfo.getUserId();
        }
        final Integer xaid = input.getXaid();
        FmbxActivity activityInfo = suiteService.queryFmbxActivity(xaid);
        //查询是否有可领取的优惠券
        List<FmbCouponType> list = userCouponDao.getCouponList();
        Iterator<FmbCouponType> iterator = list.iterator();
        while (iterator.hasNext()) {
            FmbCouponType item = iterator.next();
            item.setStartTimeDate(FmbDateUtil.localDateTime2String10(item.getStartTime()));
            item.setEndTimeDate( FmbDateUtil.localDateTime2String10(item.getEndTime()));
            //限制活动
            if(1 == item.getLimitType()){
                if(!item.getActivityId().equals(xaid)){
                    iterator.remove();
                    continue;
                }
                item.setCateName(activityInfo.getTitle());
                item.setDesc(activityInfo.getTitle());
            }
            //限制品类
            final Integer couponId = item.getCouponId();
            if(2 ==item.getLimitType()){
                MixedArray limit_cate = Pherialize.unserialize((String) item.getLimitCate()).toArray();
                ArrayList<Integer> cateIdsArr = new ArrayList<Integer>();
                ArrayList<Integer> cateIdList = new ArrayList<>();
                for (int j = 0; j < limit_cate.size(); j++) {
                    Integer cateId = Integer.parseInt(String.valueOf(((Mixed) limit_cate.get(j)).getValue()));
                    FmbGoodsCategory cateData = queryGoodsCate(cateId);
                    cateIdList.add(cateId);
                    if (cateData.getCateId().equals(cateData.getArrchildid())) {
                        cateIdsArr.add(cateData.getCateId());
                    } else {
                        String[] tmp = cateData.getArrchildid().split(",");
                        for (String s : tmp) {
                            cateIdsArr.add(Integer.valueOf(s));
                        }
                    }
                }
                if(cateIdsArr.size() > 0){
                    if(!cateIdsArr.contains(activityInfo.getCategoryId())){
                        iterator.remove();
                        continue;
                    }
                }else{
                    iterator.remove();
                    continue;
                }

                //券组黑白名单
                boolean notValid = isNotValidCoupon(xaid, couponId);
                if(notValid){
                    iterator.remove();
                    continue;
                }
                String cateStr = "";
                List<FmbGoodsCategory> goodsCateArr = getGoodsCateByArr(cateIdList);
                for (FmbGoodsCategory cateItem : goodsCateArr) {
                    cateStr += cateItem.getCateName() + "、";
                }
                cateStr = cateStr.length() > 0 ? cateStr.substring(0, cateStr.length() - 1) : cateStr;
                if (cateStr.length() > 0) {
                    item.setCateName("限" + cateStr + "类商品使用");
                    item.setDesc( cateStr + "类商品使用");
                }
            }
            //查询是否还有未发放的优惠券
            Integer count = userCouponDao.getCountNotUseCoupon(couponId);
            if(count == 0){
                iterator.remove();
                continue;
            }
            item.setIsDrawCoupon(0);
            if(0 < uid){
                Wrapper<FmbUserCoupon> wr4 = new LambdaQueryWrapper<FmbUserCoupon>()
                        .eq(FmbUserCoupon::getCouponId, couponId)
                        .eq(FmbUserCoupon::getUid, uid)
                        .notIn(FmbUserCoupon::getUseStatus,2)
                        .select(
                                FmbUserCoupon::getCouponId
                        );
                List<FmbUserCoupon> maps = userCouponService.list(wr4);
                if(maps.size() > 0){
                    item.setIsDrawCoupon(1);
                }
            }
            if(4 == item.getCouponType() && item.getDiscountRate() > 0){
                BigDecimal bigDecimal = new BigDecimal(item.getDiscountRate() / 10.0);
                BigDecimal bigDecimal1 = bigDecimal.stripTrailingZeros();
                item.setDiscountRateDesc(bigDecimal1.toPlainString() + "折");
            }
        }
        resultMap.put("couponType",list);

    }

    public boolean isNotValidCoupon(Integer xaid, Integer couponId) {
        Map<String, ArrayList<Integer>> couponBlackWhiteAidListMaps = getCouponBlackWhiteAidList(couponId);
        ArrayList<Integer> white_list =  couponBlackWhiteAidListMaps.get("white_list");
        ArrayList<Integer> black_list = couponBlackWhiteAidListMaps.get("black_list");

        if (logger.isInfoEnabled()) {
            logger.info("aid={},couponid={} white={},black={}",xaid,couponId,JSON.toJSON(white_list),JSON.toJSON(black_list));
        }

        boolean notValid = (white_list.size() > 0 && !white_list.contains(xaid)) || (black_list.size() > 0 && black_list.contains(xaid));
        return notValid;
    }
}
