package com.fmb.server2022.service.schedule.impel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_SKU_SELL_END;

@Service(value = JOB_NAME_SKU_SELL_END)
public class SkuSellEnd implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(SkuSellEnd.class);

    @Autowired
    OrderService orderService ;

    @Autowired
    SuiteService suiteService ;

    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {

            final JSONObject jsonObject = JSON.parseObject(job.getJobExtPars());
            if (2==jsonObject.getInteger("skutype").intValue()) {
                SuiteService.sendHotelSkuChangeNotify(job.getJobFmbxId());
            }

        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
