package com.fmb.server2022.service.order;

import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.domain.ConfirmOrderMoneyGroup;
import com.fmb.server2022.domain.ConfirmOrderMoneyInfo;
import com.fmb.server2022.domain.stock.SkuSellNumUpdate;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbxSku;

import java.util.List;
import java.util.Map;

public interface AbstractOrderservice {

    /**
     * 获取skutype
     * @return
     */
    public int getSkuType() ;


    public int skuType2OldGoodsType() ;

    /**
     * 下单收尾操作
     * @param input
     * @param skuDetail
     */
    public void doFinalJobWhenCreateOrderOver(ReqGenOrder input, SkuDetail skuDetail) ;

    /**
     * 扣减库存
     * @param input
     * @param nowUser
     * @param skuDetail
     */
    void subStock(ReqGenOrder input, NowUser nowUser, SkuDetail skuDetail);

    /**
     * 生成订单goods
     * @param nowUser
     * @param orderInfo
     * @param skuDetail
     * @param input
     */
    void saveOrderGoods(NowUser nowUser, FmbOrderInfo orderInfo, SkuDetail skuDetail, ReqGenOrder input);

    /**
     * 把用户输入的sku 信息 转换为具体的sku 信息
     * @param input
     * @param reqSkuMaps
     * @param skuMainOfDBMap
     * @param skuMain
     */
    void buildSkuDetail(ReqGenOrder input, Map<String, List<ReqGenSku>> reqSkuMaps, Map<String, List<FmbxSku>> skuMainOfDBMap, FmbxSku skuMain);

    /**
     * 基础sku 校验
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    void baseCheck(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input);

    /**
     * 计算该sku类型的价格
     * @param skuDetail
     * @param input
     */
    void buildPrice(SkuDetail skuDetail, ReqGenOrder input);

    /**
     * 用户支付成功后 创建用户的权益资产
     * 包括 电子码 等各种形式的资产
     *
     * 该操作是基于 FmbOrderGoods 层面的
     * @param orderGood
     * @param fmbOrderInfo
     * @return
     */
    CreateUserAssetResult createUserAsset(FmbOrderGoods orderGood, FmbOrderInfo fmbOrderInfo);

    /**
     * 查询该sku的 自动关闭秒数
     * @param skuDetail
     * @return
     */
    int getCancelSecond(SkuDetail skuDetail);

    void cancelUserGoods(FmbOrderGoods orderGood, FmbOrderInfo fmbOrderInfo);


    /**
     * 使用优惠券 模拟计算价格
     * @param fmbxSku
     * @param genSkus
     * @param confirmOrder
     * @param confirmOrderMoneyInfo
     * @param hotelReserveCode
     * @return
     */
    ConfirmOrderMoneyGroup buildPriceWhenUseCoupon(FmbxSku fmbxSku, ReqGenSku genSkus, String confirmOrder, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, String hotelReserveCode);

    /**
     * 从订单 goods 里反向解析数据 , 类似快照数据提取
     * @param goods
     * @param confirmOrderMoneyInfo
     * @param orderInfo
     * @return
     */
    OrderDetailGoodsInfo readGoodsInfoFromGoodsExt(FmbOrderGoods goods, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, FmbOrderInfo orderInfo);

    /**
     * 更新sku 购买数量 , 有可能 增加 也有可能减少
     * @param s
     */
    void updateSkuSellNum(SkuSellNumUpdate s);
}
