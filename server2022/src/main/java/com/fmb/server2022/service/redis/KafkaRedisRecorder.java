package com.fmb.server2022.service.redis;

import com.fmb.basic.FmbConstants;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Tuple;

import java.util.Set;

@Component
public class KafkaRedisRecorder {

    private static Logger logger = LoggerFactory.getLogger(KafkaRedisRecorder.class);

    public static final String KAFKAJOB = "kafkajob";
    private  static JedisPool jedisPool ;

    public static JedisPool getJedisPool() {
        return jedisPool;
    }

    @Autowired
    public  void setJedisPool(JedisPool jedisPool) {
        KafkaRedisRecorder.jedisPool = jedisPool;
    }

    public static void recordJob(String topic,String val){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            jedis.zadd(KAFKAJOB,System.currentTimeMillis(),val) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }


    public static void delJob(String val){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            jedis.zrem(KAFKAJOB,val) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }


    public static void checkNotFinishJob(){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);


//            jedis.zrange(KAFKAJOB,0,10) ;
//            final Set<Tuple> tuples = jedis.zrangeByScoreWithScores(KAFKAJOB, 0, 20);

            final Set<String> strings = jedis.zrangeByScore(KAFKAJOB, 0, System.currentTimeMillis());
            final Set<Tuple> tuples = jedis.zrangeByScoreWithScores(KAFKAJOB, 0, System.currentTimeMillis() - 3 * 60 * 1000);

//            strings.stream().forEach(s -> {
//                System.out.println(s);
//            });

            tuples.stream().forEach(tuple -> {
                System.out.println("v->"+tuple.getElement());
                System.out.println((int)(tuple.getScore()-1667232000000D));
            });

            System.out.println(Integer.MAX_VALUE);


        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

}
