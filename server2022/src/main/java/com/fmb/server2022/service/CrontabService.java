package com.fmb.server2022.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.service.order.OrderStatService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.fmb.basic.FmbConstants.ORDERSN_STAT;

@Component
@Profile({"dev","pro"})
public class CrontabService {

    @Autowired
    BpsService bpsService ;

    @Autowired
    OrderStatService statService ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    FrontHotelService frontHotelService ;

    @Autowired
    FrontSkuDao frontSkuDao ;

    @Autowired
    SuiteService suiteService ;


    @Autowired
    DingDingApproveService dingApproveService ;


    private static Logger logger = LoggerFactory.getLogger(CrontabService.class);
    // set 方法直接就能实现相关脚本
//    public static String script = " local num=redis.call('setnx',KEYS[1],'') " +
//            "if tonumber(num)==1 then " +
//            "    redis.call('EXPIRE',KEYS[1],ARGV[1]) " +
//            "    return 1 " +
//            "else " +
//            "    return 0" +
//            " end";

    @Autowired
    JedisPool jedisPool ;



    /**
     *  凌晨定时检查 1个月要到期的合同
     */
    @Scheduled(cron = "0 12 4 * * *")
    public void bpContractCheck(){
        if (getRunLock("CrontabService","bpContractCheck",60)) {
            bpsService.checkContractWillEnd() ;
        }

    }


    /**
     *  凌晨定时检查 到期的合同
     */
    @Scheduled(cron = "0 15 4 * * *")
    public void bpContractCheckEnd(){
        if (getRunLock("CrontabService","bpContractCheckEnd",60)) {
            bpsService.checkContractEnd();
        }

    }


    /**
     *  凌晨定时更新 redis 订单随机值
     */
    @Scheduled(cron = "0 13 4 * * *")
    public void randomVal(){
        if (getRunLock("CrontabService","randomVal",60)) {

            Jedis jedis = null;

            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);
                final String value = RandomUtils.nextInt(100001, 899999) + "";
                jedis.set(FmbConstants.REDIS_RANDOM, value);

                logger.info("randomValSet={}",value);

            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }

        }

    }


    /**
     * 定时任务如果部署到多个机器,会出现同时执行问题.使用redis作为裁判,谁抢到谁执行
     * @param className   类名
     * @param methord   方法名
     * @param second 多长时间过期
     * @return 是否抢到锁
     */
    public boolean getRunLock(String className,String methord ,int second) {

        String key = "Scheduled:" + className + ":" + methord;
        return getRunLock(key,second) ;
    }


    /**
     *
     * @param key
     * @param second
     * @return
     */
    public boolean getRunLock(  String key,int second) {
        return getRunLockMillions(key,second*1000L) ;
    }


    /**
     *
     * @param key
     * @param millisecondsToExpire 毫秒数
     * @return
     */
    public boolean getRunLockMillions(  String key,long millisecondsToExpire) {

        boolean flag = false ;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String eval = jedis.set(key, "", new SetParams().nx().px(millisecondsToExpire));
            flag = "OK".equals(eval) ;
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return flag ;
    }


    /**
     *  批量执行活动信息刷新
     */
    @Scheduled(cron = "15 */20 * * * *")
    public void hotelActivityLotRun(){
        if (getRunLock("CrontabService","hotelActivityLotRun",60)) {
            statService.syncActInfoLot();
        }
    }


    /**
     * *****************************************************************
     *  对所有的  酒店sku 定时进行检查
    * *****************************************************************
     **/
    @Scheduled(cron = "23 */15 * * * *")
    public void touchAllHotelSkuOfSellFlagOK(){

        if (getRunLock("CrontabService","touchAllHotelSku",60)) {

            logger.info("touchAllHotelSku run begin");
            Wrapper<FmbxActivity> wrA = new LambdaQueryWrapper<FmbxActivity>()
                    .eq(FmbxActivity::getFlagPublish,1)
                    .eq(FmbxActivity::getFlagDelete,0)
                    .eq(FmbxActivity::getTicketType,6)
                    .select(FmbxActivity::getXaid)
                    ;
            final List<FmbxActivity> listAct = activityService.list(wrA);

            for (FmbxActivity activity : listAct) {
                final Integer xaid = activity.getXaid();
              suiteService.touchActivity(xaid);

            }

            logger.info("touchAllHotelSku run end");
        }

    }



    /**
     *  批量执行酒店活动 日历价格 sku信息更新(酒店频道页里需要根据入离时间查询酒店信息 ,fmbx_date_hotel_info 表 )
     */
    @Scheduled(cron = "40 */10 * * * *")
    @Profile({"dev","pro"})
    public void hotelActivityPriceUpdate(){
        if (getRunLock("CrontabService","hotelActivityPriceUpdate",60)) {
            logger.info("hotelActivityPriceUpdate_run");
            statService.syncHotelActPriceLot();
        }
    }


    /**
     * 钉钉 获取人员列表
     */
    @Scheduled(cron = "10 12 1 * * *")
    public void updateDingDingUsers(){
        if (FmbServerConfig.isPro() &&  getRunLock("CrontabService","updateDingDingUsers",60)) {
            logger.info("hotelActivityPriceUpdate_run");
            final int userInfo = dingApproveService.updateThirdUserInfo();
            logger.info("userInfoCount={}",userInfo);
        }
    }



    /**
     * 5分钟一次 统一处理要更新 订单统统计 数据的信息
     */
    @Scheduled(fixedRate =5*60*1000 )
    public void orderStatJob(){
        if (getRunLock("CrontabService","orderStatJob",30)) {

            Jedis jedis = null;

            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);

                final int len = jedis.llen(ORDERSN_STAT).intValue();

                final LinkedHashSet<String> orderSet = new LinkedHashSet<>();
                for (int i = 0; i < len; i++) {
                    orderSet.add(jedis.lpop(ORDERSN_STAT)) ;
                }

                if (!orderSet.isEmpty()) {
                    FmbScheduleUtil.doAsyncJob(()->{
                        statService.makeOrderStatData(orderSet) ;
                    });
                }


            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }
        }
    }


    /**
     ******************************************************************
     * 定期 清理 过期的fmb admin 登录token
     ******************************************************************
     **/
    @Scheduled(cron = "0 11 2 * * *")
    public void clearNotExistSetToken(){

        if (getRunLock("CrontabService","clearNotExistSetToken",30)) {

            Jedis jedis = null;

            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);

                List<String> retListAll = new ArrayList<>();
                ScanParams scanParams = new ScanParams();
                scanParams.count(10);

                String scanRet = "0";

                //遍历  user_tokens:
                do {
                    ScanResult ret = jedis.scan(scanRet, scanParams.match(FmbConstants.TOKENSETS_PRE + "*"));
                    scanRet = ret.getCursor();
                    retListAll.addAll(ret.getResult());

                } while (!scanRet.equals("0"));

                for (String key : retListAll) {
                    //如果是 set 类型
                    if ("set".equals(jedis.type(key))) {
                        //查询所有值
                        final Set<String> smembers = jedis.smembers(key);

                        final ArrayList<String> setDel = new ArrayList<>();
                        for (String smember : smembers) {
                            //找到过期的key
                            if (!jedis.exists(FmbConstants.TOKEN_PRE_OF_FMBADMIN + smember)) {
                                setDel.add(smember);
                            }
                        }

                        if (!setDel.isEmpty()) {
                            //批量删除过期的 set里的值
                            final Long srem = jedis.srem(key, setDel.toArray(new String[]{}));
                            logger.info("set key= {} ,delNum= {}", key, srem);
                        }

                    }
                }
            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }
        }
    }

    /**
     *  批量执行cpsscr访问量更新(fmb_cpssrc 表 )
     */
    @Scheduled(cron = "15 */15 * * * *")
    @Profile({"dev","pro"})
    public void cpssrcUpdate(){
        if (getRunLock("CrontabService","cpssrcUpdate",60)) {
            logger.info("cpssrcUpdate_run");
            frontHotelService.updateCpssrcViewcount();
        }
    }

    @Scheduled(cron = "0 0 4 2 * ?")
    @Profile({"dev","pro"})
    public void cpssrcClean(){
        if (getRunLock("CrontabService","cpssrcClean",60)) {
            logger.info("cpssrcClean_run");
            frontHotelService.cleanCpssrcViewcount();
        }
    }


    @Scheduled(cron = "0 0 1 1 1 ?")
    @Profile({"dev","pro"})
    public void genDataTableClean(){
        if (getRunLock("CrontabService","genDataTableClean",60)) {
            statService.saveDateInfo();
        }
    }

}
