package com.fmb.server2022.service;

import com.fmb.basic.FmbConstants;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

@Component
public class CrontabService {

    @Autowired
    BpsService bpsService ;


    private static Logger logger = LoggerFactory.getLogger(CrontabService.class);
    // set 方法直接就能实现相关脚本
//    public static String script = " local num=redis.call('setnx',KEYS[1],'') " +
//            "if tonumber(num)==1 then " +
//            "    redis.call('EXPIRE',KEYS[1],ARGV[1]) " +
//            "    return 1 " +
//            "else " +
//            "    return 0" +
//            " end";

    @Autowired
    JedisPool jedisPool ;

    // 定时任务执行样例
//    @Scheduled(cron = "0/45 * * * * *")
    public void test01(){
        if (getRunLock("CrontabService","test01",10)) {


        }

    }


    /**
     *  凌晨定时检查 1个月要到期的合同
     */
    @Scheduled(cron = "0 12 4 * * *")
    public void bpContractCheck(){
        if (getRunLock("CrontabService","bpContractCheck",60)) {
            bpsService.checkContractWillEnd() ;
        }

    }


    /**
     *  凌晨定时检查 到期的合同
     */
    @Scheduled(cron = "0 15 4 * * *")
    public void bpContractCheckEnd(){
        if (getRunLock("CrontabService","bpContractCheckEnd",60)) {
            bpsService.checkContractEnd();
        }

    }


    /**
     *  凌晨定时更新 redis 订单随机值
     */
    @Scheduled(cron = "0 13 4 * * *")
    public void randomVal(){
        if (getRunLock("CrontabService","randomVal",60)) {

            Jedis jedis = null;

            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);
                final String value = RandomUtils.nextInt(100001, 899999) + "";
                jedis.set(FmbConstants.REDIS_RANDOM, value);

                logger.info("randomValSet={}",value);

            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }

        }

    }


    /**
     * 定时任务如果部署到多个机器,会出现同时执行问题.使用redis作为裁判,谁抢到谁执行
     * @param className   类名
     * @param methord   方法名
     * @param second 多长时间过期
     * @return 是否抢到锁
     */
    public boolean getRunLock(String className,String methord ,int second) {

        String key = "Scheduled:" + className + ":" + methord;
        return getRunLock(key,second) ;
    }


    /**
     *
     * @param key
     * @param second
     * @return
     */
    public boolean getRunLock(  String key,int second) {
        return getRunLockMillions(key,second*1000L) ;
    }


    /**
     *
     * @param key
     * @param millisecondsToExpire 毫秒数
     * @return
     */
    public boolean getRunLockMillions(  String key,long millisecondsToExpire) {

        boolean flag = false ;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String eval = jedis.set(key, "", new SetParams().nx().px(millisecondsToExpire));
            flag = "OK".equals(eval) ;
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return flag ;
    }

}
