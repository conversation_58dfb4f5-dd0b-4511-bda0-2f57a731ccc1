package com.fmb.server2022.service.kafka;

public class FmbKafkaTopic {

    public static final String ADMIN_USER_CHNAGE =  "adminUserChange" ;


    //公用队列
    public static final String PUBLIC_GROUP =  "publicGroup" ;

    public static final String PUBLIC_GROUP_BP_NEED_CHECK =  "bp_need_check" ;
    public static final String PUBLIC_GROUP_BPS_FLAG_UPDATE =  "bps_flag_update" ;





    //kafka topic 开始
    public static final  String HOTEL_SKU_CHANGE = "HotelSkuChange" ;
    public static final  String AID_CHANGE = "AID_CHANGE" ;
    public static final  String HOTEL_SKU_DELETE = "HotelSkuDelete" ;
    public static final  String HOTEL_STOCK_CHANGE = "HotelStockChange" ;
    public static final  String ORDER_SUCCESS_PAY = "orderSuccessPay" ;
    //kafka topic 结束
}
