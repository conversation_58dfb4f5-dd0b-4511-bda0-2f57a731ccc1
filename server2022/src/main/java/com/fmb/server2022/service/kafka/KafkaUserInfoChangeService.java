package com.fmb.server2022.service.kafka;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.fmbx.entity.SysUser;
import com.fmb.server2022.fmbx.service.ISysUserService;
import com.fmb.server2022.service.SysTokenService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Optional;
import java.util.Set;

import static com.fmb.basic.FmbConstants.TOKENSETS_PRE;

@Service
public class KafkaUserInfoChangeService {

    private static Logger logger = LoggerFactory.getLogger(KafkaUserInfoChangeService.class);

    @Autowired
    SysTokenService tokenService ;

    @Autowired
    ISysUserService userService ;

    @Autowired
    JedisPool jedisPool ;

    @KafkaListener(id="userInfoRead", topics = {FmbKafkaTopic.ADMIN_USER_CHNAGE},groupId = "userInfoRead")
//    @KafkaListener(topics = {FmbKafkaTopic.ADMIN_USER_CHNAGE},groupId = "userInfoRead",containerFactory = "manualAck")
    public void userInfoRead(ConsumerRecord<String,String> record
//            , Acknowledgment acknowledgment
    ) {
        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = kafkaMessage.get();
            logger.info("record.offset={} ,  userInfoRead=[{}]",record.offset(),  message);

            JSONObject jsonObject = JSON.parseObject(message);

            Integer userId = jsonObject.getInteger("userId");
            SysUser dbUser = userService.getById(userId);

            if (dbUser==null) {
                logger.info("badUserID={}",userId);
//                acknowledgment.acknowledge();
                return;
            }

            SessionUserInfoOfFmbAdmin fromDB = null;
            try {
                fromDB = tokenService.getUserInfoByUsernameFromDB(dbUser.getUsername());


                if (fromDB != null) {


                    Jedis jedis = null;

                    try {
                        jedis = jedisPool.getResource();
                        jedis.select(FmbConstants.REDIS_DEFAULT_DB);

                        //读取所有的 用户token
                        Set<String> smembers = jedis.smembers(TOKENSETS_PRE + fromDB.getUserId());
                        for (String token : smembers) {
                            tokenService.writeUserInfo2Cache(token,fromDB);

                            //标记一下 用户要更新了 ,并保持8小时
                            jedis.setex(FmbConstants.TOKEN_NEED_UPDATE+token,8*60*60,  "") ;

                        }
                    } catch (Exception ex) {
                        long errorid = SnowflakeIdWorker.getInstance().nextId();
                        logger.error("errorid " + errorid, ex);
                    } finally {
                        if (jedis != null) {
                            jedis.close();
                        }
                    }

                }


            } catch (BadLogicException ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            }



        }

//        acknowledgment.acknowledge();
    }

}
