package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmb_new.entity.FmbAdminLog;
import com.fmb.server2022.fmb_new.mapper.FmbAdminLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class FmbNewService {

    @Autowired
    FmbAdminLogMapper adminLogMapper ;



    @DS(FmbConstants.DB_master)
    public FmbAdminLog addLog( ) {
        FmbAdminLog log = new FmbAdminLog();

        log.setAdminId(0);
        log.setAdminName("数据库启动测试");
        log.setLogTime(LocalDateTime.now());
        log.setLogText("服务启动测试");
        log.setLogIdentifier("FMBX_JAVA_SERVER_STAR");

        adminLogMapper.insert(log);

        return log;
    }

}
