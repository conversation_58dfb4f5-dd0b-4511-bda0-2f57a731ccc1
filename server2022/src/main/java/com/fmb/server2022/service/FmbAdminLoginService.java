package com.fmb.server2022.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.domain.FmbAdminUsers;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.mapper.FmbLoginDao;
import com.fmb.util.SnowflakeIdWorker;
import com.github.benmanes.caffeine.cache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.HashMap;
import java.util.List;

import static com.fmb.basic.FmbConstants.*;

@Service
public class FmbAdminLoginService {

    private static Logger logger = LoggerFactory.getLogger(FmbAdminLoginService.class);

    @Autowired
    FmbLoginDao loginDao ;


    @Autowired
    Cache caffeineCache ;

    @Autowired
    IFmbAdminService adminService ;

    @Autowired
    JedisPool jedisPool ;


    public HashMap selectFmbAdmin(String username) {
        return  loginDao.selectFmbAdmin(username) ;
    }


    /**
     * 生成token
     * @param name
     * @param dbUser
     * @return
     */
    public String generateToken2(String name, HashMap dbUser) {
        MDC.put("username", name);

        //生成token
        String token = SnowflakeIdWorker.getInstance().nextId()+"" ;
        //设置用户信息缓存
        //获取用户信息
        SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = new SessionUserInfoOfFmbAdmin();
        sessionUserInfoOfFmbAdmin.setUserId((Integer) dbUser.get("uid"));
        sessionUserInfoOfFmbAdmin.setUsername(dbUser.get("name").toString());
        sessionUserInfoOfFmbAdmin.setNickname(dbUser.get("realname").toString());
        sessionUserInfoOfFmbAdmin.setPhone(dbUser.get("phone").toString());

        writeUserInfo2Cache(token, sessionUserInfoOfFmbAdmin);


        return  token ;
    }



    /**
     * 把用户信息 写入redis 和 caffeineCache
     * @param token
     * @param info  包含了用户权限的用户信息
     */
    public void writeUserInfo2Cache(String token, SessionUserInfoOfFmbAdmin info) {




        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);

            //写入 caffeineCache
            caffeineCache.put(TOKEN_PRE_OF_FMBADMIN +token,info);
            //写入 redis
            jedis.setex(TOKEN_PRE_OF_FMBADMIN +token, TOKEN_KEEP_SECONDS ,JSON.toJSONString(info)) ;

            //把用户的所有token 都存到redis setl里 ,后续禁用用户或者权限变更可以根据这个找到用户
            int userId = info.getUserId();
            jedis.sadd ( TOKENSETS_PRE+ userId,token) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    @Cacheable(value = "fmbadmin#86400", keyGenerator = "customKeyGenerator")
    public List<FmbAdmin> selectUserList() {

        LambdaQueryWrapper<FmbAdmin> wrapper = new LambdaQueryWrapper<>();

        wrapper.select(FmbAdmin::getUid,FmbAdmin::getName,FmbAdmin::getRealname,FmbAdmin::getEmail).
                eq(FmbAdmin::getDisabled,0)
                .orderByAsc(FmbAdmin::getUid)
        ;

        List<FmbAdmin> list = adminService.list(wrapper);

        return list ;
    }


    public List<FmbAdminUsers>  userLists(HashMap<String, Object> pars){

        return loginDao.selectUsers(pars) ;
    }
}
