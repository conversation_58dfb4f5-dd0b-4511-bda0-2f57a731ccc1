package com.fmb.server2022.service.schedule;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class ConsumeResult {
    boolean runSuccess ;
    String resultMessage ;

    Map<String,Object> mapInfo ;

    public ConsumeResult() {
        this.mapInfo = new HashMap<>() ;
    }

    public static ConsumeResult success(String message){
        final ConsumeResult ok = new ConsumeResult();

        ok.setRunSuccess(true);
        ok.setResultMessage(message);

        return ok;
    }
    public static ConsumeResult fail(String message){
        final ConsumeResult ok = new ConsumeResult();
        ok.setRunSuccess(false);
        ok.setResultMessage(message);

        return ok;
    }

}
