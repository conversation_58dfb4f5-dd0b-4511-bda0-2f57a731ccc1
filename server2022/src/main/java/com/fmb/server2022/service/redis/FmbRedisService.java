package com.fmb.server2022.service.redis;

import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.HashMap;

@Component
public class FmbRedisService {

    private static Logger logger = LoggerFactory.getLogger(FmbRedisService.class);

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    FmbRedisService self ;

    public void expire(String key,int sec){

        if(sec<0){
            sec = 30  ;
        }

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            jedis.expire(key,sec) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }


    /**
     *
     * @param job  具体的redis操作
     * @param map  输入输出参数通过这个对象
     */
    public void doRedisJobWithMap(RedisOperateWithMap job, HashMap<String, Object> map){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            job.execRedisOperate(jedis,map);

        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }


    public void doRedisJob(RedisOperate job){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            job.execRedisOperate(jedis);

        }
        catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }




    public void ding10(HashMap<String,Object> resultMap) {

        resultMap.put("name",self.getTime()) ;
        resultMap.put("name2",self.getTime2()) ;
        resultMap.put("name3",self.getTime3()) ;
    }


    @Cacheable(cacheNames = "test#60",keyGenerator = "customKeyGenerator" )
    public String getTime() {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return simpleDateFormat.format(Calendar.getInstance().getTime()) ;
    }

    @Cacheable(cacheNames = "test22222#160",keyGenerator = "blank")
    public String getTime2() {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return simpleDateFormat.format(Calendar.getInstance().getTime()) ;
    }

    @CacheEvict(cacheNames = "test22222",keyGenerator = "blank")
    public String getTime2Evict() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return simpleDateFormat.format(Calendar.getInstance().getTime()) ;
    }



    @Cacheable(cacheNames = "test3",keyGenerator = "customKeyGenerator")
    public FmbxBpMain getTime3() {


        FmbxBpMain bpMain = new FmbxBpMain();
        bpMain.setBpId(0);
        bpMain.setParterType(0);
        bpMain.setAccountName("");
        bpMain.setPasswd("");
        bpMain.setSalt("");
        bpMain.setAdminUid(0);
        bpMain.setAdminMobile("");
        bpMain.setAdminCity("");
        bpMain.setSettleEachMonthday(0);
        bpMain.setSettleGenType(0);
        bpMain.setSettleType(0);
        bpMain.setAccountType(0);
        bpMain.setAccountStatus(0);
        bpMain.setCheckStatus(0);
        bpMain.setCtime(LocalDateTime.now());
        bpMain.setUtime(LocalDateTime.now());





        return bpMain ;
    }

}
