package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.service.IFmbUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
public class FrontUserService {

    @Autowired
    IFmbUsersService usersService ;

    @Cacheable(value = "queryFmbUsers#3600", keyGenerator = "customKeyGenerator")
    @DS(FmbConstants.DB_master)
    public FmbUsers queryFmbUsers(int userId) {
        Wrapper<FmbUsers> quWr = new LambdaQueryWrapper<FmbUsers>()
                .select(FmbUsers::getUid,FmbUsers::getPhoneNumber,FmbUsers::getEmail,FmbUsers::getRegdate)
                .eq(FmbUsers::getUid, userId)
                ;
        final FmbUsers one = usersService.getOne(quWr);
        return one;
    }
}
