package com.fmb.server2022.service;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.basic.user.SessionUserInfoOfBp;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.util.SnowflakeIdWorker;
import com.github.benmanes.caffeine.cache.Cache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.List;

import static com.fmb.basic.FmbConstants.*;

@Service
public class UserUnionService {


    private static Logger logger = LoggerFactory.getLogger(UserUnionService.class);

    @Autowired
    BpsService bpsService ;


    @Autowired
    FmbAdminLoginService loginService ;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    Cache caffeineCache ;


    /**
     * 读取 采用二级缓存
     *
     * 先读取 caffeineCache里的
     * 再读取 redis 里的
     * 如果caffeineCache里没有,redis里有就顺便再写到caffeineCache里,相当于给 caffeineCache续命
     * @return
     */
    public SessionUser nowUserInfo(){


        String fmbtoken = MDC.get(FmbConstants.TOKEN);
        String bpstoken = MDC.get(FmbConstants.BPSTOKEN);
        if (StringUtils.isBlank(fmbtoken) &&  StringUtils.isBlank(bpstoken) ) {
            throw new BadLogicException("未登录");
        }


        //从 caffeineCache 里读取
        SessionUserInfoOfFmbAdmin sessionUserInfoFmbAdminCaffeine = (SessionUserInfoOfFmbAdmin) caffeineCache.getIfPresent(TOKEN_PRE_OF_FMBADMIN + fmbtoken);
        SessionUserInfoOfBp sessionUserInfoOfBpCaffeine = (SessionUserInfoOfBp) caffeineCache.getIfPresent(TOKEN_PRE_OF_BPS
                + bpstoken);


        if (sessionUserInfoOfBpCaffeine != null) {
            logger.info("BpCaffeine hit userid={}",sessionUserInfoOfBpCaffeine.getUserId());
            return  sessionUserInfoOfBpCaffeine.nowUser() ;
        }
        if (sessionUserInfoFmbAdminCaffeine != null) {
            logger.info("FmbAdminCaffeine hit userid={} ",sessionUserInfoFmbAdminCaffeine.getUserId());
            return sessionUserInfoFmbAdminCaffeine.nowUser() ;
        }


        //从 redis 里读取
        List<String> usersString = getUsersString(TOKEN_PRE_OF_FMBADMIN + fmbtoken, TOKEN_PRE_OF_BPS + bpstoken);

        String  fmbTokenVal = usersString.get(0);
        String  bpsTokenVal = usersString.get(1);

        // redis 里 没有对应的 用户信息
        if (StringUtils.isBlank(fmbTokenVal) &&  StringUtils.isBlank(bpsTokenVal) ) {
            throw new BadLogicException("查询不到登录信息");
        }

        SessionUserInfoOfBp sessionUserInfoBp = JSON.parseObject(bpsTokenVal, SessionUserInfoOfBp.class);
        SessionUserInfoOfFmbAdmin sessionUserInfoFmbAdmin = JSON.parseObject(fmbTokenVal, SessionUserInfoOfFmbAdmin.class);

        //redis 里有 但解析失败...
        if (sessionUserInfoBp==null && sessionUserInfoFmbAdmin==null) {
            throw new BadLogicException("登录信息异常");
        }

        if (sessionUserInfoBp != null) {
            logger.info("bpadmin redis cache -> caffeineCache userid={}",sessionUserInfoBp.getUserId());
            //回写 caffeineCache
            caffeineCache.put(TOKEN_PRE_OF_BPS +bpstoken,sessionUserInfoBp);
            return  sessionUserInfoBp.nowUser() ;
        }
        if (sessionUserInfoFmbAdmin != null) {
            logger.info("Fmbadmin redis cache -> caffeineCache userid={}",sessionUserInfoFmbAdmin.getUserId());
            //回写 caffeineCache
            caffeineCache.put(TOKEN_PRE_OF_FMBADMIN +fmbtoken,sessionUserInfoFmbAdmin);
            return sessionUserInfoFmbAdmin.nowUser() ;
        }

        return null ;
    }


    private List<String> getUsersString(String... token) {



        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);
            List<String> values = jedis.mget(token);


            return values ;


        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);

        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return null ;
    }


}



