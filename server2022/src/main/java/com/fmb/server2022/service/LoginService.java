package com.fmb.server2022.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.fmb.basic.BadAccessException;
import com.fmb.server2022.fmbx.entity.SysUser;
import com.fmb.server2022.fmbx.service.ISysUserService;
import com.fmb.server2022.mapper.LoginDao;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 登录service实现类
 */
@Service
@Slf4j
public class LoginService {

    @Autowired
    private LoginDao loginDao;
    @Autowired
    private SysTokenService tokenService;

    @Autowired
    ISysUserService userService ;

    /**
     * 登录表单提交
     */

    public String authLogin(ReqLoginUser user) {
        String token = "" ;

        SysUser userEntity = new LambdaQueryChainWrapper<SysUser>(userService.getBaseMapper())
                .eq(SysUser::getUsername, user.getUsername()).one();


        if (userEntity == null) {
            throw new BadAccessException("用户名不存在:"+user.getUsername());
        }

        //用户的密码自身需要前端md5一下  ,后端拿到后再加盐再md5 和数据库里存的密码比对.
        String rightPasswd = MD5Util.md5(user.getPasswd() + userEntity.getSalt());
//        log.info("userid = {} , rightPasswd = {}",userEntity.getId() ,  rightPasswd);
        if (!rightPasswd.equals(userEntity.getPassword())) {
            throw new BadAccessException("密码错误");
        }
        token = tokenService.generateToken(user.getUsername());

        if (StringUtils.isBlank(token)) {
            throw new BadAccessException("获取token失败");
        }

        return token;
    }



//    /**
//     * 查询当前登录用户的权限等信息
//     */
//    public JSONObject getInfo() {
//        //从session获取用户信息
//        SessionUserInfoOfFmbAdmin userInfo = tokenService.getUserInfo();
//        log.info(userInfo.toString());
//        return CommonUtil.successJson(userInfo);
//    }
//
//    /**
//     * 退出登录
//     */
//    public JSONObject logout() {
//        tokenService.invalidateToken();
//        return CommonUtil.successJson();
//    }
}
