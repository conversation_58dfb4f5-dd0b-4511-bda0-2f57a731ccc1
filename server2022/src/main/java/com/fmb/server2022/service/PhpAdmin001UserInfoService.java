package com.fmb.server2022.service;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.domain.PhpAdminInfo;
import com.fmb.server2022.service.order.OrderStatService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date: 2023/4/10 4:59 下午
 */

@Service
public class PhpAdmin001UserInfoService {

    private static Logger logger = LoggerFactory.getLogger(PhpAdmin001UserInfoService.class);

    private static JedisPool jedisPool;

    @Autowired
    public void setJedisPool(JedisPool jedisPool) {
        PhpAdmin001UserInfoService.jedisPool = jedisPool;
    }

    public static PhpAdminInfo readUserInfo(String asidValue){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "admin_info_"+asidValue ;

            String jsonValue = jedis.get(key);
            //找到了
            if (StringUtils.isNotBlank(jsonValue)) {

                PhpAdminInfo sessionInfo = JSON.parseObject(jsonValue, PhpAdminInfo.class) ;
                return sessionInfo ;
            }

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null ;
    }


}
