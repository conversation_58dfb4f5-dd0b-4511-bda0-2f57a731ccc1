package com.fmb.server2022.service;

import com.fmb.server2022.domain.FrontBpsMediaDomain;
import com.fmb.server2022.mapper.FmbxBpMediaDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BpsMediaService {

    @Autowired
    FmbxBpMediaDao fmbxBpMediaDao;


    public void getImgCateNameList(Integer bpsId,HashMap<String, Object> resultMap) {
        //1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
        HashMap<Integer, String> typeMap = new HashMap<>();
        typeMap.put(1,"外观");
        typeMap.put(2,"大厅");
        typeMap.put(3,"餐饮");
        typeMap.put(4,"家庭亲子");
        typeMap.put(5,"休闲");
        typeMap.put(6,"健身房");
        typeMap.put(7,"公共区域");
        typeMap.put(8,"周边");
        typeMap.put(9,"其他");
        typeMap.put(10,"房间");

        List<Map<String,Object>> list = fmbxBpMediaDao.getGroupNumByBpsId(bpsId);
        System.out.println(list);
        for(Map item : list){
            item.put("groupvalName",typeMap.get(item.get("groupval")));
            List<Map<String,Object>> imgList = fmbxBpMediaDao.getImgListByBpsIdAndGroupNum(bpsId, (Integer) item.get("groupval"));
            item.put("imgList",imgList);
        }
        resultMap.put("list",list);
    }

    public void getFrontImgCateNameList(Integer bpsId,HashMap<String, Object> resultMap) {
        List<FrontBpsMediaDomain> list = fmbxBpMediaDao.getFrontImgListByBpsId(bpsId);
        Map<Integer, List<FrontBpsMediaDomain>> groupBy = list.stream().collect(Collectors.groupingBy(FrontBpsMediaDomain::getGroupval, LinkedHashMap::new,Collectors.toList()));
        List<FrontBpsMediaDomain> groupList = new ArrayList<>();
        for(Map.Entry<Integer,List<FrontBpsMediaDomain>> ele : groupBy.entrySet()){
            FrontBpsMediaDomain m = new FrontBpsMediaDomain();
            m.setGroupval(ele.getValue().get(0).getGroupval());
            m.setImgList(ele.getValue());
            m.setNum(ele.getValue().size());
            groupList.add(m);
        }
        resultMap.put("list",groupList);
    }

    /**
     * 通过商户ID和分组ID获取图片列表
     * @param bpsId
     * @param groupval
     * @param resultMap
     */
    public void getImgListByBpsIdAndGroupNum(Integer bpsId, Integer groupval, HashMap<String, Object> resultMap) {
        List<Map<String,Object>> list = fmbxBpMediaDao.getImgListByBpsIdAndGroupNum(bpsId,groupval);
        resultMap.put("list",list);
    }
}
