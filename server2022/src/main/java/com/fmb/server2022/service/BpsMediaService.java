package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.domain.FrontBpsMediaDomain;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBpsMedia;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpsMediaService;
import com.fmb.server2022.mapper.FmbxBpMediaDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.DB_slave;

@Service
public class BpsMediaService {

    private final FmbxBpMediaDao fmbxBpMediaDao;
    private final IFmbxActivityService activityService;
    private final IFmbxBpsMediaService mediaService ;

    @Autowired
    public BpsMediaService(FmbxBpMediaDao fmbxBpMediaDao, IFmbxActivityService activityService, IFmbxBpsMediaService mediaService) {
        this.fmbxBpMediaDao = fmbxBpMediaDao;
        this.activityService = activityService;
        this.mediaService = mediaService;
    }


    public void imgListByAid(Integer aid,HashMap<String, Object> resultMap) {
        DynamicDataSourceContextHolder.push(DB_slave);

        Wrapper<FmbxActivity> wr = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid,aid)
                .select(FmbxActivity::getXaid,FmbxActivity::getBpId,FmbxActivity::getBpsId)
                .last(" limit 1 ")
                ;
        final FmbxActivity activity = activityService.getOne(wr);

        if (activity == null) {
            throw new BadLogicException("活动不存在"+aid);
        }
        getImgCateNameList(activity.getBpsId(),resultMap) ;

    }
    public void getBpsVideoList(Integer bpsId,HashMap<String, Object> resultMap) {

        List<Map> videoInfos  = fmbxBpMediaDao.queryBpsVideos(bpsId) ;

        resultMap.put("videoInfos",videoInfos) ;

    }
    public void getImgCateNameList(Integer bpsId,HashMap<String, Object> resultMap) {
        //1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
        HashMap<Integer, String> typeMap = new HashMap<>();
        typeMap.put(1,"外观");
        typeMap.put(2,"大厅");
        typeMap.put(3,"餐饮");
        typeMap.put(4,"家庭亲子");
        typeMap.put(5,"休闲");
        typeMap.put(6,"健身房");
        typeMap.put(7,"公共区域");
        typeMap.put(8,"周边");
        typeMap.put(9,"其他");
        typeMap.put(10,"房间");
        typeMap.put(11,"视频");

        List<Map<String,Object>> list = fmbxBpMediaDao.getGroupNumByBpsId(bpsId);
//        System.out.println(list);
        for(Map item : list){
            item.put("groupvalName",typeMap.get(item.get("groupval")));
            List<Map<String,Object>> imgList = fmbxBpMediaDao.getImgListByBpsIdAndGroupNum(bpsId, (Integer) item.get("groupval"));
            item.put("imgList",imgList);

            item.put("num",imgList.size());
        }
        resultMap.put("list",list);
    }

    public void getFrontImgCateNameList(Integer bpsId,HashMap<String, Object> resultMap) {
        List<FrontBpsMediaDomain> list = fmbxBpMediaDao.getFrontImgListByBpsId(bpsId);
        Map<Integer, List<FrontBpsMediaDomain>> groupBy = list.stream().collect(Collectors.groupingBy(FrontBpsMediaDomain::getGroupval, LinkedHashMap::new,Collectors.toList()));
        List<FrontBpsMediaDomain> groupList = new ArrayList<>();
        for(Map.Entry<Integer,List<FrontBpsMediaDomain>> ele : groupBy.entrySet()){
            FrontBpsMediaDomain m = new FrontBpsMediaDomain();
            m.setGroupval(ele.getValue().get(0).getGroupval());
            m.setImgList(ele.getValue());
            m.setNum(ele.getValue().size());
            groupList.add(m);
        }
        resultMap.put("list",groupList);
    }

    /**
     * 通过商户ID和分组ID获取图片列表
     * @param bpsId
     * @param groupval
     * @param resultMap
     */
    public void getImgListByBpsIdAndGroupNum(Integer bpsId, Integer groupval, HashMap<String, Object> resultMap) {
        List<Map<String,Object>> list = fmbxBpMediaDao.getImgListByBpsIdAndGroupNum(bpsId,groupval);
        resultMap.put("list",list);
    }
}
