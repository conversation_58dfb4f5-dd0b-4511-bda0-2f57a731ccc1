package com.fmb.server2022.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.service.IFmbGoodsCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
@Service
public class CategoryService  {

    @Autowired
    IFmbGoodsCategoryService categoryService;

    /**
     * 返回分类树结构
     * @param resultMap
     */
    public void getCateTree(HashMap<String, Object> resultMap) {
        LambdaQueryWrapper<FmbGoodsCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(FmbGoodsCategory::getCateId).select(
                FmbGoodsCategory::getCateId,
                FmbGoodsCategory::getCateName,
                FmbGoodsCategory::getParentid
        );
        List<Map<String,Object>> list = categoryService.listMaps(wrapper);
        List<Map<String, Object>> treeList = doTreeList(list, "0","parentid","cate_id" , "children");
        resultMap.put("data",treeList) ;
    }


    /**
     * 递归 返回树结构
     * @param dataList 所有数据
     * @param pid  父id
     * @param pidFiled 父字段名称
     * @param indexFiled 主键名称
     * @param childrenFiled 添加子结点名称
     * @return 树结构
     */
    private static List<Map<String, Object>> doTreeList(
            List<Map<String, Object>> dataList,
            String pid,
            String pidFiled,
            String indexFiled,
            String childrenFiled
            ) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (dataList != null) {
            for (Map<String, Object> item : dataList) {
                if (pid.equals(item.get(pidFiled).toString())) {
                    List<Map<String, Object>> children = doTreeList(dataList, item.get(indexFiled).toString(),pidFiled,indexFiled,childrenFiled);
                    item.put(childrenFiled, children);
                    resultList.add(item);
                }
            }
        }
        return resultList;
    }
}
