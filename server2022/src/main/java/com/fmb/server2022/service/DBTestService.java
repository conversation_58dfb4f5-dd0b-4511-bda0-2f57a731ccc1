package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.server2022.fmbx.entity.FmbOrderActions;
import com.fmb.server2022.fmbx.service.IFmbOrderActionsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.fmb.basic.FmbConstants.DB_slave;

@Service
public class DBTestService {


    private static Logger logger = LoggerFactory.getLogger(DBTestService.class);

    @Autowired
    FmbNewService newService ;



    @Autowired
    IFmbOrderActionsService orderActionsService ;
    

//    @DSTransactional
    public void doTest(String name) {

        newService.addLog();

    }


    @DS(DB_slave)
    public void queryAndAddTest() {

        Wrapper<FmbOrderActions> wr = new LambdaQueryWrapper<FmbOrderActions>()
                .orderByDesc( FmbOrderActions::getActionId)
                .last(" limit 1 ")
                ;
        final FmbOrderActions one = orderActionsService.getOne(wr);

        logger.info("one={}",one);

        testAdd();

    }

    //    @DS(DB_master)
    public  void testAdd(){

//        DynamicDataSourceContextHolder.clear();
//        DynamicDataSourceContextHolder.push(DB_master);
        FmbOrderActions nA = new FmbOrderActions();
        nA.setOrderSn("");
        nA.setAdminUid(0);
        nA.setOrderStatus(0);
        nA.setShippingStatus(0);
        nA.setPayStatus(0);
        nA.setActionType("");
        nA.setActionNote("");
        nA.setLogTime(LocalDateTime.now());

        orderActionsService.save(nA) ;
        logger.info("nA = {}",nA);
//        DynamicDataSourceContextHolder.clear();
    }


}
