package com.fmb.server2022.service;


import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.domain.FmbAdminMenu;
import com.fmb.server2022.domain.FmbAdminUsers;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.service.IFmbUsersService;
import com.fmb.server2022.reqdomain.FrameLogin;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.*;

@Service
@Slf4j
public class FmbAdminTokenService {

    private static Logger logger = LoggerFactory.getLogger(FmbAdminTokenService.class);

    @Autowired
    FmbAdminLoginService loginService ;


    @Autowired
    Cache caffeineCache ;

    @Autowired
    IFmbAdminService adminService ;

    @Autowired
    JedisPool jedisPool ;

    @Autowired
    UserUnionService userUnionService ;

    /**
     * 通过token值 从redis里读取用户 信息
     * @param token
     * @return
     */
    public SessionUserInfoOfFmbAdmin getUserInfoBytoken(String token) {


        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);
            String tokenKey = TOKEN_PRE_OF_FMBADMIN + token;
            String value = jedis.get(tokenKey);



            if(StringUtils.isBlank(value)){
                log.info(" cannot_get_token {}",tokenKey);
                return null ;
            }else {
                SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = JSON.parseObject(value, SessionUserInfoOfFmbAdmin.class);

                //保持session
                if (jedis.ttl(tokenKey)<TOKEN_REMAIN_SECONDS_LIMIT) {
                    jedis.expire(tokenKey,TOKEN_KEEP_SECONDS) ;
                }

                return sessionUserInfoOfFmbAdmin;
            }



        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return null ;
    }


    /**
     * 父母邦后台登录
     * @param user
     * @return
     */
    public String authFmb(ReqLoginUser user) {

        String token = "" ;
        HashMap dbUser = loginService.selectFmbAdmin(user.getUsername());
        if(dbUser==null ){
            throw new BadLogicException("用户名不存在:"+user.getUsername());
        }

        if (!MD5Util.md5( user.getPasswd() +  dbUser.get("salt").toString()).equals(dbUser.get("password").toString())) {
            throw new BadLogicException("密码错误");
        }

        token = loginService.generateToken2(dbUser.get("name").toString(),dbUser) ;

        return  token ;

    }

    public String authFmbFromAdmin001(String userInfo){
        String token = "" ;

        if (StringUtils.isBlank(userInfo)) {
            throw new BadLogicException("请求登录参数为空");
        }

        final FrameLogin frameLoginDomain = JSON.parseObject(Base64.decodeStr( userInfo), FrameLogin.class);
        if (frameLoginDomain == null) {
            throw new BadLogicException("请求登录参数异常");
        }

        if (!MD5Util.md5(frameLoginDomain.getAdminId() + "_" + frameLoginDomain.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY).equals(frameLoginDomain.getMd5())) {
            throw new BadLogicException("请求登录参数校验失败");
        }

        Wrapper<FmbAdmin> wrAdmin = new LambdaQueryWrapper<FmbAdmin>()
                .eq(FmbAdmin::getUid,frameLoginDomain.getAdminId())
                .eq(FmbAdmin::getDisabled,0)
                ;
        final FmbAdmin user = adminService.getOne(wrAdmin);
        if (user == null) {
            throw new BadLogicException("用户不存在");
        }

        MDC.put("username", user.getName());

        //生成token
        token = SnowflakeIdWorker.getInstance().nextId()+"" ;
        //设置用户信息缓存
        //获取用户信息
        SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = new SessionUserInfoOfFmbAdmin();
        sessionUserInfoOfFmbAdmin.setUserId(user.getUid());
        sessionUserInfoOfFmbAdmin.setUsername(user.getName());
        sessionUserInfoOfFmbAdmin.setNickname(user.getRealname());
        sessionUserInfoOfFmbAdmin.setPhone(user.getPhone());

        loginService.writeUserInfo2Cache(token, sessionUserInfoOfFmbAdmin);

        return  token ;
    }


    public List<FmbAdminUsers> adminSearch(String key) {

//        List<FmbAdmin> fmbAdmins = loginService.selectUserList();

        HashMap<String, Object> pars = new HashMap<>();
        List<FmbAdminUsers> fmbAdmins = loginService.userLists(pars);

        List<FmbAdminUsers> collect = fmbAdmins.stream().filter(admin -> {

            if(StringUtils.isNotBlank(admin.getName()) && admin.getName().indexOf(key)!=-1){
                return true ;
            }
            if(StringUtils.isNotBlank(admin.getRealname()) && admin.getRealname().indexOf(key)!=-1){
                return true ;
            }
            if(StringUtils.isNotBlank(admin.getEmail()) && admin.getEmail().indexOf(key)!=-1){
                return true ;
            }

           return  false ;

        }).collect(Collectors.toList());

        return  collect ;
    }

    public FmbAdminUsers nowUser() {

//        String token = MDC.get(FmbConstants.TOKEN);
//        if (StringUtils.isBlank(token)) {
//            throw new BadLogicException("未登录");
//        }
//
//        SessionUserInfoOfFmbAdmin userInfoBytoken = getUserInfoBytoken(token);
//        if (userInfoBytoken == null) {
//            throw new BadLogicException("查询不到登录信息");
//        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        if (nowUserInfo.getUserType()!=SessionUser.SESSIONUSER_FMB) {
            throw new BadLogicException("登录信息异常");
        }

        HashMap<String, Object> pars = new HashMap<>();
        pars.put("uid",nowUserInfo.getUserId() );
        List<FmbAdminUsers> fmbAdmins = loginService.userLists(pars);

        if(fmbAdmins.isEmpty()){
            throw new BadLogicException("找不到当前用户的登录信息");
        }

        return  fmbAdmins.get(0) ;

    }


    /**
     * 目前是从文件直接读取的后续需要改为从数据库构造
     * @param resultMap
     */
    public void menuTree(HashMap<String,Object> resultMap) {


        String jsonFile = "" ;
        try {

            jsonFile =  IOUtils.toString(new ClassPathResource("fmbadmintree.json").getInputStream(),"UTF-8") ;

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        List<FmbAdminMenu> fmbAdminMenus = JSONArray.parseArray(jsonFile, FmbAdminMenu.class);


        resultMap.put("trees",fmbAdminMenus) ;


    }

    /**
     * 退出登录
     * @param resultMap
     */
    public void logout(HashMap<String, Object> resultMap) {




        String fmbtoken = MDC.get(FmbConstants.TOKEN);
        caffeineCache.invalidate(TOKEN_PRE_OF_FMBADMIN+fmbtoken);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            Long del = jedis.del(TOKEN_PRE_OF_FMBADMIN+fmbtoken);

            resultMap.put("result",del>0) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }
}
