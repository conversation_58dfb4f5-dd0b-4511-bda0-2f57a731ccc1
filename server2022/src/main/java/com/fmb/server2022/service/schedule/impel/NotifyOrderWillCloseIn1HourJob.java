package com.fmb.server2022.service.schedule.impel;

import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_ORDER_CLOSE_IN_1_HOUR;

@Service(value = JOB_NAME_ORDER_CLOSE_IN_1_HOUR)
public class NotifyOrderWillCloseIn1HourJob implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(NotifyOrderWillCloseIn1HourJob.class);

    @Autowired
    OrderService orderService ;

    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {

            orderService.sendSmsOrderWillCloseIn1Hour(job.getJobFmbxId()) ;

        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
