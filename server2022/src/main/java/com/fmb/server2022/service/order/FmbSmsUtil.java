package com.fmb.server2022.service.order;

import cn.hutool.http.HttpRequest;
import com.fmb.server2022.fmbx.entity.FmbSms;
import com.fmb.server2022.fmbx.service.IFmbSmsService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date: 2023/2/8 3:40 下午
 */
@Component
public class FmbSmsUtil {

    private static Logger logger = LoggerFactory.getLogger(FmbSmsUtil.class);

    public static final String QIXINTONG = "qixintong";
    public static final String QXT_USER_NAME = "fmb";
    public static final String QXT_PASSWD = "QxtPass20220726";


    private static IFmbSmsService  smsService ;

    @Autowired
    public FmbSmsUtil(IFmbSmsService smsService) {
        FmbSmsUtil.smsService = smsService;
    }



    public static void sendSms(String mobile,String smsContent,String mtype){

        final String s = qxtSendSms(smsContent, mobile);

        FmbSms smsDB = new FmbSms();
        smsDB.setChannel(QIXINTONG);
        smsDB.setMtype(mtype);
        smsDB.setmLogId("0");
        smsDB.setPhone(mobile);
        if (StringUtils.isNotBlank(mobile)) {
            smsDB.setPhone(mobile.trim());
        }
        smsDB.setMessage(smsContent);
        smsDB.setReason("");
        
        smsDB.setRet(s);
        smsDB.setIp("");
        smsDB.setAdminUid(0);
        smsDB.setCreateTime(LocalDateTime.now());

        smsService.save(smsDB);

    }


    private static String qxtSendSms(String content, String mobile){
        try {
            String  encode = URLEncoder.encode(content,"GBK");
            String url = "http://**************:8061/?username=" + QXT_USER_NAME + "&password=" + QXT_PASSWD + "&epid=105376&linkid=&phone="
            +mobile+"&message=" + encode;

            final String body = HttpRequest.get(url).execute().body();

            return body ;

        } catch (UnsupportedEncodingException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        return "FmbxSendError" ;
    }

}
