package com.fmb.server2022.service.order;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderSysInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.domain.ConfirmOrderMoneyGroup;
import com.fmb.server2022.domain.ConfirmOrderMoneyInfo;
import com.fmb.server2022.domain.ConfirmOrderMoneyItem;
import com.fmb.server2022.domain.stock.SkuSellNumUpdate;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.service.IFmbCouponTypeService;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbOrderRelatedDataService;
import com.fmb.server2022.fmbx.service.IFmbPayOrdersService;
import com.fmb.server2022.fmbx.service.IFmbReserveHotelCodesService;
import com.fmb.server2022.fmbx.service.IFmbShieldUsersService;
import com.fmb.server2022.fmbx.service.IFmbUserCashLogService;
import com.fmb.server2022.fmbx.service.IFmbUserCouponService;
import com.fmb.server2022.fmbx.service.IFmbUserExtinfoService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.service.FrontSkuService;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.util.FmbNumberUtil.bigDecimal2Double;
import static com.fmb.util.FmbNumberUtil.double2String;

@Service
public class OrderOfHotelReserveService implements AbstractOrderservice {

    private static Logger logger = LoggerFactory.getLogger(OrderOfHotelReserveService.class);

    @Autowired
    OrderOfHotelReserveService self;

    @Autowired
    IFmbShieldUsersService shieldUsersService;

    @Autowired
    IFmbxActivityService activityService;

    @Autowired
    IFmbxSkuService skuService;

    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService;

    @Autowired
    IFmbxSuiteService suiteService;

    @Autowired
    FrontSkuService frontSkuService;

    @Autowired
    IFmbPayOrdersService payOrdersService;


    @Autowired
    IFmbOrderInfoService orderInfoService;

    @Autowired
    IFmbOrderGoodsService orderGoodsService;

    @Autowired
    SuiteDao suiteDao;

    @Autowired
    IFmbUserCouponService couponService;

    @Autowired
    IFmbCouponTypeService couponTypeService;

    @Autowired
    IFmbUserExtinfoService userExtinfoService;
    @Autowired
    IFmbUserCashLogService userCashLogService;

    @Autowired
    IFmbxSuiteStockService suiteStockService;

    @Autowired
    IFmbxSuiteRoomdatePriceService roomdatePriceService;

    @Autowired
    IFmbReserveHotelCodesService hotelCodesService;


    @Autowired
    SuiteService mySuiteService;


    @Autowired
    JedisPool jedisPool;
    @Autowired
    IFmbOrderRelatedDataService orderRelatedDataService ;

    @Override
    public int getSkuType() {
        return FmbConstants.SKU_HOTEL_RESERVE;
    }

    @Override
    public int skuType2OldGoodsType() {
        return SKU_HOTEL_RESERVE_GOODS_TYPE;
    }

    @Override
    public void doFinalJobWhenCreateOrderOver(ReqGenOrder input, SkuDetail skuDetail) {
        if (skuDetail.getSkuAimStockNumber() == 0) {
            SuiteService.sendHotelSkuChangeNotify(skuDetail.getReserveSku().getSkuId());
        }
    }

    @Override
    public void subStock(ReqGenOrder input, NowUser nowUser, SkuDetail skuDetail) {

        RedissonLockService.doLockJob(SKU_NUMBER_COMMON, skuDetail.getReserveSku().getSkuId().toString(), 2000, 1500
                , () -> self.subStockUnderLock(skuDetail), "库存异常[1579]");

    }

    public void subStockUnderLock(SkuDetail skuDetail) {
        boolean update = false;

        Integer skuNumber = skuDetail.getSkuNumber();
        FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();
        int aimVal = reserveSku.getStockNum() - skuNumber;

        if (aimVal>=0) {
            update = updateReserveStock(skuNumber, reserveSku, aimVal);
        }


        if (!update) {
            //最多重试3次
            for (int i = 0; i < 3; i++) {
                reserveSku = querySkuReserve(reserveSku.getSkuId());
                aimVal = reserveSku.getStockNum() - skuNumber;

                if (aimVal<0){
                    break;
                }

                update = updateReserveStock(skuNumber, reserveSku, aimVal);
                if (update) {
                    break;
                }
            }
        }

        if (update) {
            skuDetail.setSkuAimStockNumber(aimVal);
        } else {
            throw new BadLogicException("房券sku库存扣减不足[1592]");
        }
    }

    @Override
    public void saveOrderGoods(NowUser nowUser, FmbOrderInfo orderInfo, SkuDetail skuDetail, ReqGenOrder input) {

        FmbOrderGoods goods = new FmbOrderGoods();
        goods.setOrderSn(orderInfo.getOrderSn());
        goods.setUid(nowUser.getUserId());
        goods.setAid(input.getXaid());

        self.buildHotleReserveGoods(nowUser, orderInfo, goods, skuDetail, input);

        orderGoodsService.save(goods);
        skuDetail.setRecId(goods.getRecId());
    }

    @Override
    public void buildSkuDetail(ReqGenOrder input, Map<String, List<ReqGenSku>> reqSkuMaps, Map<String, List<FmbxSku>> skuMainOfDBMap, FmbxSku skuMain) {
        //查询房券sku  具体信息
        final FmbxHotelReserveSku reserveSku = self.querySkuReserve(skuMain.getSkuId());
        if (reserveSku == null) {
            throw new BadLogicException("sku数据异常[160]");
        }

        FmbxSuite suite = self.queryFmbxSuite(reserveSku.getSuiteId());
        if (suite == null) {
            throw new BadLogicException("套餐数据异常[162]");
        }

//        input.getOrderSysInfo().addSkuDetail(SKU_HOTEL_RESERVE, reserveSku, reqSkuMaps.get(reserveSku.getSkuId() + "").get(0),
//                skuMainOfDBMap.get(reserveSku.getSkuId() + "").get(0), suite);

        SkuDetail e = new SkuDetail();
        e.setReqGenSku(reqSkuMaps.get(reserveSku.getSkuId() + "").get(0));
        e.setFmbxSku(skuMain);
        e.setSuite(suite);
        e.setReserveSku(reserveSku);
        e.setSkuType(SKU_HOTEL_RESERVE);
        input.getOrderSysInfo().getSkuDetails().add(e);

    }


    /**
     * 查询套餐信息
     *
     * @param suiteId 套餐id
     * @return
     */
    public FmbxSuite queryFmbxSuite(Integer suiteId) {
        Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
                .eq(FmbxSuite::getSuiteId, suiteId)
                .select(FmbxSuite::getSuiteId, FmbxSuite::getXaid, FmbxSuite::getSuiteContent, FmbxSuite::getTotalNeight
                        ,FmbxSuite::getName
                        , FmbxSuite::getNightMin, FmbxSuite::getFlagStandardHotel, FmbxSuite::getFlagHotelReserve
                        , FmbxSuite::getBuyLimit, FmbxSuite::getAutoCloseOrderSecond, FmbxSuite::getCertFlag, FmbxSuite::getCertTip
                        , FmbxSuite::getFlagRiskWarning, FmbxSuite::getRiskWarningTip, FmbxSuite::getIsReturn, FmbxSuite::getReturnPolicy

                        , FmbxSuite::getPreReserveDay, FmbxSuite::getPreReserveTime
                        , FmbxSuite::getReturnPolicy, FmbxSuite::getReturnValidDay, FmbxSuite::getReturnValidSecond, FmbxSuite::getReturnValidTime
                        , FmbxSuite::getIsInvoice, FmbxSuite::getVersionNum
                );
        final FmbxSuite suite = suiteService.getOne(wrSuite);
        return suite;
    }

    void buildHotleReserveGoods(NowUser nowUser, FmbOrderInfo orderInfo, FmbOrderGoods goods, SkuDetail skuDetail, ReqGenOrder input) {
        final FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();
        goods.setThirdSn("");

        goods.setPackProductId(0);
        goods.setProductId(0);
        goods.setSupplierId(0);
        goods.setSellId(0);
        goods.setTypeName("");
        goods.setPlaneTripStatus(0);

        goods.setActivityType(0);
        goods.setShopUserId(skuDetail.getFmbxSku().getBpId());
        goods.setShopWarehouseId(0);
        goods.setGoodsGroupId(0);
        goods.setGoodsType(skuType2OldGoodsType());
        goods.setGoodsId(reserveSku.getSkuId());
        goods.setGoodsNumber(skuDetail.getSkuNumber());
        goods.setMarketPrice(skuDetail.getMarketPrice());
        goods.setGoodsPrice(skuDetail.getGoodsPrice());
        goods.setMycpsPrice(BigDecimal.ZERO);
        goods.setSettlePrice(skuDetail.getSettlePrice());
        goods.setPlayTime(DEFAULT_DATETIME);
        goods.setLeaveTime(DEFAULT_DATETIME);
        goods.setCtime(LocalDateTime.now());
        goods.setBedRequire("");
        goods.setExtInfo("");
        goods.setRowIndex(0);
        goods.setTaxMoney(BigDecimal.ZERO);
        goods.setSettleTaxMoney(BigDecimal.ZERO);
        goods.setTuanStatus(0);
        goods.setExtendInfo("");

        HashMap<String, Object> extInfo = new HashMap<>();
//        extInfo.put("suite", skuDetail.getSuite());
        extInfo.put("skuDetail", skuDetail);
        extInfo.put("activity", input.getOrderSysInfo().getActivity());

        goods.setGoodExt(JSONUtil.toJsonStr(extInfo));
    }

    @Override
    public OrderDetailGoodsInfo readGoodsInfoFromGoodsExt(FmbOrderGoods goods, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, FmbOrderInfo orderInfo){

        OrderDetailGoodsInfo orderDetailGoodsInfo = new OrderDetailGoodsInfo();
        orderDetailGoodsInfo.setGoodsType(goods.getGoodsType());

        final JSON json = JSONUtil.parse(goods.getGoodExt());
        final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

        FmbxSuite suite = skuDetail.getSuite() ;
//


        orderDetailGoodsInfo.setTitle(activity.getTitle());
        orderDetailGoodsInfo.setSuiteName(suite.getName());
        orderDetailGoodsInfo.setSkuNumber(goods.getGoodsNumber());
        final FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();

        logger.info("reserveSku={}",reserveSku);

        orderDetailGoodsInfo.setValidStartEndDate( FmbDateUtil.localDateTime2String10(  reserveSku.getReserveValidityStartTime())+"至" +FmbDateUtil.localDateTime2String10(  reserveSku.getReserveValidityEndTime()) );


        if (StringUtils.isBlank(confirmOrderMoneyInfo.getSubTitle())) {
            confirmOrderMoneyInfo.setSubTitle("");
        }

        ConfirmOrderMoneyGroup group = new ConfirmOrderMoneyGroup();
        group.setTitle("房券");
        group.setGroupType(ConfirmOrderMoneyGroup.GROUPTYPE_1);

            ConfirmOrderMoneyItem item = new ConfirmOrderMoneyItem();
            item.setLeftTitle( reserveSku.getSkuName());
            item.setSkuNumber(goods.getGoodsNumber()+ MUTI_CHAR);
            item.setSkuNumberN(goods.getGoodsNumber());
            item.setPrice( RMB_CHAR+ FmbNumberUtil.bigDecimal2Str(goods.getGoodsPrice()));
            item.setPriceBig(goods.getGoodsPrice());

            group.getItemList().add(item) ;


        confirmOrderMoneyInfo.getGroups().add( group) ;




        return orderDetailGoodsInfo;


    }

    @Override
    public void updateSkuSellNum(SkuSellNumUpdate s) {


        RedissonLockService.doLockJobNoWait("updateSkuSellNumReserve",s.getSkuId().toString(),200,()->{

            final FmbxHotelReserveSku reserveSku = querySkuReserve(s.getSkuId());

            Wrapper<FmbxHotelReserveSku> wru = new LambdaUpdateWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSkuId,s.getSkuId())
                    .eq(FmbxHotelReserveSku::getVersionNum,reserveSku.getVersionNum())
                    .set(FmbxHotelReserveSku::getSellNum,reserveSku.getSellNum()+s.getSellNum())
                    .set(FmbxHotelReserveSku::getVersionNum,reserveSku.getVersionNum()+1)
                    ;
            reserveSkuService.update(wru) ;
        }) ;


    }

    public boolean updateReserveStock(Integer skuNumber, FmbxHotelReserveSku reserveSku, int val) {

        Wrapper<FmbxHotelReserveSku> wr = new LambdaUpdateWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId, reserveSku.getSkuId())
                .eq(FmbxHotelReserveSku::getVersionNum, reserveSku.getVersionNum())
                .eq(FmbxHotelReserveSku::getStatus, 1)
                .set(FmbxHotelReserveSku::getStockNum, val)
                .set(FmbxHotelReserveSku::getVersionNum, reserveSku.getVersionNum() + 1) ;
//                .set(FmbxHotelReserveSku::getSellNum, reserveSku.getSellNum() + skuNumber);

        if (val < 0) {
            throw new BadLogicException("房券sku库存不足[1590]");
        }

        return reserveSkuService.update(wr);
    }


    @DS(FmbConstants.DB_master)
    public FmbxHotelReserveSku querySkuReserve(Integer skuid) {

        Wrapper<FmbxHotelReserveSku> wrRe = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId, skuid)
                .select(FmbxHotelReserveSku::getSkuId, FmbxHotelReserveSku::getSkuName, FmbxHotelReserveSku::getSellStartTime,
                        FmbxHotelReserveSku::getSellEndTime, FmbxHotelReserveSku::getFlagBuyLimit,
                        FmbxHotelReserveSku::getSuiteId,
                        FmbxHotelReserveSku::getCanDivide,
                        FmbxHotelReserveSku::getEachOrderMaxNum, FmbxHotelReserveSku::getEachOrderMinNum,
                        FmbxHotelReserveSku::getMaxBuyAllNum, FmbxHotelReserveSku::getMarketPrice,
                        FmbxHotelReserveSku::getReserveChooseStartTime,FmbxHotelReserveSku::getReserveChooseEndTime,
                        FmbxHotelReserveSku::getSellNum,FmbxHotelReserveSku::getInvalidReserveWeekDays,FmbxHotelReserveSku::getInvalidReserveDate
                        ,FmbxHotelReserveSku::getReserveValidityStartTime,FmbxHotelReserveSku::getReserveValidityEndTime
                        ,FmbxHotelReserveSku::getCanDivide ,
                        FmbxHotelReserveSku::getGoodsPrice, FmbxHotelReserveSku::getSettlePrice, FmbxHotelReserveSku::getStockNum,
                        FmbxHotelReserveSku::getIsReturn, FmbxHotelReserveSku::getFlagSell, FmbxHotelReserveSku::getStatus, FmbxHotelReserveSku::getVersionNum);
        final FmbxHotelReserveSku reserveSku = reserveSkuService.getOne(wrRe);
        return reserveSku;
    }


    /**
     * 房券规则校验
     *
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    @Override
    @DS(FmbConstants.DB_master)
    public void baseCheck(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {
        final FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();

        final LocalDateTime now = LocalDateTime.now();

//        logger.info("reserveSku={}", reserveSku);
        if (!(reserveSku.getSellStartTime().isBefore(now) && reserveSku.getSellEndTime().isAfter(now))) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "不在售卖时间范围内[1100]");
        }

        orderSysInfo.setHaveHotelReserveSku(true);

        //默认最少买1个
        int minBuySku = 1;

        //限购校验
        //   `flag_buy_limit` int(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否限购:0-不限,1-限购',
        if (1 == reserveSku.getFlagBuyLimit().intValue()) {

//              `each_order_max_num` int(6) unsigned NOT NULL DEFAULT '100' COMMENT '每单最多购买',
            //购买数量超过最大限制
            if (skuDetail.getReqGenSku().getSkuNumber() > reserveSku.getEachOrderMaxNum()) {
                throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "最多购买" + reserveSku.getEachOrderMaxNum() + "[1103]");
            }
            //  `each_order_min_num` int(6) unsigned NOT NULL DEFAULT '1' COMMENT '每单最少购买',
            if (reserveSku.getEachOrderMinNum() != null && skuDetail.getReqGenSku().getSkuNumber() < reserveSku.getEachOrderMinNum()) {
                throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "最少购买" + reserveSku.getEachOrderMinNum() + "[1106]");
            }
            minBuySku = reserveSku.getEachOrderMinNum();

            Integer nowBuy = self.querySkuBuyNum(reserveSku.getSkuId(), nowUser.getUserInfo().getUid());
            if (nowBuy!=null &&  nowBuy > reserveSku.getMaxBuyAllNum()) {
                throw new BadLogicException("skuid=" + reserveSku.getSkuId() + ",该sku最多购买" + reserveSku.getMaxBuyAllNum() + "[1109]");
            }
        }

        if (reserveSku.getStockNum() < minBuySku) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "库存不足" + reserveSku.getStockNum() + "[1112]");
        }
        //售卖状态
        //         `flag_sell` smallint(2) unsigned NOT NULL DEFAULT '0' COMMENT '售买状态(库存和当前时间在售卖时间内的结果):0-无效,1-有效',
        if (0 == reserveSku.getFlagSell().intValue()) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "当前sku不可售" + "[1115]");
        }

        //上下架状态
        //`status` smallint(2) unsigned NOT NULL DEFAULT '3' COMMENT '上下架状态:0-已删除,1-已上架,2-已下架,3-待完善',
        if (1 != reserveSku.getStatus().intValue()) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "当前sku不可售" + "[1118]");
        }

        //套餐信息校验
        // `flag_hotel_reserve` smallint(2) unsigned NOT NULL DEFAULT '0' COMMENT '预售房券:0-否,1-是',
        if (1 != skuDetail.getSuite().getFlagHotelReserve().intValue()) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "套餐信息异常,suiteid=" + skuDetail.getSuite().getSuiteId() + " [1121]");
        }

    }


    /**
     * todo 查询用户之前购买过多少该sku
     *
     * @param skuId
     * @param uid
     * @return
     */
    @DS(FmbConstants.DB_slave)
    Integer querySkuBuyNum(Integer skuId, Integer uid) {

        HashMap<String, Object> map = new HashMap<>();
        map.put("skuId", skuId);
        map.put("uid", uid);

        return suiteDao.querySkuBuyNum(map);

//        return 0 ;
    }


    @Override
    public void buildPrice(SkuDetail skuDetail, ReqGenOrder input) {

        final Integer skuNumber = skuDetail.getReqGenSku().getSkuNumber();

//        SkuDetail skuDetail = new SkuDetail() ;

        final FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();

        if (skuNumber > reserveSku.getStockNum().intValue()) {
            throw new BadLogicException("skuid=" + reserveSku.getSkuId() + "库存不足[1423]");
        }

        skuDetail.setMarketPrice(reserveSku.getMarketPrice());
        skuDetail.setMarketPriceAll(reserveSku.getMarketPrice().multiply(new BigDecimal(skuNumber.toString())));
        skuDetail.setGoodsPrice(reserveSku.getGoodsPrice());
        skuDetail.setGoodsPriceAll(reserveSku.getGoodsPrice().multiply(new BigDecimal(skuNumber.toString())));
        skuDetail.setSettlePrice(reserveSku.getSettlePrice());
        skuDetail.setSettlePriceAll(reserveSku.getSettlePrice().multiply(new BigDecimal(skuNumber.toString())));

        skuDetail.setSkuNumber(skuNumber);

    }


    /**
     * 房券下发房券电子码
     *
     * @param orderGood
     * @param orderInfo
     */
    @Override
    public CreateUserAssetResult createUserAsset(FmbOrderGoods orderGood, FmbOrderInfo orderInfo) {

        CreateUserAssetResult createUserAssetResult = new CreateUserAssetResult();

        logger.info("createUserAsset[房券]");
        final String jsonTxt = orderGood.getGoodExt();

        SkuDetail skuDetail = JSONUtil.parse(jsonTxt).getByPath("skuDetail", SkuDetail.class);
        FmbxSuite suite = JSONUtil.parse(jsonTxt).getByPath("suite", FmbxSuite.class);

        final LocalDateTime now = LocalDateTime.now();
        for (Integer i = 0; i < skuDetail.getSkuNumber(); i++) {
            FmbReserveHotelCodes codeAddDB = new FmbReserveHotelCodes();
            codeAddDB.setAid(orderInfo.getAid());
            codeAddDB.setTicketId(orderGood.getGoodsId());
            codeAddDB.setOrderSn(orderInfo.getOrderSn());
            codeAddDB.setExchangeCode("");
            final String code = self.createCode();

            if (StringUtils.isBlank(code)) {
                throw new BadLogicException("生成酒店预约号失败[2334]");
            }
            codeAddDB.setCode(code);
            createUserAssetResult.getHotelReserveCode().add(code);
            codeAddDB.setNumber(skuDetail.getSuite().getTotalNeight());
            codeAddDB.setUseTime(DEFAULT_DATETIME);
            codeAddDB.setStatus(1);
            codeAddDB.setAdminUid(0);
            codeAddDB.setUtime(DEFAULT_DATETIME);

            codeAddDB.setCtime(now);
            hotelCodesService.save(codeAddDB);
        }

        FmbOrderRelatedData relatedData = new FmbOrderRelatedData();
        relatedData.setOrderSn(orderInfo.getOrderSn());
        relatedData.setUid(orderInfo.getUid());
        relatedData.setCallUser(0);
        relatedData.setCommonId(orderGood.getGoodsId());
        relatedData.setType(4);
        relatedData.setContainTimes(skuDetail.getSuite().getTotalNeight());
        relatedData.setNumber(skuDetail.getSkuNumber());
        relatedData.setDesc("");
        relatedData.setCtime(LocalDateTime.now());
        relatedData.setReserveEndStatus(0);

        orderRelatedDataService.save(relatedData) ;


        createUserAssetResult.setHotelReserve(true);

        return createUserAssetResult;

    }

    public String createCode() {

        for (int i = 0; i < 10; i++) {

            String code = "H" + RandomUtils.nextInt(10000000, 99999999);
            Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                    .eq(FmbReserveHotelCodes::getCode, code);
            if (hotelCodesService.count(wr) == 0) {
                return code;
            }
        }

        return "";

    }

    @Override
    public int getCancelSecond(SkuDetail skuDetail) {
        return skuDetail.getSuite().getAutoCloseOrderSecond();
    }

    /**
     * todo 取消预约权益
     *
     * @param orderGood
     * @param fmbOrderInfo
     */
    @Override
    public void cancelUserGoods(FmbOrderGoods orderGood, FmbOrderInfo fmbOrderInfo) {


        //更新 购买数量
        SkuSellNumUpdate sell = new SkuSellNumUpdate();
        sell.setSkuId(orderGood.getGoodsId());
        sell.setSellNum(orderGood.getGoodsNumber());
        sell.setGoodSType(orderGood.getGoodsType());

        RedissonLockService.doLockJobNoWait("updateSkuSellNumReserve",sell.getSkuId().toString(),200,()->{

            final FmbxHotelReserveSku reserveSku = querySkuReserve(sell.getSkuId());

            Wrapper<FmbxHotelReserveSku> wru = new LambdaUpdateWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSkuId,sell.getSkuId())
                    .eq(FmbxHotelReserveSku::getVersionNum,reserveSku.getVersionNum())
                    .set(FmbxHotelReserveSku::getSellNum,reserveSku.getSellNum()-sell.getSellNum())
                    .set(FmbxHotelReserveSku::getStockNum,reserveSku.getStockNum()+sell.getSellNum())
                    .set(FmbxHotelReserveSku::getVersionNum,reserveSku.getVersionNum()+1)
                    ;
            reserveSkuService.update(wru) ;
        }) ;

        //回库存

    }

    @Override
    public ConfirmOrderMoneyGroup buildPriceWhenUseCoupon(FmbxSku fmbxSku, ReqGenSku genSkus, String confirmOrder, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, String hotelReserveCode) {

        final ConfirmOrderMoneyGroup confirmOrderMoneyGroup = new ConfirmOrderMoneyGroup();
        confirmOrderMoneyGroup.setTitle("房券");
        confirmOrderMoneyGroup.setGroupType(ConfirmOrderMoneyGroup.GROUPTYPE_1);


        List<ConfirmOrderMoneyItem> list =  new ArrayList<>() ;
        FmbxHotelReserveSku reserveSku = self.querySkuReserve(genSkus.getSkuId());

        if("confirmOrder".equals(confirmOrder)){
            //第一次进入确认订单页
            if (genSkus.getSkuNumber() ==0) {
                if (reserveSku.getFlagBuyLimit()==1) {
                    if (reserveSku.getStockNum()>=reserveSku.getEachOrderMinNum()) {
                        genSkus.setSkuNumber(reserveSku.getEachOrderMinNum());

                        confirmOrderMoneyInfo.setAdviseSkuNumber(reserveSku.getEachOrderMinNum());
                    }else {
                        throw new BadLogicException("库存不足[3986]");
                    }
                }else if (reserveSku.getStockNum()>0){
                    genSkus.setSkuNumber(1);
                    confirmOrderMoneyInfo.setAdviseSkuNumber(1);
                }else {
                    throw new BadLogicException("库存不足[3987]");
                }
            }
        }


        ConfirmOrderMoneyItem confirmOrderMoneyItem = new ConfirmOrderMoneyItem();

        confirmOrderMoneyItem.setLeftTitle(reserveSku.getSkuName());
        confirmOrderMoneyItem.setSkuNumber(genSkus.getSkuNumber()+ FmbConstants.MUTI_CHAR );
        confirmOrderMoneyItem.setPrice(RMB_CHAR+ double2String(bigDecimal2Double( reserveSku.getGoodsPrice())));

        confirmOrderMoneyItem.setSkuNumberN(genSkus.getSkuNumber());
        confirmOrderMoneyItem.setPriceBig(reserveSku.getGoodsPrice());

        list.add(confirmOrderMoneyItem) ;

        confirmOrderMoneyGroup.setItemList(list);


        return confirmOrderMoneyGroup;
    }

}