package com.fmb.server2022.service.schedule.impel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_ORDER_RETURN_PASS;

@Service(value = JOB_NAME_ORDER_RETURN_PASS)
public class OrderReturnPassJob implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(OrderReturnPassJob.class);


    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {
            //	{"return_id":"23603","aid":"105795","goods_id":"769","type":"order_return_pass"}
            final JSONObject jsonObject = JSON.parseObject(job.getJobExtPars());
            if (jsonObject.containsKey("goods_id")) {
                SuiteService.sendHotelSkuChangeNotify( Integer.parseInt( jsonObject.getString("goods_id")));
            }

        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
