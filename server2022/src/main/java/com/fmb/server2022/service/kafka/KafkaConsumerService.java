package com.fmb.server2022.service.kafka;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.concurrent.ThreadPoolExecutor;

//@Service
//@DependsOn("fmbServerConfig")
//@Order(10)
public class KafkaConsumerService
//        implements ApplicationRunner

{


    private static Logger logger = LoggerFactory.getLogger(KafkaConsumerService.class);


    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;


//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//
//                Properties props = new Properties();
//                props.setProperty("bootstrap.servers", "192.168.30.225:9092");
//                props.setProperty("group.id", "default");
//                props.setProperty("enable.auto.commit", "false");
//                props.setProperty("max.poll.records", "100");
//                props.setProperty("max.poll.interval.ms", "60000");
//                props.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//                props.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//                KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
//                consumer.subscribe(Arrays.asList(TEST_1027));
//                while (true) {
//                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(500));
//                    if (records.isEmpty()) {
////                        logger.info("no record");
//                        continue;
//                    }
//                    final CountDownLatch countDownLatch = new CountDownLatch(records.count());
//                    for (ConsumerRecord<String, String> r : records) {
//
//                        executor.submit(new FmbKafkaWorker(countDownLatch, r) {
//                            @Override
//                            public void doBusinessJob(ConsumerRecord<String, String> record) {
//
//                                simulateJob();
//
////                                logger.info(" record {}  done ",record.value() );
//                            }
//                        }) ;
//                    }
//                    try {
//                        countDownLatch.await();
//                    } catch (InterruptedException e) {
//                    }
//                    consumer.commitSync();
//                    logger.info("consumer.commitSync()");
//
//                }
//            }
//        },"kafkaManualAckTest").start();
//    }
//
//
//    public void simulateJob() {
//        try {
//            TimeUnit.MILLISECONDS.sleep(new Random().nextInt(200)+1);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
}
