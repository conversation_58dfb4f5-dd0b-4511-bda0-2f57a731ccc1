package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.domain.HotelReserveSkuDomain;
import com.fmb.server2022.domain.suite.HotelReserveSkuInfo;
import com.fmb.server2022.domain.suite.SplitReserveSku;
import com.fmb.server2022.domain.suite.SuiteInfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSkuExt;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteExt;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuExtService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteExtService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.FmbDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SkuService {
    private static Logger logger = LoggerFactory.getLogger(SkuService.class);
    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    IFmbxSuiteService suiteService;

    @Autowired
    SkuService self ;

    @Autowired
    IFmbxSuiteExtService suiteExtService;

    @Autowired
    IFmbxSkuService skuService;

    @Autowired
    IFmbxHotelReserveSkuService hotelReserveSkuService;

    @Autowired
    IFmbxHotelReserveSkuExtService hotelReserveSkuExtService;

    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService;

    @Autowired
    IFmbxActivityService activityService;

    @Autowired
    IFmbxBpsRoomService roomService;

    @Autowired
    SuiteDao suiteDao ;

    Random random = new Random();

    @DSTransactional
    public void addSuite(HashMap<String, Object> resultMap, SuiteInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);

        LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxActivity::getXaid, input1.getXaid());
        FmbxActivity a = activityService.getOne(wr);
        if (a == null) {
            throw new BadLogicException("产品不存在");
        }
        input1.setSuiteId(null);
        LocalDateTime now = LocalDateTime.now();
        input1.setCtime(now);
        input1.setUtime(now);
        //pre_reserve_second + 预定日0时 after now 可预定；pre_reserve_second = pre_reserve_day*24*3600 + pre_reserve_time
        input1.setPreReserveSecond(FmbDateUtil.getEndtimeSec(input1.getPreReserveDay(), input1.getPreReserveTime()));
        //return_valid_second + endTime after now 可退；return_valid_second = (return_valid_day+1)*24*3600 + return_valid_time
        input1.setReturnValidSecond(FmbDateUtil.getEndtimeSec(input1.getReturnValidDay()+1, input1.getReturnValidTime()));

        Integer flagStandardHotel = input1.getFlagStandardHotel();
        Integer flagHotelReserve = input1.getFlagHotelReserve();
        if (flagStandardHotel == 0 && flagHotelReserve == 0) {
            throw new BadLogicException("请选择售卖形式");
        }
        FmbxSuite dbSuiteAdd = new FmbxSuite();
        BeanUtils.copyProperties(input1, dbSuiteAdd);
        if (dbSuiteAdd.getTotalNeight()==null || dbSuiteAdd.getTotalNeight()<=1){
            dbSuiteAdd.setNightMin(1);
        }
        dbSuiteAdd.setIsReturn(0);
        dbSuiteAdd.setIsAutoReturn(0);


        boolean save = suiteService.save(dbSuiteAdd);
        if (save) {
            //套餐扩展信息
            FmbxSuiteExt ext = new FmbxSuiteExt();
            BeanUtils.copyProperties(input1, ext);
            if (StringUtils.isBlank(ext.getBuyNote())) {
                ext.setBuyNote("");
            }
            if (StringUtils.isBlank(ext.getSmsPaySuccess())) {
                ext.setSmsPaySuccess("");
            }
            if (StringUtils.isBlank(ext.getSmsSecondConfirm())) {
                ext.setSmsSecondConfirm("");
            }
            if (StringUtils.isBlank(ext.getSmsReorder())) {
                ext.setSmsReorder("");
            }
            if (StringUtils.isBlank(ext.getSmsResSuccess())) {
                ext.setSmsResSuccess("");
            }
            if (StringUtils.isBlank(ext.getRoomOrderNote())) {
                ext.setRoomOrderNote("");
            }
            if (StringUtils.isBlank(ext.getInteriorNote())) {
                ext.setInteriorNote("");
            }
            ext.setSuiteId(dbSuiteAdd.getSuiteId());
            ext.setCtime(now);
            ext.setUtime(now);
            suiteExtService.save(ext);

            List<FmbxSuiteRoomSku> roomList = input1.getRoomList();
            if (roomList == null || roomList.size() == 0) {
                throw new BadLogicException("请关联套餐的房型");
            }
            for (FmbxSuiteRoomSku roomSku : roomList) {
                //sku
                String skuName = input1.getName() +"("+ roomSku.getRoomName()+")";
                FmbxSku sku = new FmbxSku();
                sku.setSkuId(null);
                sku.setSkuName(skuName);
                sku.setXaid(a.getXaid());
                sku.setBpsId(a.getBpsId());
                sku.setBpId(a.getBpId());
                sku.setSkuType(1);//日历房
                sku.setCreateUid(nowUserInfo.getUserId());
                sku.setCreateUidName(nowUserInfo.getUsername());
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setCtime(now);
                sku.setUtime(now);
                skuService.save(sku);
                //日历房sku
                roomSku.setSkuId(sku.getSkuId());
                roomSku.setSuiteId(dbSuiteAdd.getSuiteId());
                roomSku.setSkuName(skuName);
                roomSku.setFlagSell(0);//无效
                roomSku.setStatus(1);//已上架
                roomSku.setCtime(now);
                roomSku.setUtime(now);
                logger.info("suiteRoomSkuServiceSave1 {}",roomSku);
                suiteRoomSkuService.save(roomSku);
            }

            //房劵sku
            if (flagHotelReserve == 1) {

                //添加单一房券
                input1.setHotelReserveSplit(2);
                createReserveSku(resultMap, input1, nowUserInfo, a, now, dbSuiteAdd, null);
                //添加拆分房券
                input1.setHotelReserveSplit(1);
                for (FmbxSuiteRoomSku roomSku : roomList) {
                    createReserveSku(resultMap, input1, nowUserInfo, a, now, dbSuiteAdd, roomSku);
                }

                Wrapper<FmbxSuite> wrSuiteUpdate = new LambdaUpdateWrapper<FmbxSuite>()
                        .eq(FmbxSuite::getSuiteId, dbSuiteAdd.getSuiteId())
                        .set(FmbxSuite::getHotelReserveSplit, input1.getHotelReserveSplit());
                suiteService.update(wrSuiteUpdate);

            }
        }
        resultMap.put("result", 1);
        resultMap.put("suite_id", dbSuiteAdd.getSuiteId());
    }

    private void createReserveSku(HashMap<String, Object> resultMap, SuiteInfo input1, SessionUser nowUserInfo, FmbxActivity a,
                                  LocalDateTime now, FmbxSuite db,FmbxSuiteRoomSku dateRoomSku) {
        String reserveSku = input1.getName() + FmbConstants.RESERVE_SKU_NAME_POSTFIX;
        FmbxSku sku = new FmbxSku();
        sku.setSkuName(reserveSku);
        sku.setXaid(a.getXaid());
        sku.setBpsId(a.getBpsId());
        sku.setBpId(a.getBpId());
        sku.setSkuType(2);//房券
        sku.setCreateUid(nowUserInfo.getUserId());
        sku.setCreateUidName(nowUserInfo.getUsername());
        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        sku.setCtime(now);
        sku.setUtime(now);
        skuService.save(sku);
        FmbxHotelReserveSku hotelReserveSku = new FmbxHotelReserveSku();
        hotelReserveSku.setSkuId(sku.getSkuId());
        hotelReserveSku.setSuiteId(db.getSuiteId());
        hotelReserveSku.setSkuName(reserveSku);
        hotelReserveSku.setFlagSell(0);//无效
        hotelReserveSku.setStatus(3);//待完善
        hotelReserveSku.setCtime(now);
        hotelReserveSku.setUtime(now);
        if (dateRoomSku != null) {
            hotelReserveSku.setRoomId(dateRoomSku.getRoomId());
            hotelReserveSku.setHotelDateSkuId(dateRoomSku.getSkuId());
        }

        final boolean save1 = hotelReserveSkuService.save(hotelReserveSku);

        HotelReserveSkuInfo hotelReserveSkuInfo = new HotelReserveSkuInfo();
        BeanUtils.copyProperties(input1,hotelReserveSkuInfo);
        hotelReserveSkuInfo.setSuiteId(db.getSuiteId());
        hotelReserveSkuInfo.setSkuId(sku.getSkuId());
        hotelReserveSkuInfo.setSmsPaySuccess(StringUtils.isBlank(input1.getResSmsPaySuccess())?"": input1.getResSmsPaySuccess());
        this.updateHotelReserve(resultMap,hotelReserveSkuInfo);

        if (save1) {

            addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSku.getSkuId(),
                    hotelReserveSku.getSellStartTime(),hotelReserveSku.getSellEndTime());

        }
    }

    /**
     * 增加房券sku 开始售卖和结束售卖 定时任务
     * @param skuId
     * @param sellStartTime
     * @param sellEndTime
     */
    public void addHotelReserveSkuSellStartEndScheduleJob(Integer skuId, LocalDateTime sellStartTime, LocalDateTime sellEndTime) {
        final LocalDateTime now = LocalDateTime.now();
        if (sellStartTime != null && sellStartTime.isAfter(now)) {
                HashMap<String, Object> emp = new HashMap<>();
                emp.put("skutype",2) ;
                FmbScheduleUtil.addScheduleJob(FmbScheduleUtil.JOB_NAME_SKU_SELL_START, skuId, emp,
                        FmbDateUtil.localDateTime2Date(sellStartTime));
        }
        if (sellEndTime!=null && sellEndTime.isAfter(now)) {
            HashMap<String, Object> emp = new HashMap<>();
            emp.put("skutype",2) ;
            FmbScheduleUtil.addScheduleJob(FmbScheduleUtil.JOB_NAME_SKU_SELL_END, skuId, emp,
                    FmbDateUtil.localDateTime2Date(sellEndTime)  ) ;
        }
    }

    @DSTransactional
    public void updateSuite(HashMap<String, Object> resultMap, SuiteInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);

        //先查查是否存在
        FmbxSuite suitedb = suiteService.getById(input1.getSuiteId());
        if (suitedb == null) {
            throw new BadLogicException("套餐不存在" + input1.getSuiteId());
        }
        LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxActivity::getXaid, input1.getXaid());
        FmbxActivity a = activityService.getOne(wr);
        if (a == null) {
            throw new BadLogicException("产品不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        suitedb.setUtime(now);
        suitedb.setPreReserveSecond(FmbDateUtil.getEndtimeSec(input1.getPreReserveDay(), input1.getPreReserveTime()));
        suitedb.setReturnValidSecond(FmbDateUtil.getEndtimeSec(input1.getReturnValidDay()+1, input1.getReturnValidTime()));

        BeanUtils.copyProperties(input1, suitedb);
        if (suitedb.getTotalNeight()==null || suitedb.getTotalNeight()<=1){
            suitedb.setNightMin(1);
        }
        suitedb.setIsReturn(0);
        suitedb.setIsAutoReturn(0);
        suiteService.updateById(suitedb);

        FmbxSuiteExt ext = suiteExtService.getById(input1.getSuiteId());
        if (ext != null) {
            ext.setBuyNote(input1.getBuyNote());
            ext.setSmsPaySuccess(input1.getSmsPaySuccess());
            ext.setSmsSecondConfirm(input1.getSmsSecondConfirm());
            ext.setSmsReorder(input1.getSmsReorder());
            ext.setSmsResSuccess(input1.getSmsResSuccess());
            ext.setRoomOrderNote(input1.getRoomOrderNote());
            ext.setInteriorNote(input1.getInteriorNote());
            ext.setUtime(now);

            suiteExtService.updateById(ext);
        }
        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .select(FmbxSuiteRoomSku::getRoomId)
                .eq(FmbxSuiteRoomSku::getSuiteId, input1.getSuiteId());
        List<FmbxSuiteRoomSku> updList = input1.getRoomList();
        if (updList != null && updList.size() > 0) {
            List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
            List<Integer> dbRoomList = dbList.stream().map(FmbxSuiteRoomSku::getRoomId).collect(Collectors.toList());
            List<Integer> upRoomList = updList.stream().map(FmbxSuiteRoomSku::getRoomId).collect(Collectors.toList());
            //删除dbList-updList
            List<Integer> rmRoomList = dbRoomList.stream().filter(entity -> !upRoomList.contains(entity)).collect(Collectors.toList());
            if (rmRoomList != null && rmRoomList.size() > 0) {
                String room = "";
                for (FmbxSuiteRoomSku roomSku : dbList) {
                    //room += sku.getRoomId() + ",";
                    if(rmRoomList.contains(roomSku.getRoomId())){
                        LambdaQueryWrapper<FmbxSuiteRoomSku> wrsku = new LambdaQueryWrapper<>();
                        wrsku.eq(FmbxSuiteRoomSku::getRoomId, roomSku.getRoomId()).eq(FmbxSuiteRoomSku::getSuiteId, suitedb.getSuiteId());
                        FmbxSuiteRoomSku roomSkudb = suiteRoomSkuService.getOne(wrsku);
                        if (roomSkudb != null) {
                            roomSkudb.setStatus(0);
                            roomSkudb.setUtime(now);
                            suiteRoomSkuService.updateById(roomSkudb);
                            SuiteService.sendHotelSkuChangeNotify(roomSkudb.getSkuId());
                        }
                    }
                }
                //throw new BadLogicException("需勾选已有房型sku" + room);
            }
            //新增updList-dbList
            List<Integer> addRoomList = upRoomList.stream().filter(entity -> !dbRoomList.contains(entity)).collect(Collectors.toList());
            if (addRoomList != null && addRoomList.size() > 0) {
                for (FmbxSuiteRoomSku roomSku : updList) {
                    if (addRoomList.contains(roomSku.getRoomId())) {
                        String skuName = suitedb.getName() +"("+ roomSku.getRoomName()+")";
                        FmbxSku sku = new FmbxSku();
                        sku.setSkuId(null);
                        sku.setSkuName(skuName);
                        sku.setXaid(a.getXaid());
                        sku.setBpsId(a.getBpsId());
                        sku.setBpId(a.getBpId());
                        sku.setSkuType(1);//日历房
                        sku.setCreateUid(nowUserInfo.getUserId());
                        sku.setCreateUidName(nowUserInfo.getUsername());
                        sku.setLastUpdateUid(nowUserInfo.getUserId());
                        sku.setLastUpdateUidName(nowUserInfo.getUsername());
                        sku.setCtime(now);
                        sku.setUtime(now);
                        skuService.save(sku);
                        //日历房sku
                        roomSku.setSkuId(sku.getSkuId());
                        roomSku.setSuiteId(suitedb.getSuiteId());
                        roomSku.setSkuName(skuName);
                        roomSku.setFlagSell(0);//无效
                        roomSku.setStatus(1);//已上架
                        roomSku.setCtime(now);
                        roomSku.setUtime(now);
                        logger.info("suiteRoomSkuServiceSave2 {}", roomSku);
                        suiteRoomSkuService.save(roomSku);
                        SuiteService.sendHotelSkuChangeNotify(sku.getSkuId());
                    }
                }
            }
            //修改updList-dbList
            List<Integer> updRoomList = upRoomList.stream().filter(entity -> !addRoomList.contains(entity)).collect(Collectors.toList());
            if (updRoomList != null && updRoomList.size() > 0) {
                for (FmbxSuiteRoomSku roomSku : updList) {
                    LambdaQueryWrapper<FmbxSuiteRoomSku> wrsku = new LambdaQueryWrapper<>();
                    wrsku.eq(FmbxSuiteRoomSku::getRoomId, roomSku.getRoomId()).eq(FmbxSuiteRoomSku::getSuiteId, suitedb.getSuiteId());
                    FmbxSuiteRoomSku roomSkudb = suiteRoomSkuService.getOne(wrsku);
                    if (roomSkudb != null) {
                        roomSku.setSkuId(roomSkudb.getSkuId());
                    }
                    if (updRoomList.contains(roomSku.getRoomId())) {
                        String skuName = suitedb.getName() +"("+ roomSku.getRoomName()+")";
                        if (roomSkudb.getStatus()==0) {
                            roomSkudb.setStatus(1);
                        }
                        roomSkudb.setSkuName(skuName);
                        roomSkudb.setUtime(now);
                        suiteRoomSkuService.updateById(roomSkudb);
                        FmbxSku sku = skuService.getById(roomSkudb.getSkuId());
                        sku.setSkuName(skuName);
                        sku.setLastUpdateUid(nowUserInfo.getUserId());
                        sku.setLastUpdateUidName(nowUserInfo.getUsername());
                        sku.setUtime(now);
                        skuService.updateById(sku);
                        SuiteService.sendHotelSkuChangeNotify(sku.getSkuId());
                    }
                }
            }
        }
        //房券sku
        if (suitedb.getFlagHotelReserve() == 1) {
            //单一房券
            HotelReserveSkuInfo hotelReserveSkuInfo = new HotelReserveSkuInfo();
            BeanUtils.copyProperties(input1, hotelReserveSkuInfo);
            hotelReserveSkuInfo.setSmsPaySuccess(StringUtils.isBlank(input1.getResSmsPaySuccess()) ? "" : input1.getResSmsPaySuccess());
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, suitedb.getSuiteId())
                    .eq(FmbxHotelReserveSku::getHotelDateSkuId, 0)
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku != null) {
                if (hotelReserveSkuInfo != null ){
                    HashMap<String, Object> emp = new HashMap<>();
                    hotelReserveSkuInfo.setSkuId(hotelReserveSku.getSkuId());
                    hotelReserveSkuInfo.setIsReturn(input1.getIsReturn());
                    hotelReserveSkuInfo.setIsAutoReturn(input1.getIsAutoReturn());
                    hotelReserveSkuInfo.setReturnPolicy(input1.getReturnPolicy());
                    this.updateHotelReserve(emp,hotelReserveSkuInfo);
                }
                SuiteService.sendHotelSkuChangeNotify(hotelReserveSku.getSkuId());
            }else{
                createReserveSku(resultMap, input1, nowUserInfo, a, now, suitedb,null);
            }
            //拆分房券
            Wrapper<FmbxHotelReserveSku> wrSkuSplit = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId,input1.getSuiteId())
                    .gt(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .ne(FmbxHotelReserveSku::getStatus, 0)
                    .select(FmbxHotelReserveSku::getRoomId);
            if (updList != null && updList.size() > 0) {
                List<FmbxHotelReserveSku> dbList = hotelReserveSkuService.list(wrSkuSplit);
                List<Integer> dbRoomList = dbList.stream().map(FmbxHotelReserveSku::getRoomId).collect(Collectors.toList());
                List<Integer> upRoomList = updList.stream().map(FmbxSuiteRoomSku::getRoomId).collect(Collectors.toList());
                //删除dbList-updList
                List<Integer> rmRoomList = dbRoomList.stream().filter(entity -> !upRoomList.contains(entity)).collect(Collectors.toList());
                if (rmRoomList != null && rmRoomList.size() > 0) {
                    for (FmbxHotelReserveSku reserveSku : dbList) {
                        if(rmRoomList.contains(reserveSku.getRoomId())){
                            LambdaQueryWrapper<FmbxHotelReserveSku> wrsku = new LambdaQueryWrapper<>();
                            wrsku.eq(FmbxHotelReserveSku::getRoomId, reserveSku.getRoomId())
                                    .eq(FmbxHotelReserveSku::getSuiteId, suitedb.getSuiteId())
                                    .ne(FmbxHotelReserveSku::getStatus, 0);
                            List<FmbxHotelReserveSku> reserveSkuList = hotelReserveSkuService.list(wrsku);
                            if (reserveSkuList!=null && reserveSkuList.size()>0) {
                                FmbxHotelReserveSku reserveSkudb = reserveSkuList.get(0);
                                if (reserveSkudb != null) {
                                    reserveSkudb.setStatus(0);
                                    reserveSkudb.setUtime(now);
                                    hotelReserveSkuService.updateById(reserveSkudb);
                                    SuiteService.sendHotelSkuChangeNotify(reserveSkudb.getSkuId());
                                }
                            }
                        }
                    }
                }
                //新增updList-dbList
                List<Integer> addRoomList = upRoomList.stream().filter(entity -> !dbRoomList.contains(entity)).collect(Collectors.toList());
                if (addRoomList != null && addRoomList.size() > 0) {
                    for (FmbxSuiteRoomSku roomSku : updList) {
                        if (addRoomList.contains(roomSku.getRoomId())) {
                            createReserveSku(resultMap, input1, nowUserInfo, a, now, suitedb,roomSku);
                        }
                    }
                }
                //修改updList-dbList
                List<Integer> updRoomList = upRoomList.stream().filter(entity -> !addRoomList.contains(entity)).collect(Collectors.toList());
                if (updRoomList != null && updRoomList.size() > 0) {
                    for (FmbxSuiteRoomSku roomSku : updList) {
                        if (updRoomList.contains(roomSku.getRoomId())) {
                            //String skuName = suitedb.getName();
                            LambdaQueryWrapper<FmbxHotelReserveSku> wrsku = new LambdaQueryWrapper<>();
                            wrsku.eq(FmbxHotelReserveSku::getRoomId, roomSku.getRoomId())
                                    .eq(FmbxHotelReserveSku::getSuiteId, suitedb.getSuiteId())
                                    .ne(FmbxHotelReserveSku::getStatus, 0);
                            List<FmbxHotelReserveSku> reserveSkuList = hotelReserveSkuService.list(wrsku);
                            if (reserveSkuList!=null && reserveSkuList.size()>0) {
                                FmbxHotelReserveSku reserveSkudb = reserveSkuList.get(0);
                                //reserveSkudb.setSkuName(skuName);
                                reserveSkudb.setUtime(now);
                                reserveSkudb.setIsReturn(input1.getIsReturn());
                                reserveSkudb.setIsAutoReturn(input1.getIsAutoReturn());
                                reserveSkudb.setReturnPolicy(input1.getReturnPolicy());
                                hotelReserveSkuService.updateById(reserveSkudb);
                                FmbxSku sku = skuService.getById(reserveSkudb.getSkuId());
                                //sku.setSkuName(skuName);
                                sku.setLastUpdateUid(nowUserInfo.getUserId());
                                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                                sku.setUtime(now);
                                skuService.updateById(sku);
                                SuiteService.sendHotelSkuChangeNotify(sku.getSkuId());
                            }
                        }
                    }
                }
            }
        }
        Wrapper<FmbxHotelReserveSku> skuExtUpdate = new LambdaUpdateWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId,input1.getSuiteId());
        List<FmbxHotelReserveSku> skuList = hotelReserveSkuService.list(skuExtUpdate);
        for (FmbxHotelReserveSku sku : skuList) {
            FmbxHotelReserveSkuExt reserveSkuExt = hotelReserveSkuExtService.getById(sku.getSkuId());
            if (reserveSkuExt != null) {
                BeanUtils.copyProperties(input1, reserveSkuExt);
                reserveSkuExt.setSmsPaySuccess(input1.getResSmsPaySuccess());
                reserveSkuExt.setSkuId(sku.getSkuId());
                reserveSkuExt.setBuyNote(ext.getBuyNote());
                hotelReserveSkuExtService.updateById(reserveSkuExt);
            } else {
                reserveSkuExt = new FmbxHotelReserveSkuExt();
                BeanUtils.copyProperties(input1, reserveSkuExt);
                reserveSkuExt.setSmsPaySuccess(input1.getResSmsPaySuccess());
                reserveSkuExt.setSkuId(sku.getSkuId());
                reserveSkuExt.setBuyNote(ext.getBuyNote());
                hotelReserveSkuExtService.save(reserveSkuExt);
            }
        }
        resultMap.put("result", 1);
    }

    public void suiteDetail(HashMap<String, Object> resultMap, Integer suiteId) {

        SuiteInfo re = new SuiteInfo();
        FmbxSuite suite = suiteService.getById(suiteId);
        BeanUtils.copyProperties(suite, re);

        FmbxSuiteExt extOne = suiteExtService.getById(suiteId);
        BeanUtils.copyProperties(extOne, re);

        Wrapper<FmbxSuiteRoomSku> wrapRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .select(FmbxSuiteRoomSku::getSkuId, FmbxSuiteRoomSku::getRoomId, FmbxSuiteRoomSku::getRoomName, FmbxSuiteRoomSku::getSellNumTotal)
                .eq(FmbxSuiteRoomSku::getSuiteId, suiteId)
                .ne(FmbxSuiteRoomSku::getStatus, 0);
        List<FmbxSuiteRoomSku> roomList = suiteRoomSkuService.list(wrapRoomSku);
        int sellNumTotal = roomList.stream().collect(Collectors.summingInt(FmbxSuiteRoomSku::getSellNumTotal));
        re.setRoomList(roomList);

        if (suite.getFlagHotelReserve() == 1) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                    .eq(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            HotelReserveSkuInfo hotelReserve = new HotelReserveSkuInfo();
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku == null) {
                throw new BadLogicException("房券SKU不存在");
            }
            FmbxHotelReserveSkuExt ext = hotelReserveSkuExtService.getById(hotelReserveSku.getSkuId());
            BeanUtils.copyProperties(hotelReserveSku,hotelReserve);
            if (ext!=null){
                BeanUtils.copyProperties(ext,hotelReserve);
            }
            sellNumTotal += hotelReserveSku.getSellNumTotal();

            Wrapper<FmbxHotelReserveSku> wrSkuSplit = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId,suite.getSuiteId())
                    .gt(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .ne(FmbxHotelReserveSku::getStatus, 0)
                    .select(FmbxHotelReserveSku::getSkuId,FmbxHotelReserveSku::getMarketPrice,FmbxHotelReserveSku::getGoodsPrice,
                            FmbxHotelReserveSku::getSettlePrice,FmbxHotelReserveSku::getStockNum,FmbxHotelReserveSku::getRoomId)
                    ;
            final List<FmbxHotelReserveSku> list = hotelReserveSkuService.list(wrSkuSplit);
            for (FmbxHotelReserveSku reserveSku : list) {

                Wrapper<FmbxBpsRoom> wrRoom = new LambdaQueryWrapper<FmbxBpsRoom>()
                        .eq(FmbxBpsRoom::getRoomId,reserveSku.getRoomId())
                        .select(FmbxBpsRoom::getRoomId,FmbxBpsRoom::getRoomName)
                        ;
                final FmbxBpsRoom roomOne = roomService.getOne(wrRoom);
                reserveSku.setRoomName(roomOne.getRoomName());

            }

            resultMap.put("hotelReserve", hotelReserve);
            resultMap.put("splitSkus", list);
            re.setIsReturn(hotelReserve.getIsReturn());
            re.setReturnPolicy(hotelReserve.getReturnPolicy());
            re.setIsAutoReturn(hotelReserve.getIsAutoReturn());
        }
        if (sellNumTotal>0){
            re.setHasSold(1);
        }else{
            re.setHasSold(0);
        }
        resultMap.put("suite", re);

    }

    @DSTransactional
    public void copySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        FmbxSuiteExt suiteExt = suiteExtService.getById(suiteId);
        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxSuiteRoomSku::getStatus, 0);
        List<FmbxSuiteRoomSku> skuRoomList = suiteRoomSkuService.list(wrapSku);

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        List<FmbxHotelReserveSku> hotelReserveSkuList = hotelReserveSkuService.list(wrapHotelReserveSku);

        LocalDateTime now = LocalDateTime.now();

        FmbxSuite suiteCp = new FmbxSuite();
        BeanUtils.copyProperties(suite, suiteCp);
        if (suiteCp.getTotalNeight()==null || suiteCp.getTotalNeight()<=1){
            suiteCp.setNightMin(1);
        }
        suiteCp.setSuiteId(null);
        suiteCp.setName(suite.getName()+"_副本"+ random.nextInt(100));
        suiteCp.setFlagHaveOrder(0);
        suiteCp.setFlagHaveOrderReserve(0);
        suiteCp.setCtime(now);
        suiteCp.setUtime(now);
        boolean save = suiteService.save(suiteCp);
        if (save) {

            if (suiteExt != null) {
                FmbxSuiteExt extCp = new FmbxSuiteExt();

                BeanUtils.copyProperties(suiteExt, extCp);
                extCp.setSuiteId(suiteCp.getSuiteId());
                extCp.setCtime(now);
                extCp.setUtime(now);

                suiteExtService.save(extCp);
            }

            for (FmbxSuiteRoomSku roomSku : skuRoomList) {
                FmbxSku skuCp = new FmbxSku();
                FmbxSku sku = skuService.getById(roomSku.getSkuId());
                String skuName = suiteCp.getName()+"("+roomSku.getRoomName()+")";
                BeanUtils.copyProperties(sku, skuCp);
                skuCp.setSkuId(null);
                skuCp.setSkuName(skuName);
                skuCp.setCreateUid(nowUserInfo.getUserId());
                skuCp.setCreateUidName(nowUserInfo.getUsername());
                skuCp.setLastUpdateUid(nowUserInfo.getUserId());
                skuCp.setLastUpdateUidName(nowUserInfo.getUsername());
                skuCp.setCtime(now);
                skuCp.setUtime(now);
                skuService.save(skuCp);

                FmbxSuiteRoomSku roonSkuCp = new FmbxSuiteRoomSku();
                BeanUtils.copyProperties(roomSku, roonSkuCp);
                roonSkuCp.setSkuName(skuName);
                roonSkuCp.setSkuId(skuCp.getSkuId());
                roonSkuCp.setSuiteId(suiteCp.getSuiteId());
                roonSkuCp.setCtime(now);
                roonSkuCp.setUtime(now);
                roonSkuCp.setStatus(2);
                roonSkuCp.setFlagSell(0);
                roonSkuCp.setSellNum(0);
                roonSkuCp.setSellNumTotal(0);
                roonSkuCp.setMinGoodsPrice(0d);
                roonSkuCp.setMaxGoodsPrice(0d);
                roonSkuCp.setLastFullpriceOperateId(0);
                roonSkuCp.setLastAddpriceOperateId(0);
                logger.info("suiteRoomSkuServiceSave3 {}",roomSku);
                suiteRoomSkuService.save(roonSkuCp);
            }

            for (FmbxHotelReserveSku hotelReserveSku : hotelReserveSkuList) {
                FmbxSku skuCp = new FmbxSku();
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                String skuName = suiteCp.getName()+FmbConstants.RESERVE_SKU_NAME_POSTFIX;
                BeanUtils.copyProperties(sku, skuCp);
                skuCp.setSkuId(null);
                skuCp.setSkuName(skuName);
                skuCp.setCreateUid(nowUserInfo.getUserId());
                skuCp.setCreateUidName(nowUserInfo.getUsername());
                skuCp.setLastUpdateUid(nowUserInfo.getUserId());
                skuCp.setLastUpdateUidName(nowUserInfo.getUsername());
                skuCp.setCtime(now);
                skuCp.setUtime(now);
                skuService.save(skuCp);
                FmbxHotelReserveSku hotelReserveSkuNew = new FmbxHotelReserveSku();
                BeanUtils.copyProperties(hotelReserveSku, hotelReserveSkuNew);
                hotelReserveSkuNew.setSkuName(skuName);
                hotelReserveSkuNew.setSkuId(skuCp.getSkuId());
                hotelReserveSkuNew.setSuiteId(suiteCp.getSuiteId());
                if (hotelReserveSku.getRoomId() > 0) {
                    LambdaQueryWrapper<FmbxSuiteRoomSku> wrsku = new LambdaQueryWrapper<>();
                    wrsku.eq(FmbxSuiteRoomSku::getRoomId, hotelReserveSku.getRoomId()).eq(FmbxSuiteRoomSku::getSuiteId, hotelReserveSkuNew.getSuiteId());
                    FmbxSuiteRoomSku roomSkudb = suiteRoomSkuService.getOne(wrsku);
                    if (roomSkudb != null) {
                        hotelReserveSkuNew.setHotelDateSkuId(roomSkudb.getSkuId());
                    }
                }
                hotelReserveSkuNew.setCtime(now);
                hotelReserveSkuNew.setUtime(now);
                hotelReserveSkuNew.setStatus(2);
                hotelReserveSkuNew.setFlagSell(0);
                hotelReserveSkuNew.setSellNum(0);
                hotelReserveSkuNew.setSellNumTotal(0);
                hotelReserveSkuNew.setStockNum(0);
                hotelReserveSkuNew.setMarketPrice(BigDecimal.ZERO);
                hotelReserveSkuNew.setGoodsPrice(BigDecimal.ZERO);
                hotelReserveSkuNew.setSettlePrice(BigDecimal.ZERO);
                final boolean save1 = hotelReserveSkuService.save(hotelReserveSkuNew);
                if (save1) {

                    addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSkuNew.getSkuId(),hotelReserveSkuNew.getSellStartTime(),
                            hotelReserveSkuNew.getSellEndTime());

                }
            }

        }
        resultMap.put("result", 1);
        resultMap.put("suite_id", suite.getSuiteId());
        resultMap.put("copy_suite_id", suiteCp.getSuiteId());
    }

    @DSTransactional
    public void toggleSuite(HashMap<String, Object> resultMap, Integer suiteId, Integer status) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();

        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxSuiteRoomSku::getStatus, 0);
        List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
        for (FmbxSuiteRoomSku roomSku : dbList) {
            FmbxSku sku = skuService.getById(roomSku.getSkuId());
            if (sku != null) {
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setUtime(now);
                skuService.updateById(sku);
            }
            roomSku.setStatus(status);
            roomSku.setUtime(now);
            suiteRoomSkuService.updateById(roomSku);
            SuiteService.sendHotelSkuChangeNotify(roomSku.getSkuId());
        }

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        List<FmbxHotelReserveSku> hotelReserveSkuList = hotelReserveSkuService.list(wrapHotelReserveSku);
        for (FmbxHotelReserveSku hotelReserveSku : hotelReserveSkuList) {
            FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
            sku.setUtime(now);
            sku.setLastUpdateUid(nowUserInfo.getUserId());
            sku.setLastUpdateUidName(nowUserInfo.getUsername());
            skuService.updateById(sku);
            hotelReserveSku.setUtime(now);
            hotelReserveSku.setStatus(status);
            hotelReserveSkuService.updateById(hotelReserveSku);
            SuiteService.sendHotelSkuChangeNotify(hotelReserveSku.getSkuId());
        }
        suite.setUtime(now);
        suiteService.updateById(suite);
        resultMap.put("result", 1);
    }

    @DSTransactional
    public void deleteSuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();

        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId());
        List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
        Set<Integer> disableSkuId = new HashSet<>();//SellNum>0不能删
        for (FmbxSuiteRoomSku roomSku : dbList) {
            if (roomSku.getSellNumTotal()>0){
                disableSkuId.add(roomSku.getSkuId());
                continue;
            }
            FmbxSku sku = skuService.getById(roomSku.getSkuId());
            if (sku != null) {
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setUtime(now);
                skuService.updateById(sku);
            }
            roomSku.setStatus(0);
            roomSku.setUtime(now);
            suiteRoomSkuService.updateById(roomSku);
            SuiteService.sendHotelSkuDelete(roomSku.getSkuId());
        }

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        List<FmbxHotelReserveSku> hotelReserveSkuList = hotelReserveSkuService.list(wrapHotelReserveSku);
        for (FmbxHotelReserveSku hotelReserveSku : hotelReserveSkuList) {
            if (hotelReserveSku.getSellNumTotal()==0){
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                sku.setUtime(now);
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.updateById(sku);
                hotelReserveSku.setUtime(now);
                hotelReserveSku.setStatus(0);
                hotelReserveSkuService.updateById(hotelReserveSku);
                SuiteService.sendHotelSkuDelete(hotelReserveSku.getSkuId());
                suite.setFlagHotelReserve(0);
            }else{
                disableSkuId.add(hotelReserveSku.getSkuId());
            }
        }
        suite.setUtime(now);
        suiteService.updateById(suite);
        if(disableSkuId.size()==0) {
            resultMap.put("result", 1);
        }else{
            throw new BadLogicException("以下sku" + disableSkuId + "已售出，未删除");
        }
    }

    @DSTransactional
    public void toggleSku(HashMap<String, Object> resultMap, Integer skuId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        LocalDateTime now = LocalDateTime.now();

        FmbxSku sku = skuService.getById(skuId);
        if (sku == null) {
            throw new BadLogicException("SKU不存在" + skuId);
        }

        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        sku.setUtime(now);
        skuService.updateById(sku);

        if (sku.getSkuType()==1){
            FmbxSuiteRoomSku roomSku = suiteRoomSkuService.getById(skuId);
            putOnOffRoomSku(now, roomSku);
            suiteRoomSkuService.updateById(roomSku);
        }else if (sku.getSkuType()==2){
            FmbxHotelReserveSku reserveSku = hotelReserveSkuService.getById(skuId);
            putOnOffHotelReserveSku(now, reserveSku);
            hotelReserveSkuService.updateById(reserveSku);
        }
        SuiteService.sendHotelSkuChangeNotify(skuId);
        resultMap.put("result", 1);
    }

    private void putOnOffHotelReserveSku(LocalDateTime now, FmbxHotelReserveSku reserveSku) {
        reserveSku.setUtime(now);
        if(reserveSku.getStatus()==1) {
            reserveSku.setStatus(2);
        }else if(reserveSku.getStatus()==2){
            reserveSku.setStatus(1);
        }
    }

    private void putOnOffRoomSku(LocalDateTime now, FmbxSuiteRoomSku roomSku) {
        roomSku.setUtime(now);
        if(roomSku.getStatus()==1) {
            roomSku.setStatus(2);
        }else if(roomSku.getStatus()==2){
            roomSku.setStatus(1);
        }
    }

    @DSTransactional
    public void deleteSku(HashMap<String, Object> resultMap, Integer skuId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        LocalDateTime now = LocalDateTime.now();

        FmbxSku sku = skuService.getById(skuId);
        if (sku == null) {
            throw new BadLogicException("SKU不存在" + skuId);
        }

        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        sku.setUtime(now);
        skuService.updateById(sku);

        if (sku.getSkuType()==1){
            FmbxSuiteRoomSku roomSku = suiteRoomSkuService.getById(skuId);
            if (roomSku.getSellNumTotal()>0){
                throw new BadLogicException("SKU有销量不能删除" + skuId);
            }
            roomSku.setUtime(now);
            if(roomSku.getStatus()==0) {
                roomSku.setStatus(1);
                suiteRoomSkuService.updateById(roomSku);
            }else{
                roomSku.setStatus(0);
                suiteRoomSkuService.updateById(roomSku);
                Wrapper<FmbxSuiteRoomSku> wrapSuiteRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                        .eq(FmbxSuiteRoomSku::getSuiteId, roomSku.getSuiteId())
                        .ne(FmbxSuiteRoomSku::getStatus, 0);
                long count = suiteRoomSkuService.count(wrapSuiteRoomSku);
                if (count == 0) {
                    Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                            .eq(FmbxHotelReserveSku::getSuiteId, roomSku.getSuiteId())
                            .ne(FmbxHotelReserveSku::getStatus, 0);
                    List<FmbxHotelReserveSku> hotelReserveSkuList = hotelReserveSkuService.list(wrapHotelReserveSku);
                    for (FmbxHotelReserveSku hotelReserveSku : hotelReserveSkuList){
                        FmbxSuite suite = suiteService.getById(hotelReserveSku.getSuiteId());
                        suite.setFlagHotelReserve(0);
                        suite.setUtime(now);
                        suiteService.updateById(suite);
                        hotelReserveSku.setStatus(0);
                        hotelReserveSkuService.updateById(hotelReserveSku);
                        SuiteService.sendHotelSkuDelete(hotelReserveSku.getSkuId());
                    }
                }
            }
        }else if (sku.getSkuType()==2){
            FmbxHotelReserveSku reserveSku = hotelReserveSkuService.getById(skuId);
            if (reserveSku.getSellNumTotal()>0){
                throw new BadLogicException("SKU有销量不能删除" + skuId);
            }
            deReHotelReserveSku(now, reserveSku);
            hotelReserveSkuService.updateById(reserveSku);
            SuiteService.sendHotelSkuDelete(reserveSku.getSkuId());
        }
        SuiteService.sendHotelSkuDelete(skuId);
        resultMap.put("result", 1);
    }

    private void deReHotelReserveSku(LocalDateTime now, FmbxHotelReserveSku reserveSku) {
        reserveSku.setUtime(now);
        if(reserveSku.getStatus()==0) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, reserveSku.getSuiteId())
                    .eq(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku != null) {
                throw new BadLogicException("已存在房券SKU" + hotelReserveSku.getSuiteId());
            }
            Wrapper<FmbxSuiteRoomSku> wrapSuiteRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                    .eq(FmbxSuiteRoomSku::getSuiteId, reserveSku.getSuiteId())
                    .ne(FmbxSuiteRoomSku::getStatus, 0);
            long count = suiteRoomSkuService.count(wrapSuiteRoomSku);
            if (count == 0) {
                throw new BadLogicException("套餐要有日历房SKU" + reserveSku.getSuiteId());
            }
            FmbxSuite suite = suiteService.getById(reserveSku.getSuiteId());
            suite.setFlagHotelReserve(1);
            suite.setUtime(now);
            suiteService.updateById(suite);
            reserveSku.setStatus(1);
        }else{
            FmbxSuite suite = suiteService.getById(reserveSku.getSuiteId());
            suite.setFlagHotelReserve(0);
            suite.setUtime(now);
            suiteService.updateById(suite);
            reserveSku.setStatus(0);
        }
    }

    @DSTransactional
    public void updateHotelReserve(HashMap<String, Object> resultMap, HotelReserveSkuInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxHotelReserveSku db = hotelReserveSkuService.getById(input1.getSkuId());

        if (db == null) {
            throw new BadLogicException("房券SKU不存在" + input1.getSkuId());
        }
        final Integer suiteId = db.getSuiteId();

//        if (null==input1.getHotelReserveSplit()) {
//            input1.setHotelReserveSplit(1);
//        }

        LocalDateTime now = LocalDateTime.now();
//
        if (input1.getHotelReserveSplit() != null) {
            if (input1.getHotelReserveSplit()==1 || input1.getHotelReserveSplit()==2) {
                Wrapper<FmbxSuite> wrSuiteUpdate = new LambdaUpdateWrapper<FmbxSuite>()
                        .eq(FmbxSuite::getSuiteId,db.getSuiteId())
                        .set(FmbxSuite::getHotelReserveSplit,input1.getHotelReserveSplit());
                suiteService.update(wrSuiteUpdate) ;
            }





            if (input1.getHotelReserveSplit()==1) {
                for (SplitReserveSku splitSkus : input1.getSplitSkus()) {

                    FmbxSku skuSplit = new FmbxSku() ;
                    skuSplit.setSkuId(splitSkus.getSkuId()) ;
                    skuSplit.setUtime(now);
                    skuSplit.setLastUpdateUid(nowUserInfo.getUserId());
                    skuSplit.setLastUpdateUidName(nowUserInfo.getUsername());
                    skuService.updateById(skuSplit);
                }
            }else if (input1.getHotelReserveSplit()==2) {
                FmbxSku sku = skuService.getById(input1.getSkuId());
                sku.setUtime(now);
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.updateById(sku);

            }


        }






        BeanUtils.copyProperties(input1,db);
        if(StringUtils.isNotBlank(input1.getInvalidReserveWeekDays()) && !input1.getInvalidReserveWeekDays().contains("8,")){
            db.setInvalidReserveDate("");
        }
        db.setUtime(now);
        if (db.getStockNum()!=null && db.getStockNum() > 0 && db.getSellStartTime()!=null && now.isAfter(db.getSellStartTime())
                && db.getSellEndTime()!=null && now.isBefore(db.getSellEndTime())) {
            db.setFlagSell(1);//有效
        }else{
            db.setFlagSell(0);
        }
        if (db.getStatus() == 3) {
            db.setStatus(1);//已上架
        }
        FmbxSuiteExt suitext = suiteExtService.getById(suiteId);
        Wrapper<FmbxHotelReserveSku> skuExtUpdate = new LambdaUpdateWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId,db.getSuiteId());
        List<FmbxHotelReserveSku> skuList = hotelReserveSkuService.list(skuExtUpdate);
        for (FmbxHotelReserveSku sku : skuList) {
            FmbxHotelReserveSkuExt ext = hotelReserveSkuExtService.getById(sku.getSkuId());
            if (ext != null) {
                BeanUtils.copyProperties(input1, ext);
                ext.setSkuId(sku.getSkuId());
                ext.setBuyNote(suitext.getBuyNote());
                hotelReserveSkuExtService.updateById(ext);
            } else {
                ext = new FmbxHotelReserveSkuExt();
                BeanUtils.copyProperties(input1, ext);
                ext.setSkuId(sku.getSkuId());
                ext.setBuyNote(suitext.getBuyNote());
                hotelReserveSkuExtService.save(ext);
            }
        }

        if (input1.getHotelReserveSplit() != null) {
            if (input1.getHotelReserveSplit()==1) {
                db.setStatus(3);
            }else {
                db.setStatus(1);
            }
        }

        hotelReserveSkuService.updateById(db);
        SuiteService.sendHotelSkuChangeNotify(input1.getSkuId());


        for (SplitReserveSku splitSkus : input1.getSplitSkus()) {

            FmbxHotelReserveSku upRecord = new FmbxHotelReserveSku();
            BeanUtils.copyProperties(db,upRecord);

            upRecord.setSkuId(null);
            upRecord.setRoomId(null);
            upRecord.setHotelDateSkuId(null);
            upRecord.setMarketPrice(null);
            upRecord.setGoodsPrice(null);
            upRecord.setSettlePrice(null);
            upRecord.setStockNum(null);


            BeanUtils.copyProperties(splitSkus,upRecord);
            if (input1.getHotelReserveSplit()==2) {
                upRecord.setStatus(3);
            }else {
                upRecord.setStatus(1);
            }

            hotelReserveSkuService.updateById(upRecord) ;

            SuiteService.sendHotelSkuChangeNotify(splitSkus.getSkuId());
        }


        resultMap.put("result", 1);
    }

    @DSTransactional
    public void enabledStandardHotelBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();
        Integer flagStandardHotel = suite.getFlagStandardHotel();
        if (flagStandardHotel == 1) {
            suite.setFlagStandardHotel(0);
        } else if (flagStandardHotel == 0) {
            suite.setFlagStandardHotel(1);
        }
        suite.setUtime(now);
        suiteService.updateById(suite);

        SuiteService.sendActivityChangeNotify(suite.getXaid());

        resultMap.put("result", 1);

    }

    @DSTransactional
    public void enabledHotelReserveBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();
        Integer flagHotelReserve = suite.getFlagHotelReserve();
        if (flagHotelReserve == 1) {
            suite.setFlagHotelReserve(0);
        } else if (flagHotelReserve == 0) {
            suite.setFlagHotelReserve(1);
        }
        suite.setUtime(now);
        suiteService.updateById(suite);

        self.enabledHotelReserveBySuite(nowUserInfo, suite, now, null);

        resultMap.put("result", 1);

    }

    public void isFirstHotelReserveBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .eq(FmbxHotelReserveSku::getHotelDateSkuId,0)
                .ne(FmbxHotelReserveSku::getStatus, 0);
        FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
        if (hotelReserveSku != null) {
            resultMap.put("isFirstHotelReserve", 1);
        }else{
            resultMap.put("isFirstHotelReserve", 0);
        }
    }

    /**
     * todo 关闭可能有问题
     * @param nowUserInfo
     * @param suite
     * @param now
     * @return
     */
    public boolean enabledHotelReserveBySuite(SessionUser nowUserInfo, FmbxSuite suite, LocalDateTime now, HotelReserveSkuInfo hotelReserveSkuInfo) {
        boolean b2 = false;
        if (suite.getFlagHotelReserve() == 1) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                    .eq(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku != null) {
                String skuName = suite.getName()+FmbConstants.RESERVE_SKU_NAME_POSTFIX;
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                sku.setSkuName(skuName);
                sku.setUtime(now);
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.updateById(sku);
                hotelReserveSku.setSkuName(skuName);
                hotelReserveSku.setUtime(now);
                if (hotelReserveSku.getStockNum() > 0
                        && hotelReserveSku.getSellStartTime()!=null && now.isAfter(hotelReserveSku.getSellStartTime())
                        && hotelReserveSku.getSellEndTime()!=null && now.isBefore(hotelReserveSku.getSellEndTime())) {
                    hotelReserveSku.setFlagSell(1);//有效
                }else{
                    hotelReserveSku.setFlagSell(0);
                }
                hotelReserveSku.setStatus(1);//已上架
                b2 = hotelReserveSkuService.updateById(hotelReserveSku);
                if (hotelReserveSkuInfo != null ){
                    HashMap<String, Object> emp = new HashMap<>();
                    hotelReserveSkuInfo.setSkuId(hotelReserveSku.getSkuId());
                    this.updateHotelReserve(emp,hotelReserveSkuInfo);
                }
                SuiteService.sendHotelSkuChangeNotify(hotelReserveSku.getSkuId());
            }else{
                LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
                wr.eq(FmbxActivity::getXaid, suite.getXaid());
                FmbxActivity a = activityService.getOne(wr);
                if (a == null) {
                    throw new BadLogicException("产品不存在");
                }
                String reserveSku = suite.getName() + FmbConstants.RESERVE_SKU_NAME_POSTFIX;
                FmbxSku sku = new FmbxSku();
                sku.setSkuName(reserveSku);
                sku.setXaid(a.getXaid());
                sku.setBpsId(a.getBpsId());
                sku.setBpId(a.getBpId());
                sku.setSkuType(2);//房券
                sku.setCtime(now);
                sku.setCreateUid(nowUserInfo.getUserId());
                sku.setCreateUidName(nowUserInfo.getUsername());
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.save(sku);
                FmbxHotelReserveSku hotelReserveSkuNew = new FmbxHotelReserveSku();
                hotelReserveSkuNew.setSkuId(sku.getSkuId());
                hotelReserveSkuNew.setSuiteId(suite.getSuiteId());
                hotelReserveSkuNew.setSkuName(reserveSku);
                hotelReserveSkuNew.setFlagSell(0);//无效
                hotelReserveSkuNew.setStatus(3);//待完善
                hotelReserveSkuNew.setCtime(now);
                hotelReserveSkuNew.setUtime(now);
                b2 = hotelReserveSkuService.save(hotelReserveSkuNew);
                if (hotelReserveSkuInfo != null ){
                    HashMap<String, Object> emp = new HashMap<>();
                    hotelReserveSkuInfo.setSkuId(sku.getSkuId());
                    this.updateHotelReserve(emp,hotelReserveSkuInfo);
                }

                if (b2) {
                    addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSkuNew.getSkuId(),hotelReserveSkuNew.getSellStartTime(),
                            hotelReserveSkuNew.getSellEndTime());

                    SuiteService.sendHotelSkuChangeNotify(hotelReserveSkuNew.getSkuId());
                }

            }
        }else{
            b2 = true;

            Wrapper<FmbxHotelReserveSku> wrQ = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId,suite.getSuiteId())
                    .select(FmbxHotelReserveSku::getSkuId);
            List <FmbxHotelReserveSku> list = hotelReserveSkuService.list(wrQ);
            for (FmbxHotelReserveSku rsku : list){
                SuiteService.sendHotelSkuChangeNotify(rsku.getSkuId());
            }

        }
        return b2;
    }

    public void selectHotelReserveSkuMenu(HashMap<String,Object> resultMap, ReqHotelSku req) {

        List<HotelReserveSkuDomain> lists = suiteDao.selectHotelReserveSkuMenu(req);

        resultMap.put("skuInfo",lists) ;

    }

}
