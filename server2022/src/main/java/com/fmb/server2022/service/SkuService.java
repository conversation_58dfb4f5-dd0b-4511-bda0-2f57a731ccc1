package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.domain.HotelReserveSkuDomain;
import com.fmb.server2022.domain.suite.HotelReserveSkuInfo;
import com.fmb.server2022.domain.suite.SuiteInfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSkuExt;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteExt;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuExtService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteExtService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.FmbDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
public class SkuService {

    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    IFmbxSuiteService suiteService;

    @Autowired
    IFmbxSuiteExtService suiteExtService;

    @Autowired
    IFmbxSkuService skuService;

    @Autowired
    IFmbxHotelReserveSkuService hotelReserveSkuService;

    @Autowired
    IFmbxHotelReserveSkuExtService hotelReserveSkuExtService;

    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService;

    @Autowired
    IFmbxActivityService activityService;

    @Autowired
    SuiteDao suiteDao ;

    Random random = new Random();

    @DSTransactional
    public void addSuite(HashMap<String, Object> resultMap, SuiteInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);

        LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxActivity::getXaid, input1.getXaid());
        FmbxActivity a = activityService.getOne(wr);
        if (a == null) {
            throw new BadLogicException("产品不存在");
        }
        input1.setSuiteId(null);
        LocalDateTime now = LocalDateTime.now();
        input1.setCtime(now);
        input1.setUtime(now);
        input1.setPreReserveSecond(FmbDateUtil.getEndtimeSec(input1.getPreReserveDay(), input1.getPreReserveTime()));
        input1.setReturnValidSecond(FmbDateUtil.getEndtimeSec(input1.getReturnValidDay(), input1.getReturnValidTime()));

        Integer flagStandardHotel = input1.getFlagStandardHotel();
        Integer flagHotelReserve = input1.getFlagHotelReserve();
        if (flagStandardHotel == 0 && flagHotelReserve == 0) {
            throw new BadLogicException("请选择售卖形式");
        }
        FmbxSuite db = new FmbxSuite();
        BeanUtils.copyProperties(input1, db);
        boolean save = suiteService.save(db);
        if (save) {
            //套餐扩展信息
            FmbxSuiteExt ext = new FmbxSuiteExt();
            BeanUtils.copyProperties(input1, ext);
            if (StringUtils.isBlank(ext.getBuyNote())) {
                ext.setBuyNote("");
            }
            if (StringUtils.isBlank(ext.getSmsPaySuccess())) {
                ext.setSmsPaySuccess("");
            }
            if (StringUtils.isBlank(ext.getSmsSecondConfirm())) {
                ext.setSmsSecondConfirm("");
            }
            if (StringUtils.isBlank(ext.getSmsReorder())) {
                ext.setSmsReorder("");
            }
            if (StringUtils.isBlank(ext.getRoomOrderNote())) {
                ext.setRoomOrderNote("");
            }
            if (StringUtils.isBlank(ext.getInteriorNote())) {
                ext.setInteriorNote("");
            }
            ext.setSuiteId(db.getSuiteId());
            ext.setCtime(now);
            ext.setUtime(now);
            suiteExtService.save(ext);

            List<FmbxSuiteRoomSku> roomList = input1.getRoomList();
            if (roomList == null || roomList.size() == 0) {
                throw new BadLogicException("请关联套餐的房型");
            }
            for (FmbxSuiteRoomSku roomSku : roomList) {
                //sku
                String skuName = input1.getName() + roomSku.getRoomName();
                FmbxSku sku = new FmbxSku();
                sku.setSkuId(null);
                sku.setSkuName(skuName);
                sku.setXaid(a.getXaid());
                sku.setBpsId(a.getBpsId());
                sku.setBpId(a.getBpId());
                sku.setSkuType(1);//日历房
                sku.setCreateUid(nowUserInfo.getUserId());
                sku.setCreateUidName(nowUserInfo.getUsername());
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setCtime(now);
                sku.setUtime(now);
                skuService.save(sku);
                //日历房sku
                roomSku.setSkuId(sku.getSkuId());
                roomSku.setSuiteId(db.getSuiteId());
                roomSku.setSkuName(skuName);
                roomSku.setFlagSell(0);//无效
                roomSku.setStatus(1);//已上架
                roomSku.setCtime(now);
                roomSku.setUtime(now);
                suiteRoomSkuService.save(roomSku);
            }

            //房劵sku
            if (flagHotelReserve == 1) {
                String reserveSku = input1.getName() + "房劵";
                FmbxSku sku = new FmbxSku();
                sku.setSkuName(reserveSku);
                sku.setXaid(a.getXaid());
                sku.setBpsId(a.getBpsId());
                sku.setBpId(a.getBpId());
                sku.setSkuType(2);//房劵
                sku.setCreateUid(nowUserInfo.getUserId());
                sku.setCreateUidName(nowUserInfo.getUsername());
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setCtime(now);
                sku.setUtime(now);
                skuService.save(sku);
                FmbxHotelReserveSku hotelReserveSku = new FmbxHotelReserveSku();
                hotelReserveSku.setSkuId(sku.getSkuId());
                hotelReserveSku.setSuiteId(db.getSuiteId());
                hotelReserveSku.setSkuName(reserveSku);
                hotelReserveSku.setFlagSell(0);//无效
                hotelReserveSku.setStatus(3);//待完善
                hotelReserveSku.setCtime(now);
                hotelReserveSku.setUtime(now);
                final boolean save1 = hotelReserveSkuService.save(hotelReserveSku);
                if (save1) {
//                    hotelReserveSku.getsel

                    addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSku.getSkuId(),
                            hotelReserveSku.getSellStartTime(),hotelReserveSku.getSellEndTime());

                }
            }
        }
        resultMap.put("result", 1);
        resultMap.put("suite_id", db.getSuiteId());
    }

    /**
     * 增加房券sku 开始售卖和结束售卖 定时任务
     * @param skuId
     * @param sellStartTime
     * @param sellEndTime
     */
    public void addHotelReserveSkuSellStartEndScheduleJob(Integer skuId, LocalDateTime sellStartTime, LocalDateTime sellEndTime) {
        final LocalDateTime now = LocalDateTime.now();
        if (sellStartTime != null && sellStartTime.isAfter(now)) {
                HashMap<String, Object> emp = new HashMap<>();
                emp.put("skutype",2) ;
                FmbScheduleUtil.addScheduleJob(FmbScheduleUtil.JOB_NAME_SKU_SELL_START, skuId, emp,
                        FmbDateUtil.localDateTime2Date(sellStartTime));
        }
        if (sellEndTime!=null && sellEndTime.isAfter(now)) {
            HashMap<String, Object> emp = new HashMap<>();
            emp.put("skutype",2) ;
            FmbScheduleUtil.addScheduleJob(FmbScheduleUtil.JOB_NAME_SKU_SELL_END, skuId, emp,
                    FmbDateUtil.localDateTime2Date(sellEndTime)  ) ;
        }
    }

    @DSTransactional
    public void updateSuite(HashMap<String, Object> resultMap, SuiteInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);

        //先查查是否存在
        FmbxSuite suitedb = suiteService.getById(input1.getSuiteId());
        if (suitedb == null) {
            throw new BadLogicException("套餐不存在" + input1.getSuiteId());
        }
        LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxActivity::getXaid, input1.getXaid());
        FmbxActivity a = activityService.getOne(wr);
        if (a == null) {
            throw new BadLogicException("产品不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        suitedb.setUtime(now);
        suitedb.setPreReserveSecond(FmbDateUtil.getEndtimeSec(input1.getPreReserveDay(), input1.getPreReserveTime()));
        suitedb.setReturnValidSecond(FmbDateUtil.getEndtimeSec(input1.getReturnValidDay(), input1.getReturnValidTime()));

        BeanUtils.copyProperties(input1, suitedb);
        suiteService.updateById(suitedb);

        FmbxSuiteExt ext = suiteExtService.getById(input1.getSuiteId());
        if (ext != null) {
            ext.setBuyNote(input1.getBuyNote());
            ext.setSmsPaySuccess(input1.getSmsPaySuccess());
            ext.setSmsSecondConfirm(input1.getSmsSecondConfirm());
            ext.setSmsReorder(input1.getSmsReorder());
            ext.setRoomOrderNote(input1.getRoomOrderNote());
            ext.setInteriorNote(input1.getInteriorNote());
            ext.setUtime(now);

            suiteExtService.updateById(ext);
        }
        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .select(FmbxSuiteRoomSku::getRoomId)
                .eq(FmbxSuiteRoomSku::getSuiteId, input1.getSuiteId());
        List<FmbxSuiteRoomSku> updList = input1.getRoomList();
        if (updList != null && updList.size() > 0) {
            List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
            List<Integer> dbRoomList = dbList.stream().map(FmbxSuiteRoomSku::getRoomId).collect(Collectors.toList());
            List<Integer> upRoomList = updList.stream().map(FmbxSuiteRoomSku::getRoomId).collect(Collectors.toList());
            //dbList-updList不能去除
            List<Integer> rmRoomList = dbRoomList.stream().filter(entity -> !upRoomList.contains(entity)).collect(Collectors.toList());
            if (rmRoomList != null && rmRoomList.size() > 0) {
                String room = "";
                for (FmbxSuiteRoomSku sku : dbList) {
                    room += sku.getRoomId() + ",";
                }
                throw new BadLogicException("需勾选已有房型sku" + room);
            }
            //新增updList-dbList
            List<Integer> addRoomList = upRoomList.stream().filter(entity -> !dbRoomList.contains(entity)).collect(Collectors.toList());
            if (addRoomList != null && addRoomList.size() > 0) {
                for (FmbxSuiteRoomSku roomSku : updList) {
                    if (addRoomList.contains(roomSku.getRoomId())) {
                        String skuName = suitedb.getName() + roomSku.getRoomName();
                        FmbxSku sku = new FmbxSku();
                        sku.setSkuId(null);
                        sku.setSkuName(skuName);
                        sku.setXaid(a.getXaid());
                        sku.setBpsId(a.getBpsId());
                        sku.setBpId(a.getBpId());
                        sku.setSkuType(1);//日历房
                        sku.setCreateUid(nowUserInfo.getUserId());
                        sku.setCreateUidName(nowUserInfo.getUsername());
                        sku.setLastUpdateUid(nowUserInfo.getUserId());
                        sku.setLastUpdateUidName(nowUserInfo.getUsername());
                        sku.setCtime(now);
                        sku.setUtime(now);
                        skuService.save(sku);
                        //日历房sku
                        roomSku.setSkuId(sku.getSkuId());
                        roomSku.setSuiteId(suitedb.getSuiteId());
                        roomSku.setSkuName(skuName);
                        roomSku.setFlagSell(0);//无效
                        roomSku.setStatus(1);//已上架
                        roomSku.setCtime(now);
                        roomSku.setUtime(now);
                        suiteRoomSkuService.save(roomSku);
                    }
                }
            }
            //修改updList-dbList
            List<Integer> updRoomList = upRoomList.stream().filter(entity -> !addRoomList.contains(entity)).collect(Collectors.toList());
            if (updRoomList != null && updRoomList.size() > 0) {
                for (FmbxSuiteRoomSku roomSku : updList) {
                    if (updRoomList.contains(roomSku.getRoomId())) {
                        String skuName = suitedb.getName() + roomSku.getRoomName();
                        LambdaQueryWrapper<FmbxSuiteRoomSku> wrsku = new LambdaQueryWrapper<>();
                        wrsku.eq(FmbxSuiteRoomSku::getRoomId, roomSku.getRoomId()).eq(FmbxSuiteRoomSku::getSuiteId, suitedb.getSuiteId());
                        FmbxSuiteRoomSku roomSkudb = suiteRoomSkuService.getOne(wrsku);
                        roomSkudb.setSkuName(skuName);
                        roomSkudb.setUtime(now);
                        suiteRoomSkuService.updateById(roomSkudb);
                        FmbxSku sku = skuService.getById(roomSkudb.getSkuId());
                        sku.setSkuName(skuName);
                        sku.setLastUpdateUid(nowUserInfo.getUserId());
                        sku.setLastUpdateUidName(nowUserInfo.getUsername());
                        sku.setUtime(now);
                        skuService.updateById(sku);
                    }
                }
            }
        }
        //房劵sku
        enabledHotelReserveBySuite(nowUserInfo, suitedb, now);
        resultMap.put("result", 1);
    }

    public void suiteDetail(HashMap<String, Object> resultMap, Integer suiteId) {

        SuiteInfo re = new SuiteInfo();
        FmbxSuite suite = suiteService.getById(suiteId);
        BeanUtils.copyProperties(suite, re);

        FmbxSuiteExt extOne = suiteExtService.getById(suiteId);
        BeanUtils.copyProperties(extOne, re);

        Wrapper<FmbxSuiteRoomSku> wrapRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .select(FmbxSuiteRoomSku::getSkuId, FmbxSuiteRoomSku::getRoomId, FmbxSuiteRoomSku::getRoomName)
                .eq(FmbxSuiteRoomSku::getSuiteId, suiteId);
        List<FmbxSuiteRoomSku> roomList = suiteRoomSkuService.list(wrapRoomSku);
        re.setRoomList(roomList);

        resultMap.put("suite", re);
        if (suite.getFlagHotelReserve() == 1) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            HotelReserveSkuInfo hotelReserve = new HotelReserveSkuInfo();
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku == null) {
                throw new BadLogicException("房劵SKU不存在");
            }
            FmbxHotelReserveSkuExt ext = hotelReserveSkuExtService.getById(hotelReserveSku.getSkuId());
            BeanUtils.copyProperties(hotelReserveSku,hotelReserve);
            if (ext!=null){
                BeanUtils.copyProperties(ext,hotelReserve);
            }
            resultMap.put("hotelReserve", hotelReserve);
        }

    }

    @DSTransactional
    public void copySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        FmbxSuiteExt suiteExt = suiteExtService.getById(suiteId);
        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId());
        List<FmbxSuiteRoomSku> skuRoomList = suiteRoomSkuService.list(wrapSku);

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);

        LocalDateTime now = LocalDateTime.now();

        FmbxSuite suiteCp = new FmbxSuite();
        BeanUtils.copyProperties(suite, suiteCp);
        suiteCp.setSuiteId(null);
        suiteCp.setName(suite.getName()+"_副本"+ random.nextInt(100));
        suiteCp.setCtime(now);
        suiteCp.setUtime(now);
        boolean save = suiteService.save(suiteCp);
        if (save) {

            if (suiteExt != null) {
                FmbxSuiteExt extCp = new FmbxSuiteExt();

                BeanUtils.copyProperties(suiteExt, extCp);
                extCp.setSuiteId(suiteCp.getSuiteId());
                extCp.setCtime(now);
                extCp.setUtime(now);

                suiteExtService.save(extCp);
            }

            for (FmbxSuiteRoomSku roomSku : skuRoomList) {
                FmbxSku skuCp = new FmbxSku();
                FmbxSku sku = skuService.getById(roomSku.getSkuId());
                String skuName = suiteCp.getName()+roomSku.getRoomName();
                BeanUtils.copyProperties(sku, skuCp);
                skuCp.setSkuId(null);
                skuCp.setSkuName(skuName);
                skuCp.setCreateUid(nowUserInfo.getUserId());
                skuCp.setCreateUidName(nowUserInfo.getUsername());
                skuCp.setLastUpdateUid(nowUserInfo.getUserId());
                skuCp.setLastUpdateUidName(nowUserInfo.getUsername());
                skuCp.setCtime(now);
                skuCp.setUtime(now);
                skuService.save(skuCp);

                FmbxSuiteRoomSku roonSkuCp = new FmbxSuiteRoomSku();
                BeanUtils.copyProperties(roomSku, roonSkuCp);
                roonSkuCp.setSkuName(skuName);
                roonSkuCp.setSkuId(skuCp.getSkuId());
                roonSkuCp.setSuiteId(suiteCp.getSuiteId());
                roonSkuCp.setCtime(now);
                roonSkuCp.setUtime(now);
                roonSkuCp.setStatus(2);
                roonSkuCp.setFlagSell(0);
                roonSkuCp.setLastFullpriceOperateId(0);
                roonSkuCp.setLastAddpriceOperateId(0);
                suiteRoomSkuService.save(roonSkuCp);
            }

            if (hotelReserveSku != null) {
                FmbxSku skuCp = new FmbxSku();
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                String skuName = suiteCp.getName()+"房劵";
                BeanUtils.copyProperties(sku, skuCp);
                skuCp.setSkuId(null);
                skuCp.setSkuName(skuName);
                skuCp.setCreateUid(nowUserInfo.getUserId());
                skuCp.setCreateUidName(nowUserInfo.getUsername());
                skuCp.setLastUpdateUid(nowUserInfo.getUserId());
                skuCp.setLastUpdateUidName(nowUserInfo.getUsername());
                skuCp.setCtime(now);
                skuCp.setUtime(now);
                skuService.save(skuCp);
                FmbxHotelReserveSku hotelReserveSkuNew = new FmbxHotelReserveSku();
                BeanUtils.copyProperties(hotelReserveSku, hotelReserveSkuNew);
                hotelReserveSkuNew.setSkuName(skuName);
                hotelReserveSkuNew.setSkuId(skuCp.getSkuId());
                hotelReserveSkuNew.setSuiteId(suiteCp.getSuiteId());
                hotelReserveSkuNew.setCtime(now);
                hotelReserveSkuNew.setUtime(now);
                hotelReserveSkuNew.setStatus(2);
                hotelReserveSkuNew.setFlagSell(0);
                final boolean save1 = hotelReserveSkuService.save(hotelReserveSkuNew);
                if (save1) {

                    addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSkuNew.getSkuId(),hotelReserveSkuNew.getSellStartTime(),
                            hotelReserveSkuNew.getSellEndTime());

                }
            }

        }
        resultMap.put("result", 1);
        resultMap.put("suite_id", suite.getSuiteId());
        resultMap.put("copy_suite_id", suiteCp.getSuiteId());
    }

    @DSTransactional
    public void toggleSuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();

        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId());
        List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
        for (FmbxSuiteRoomSku roomSku : dbList) {
            FmbxSku sku = skuService.getById(roomSku.getSkuId());
            if (sku != null) {
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setUtime(now);
                skuService.updateById(sku);
            }
            roomSku.setStatus(2);
            roomSku.setUtime(now);
            suiteRoomSkuService.updateById(roomSku);
            SuiteService.sendHotelSkuChangeNotify(roomSku.getSkuId());
        }

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
        if (hotelReserveSku != null) {
            FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
            sku.setUtime(now);
            sku.setLastUpdateUid(nowUserInfo.getUserId());
            sku.setLastUpdateUidName(nowUserInfo.getUsername());
            skuService.updateById(sku);
            hotelReserveSku.setUtime(now);
            hotelReserveSku.setStatus(2);
            hotelReserveSkuService.updateById(hotelReserveSku);
            SuiteService.sendHotelSkuChangeNotify(hotelReserveSku.getSkuId());
        }
        suite.setUtime(now);
        suiteService.updateById(suite);
        resultMap.put("result", 1);
    }

    @DSTransactional
    public void deleteSuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();

        Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSuiteId, suite.getSuiteId());
        List<FmbxSuiteRoomSku> dbList = suiteRoomSkuService.list(wrapSku);
        for (FmbxSuiteRoomSku roomSku : dbList) {
            if (roomSku.getSellNum()>0){
                continue;
            }
            FmbxSku sku = skuService.getById(roomSku.getSkuId());
            if (sku != null) {
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                sku.setUtime(now);
                skuService.updateById(sku);
            }
            roomSku.setStatus(0);
            roomSku.setUtime(now);
            suiteRoomSkuService.updateById(roomSku);
            SuiteService.sendHotelSkuDelete(roomSku.getSkuId());
        }

        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
        if (hotelReserveSku != null) {
            if (hotelReserveSku.getSellNum()==0){
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                sku.setUtime(now);
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.updateById(sku);
                hotelReserveSku.setUtime(now);
                hotelReserveSku.setStatus(0);
                hotelReserveSkuService.updateById(hotelReserveSku);
                SuiteService.sendHotelSkuDelete(hotelReserveSku.getSkuId());
                suite.setFlagHotelReserve(0);
            }
        }
        suite.setUtime(now);
        suiteService.updateById(suite);
        resultMap.put("result", 1);
    }

    @DSTransactional
    public void toggleSku(HashMap<String, Object> resultMap, Integer skuId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        LocalDateTime now = LocalDateTime.now();

        FmbxSku sku = skuService.getById(skuId);
        if (sku == null) {
            throw new BadLogicException("SKU不存在" + skuId);
        }

        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        sku.setUtime(now);
        skuService.updateById(sku);

        if (sku.getSkuType()==1){
            FmbxSuiteRoomSku roomSku = suiteRoomSkuService.getById(skuId);
            putOnOffRoomSku(now, roomSku);
            suiteRoomSkuService.updateById(roomSku);
        }else if (sku.getSkuType()==2){
            FmbxHotelReserveSku reserveSku = hotelReserveSkuService.getById(skuId);
            putOnOffHotelReserveSku(now, reserveSku);
            hotelReserveSkuService.updateById(reserveSku);
        }
        SuiteService.sendHotelSkuChangeNotify(skuId);
        resultMap.put("result", 1);
    }

    private void putOnOffHotelReserveSku(LocalDateTime now, FmbxHotelReserveSku reserveSku) {
        reserveSku.setUtime(now);
        if(reserveSku.getStatus()==1) {
            reserveSku.setStatus(2);
        }else if(reserveSku.getStatus()==2){
            reserveSku.setStatus(1);
        }
    }

    private void putOnOffRoomSku(LocalDateTime now, FmbxSuiteRoomSku roomSku) {
        roomSku.setUtime(now);
        if(roomSku.getStatus()==1) {
            roomSku.setStatus(2);
        }else if(roomSku.getStatus()==2){
            roomSku.setStatus(1);
        }
    }

    @DSTransactional
    public void deleteSku(HashMap<String, Object> resultMap, Integer skuId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        LocalDateTime now = LocalDateTime.now();

        FmbxSku sku = skuService.getById(skuId);
        if (sku == null) {
            throw new BadLogicException("SKU不存在" + skuId);
        }

        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        sku.setUtime(now);
        skuService.updateById(sku);

        if (sku.getSkuType()==1){
            FmbxSuiteRoomSku roomSku = suiteRoomSkuService.getById(skuId);
            if (roomSku.getSellNum()>0){
                throw new BadLogicException("SKU有销量不能删除" + skuId);
            }
            roomSku.setUtime(now);
            if(roomSku.getStatus()==0) {
                roomSku.setStatus(1);
                suiteRoomSkuService.updateById(roomSku);
            }else{
                roomSku.setStatus(0);
                suiteRoomSkuService.updateById(roomSku);
                Wrapper<FmbxSuiteRoomSku> wrapSuiteRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                        .eq(FmbxSuiteRoomSku::getSuiteId, roomSku.getSuiteId())
                        .ne(FmbxSuiteRoomSku::getStatus, 0);
                long count = suiteRoomSkuService.count(wrapSuiteRoomSku);
                if (count == 0) {
                    Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                            .eq(FmbxHotelReserveSku::getSuiteId, roomSku.getSuiteId())
                            .ne(FmbxHotelReserveSku::getStatus, 0);
                    FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
                    if (hotelReserveSku != null) {
                        FmbxSuite suite = suiteService.getById(hotelReserveSku.getSuiteId());
                        suite.setFlagHotelReserve(0);
                        suite.setUtime(now);
                        suiteService.updateById(suite);
                        hotelReserveSku.setStatus(0);
                        hotelReserveSkuService.updateById(hotelReserveSku);
                        SuiteService.sendHotelSkuDelete(hotelReserveSku.getSkuId());
                    }
                }
            }
        }else if (sku.getSkuType()==2){
            FmbxHotelReserveSku reserveSku = hotelReserveSkuService.getById(skuId);
            if (reserveSku.getSellNum()>0){
                throw new BadLogicException("SKU有销量不能删除" + skuId);
            }
            deReHotelReserveSku(now, reserveSku);
            hotelReserveSkuService.updateById(reserveSku);
            SuiteService.sendHotelSkuDelete(reserveSku.getSkuId());
        }
        SuiteService.sendHotelSkuDelete(skuId);
        resultMap.put("result", 1);
    }

    private void deReHotelReserveSku(LocalDateTime now, FmbxHotelReserveSku reserveSku) {
        reserveSku.setUtime(now);
        if(reserveSku.getStatus()==0) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, reserveSku.getSuiteId())
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku != null) {
                throw new BadLogicException("已存在房劵SKU" + hotelReserveSku.getSuiteId());
            }
            Wrapper<FmbxSuiteRoomSku> wrapSuiteRoomSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                    .eq(FmbxSuiteRoomSku::getSuiteId, reserveSku.getSuiteId())
                    .ne(FmbxSuiteRoomSku::getStatus, 0);
            long count = suiteRoomSkuService.count(wrapSuiteRoomSku);
            if (count == 0) {
                throw new BadLogicException("套餐要有日历房SKU" + reserveSku.getSuiteId());
            }
            FmbxSuite suite = suiteService.getById(reserveSku.getSuiteId());
            suite.setFlagHotelReserve(1);
            suite.setUtime(now);
            suiteService.updateById(suite);
            reserveSku.setStatus(1);
        }else{
            FmbxSuite suite = suiteService.getById(reserveSku.getSuiteId());
            suite.setFlagHotelReserve(0);
            suite.setUtime(now);
            suiteService.updateById(suite);
            reserveSku.setStatus(0);
        }
    }

    @DSTransactional
    public void updateHotelReserve(HashMap<String, Object> resultMap, HotelReserveSkuInfo input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxHotelReserveSku db = hotelReserveSkuService.getById(input1.getSkuId());
        if (db == null) {
            throw new BadLogicException("套餐不存在" + input1.getSuiteId());
        }
        LocalDateTime now = LocalDateTime.now();

        FmbxSku sku = skuService.getById(input1.getSkuId());
        sku.setUtime(now);
        sku.setLastUpdateUid(nowUserInfo.getUserId());
        sku.setLastUpdateUidName(nowUserInfo.getUsername());
        skuService.updateById(sku);

        BeanUtils.copyProperties(input1,db);
        db.setUtime(now);
        if (db.getStockNum() > 0 && db.getSellStartTime()!=null && now.isAfter(db.getSellStartTime())
                && db.getSellEndTime()!=null && now.isBefore(db.getSellEndTime())) {
            db.setFlagSell(1);//有效
        }else{
            db.setFlagSell(0);
        }
        if (db.getStatus() == 3) {
            db.setStatus(1);//已上架
        }
        FmbxHotelReserveSkuExt ext = hotelReserveSkuExtService.getById(db.getSkuId());
        if(ext != null){
            BeanUtils.copyProperties(input1,ext);
            hotelReserveSkuExtService.updateById(ext);
        }else{
            ext = new FmbxHotelReserveSkuExt();
            BeanUtils.copyProperties(input1,ext);
            hotelReserveSkuExtService.save(ext);
        }
        hotelReserveSkuService.updateById(db);
        SuiteService.sendHotelSkuChangeNotify(input1.getSkuId());
        resultMap.put("result", 1);
    }

    @DSTransactional
    public void enabledStandardHotelBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();
        Integer flagStandardHotel = suite.getFlagStandardHotel();
        if (flagStandardHotel == 1) {
            suite.setFlagStandardHotel(0);
        } else if (flagStandardHotel == 0) {
            suite.setFlagStandardHotel(1);
        }
        suite.setUtime(now);
        suiteService.updateById(suite);

        resultMap.put("result", 1);

    }

    @DSTransactional
    public void enabledHotelReserveBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        LocalDateTime now = LocalDateTime.now();
        Integer flagHotelReserve = suite.getFlagHotelReserve();
        if (flagHotelReserve == 1) {
            suite.setFlagHotelReserve(0);
        } else if (flagHotelReserve == 0) {
            suite.setFlagHotelReserve(1);
        }
        suite.setUtime(now);
        suiteService.updateById(suite);

        enabledHotelReserveBySuite(nowUserInfo, suite, now);

        resultMap.put("result", 1);

    }

    public void isFirstHotelReserveBySuite(HashMap<String, Object> resultMap, Integer suiteId) {
        FmbxSuite suite = suiteService.getById(suiteId);
        if (suite == null) {
            throw new BadLogicException("套餐不存在" + suiteId);
        }
        Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                .ne(FmbxHotelReserveSku::getStatus, 0);
        FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
        if (hotelReserveSku != null) {
            resultMap.put("isFirstHotelReserve", 1);
        }else{
            resultMap.put("isFirstHotelReserve", 0);
        }
    }

    private boolean enabledHotelReserveBySuite(SessionUser nowUserInfo, FmbxSuite suite, LocalDateTime now) {
        boolean b2 = false;
        if (suite.getFlagHotelReserve() == 1) {
            Wrapper<FmbxHotelReserveSku> wrapHotelReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId, suite.getSuiteId())
                    .ne(FmbxHotelReserveSku::getStatus, 0);
            FmbxHotelReserveSku hotelReserveSku = hotelReserveSkuService.getOne(wrapHotelReserveSku);
            if (hotelReserveSku != null) {
                String skuName = suite.getName()+"房劵";
                FmbxSku sku = skuService.getById(hotelReserveSku.getSkuId());
                sku.setSkuName(skuName);
                sku.setUtime(now);
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.updateById(sku);
                hotelReserveSku.setSkuName(skuName);
                hotelReserveSku.setUtime(now);
                if (hotelReserveSku.getStockNum() > 0
                        && hotelReserveSku.getSellStartTime()!=null && now.isAfter(hotelReserveSku.getSellStartTime())
                        && hotelReserveSku.getSellEndTime()!=null && now.isBefore(hotelReserveSku.getSellEndTime())) {
                    hotelReserveSku.setFlagSell(1);//有效
                }else{
                    hotelReserveSku.setFlagSell(0);
                }
                hotelReserveSku.setStatus(1);//已上架
                b2 = hotelReserveSkuService.updateById(hotelReserveSku);
                SuiteService.sendHotelSkuChangeNotify(hotelReserveSku.getSkuId());
            }else{
                LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
                wr.eq(FmbxActivity::getXaid, suite.getXaid());
                FmbxActivity a = activityService.getOne(wr);
                if (a == null) {
                    throw new BadLogicException("产品不存在");
                }
                String reserveSku = suite.getName() + "房劵";
                FmbxSku sku = new FmbxSku();
                sku.setSkuName(reserveSku);
                sku.setXaid(a.getXaid());
                sku.setBpsId(a.getBpsId());
                sku.setBpId(a.getBpId());
                sku.setSkuType(2);//房劵
                sku.setCtime(now);
                sku.setCreateUid(nowUserInfo.getUserId());
                sku.setCreateUidName(nowUserInfo.getUsername());
                sku.setLastUpdateUid(nowUserInfo.getUserId());
                sku.setLastUpdateUidName(nowUserInfo.getUsername());
                skuService.save(sku);
                FmbxHotelReserveSku hotelReserveSkuNew = new FmbxHotelReserveSku();
                hotelReserveSkuNew.setSkuId(sku.getSkuId());
                hotelReserveSkuNew.setSuiteId(suite.getSuiteId());
                hotelReserveSkuNew.setSkuName(reserveSku);
                hotelReserveSkuNew.setFlagSell(0);//无效
                hotelReserveSkuNew.setStatus(3);//待完善
                hotelReserveSkuNew.setCtime(now);
                hotelReserveSkuNew.setUtime(now);
                b2 = hotelReserveSkuService.save(hotelReserveSkuNew);

                if (b2) {
                    addHotelReserveSkuSellStartEndScheduleJob(hotelReserveSkuNew.getSkuId(),hotelReserveSkuNew.getSellStartTime(),
                            hotelReserveSkuNew.getSellEndTime());
                }

            }
        }else{
            b2 = true;
        }
        return b2;
    }

    public void selectHotelReserveSkuMenu(HashMap<String,Object> resultMap, ReqHotelSku req) {

        List<HotelReserveSkuDomain> lists = suiteDao.selectHotelReserveSkuMenu(req);

        resultMap.put("skuInfo",lists) ;

    }

}
