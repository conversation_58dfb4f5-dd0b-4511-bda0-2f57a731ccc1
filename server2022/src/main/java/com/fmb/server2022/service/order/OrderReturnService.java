package com.fmb.server2022.service.order;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.OrderSysInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.domain.AdminOrder;
import com.fmb.server2022.domain.FmbOrderDetailQueryResult;
import com.fmb.server2022.domain.PhpAdminInfo;
import com.fmb.server2022.fmbx.entity.FmbExchangeCodes;
import com.fmb.server2022.fmbx.entity.FmbMycpsCommissionOrder;
import com.fmb.server2022.fmbx.entity.FmbOrderActions;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.entity.FmbOrderReserveCancelSettle;
import com.fmb.server2022.fmbx.entity.FmbOrderReturns;
import com.fmb.server2022.fmbx.entity.FmbOrderReturnsAction;
import com.fmb.server2022.fmbx.entity.FmbOrderReturnsNote;
import com.fmb.server2022.fmbx.entity.FmbOrderReturnsSettle;
import com.fmb.server2022.fmbx.entity.FmbOrderSettleDetails;
import com.fmb.server2022.fmbx.entity.FmbOrderTradeSnapshot;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbUserCashChange;
import com.fmb.server2022.fmbx.entity.FmbUserCashLog;
import com.fmb.server2022.fmbx.entity.FmbUserExtinfo;
import com.fmb.server2022.fmbx.entity.FmbxHotelGoodsDetail;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.service.IFmbExchangeCodesService;
import com.fmb.server2022.fmbx.service.IFmbOrderActionsService;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbOrderReserveCancelSettleService;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsActionService;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsNoteService;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsService;
import com.fmb.server2022.fmbx.service.IFmbOrderReturnsSettleService;
import com.fmb.server2022.fmbx.service.IFmbOrderSettleDetailsService;
import com.fmb.server2022.fmbx.service.IFmbOrderTradeSnapshotService;
import com.fmb.server2022.fmbx.service.IFmbReserveHotelCodesService;
import com.fmb.server2022.fmbx.service.IFmbUserCashChangeService;
import com.fmb.server2022.fmbx.service.IFmbUserCashLogService;
import com.fmb.server2022.fmbx.service.IFmbUserExtinfoService;
import com.fmb.server2022.fmbx.service.IFmbxHotelGoodsDetailService;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.UserUnionService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.basic.FmbConstants.DEFAULT_DATETIME;
import static com.fmb.basic.user.SessionUser.SESSIONUSER_FRONT;
import static com.fmb.server2022.service.FmbDBDictInfo.FMBX_HOTEL_RESERVE_SKU__IS_RETURN_Map;
import static com.fmb.server2022.service.FmbDBDictInfo.FMBX_SUITE__IS_RETURN_Map;
import static com.fmb.server2022.service.order.OrderService.*;

/**
 * 订单退款
 * <AUTHOR>
 * @Date: 2023/3/2 4:11 下午
 */
@Service
public class OrderReturnService {
    public static final String SMS_TEMPLATE_HOTEL_RESERVE_ORDER_RETURN_DIRECT_OK = "{收货人姓名}，您的预约需求{活动标题}已确认取消。本次预约不消耗酒店次卡预约次数，请您重新预约。";
    public static final String SMS_TEMPLATE_HOTEL_RESERVE_ORDER_RETURN_NEED_RETURN_PART = "{收货人姓名}，您申请的{活动标题}的退款{退款额}{余额}已通过审核，我们将于5个工作日内给您退款。";
    public static final String FMBX_HOTEL_RESERVE_RETURN_NEED_PART_MONEY = "fmbx_hotel_reserve_return_need_part_money";
    private static Logger logger = LoggerFactory.getLogger(OrderReturnService.class);

    @Autowired
    OrderReturnService self ;

    private  static   int[] ORDER_OK_STATUS = new int[]{1,2,5,8} ;


    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    OrderService orderService ;

    @Autowired
    OrderOfHotelReserveService orderOfHotelReserveService ;

    @Autowired
    OrderOfHotelDateService orderOfHotelDateService ;


    public AbstractOrderservice getOrderServiceFromGoodsType(int type){
        AbstractOrderservice orderservice =null;
        switch (type){
            case SKU_HOTEL_DATE_GOODS_TYPE:
                orderservice = orderOfHotelDateService ;
                break;
            case SKU_HOTEL_RESERVE_GOODS_TYPE:
                orderservice= orderOfHotelReserveService ;
                break;
        }
        return orderservice ;
    }




    @Autowired
    IFmbReserveHotelCodesService hotelCodesService;


    private final IFmbOrderReturnsService orderReturnsService ;
    private final IFmbOrderReturnsActionService returnsActionService ;
    private final IFmbOrderReturnsNoteService returnsNoteService ;
    private final IFmbxHotelGoodsDetailService hotelGoodsDetailService ;
    private final IFmbOrderInfoService fmbOrderInfoService ;
    private final IFmbOrderGoodsService fmbOrderGoodsService ;
    private final IFmbOrderTradeSnapshotService snapshotService ;
    private final IFmbOrderActionsService orderActionsService ;

    private final IFmbUserCashLogService userCashLogService ;

    private final  IFmbUserExtinfoService userExtinfoService ;
    private final IFmbUserCashChangeService cashChangeService ;

    private final IFmbExchangeCodesService exchangeCodesService ;

//    查fmb_order_settle_details订单结算数据，
//    发起退款或取消时插入 fmb_order_returns_settle ,
//            fmb_order_reserve_cancel_settle

    private final IFmbOrderSettleDetailsService settleDetailsService ;
    private final IFmbOrderReturnsSettleService returnsSettleService ;
    private final IFmbOrderReserveCancelSettleService reserveCancelSettleService ;



    @Autowired
    SuiteDao suiteDao ;

    public static boolean isOrderSuccessStatus(int status){

        for (int orderOkStatus : ORDER_OK_STATUS) {
            if (orderOkStatus== status) {
                return true ;
            }
        }

        return false ;

    }


    @Autowired
    public OrderReturnService(IFmbOrderReturnsService orderReturnsService, IFmbOrderReturnsActionService returnsActionService, IFmbOrderReturnsNoteService returnsNoteService, IFmbxHotelGoodsDetailService hotelGoodsDetailService, IFmbOrderInfoService fmbOrderInfoService, IFmbOrderGoodsService fmbOrderGoodsService, IFmbOrderTradeSnapshotService snapshotService, IFmbOrderActionsService orderActionsService, IFmbUserCashLogService userCashLogService, IFmbUserExtinfoService userExtinfoService, IFmbUserCashChangeService cashChangeService, IFmbExchangeCodesService exchangeCodesService, IFmbOrderSettleDetailsService settleDetailsService, IFmbOrderReturnsSettleService returnsSettleService, IFmbOrderReserveCancelSettleService reserveCancelSettleService) {
        this.orderReturnsService = orderReturnsService;
        this.returnsActionService = returnsActionService;
        this.returnsNoteService = returnsNoteService;
        this.hotelGoodsDetailService = hotelGoodsDetailService;
        this.fmbOrderInfoService = fmbOrderInfoService;
        this.fmbOrderGoodsService = fmbOrderGoodsService;
        this.snapshotService = snapshotService;
        this.orderActionsService = orderActionsService;
        this.userCashLogService = userCashLogService;
        this.userExtinfoService = userExtinfoService;
        this.cashChangeService = cashChangeService;
        this.exchangeCodesService = exchangeCodesService;
        this.settleDetailsService = settleDetailsService;
        this.returnsSettleService = returnsSettleService;
        this.reserveCancelSettleService = reserveCancelSettleService;
    }


    /**
     * 后台打开 退款页面

     * @param orderSn
     * @param saveDB  提交退款时 携带退款对象
     *                发起退款时为空
     * @return
     */
    public FmbOrderDetailQueryResult orderReturnBeginQuery(String orderSn, FmbOrderReturns saveDB) {

        final FmbOrderDetailQueryResult result = new FmbOrderDetailQueryResult();

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn);
        final FmbOrderInfo orderInfoDB = fmbOrderInfoService.getOne(wrOrder);


        if (orderInfoDB == null) {
            throw new BadLogicException("订单不存在,订单号"+orderSn);
        }

        BeanUtils.copyProperties(orderInfoDB,result);

        int last30DayReturnNum = queryRecentUserReturnCount(orderInfoDB.getAid(),orderInfoDB.getUid()) ;
        result.setLast30DayReturnNum(last30DayReturnNum) ;

        result.setOrderInfo(orderInfoDB);

        if (orderInfoDB.getOrderStatus()==0) {
            throw new BadLogicException("订单状态异常,不能发起退款查询");
        }


        Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn);
        final List<FmbOrderGoods> goodsListDB = fmbOrderGoodsService.list(wrGoods);
        FmbOrderGoods goodsInfo = null ;

        Wrapper<FmbOrderTradeSnapshot> wrSnap = new LambdaQueryWrapper<FmbOrderTradeSnapshot>()
                .eq(FmbOrderTradeSnapshot::getOrderSn, orderSn);
        List<FmbOrderTradeSnapshot> snapshots = snapshotService.list(wrSnap);

        JSON parse = null ;
//        logger.info("snapshots.get(0).getSummary()={}",snapshots.get(0).getSummary());
        if (GOODS_TYPE_6==orderInfoDB.getGoodsType()  || GOODS_TYPE_7== orderInfoDB.getGoodsType()) {
            parse = JSONUtil.parse(snapshots.get(0).getSummary());

        }


        int dayDiff = 0 ;
        //总间夜
        int totalNightRoom = 0 ;
        if (orderInfoDB.getGoodsType()==GOODS_TYPE_6){
            goodsInfo = goodsListDB.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_6).findFirst().get();

            result.setHotelCheckInDate(FmbDateUtil.localDateTime2String10( goodsInfo.getPlayTime()));
            result.setHotelCheckOutDate(FmbDateUtil.localDateTime2String10( goodsInfo.getLeaveTime()));

            dayDiff =FmbDateUtil.dayDiff(FmbDateUtil.localDateTime2String10( goodsInfo.getLeaveTime()),FmbDateUtil.localDateTime2String10( goodsInfo.getPlayTime())) ;
            result.setHotelCheckDateStr("(共"+dayDiff+"晚)");
            result.setHotelRoomNum(goodsInfo.getGoodsNumber());


            totalNightRoom = dayDiff*goodsInfo.getGoodsNumber() ;
        }

        if( GOODS_TYPE_7== orderInfoDB.getGoodsType()) {
            goodsInfo = goodsListDB.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_7).findFirst().get();
        }

        result.setSkuGoodsPrice(goodsInfo.getGoodsPrice());
        result.setSkuSettlePrice(goodsInfo.getSettlePrice());
        result.setSkuNumber(goodsInfo.getGoodsNumber());

        result.setOrderGoods(goodsInfo) ;

        //交易快照里的 下单信息
        final OrderSysInfo orderSysInfo = parse.getByPath("orderSysInfo", OrderSysInfo.class);
        final SkuDetail skuDetail = orderSysInfo.getSkuDetails().get(0);


        final FmbxSuite suiteInfo =   skuDetail.getSuite();

        final String activityTitle = orderSysInfo.getActivity().getTitle();
        result.setActivityTitle(activityTitle);

        final BigDecimal couponSubMoney = orderInfoDB.getCouponSubMoney();
        BigDecimal eachSkuCouponDivide = BigDecimal.ZERO ;

        BigDecimal eachSkuGoodsPrice = BigDecimal.ZERO ;

        if (GOODS_TYPE_6==orderInfoDB.getGoodsType() ){
             result.setSkuName(   skuDetail.getRoomSku().getSkuName()) ;

            final List<FmbExchangeCodes> codes = queryExchangeCode(orderSn);

            final int notUsed = (int) codes.stream().filter(c -> c.getStatus() == 0).count();

            //酒店入住 详细信息
            Wrapper<FmbxHotelGoodsDetail> wrDetail = new LambdaQueryWrapper<FmbxHotelGoodsDetail>()
                    .eq(FmbxHotelGoodsDetail::getOrderSn, orderSn)
                    .select(FmbxHotelGoodsDetail::getRoomDate,FmbxHotelGoodsDetail::getRoomNumber,FmbxHotelGoodsDetail::getRoomNumberReturn,
                            FmbxHotelGoodsDetail::getGoodsPrice,FmbxHotelGoodsDetail::getDetailId);
            final List<FmbxHotelGoodsDetail> listHotelDetail = hotelGoodsDetailService.list(wrDetail);
            result.setListHotelDetail(listHotelDetail);


            //构造可退的 房间数量
            final ArrayList<Integer> hotelRoomReturnOption = new ArrayList<>();

            //每个电子码 代表的间夜数 除以dayDiff 就代表每个码选中时需要退的 房间数量
            final int eachExchangeCodeTotalNightRoom = totalNightRoom / codes.size();
            final int eachCode_room = eachExchangeCodeTotalNightRoom / dayDiff;

            if (result.getOrderInfo().getOrderType()==1) {
                for (int i = 1; i <= notUsed; i++) {
                    hotelRoomReturnOption.add(i*eachCode_room) ;
                }
            }
            if (result.getOrderInfo().getOrderType()==2) {
                    hotelRoomReturnOption.add(notUsed) ;
            }

            eachSkuCouponDivide = couponSubMoney.divide(new BigDecimal(goodsInfo.getGoodsNumber()),2,RoundingMode.HALF_UP);

            result.setHotelExchangeCode(codes.size());
            result.setEachExchangeCodeTotalNightRoom(eachExchangeCodeTotalNightRoom);
            result.setDayDiff(dayDiff) ;

            eachSkuGoodsPrice = goodsInfo.getGoodsPrice() ;
//                    goodsInfo.getGoodsPrice().multiply(new BigDecimal(goodsInfo.getGoodsNumber())).divide( new BigDecimal( codes.size()),
//                            2,BigDecimal.ROUND_HALF_UP) ;

            result.setHotelRoomReturnOption(hotelRoomReturnOption);

            result.setHotelRemainMaxRoomCanCancel(notUsed*eachCode_room);

            final FmbxSuite stemp = orderOfHotelDateService.queryFmbxSuite(suiteInfo.getSuiteId());
            //退货政策
            result.setReturnStr(FMBX_SUITE__IS_RETURN_Map.get(""+stemp.getIsReturn()));

        }


        //房券的
        if( GOODS_TYPE_7== orderInfoDB.getGoodsType()) {
            eachSkuGoodsPrice = goodsInfo.getGoodsPrice() ;
            final FmbxHotelReserveSku reserveSku = skuDetail.getReserveSku();
            result.setSkuName(   reserveSku.getSkuName()) ;

            String yxq = FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseStartTime()) + "至" + FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseEndTime());

            result.setReserveChooseDate(yxq) ;

            final List<FmbReserveHotelCodes> reserveHotelCodes = orderService.queryHotelCodeList(orderSn, 2);
            final FmbOrderRelatedData relatedData = orderService.queryFmbOrderRelatedData(orderSn);

            final int containTimes = relatedData.getContainTimes().intValue();

            //剩余次数大于0 而且状态为1 次数还是满的不存在部分使用情况 的电子码
            final List<FmbReserveHotelCodes> hotelCodeCanReturn =
                    reserveHotelCodes.stream().filter(code -> code.getNumber() > 0 && code.getStatus() == 1  && code.getNumber().intValue()== containTimes ).collect(Collectors.toList());



            //还能使用的 电子码
            final List<FmbReserveHotelCodes> hotelCodeCanReturnPartUse =
                    reserveHotelCodes.stream().filter(code -> code.getNumber() > 0 && code.getStatus() == 1   ).collect(Collectors.toList());

            for (FmbReserveHotelCodes c : hotelCodeCanReturn) {
                c.setCodeDesc("(间夜总"+containTimes+"余"+containTimes+")");
            }
            for (FmbReserveHotelCodes c : hotelCodeCanReturnPartUse) {
                c.setCodeDesc("(间夜总"+containTimes+"余"+c.getNumber()+")");
            }

            //总个数
            result.setHotelReserveCodeTotal(relatedData.getNumber());
            //当前没使用的
            result.setHotelReserveCodeCanReturnTotal(hotelCodeCanReturn.size());
            //总个数-当前没使用的
            result.setHotelReserveCodeNotReturn(relatedData.getNumber()-hotelCodeCanReturn.size());

            result.setHotelCodeCanReturn(hotelCodeCanReturn) ;
            result.setHotelCodeCanReturnInCludePartUse(hotelCodeCanReturnPartUse); ;

            final FmbxHotelReserveSku reserveSkuTemp = orderOfHotelReserveService.querySkuReserve(goodsInfo.getGoodsId());
            //退货政策
            result.setReturnStr(FMBX_HOTEL_RESERVE_SKU__IS_RETURN_Map.get(""+reserveSkuTemp.getIsReturn()));


            eachSkuCouponDivide = couponSubMoney.divide(new BigDecimal(goodsInfo.getGoodsNumber().intValue()),2,RoundingMode.HALF_UP);
        }
        result.setSuiteName(suiteInfo.getName());

        //查出所有的关于这个订单的 退款单
        Wrapper<FmbOrderReturns> wrReturn = new LambdaQueryWrapper<FmbOrderReturns>()
                .eq(FmbOrderReturns::getOrderSn, orderSn);
        final List<FmbOrderReturns> returnsList = orderReturnsService.list(wrReturn);

        result.setReturnsList(returnsList);

        BigDecimal returnCashMoney =
                returnsList.stream().filter(re-> re.getStatus()!=3).map(FmbOrderReturns::getUsedCashMoney).reduce(BigDecimal.ZERO,
                        BigDecimal::add);

        result.setReturnedCashMoney(returnCashMoney);

        //计算非拒绝的 退款单 数量
        final Integer alreadyReturn = returnsList.stream().filter(r -> r.getStatus() != 3).map(FmbOrderReturns::getGoodsNum).reduce(0,
                Integer::sum);

        //计算已退 包括 现金和余额
        final BigDecimal alreadyReturnMoney =
                returnsList.stream().filter(r -> r.getStatus() != 3).map( r-> r.getMoney().add(r.getUsedCashMoney())).reduce(BigDecimal.ZERO,
                BigDecimal::add);
//        final BigDecimal alreadyReturnMaxMoney =
//                returnsList.stream().filter(r -> r.getStatus() != 3).map(FmbOrderReturns::getMaxMoney).reduce(BigDecimal.ZERO,
//                BigDecimal::add);

        //已退现金
        final BigDecimal alreadyReturnXJ = returnsList.stream().filter(r -> r.getStatus() != 3).map(r -> r.getMoney()).reduce(BigDecimal.ZERO,
                BigDecimal::add);

        result.setAlreadyReturnSkuNumber(alreadyReturn);
        result.setAlreadyReturnMoney(alreadyReturnMoney);

        String overReturn = "" ;
        if(alreadyReturnMoney.compareTo(orderInfoDB.getRealpayMoney().add(orderInfoDB.getCashMoney()))>0){
            overReturn = ",目前超退￥"+ alreadyReturnMoney.subtract(orderInfoDB.getRealpayMoney()).subtract(orderInfoDB.getCashMoney()) ;
        }

        result.setAlreadyReturnMoneyDesc("(已退现金:￥"+alreadyReturnXJ.toString()+",已退余额:￥"+alreadyReturnMoney.subtract(alreadyReturnXJ).toString()+overReturn+")");





        final BigDecimal moneyTotalOfOder = orderInfoDB.getMoney();

        BigDecimal moneyNoCouponAndShippingFee = orderInfoDB.getMoney().subtract(couponSubMoney).subtract(orderInfoDB.getShippingFee()) ;
        result.setMoneyNoCouponAndShippingFee(moneyNoCouponAndShippingFee);

        result.setEachSkuCouponDivide(eachSkuCouponDivide);

        result.setSkuGoodsPriceSubCoupon(eachSkuGoodsPrice.subtract(eachSkuCouponDivide));

        result.setCouponMoney(couponSubMoney);
        result.setCashMoney(orderInfoDB.getCashMoney());

        result.setRealPayMoney(orderInfoDB.getRealpayMoney());




        result.setReceiver(orderInfoDB.getReceiver()) ;
        result.setMobile(orderInfoDB.getMobile()) ;

        //剩余最大可退   用户实际支付金额- 非拒绝退款单的money和余额
        final BigDecimal subtract = orderInfoDB.getRealpayMoney().add(orderInfoDB.getCashMoney()).subtract(alreadyReturnMoney);
        if (subtract.compareTo(BigDecimal.ZERO)>=0) {
            result.setRemainReturnMaxMoney(subtract) ;

            result.setRemainReturnMaxMoneyDesc("(可退现金:￥"+ orderInfoDB.getRealpayMoney().subtract( alreadyReturnXJ).toString()+",可退余额:￥"+orderInfoDB.getCashMoney().subtract(alreadyReturnMoney.subtract(alreadyReturnXJ))+
                    ")") ;

        }else {
            result.setRemainReturnMaxMoney(BigDecimal.ZERO) ;
            result.setRemainReturnMaxMoneyDesc("(无可退金额)") ;
        }



        if( GOODS_TYPE_6== orderInfoDB.getGoodsType()) {

            //减去 用户已经使用的
//            BigDecimal remainReturnMaxMoneySubUsed = BigDecimal.ZERO ;
//            final BigDecimal subtract1 = result.getRemainReturnMaxMoney()
//                    .subtract(new BigDecimal(result.getSkuNumber() - result.getHotelRemainMaxRoomCanCancel()).multiply(result.getSkuGoodsPriceSubCoupon()));
//            if (subtract1.compareTo(BigDecimal.ZERO)==1) {
//                remainReturnMaxMoneySubUsed = subtract1 ;
//            }
            result.setRemainReturnMaxMoneySubUsed(result.getRemainReturnMaxMoney());
        }

        if( GOODS_TYPE_7== orderInfoDB.getGoodsType()) {

            //减去 用户已经使用的
            BigDecimal remainReturnMaxMoneySubUsed = BigDecimal.ZERO ;

            if (result.getHotelReserveCodeCanReturnTotal()==result.getSkuNumber()) {
                remainReturnMaxMoneySubUsed = result.getRemainReturnMaxMoney() ;
            }else if (result.getHotelReserveCodeCanReturnTotal()<result.getSkuNumber()){
                final BigDecimal multiply = new BigDecimal(result.getHotelReserveCodeCanReturnTotal()).multiply(result.getSkuGoodsPriceSubCoupon());
//                if(result.getRemainReturnMaxMoney().compareTo(multiply)==1){
//                    remainReturnMaxMoneySubUsed = multiply ;
//                }else {
//                    remainReturnMaxMoneySubUsed = result.getRemainReturnMaxMoney() ;
//                }


                if(multiply.compareTo(result.getRemainReturnMaxMoney())>=0 ){
                    remainReturnMaxMoneySubUsed = result.getRemainReturnMaxMoney() ;
                }else {
                    remainReturnMaxMoneySubUsed = multiply ;
                }

            }

            result.setRemainReturnMaxMoneySubUsed(remainReturnMaxMoneySubUsed);
        }




        HashMap<String, Object> myCpsInfo = new HashMap<>();
        orderService.getFmbMycpsCommissionOrder(myCpsInfo,orderSn,null,"orderReturn") ;
        result.setMycpsOrderList((List<FmbMycpsCommissionOrder>) myCpsInfo.get("mycpsCommissionOrder"));

        Wrapper<FmbOrderSettleDetails> settleWr = new LambdaQueryWrapper<FmbOrderSettleDetails>()
                .eq(FmbOrderSettleDetails::getRecId, goodsInfo.getRecId())
                .select(FmbOrderSettleDetails::getId)
                ;
        //增加标记位 判断是否存在已经结算的情况
        result.setExistsSettle( settleDetailsService.count(settleWr) > 0) ;

        // 退货信息 构造
        prepareReturnData(saveDB, orderInfoDB, goodsInfo);

        return result;
    }

    public List<FmbExchangeCodes> queryExchangeCode(String orderSn) {
        // 通过数 未使用 电子码个数 来输出用户可以退几间
        Wrapper<FmbExchangeCodes> wrCode = new LambdaQueryWrapper<FmbExchangeCodes>()
                .eq(FmbExchangeCodes::getOrderSn, orderSn)
                .select(FmbExchangeCodes::getCodeId,FmbExchangeCodes::getExchangeCode,FmbExchangeCodes::getStatus)
                ;
        final List<FmbExchangeCodes> codes = exchangeCodesService.list(wrCode);
        return codes;
    }

    /**
     * 退款数据提前整理
     * @param saveDB
     * @param orderInfoDB
     * @param goodsInfo
     */
    private void prepareReturnData(FmbOrderReturns saveDB, FmbOrderInfo orderInfoDB, FmbOrderGoods goodsInfo) {
        //生成退款单时 写入一些数据
        if (saveDB != null) {

            saveDB.setRecId(goodsInfo.getRecId());
            saveDB.setGoodsId(goodsInfo.getGoodsId());

            if (!isOrderSuccessStatus(orderInfoDB.getOrderStatus().intValue())) {
                throw new BadLogicException("订单状态不是交易成功状态,不能发起退款");
            }

            if (isExistUnFinishOrderReturn(orderInfoDB.getOrderSn())) {
                throw new BadLogicException("该订单还存在未完成退款单,不能发起退款");
            }

            saveDB.setReturnType(isOrderSuccessStatus( orderInfoDB.getOrderStatus().intValue())?0:1);

            saveDB.setUid(orderInfoDB.getUid());


            saveDB.setReceiverTxt("");
//            saveDB.setReceiver("");
            saveDB.setBuyerAccount("");
            saveDB.setCountry(1);
            saveDB.setProvince(orderInfoDB.getProvince());
            saveDB.setCity(orderInfoDB.getCity());
            saveDB.setArea(orderInfoDB.getArea());
            saveDB.setStreet(orderInfoDB.getStreet());
            saveDB.setAddress(orderInfoDB.getAddress());
            saveDB.setZipcode(orderInfoDB.getZipcode());
            saveDB.setTel(orderInfoDB.getTel());
//            saveDB.setMobile("");
            saveDB.setEmail(orderInfoDB.getEmail());
            saveDB.setShippingName("");
            saveDB.setShippingCode("");
            saveDB.setShippingFee(new BigDecimal("0"));


            saveDB.setIsReturn(0);
            saveDB.setReturnTime(FmbConstants.DEFAULT_DATETIME);
            saveDB.setApplyTime(LocalDateTime.now());

            saveDB.setIdentyCids("");
            saveDB.setReturnGroup("");
            saveDB.setOperTypes("");
            saveDB.setOrderTypes("old");

            saveDB.setCashNote("");
            saveDB.setIsUserReturn(0);
            saveDB.setIsOvertimeReturn(0);

        }
    }

    /**
     * 是否存在未完成的 退款单
     * @param orderSn
     * @return
     */
    public boolean isExistUnFinishOrderReturn(String orderSn) {

        Wrapper<FmbOrderReturns> wrC = new LambdaQueryWrapper<FmbOrderReturns>()
                .eq(FmbOrderReturns::getOrderSn,orderSn)
                .in(FmbOrderReturns::getStatus, Arrays.asList(new Integer[]{0,1}) )
                ;
       return orderReturnsService.count(wrC)>0;

    }

    public List<FmbOrderReturns> queryOrderReturns(String orderSn) {

        Wrapper<FmbOrderReturns> wrC = new LambdaQueryWrapper<FmbOrderReturns>()
                .eq(FmbOrderReturns::getOrderSn,orderSn)
                .select(FmbOrderReturns::getReturnId,FmbOrderReturns::getStatus,FmbOrderReturns::getGoodsId
                ,FmbOrderReturns::getUserCashMoney,FmbOrderReturns::getMoney
                )
                ;
        return orderReturnsService.list(wrC) ;

    }


    public void addCashLog(FmbOrderReturns returnDBInfo, BigDecimal moreCash){


        FmbUserCashLog dbCashLog = new FmbUserCashLog();
        dbCashLog.setUid(returnDBInfo.getUid());
        dbCashLog.setOrderSn(returnDBInfo.getOrderSn());
        dbCashLog.setMoney(returnDBInfo.getUserCashMoney());
        dbCashLog.setOpenid("");
        dbCashLog.setSourceType(3);
        dbCashLog.setDirectionType(1);
        dbCashLog.setReturnIds(returnDBInfo.getReturnId()+"");
        dbCashLog.setNumber(0);
        dbCashLog.setStatus(2);
        dbCashLog.setValidTime(DEFAULT_DATETIME);
        dbCashLog.setCtime(LocalDateTime.now());
        dbCashLog.setUtime(DEFAULT_DATETIME);
        dbCashLog.setAdminUid(0);

        if (returnDBInfo.getReturnClass().intValue()==0) {
            dbCashLog.setNote("订单号"+ returnDBInfo.getOrderSn()+"发起正常退款,返还余额 "+ returnDBInfo.getUserCashMoney()+"元,为冻结中");
        }
        if (returnDBInfo.getReturnClass().intValue()==1) {

            String note = "" ;

            if (moreCash.compareTo(BigDecimal.ZERO)==1){
                if (returnDBInfo.getUserCashMoney().compareTo(moreCash)==0) {
                    note = "所有都是补偿退款," ;
                }
                if (returnDBInfo.getUserCashMoney().compareTo(moreCash)==1) {
                    note = "其中"+moreCash+"元是额外补偿退款," ;
                }
            }

            dbCashLog.setNote("订单号"+ returnDBInfo.getOrderSn()+"发起补偿退款,增加余额 "+ returnDBInfo.getUserCashMoney()+"元,"+note+"为冻结中");
        }



        userCashLogService.save(dbCashLog);
    }


    /**
     * 构造退款短信内容
     * @param orderInfoDB
     * @param orderReturns
     * @param smsContent
     * @return
     */
    public String createReturnOrderSmsContent(FmbOrderInfo orderInfoDB, FmbOrderReturns orderReturns, String smsContent){

//                             {收货人}，您购买的{活动名}，订单号：{订单号}，金额：{退款额}{余额}已发起退款。如有问题，请致电父母邦客服：{客服电话}。
//        String smsContent = "{收货人姓名}，您购买的{活动标题}，订单号：{订单号}，金额：{退款额}{余额}已发起退款。如有问题，请致电父母邦客服：{kefu_phone}。更多详情：http://fmb.so/v61 。" ;

        String smsContentReturn = "" ;

        HashMap<String, String> info = new HashMap<>();
        info.put("{活动标题}", orderInfoDB.getOrderName());
        info.put("{订单号}",  orderInfoDB.getOrderSn());

        info.put("{kefu_phone}", FmbServerConfig.getServiceTel());
        info.put("{收货人姓名}",  orderInfoDB.getReceiver());

        //先给默认值
        info.put("{退款额}",  "");
        if (orderReturns.getMoney().compareTo(BigDecimal.ZERO)==1) {
            info.put("{退款额}",  FmbNumberUtil.bigDecimal2Str(orderReturns.getMoney().add(orderReturns.getUserCashMoney())) +"元");
        }

        //（含余额**元）
        info.put("{余额}",  "");
        if (orderReturns.getUsedCashMoney().compareTo(BigDecimal.ZERO)==1) {
            info.put("{余额}",  "（含余额"+ FmbNumberUtil.bigDecimal2Str(orderReturns.getUsedCashMoney()) +"元）");
        }

        final String[] aim = new String[info.size()];
        final String[] result = new String[info.size()];

        int i = 0 ;
        for (Map.Entry<String, String> stringEntry : info.entrySet()) {
            aim[i] = stringEntry.getKey() ;
            result[i] = stringEntry.getValue() ;
            i++ ;
        }

        smsContentReturn  = StringUtils.replaceEach(smsContent,aim,result) ;
        return smsContentReturn;

    }

    /**
     * 查询这个用户最近30天 已退订单数量 查询
     * @param aid
     * @param uid
     * @return
     */
    public int queryRecentUserReturnCount(Integer aid, Integer uid) {

        HashMap<String, Object> par = new HashMap<>();
        par.put("aid", aid);
        par.put("uid", uid);

        return suiteDao.selectRecentUserReturnCount(par) ;

    }


    /**
     * 创建退款单
     * @param input
     * @param resultMap
     */
    public void createReturn(AdminOrder input, HashMap<String, Object> resultMap) {

        final Integer returnClass = input.getReturnClass();

        SessionUser userInfo = null ;
        if (input.getPhpAdminInfo()!=null ){
            final PhpAdminInfo phpAdminInfo = input.getPhpAdminInfo();
            userInfo = new SessionUser() ;
            userInfo.setUserId(Integer.parseInt( phpAdminInfo.getUid()));
            userInfo.setUsername(phpAdminInfo.getName());
            userInfo.setUserType(SessionUser.SESSIONUSER_FMB);
        }else{
            userInfo = userUnionService.nowUserInfo();
        }

        //普通退款
        if (returnClass==0) {
            normalReturn(input,resultMap,userInfo) ;
        }
        //补偿退款
        if (returnClass==1) {
            fixReturn(input,resultMap,userInfo) ;
        }

    }


    /**
     * 酒店预约单直接退
     * @param input
     * @param resultMap
     */
    public void hotelOrderOfFromReserveReturn(AdminOrder input, HashMap<String, Object> resultMap) {

        DynamicDataSourceContextHolder.push(DB_slave);
        final FmbOrderInfo orderInfo = orderService.queryFmbOrderInfo(input.getOrderSn());

        if ( !(GOODS_TYPE_6 == orderInfo.getGoodsType() && orderInfo.getOrderType()==2)) {
            throw new BadLogicException("订单不是酒店预约单,不能操作");
        }

        final FmbOrderGoods fmbOrderGoods = orderService.queryFmbOrderGoods(input.getOrderSn()).get(0);

        input.setReturnClass(0);
        input.setRoomNumber(fmbOrderGoods.getGoodsNumber());
        input.setReturnMoney(orderInfo.getRealpayMoney().add(orderInfo.getCashMoney()));
        input.setReason("后台取消预约单");
        input.setReasonTxt("程序直接发起酒店预约单退款");
        input.setUploadFile(new ArrayList<String>());
        input.setInternalUploadFile(new ArrayList<String>());
        input.setReceiver(orderInfo.getReceiver());
        input.setMobile(orderInfo.getMobile());
        input.setSendSms(1);
        input.setSmsTemplate(SMS_TEMPLATE_HOTEL_RESERVE_ORDER_RETURN_NEED_RETURN_PART);
        input.setInternalNote("酒店预约单直接发起退货");
        /**
         ******************************************************************
         *     标记是从前台 发起订房单退款 
         ******************************************************************
         **/
        input.setHotelOrderOfFromReserveReturn(1) ;

        createReturn(input,resultMap);


    }


    /**
     * 补偿退款
     * @param input
     * @param resultMap
     * @param userInfo
     */
    public void fixReturn(AdminOrder input, HashMap<String, Object> resultMap, SessionUser userInfo) {
        {

            DynamicDataSourceContextHolder.push(DB_slave);

            FmbOrderReturns saveDB = new FmbOrderReturns();

            boolean needOperateCash = false ;

            //saveDB 传入到orderReturnBeginQuery 并设置一些 数据
            final FmbOrderDetailQueryResult orderQueryResult = orderReturnBeginQuery(input.getOrderSn(),saveDB);

            final BigDecimal orderCashMoney = orderQueryResult.getOrderInfo().getCashMoney();
            if (orderCashMoney.compareTo(BigDecimal.ZERO)==1) {
                needOperateCash = true ;
            }


            final ArrayList<String> reserveCode = input.getReserveCode();
            logger.info("{}",reserveCode);


            BeanUtils.copyProperties(input,saveDB);
            saveDB.setMoney(new BigDecimal("0"));
            saveDB.setFmbReturnMoney(new BigDecimal("0"));
            saveDB.setShopReturnMoney(new BigDecimal("0"));
            saveDB.setDivideCouponMoney(new BigDecimal("0"));

            saveDB.setCouponMoney(new BigDecimal("0"));
            saveDB.setCulturalCouponMoney(new BigDecimal("0"));
            saveDB.setUsedCashMoney(new BigDecimal("0"));
            saveDB.setDeductMoney(new BigDecimal("0"));
            saveDB.setBackMoney(new BigDecimal("0"));
            saveDB.setUserCashMoney(new BigDecimal("0"));
            saveDB.setReduceMaxMoney(new BigDecimal("0"));

            saveDB.setGoodsNum(0);
            saveDB.setReturnCodes("");

            saveDB.setCouponMoney(BigDecimal.ZERO);
            saveDB.setMaxMoney(BigDecimal.ZERO);


            //日历房逻辑
            if  (GOODS_TYPE_6==  orderQueryResult.getGoodsType() ) {


                int selectCount = 0;

                saveDB.setGoodsNum(input.getRoomNumber());
                saveDB.setReturnCodes("");

            }



            if  (GOODS_TYPE_7==  orderQueryResult.getGoodsType() &&  reserveCode!=null && !reserveCode.isEmpty()) {
                final Set<String> inputCodeSet = input.getReserveCode().stream().collect(Collectors.toSet());
                final Set<String> codeSet = orderQueryResult.getHotelCodeCanReturnInCludePartUse().stream().map(FmbReserveHotelCodes::getCode).collect(Collectors.toSet());

                for (String is : inputCodeSet) {
                    if (!codeSet.contains(is)) {
                        throw new BadLogicException("房券号异常");
                    }
                }

                saveDB.setReturnCodes(input.getReserveCode().stream().collect(Collectors.joining(",")));

            }

//            final BigDecimal alreadyReturnMoney = orderQueryResult.getAlreadyReturnMoney();
            final BigDecimal remainReturnMaxMoney = orderQueryResult.getRemainReturnMaxMoney();

            //已经 退还余额
            final BigDecimal returnCashMoney =
                    orderQueryResult.getReturnsList().stream().filter(re-> re.getStatus()!=3).map(FmbOrderReturns::getUsedCashMoney).reduce(BigDecimal.ZERO,
                            BigDecimal::add);


            BigDecimal inputTempReturnMoney = input.getReturnMoney();
            BigDecimal moreCash = BigDecimal.ZERO ;
            //要退的金额 超过最大可退, 多余部分要就要退到余额
            if (input.getReturnMoney().compareTo(orderQueryResult.getRemainReturnMaxMoney())>=0) {
                moreCash = input.getReturnMoney().subtract(orderQueryResult.getRemainReturnMaxMoney()) ;

                //把下面要退的金额 临时替换为 最大可退值,后续 通过 moreCash 的值找平
                inputTempReturnMoney = orderQueryResult.getRemainReturnMaxMoney() ;
            }

            returnMoneyMathMoneyFirst(input.getReturnMoney(), orderQueryResult.getOrderInfo().getRealpayMoney(),
                    orderQueryResult.getOrderInfo().getCashMoney(), orderQueryResult.getAlreadyReturnMoney().subtract(returnCashMoney),
                    returnCashMoney,saveDB);



            saveDB.setReturnClass(1);
            saveDB.setAdminUid(userInfo.getUserId());
            saveDB.setAdminDesc("后台发起补偿退货");
            saveDB.setStatus(0);

            saveDB.setThirdPartStatus(0);

            saveDB.setMaxMoney(saveDB.getMoney().add(saveDB.getUserCashMoney()));

            saveDB.setUploadFile("");
            if (input.getUploadFile() != null && !input.getUploadFile().isEmpty()) {
                saveDB.setUploadFile(input.getUploadFile().stream().collect(Collectors.joining(",")));
            }

            final int rInt = self.saveReturnCore(input, userInfo, saveDB, orderQueryResult,moreCash);

            resultMap.put("result",1) ;

            if (rInt==1) {
                //通知用户
                if (1==input.getSendSms()&& StringUtils.isNotBlank(input.getSmsTemplate())) {
                    FmbScheduleUtil.doAsyncJob(() -> {

                        final String returnOrderSmsContent = createReturnOrderSmsContent(orderQueryResult.getOrderInfo(), saveDB, input.getSmsTemplate());

                        logger.info("returnOrderSmsContent {} #[{}] ",saveDB.getMobile(),returnOrderSmsContent );

                    FmbSmsUtil.sendSms(saveDB.getMobile(),returnOrderSmsContent,"fmbx_create_order_return");

                    });
                }
            }


        }
    }


    /**
     *  退款优先退现金
     *
     *  @param userInputReturnMoney  用户要退多少钱
     * @param orderRealPayMoney     订单实际支付的现金
     * @param orderCashMoney        订单支付的余额
     * @param alreadyReturnMoney    已经退的现金
     * @param alreadyReturnCash     已经退的余额
     * @param saveDB
     * @return
     */
    public HashMap<String, Object> returnMoneyMathMoneyFirst(BigDecimal userInputReturnMoney, BigDecimal orderRealPayMoney,
                                                             BigDecimal orderCashMoney, BigDecimal alreadyReturnMoney, BigDecimal alreadyReturnCash, FmbOrderReturns saveDB) {

        HashMap<String, Object> rHash = new HashMap<>();

        //剩余可退的现金
        BigDecimal remainMoney = orderRealPayMoney.subtract(alreadyReturnMoney) ;
        //剩余可退的余额
        BigDecimal remainCash = orderCashMoney.subtract(alreadyReturnCash) ;

        if (userInputReturnMoney.compareTo(remainMoney)>=0) {
            rHash.put("money",remainMoney) ;
            rHash.put("cash",userInputReturnMoney.subtract(remainMoney)) ;
        }else {
            rHash.put("money",userInputReturnMoney) ;
            rHash.put("cash",BigDecimal.ZERO) ;
        }


        saveDB.setMoney((BigDecimal) rHash.get("money"));
        final BigDecimal cash = (BigDecimal) rHash.get("cash");
        saveDB.setUserCashMoney(cash);
        saveDB.setUsedCashMoney(cash);
        saveDB.setDeductMoney(cash);

        return rHash;

    }
    /**
     *  退款优先退余额
     *
     *  @param userInputReturnMoney  用户要退多少钱
     * @param orderRealPayMoney     订单实际支付的现金
     * @param orderCashMoney        订单支付的余额
     * @param alreadyReturnMoney    已经退的现金
     * @param alreadyReturnCash     已经退的余额
     * @return
     */
    public HashMap<String, Object> returnMoneyMathCashFirst(BigDecimal userInputReturnMoney, BigDecimal orderRealPayMoney,
                                                   BigDecimal orderCashMoney, BigDecimal alreadyReturnMoney, BigDecimal alreadyReturnCash, FmbOrderReturns saveDB) {

        HashMap<String, Object> rHash = new HashMap<>();

        //剩余可退的现金
        BigDecimal remainMoney = orderRealPayMoney.subtract(alreadyReturnMoney) ;
        //剩余可退的余额
        BigDecimal remainCash = orderCashMoney.subtract(alreadyReturnCash) ;

        if (userInputReturnMoney.compareTo(remainMoney.add(remainCash))>=0) {
            rHash.put("money",remainMoney) ;
            rHash.put("cash",userInputReturnMoney.subtract(remainMoney)) ;
        }else if(userInputReturnMoney.compareTo(remainCash)<=0) {

            rHash.put("money",BigDecimal.ZERO) ;
            rHash.put("cash",userInputReturnMoney) ;
        }else
        {
            rHash.put("money",userInputReturnMoney.subtract(remainCash)) ;
            rHash.put("cash",remainCash) ;
        }

        saveDB.setMoney((BigDecimal) rHash.get("money"));
        final BigDecimal cash = (BigDecimal) rHash.get("cash");
        saveDB.setUserCashMoney(cash);
        saveDB.setUsedCashMoney(cash);
        saveDB.setDeductMoney(cash);

        return rHash;

    }



    /**
     * 普通退款
     * @param input
     * @param resultMap
     * @param userInfo  后台发起退款的用户信息
     */
    public void normalReturn(AdminOrder input, HashMap<String, Object> resultMap, SessionUser userInfo) {

        DynamicDataSourceContextHolder.push(DB_slave);

        FmbOrderReturns saveDB = new FmbOrderReturns();

        boolean needOperateCash = false ;
        boolean lastCodeReturn = false ;

        //saveDB 传入到orderReturnBeginQuery 并设置一些 数据
        final FmbOrderDetailQueryResult orderQueryResult = orderReturnBeginQuery(input.getOrderSn(),saveDB);

        final BigDecimal orderCashMoney = orderQueryResult.getOrderInfo().getCashMoney();
        if (orderCashMoney.compareTo(BigDecimal.ZERO)==1) {
            needOperateCash = true ;
        }


        final ArrayList<String> reserveCode = input.getReserveCode();
        logger.info("{}",reserveCode);

        if  (GOODS_TYPE_7==  orderQueryResult.getGoodsType() && ( reserveCode==null || reserveCode.isEmpty())) {
            throw new BadLogicException("没有选择房券code");
        }

        if  (GOODS_TYPE_6==  orderQueryResult.getGoodsType()  && input.getRoomNumber()> orderQueryResult.getHotelRemainMaxRoomCanCancel() ) {
            throw new BadLogicException("日历房超出最大可退房间数量");
        }


        BeanUtils.copyProperties(input,saveDB);
        saveDB.setMoney(new BigDecimal("0"));
        saveDB.setMaxMoney(new BigDecimal("0"));
        saveDB.setFmbReturnMoney(new BigDecimal("0"));
        saveDB.setShopReturnMoney(new BigDecimal("0"));
        saveDB.setDivideCouponMoney(new BigDecimal("0"));

        saveDB.setCouponMoney(new BigDecimal("0"));
        saveDB.setCulturalCouponMoney(new BigDecimal("0"));
        saveDB.setUsedCashMoney(new BigDecimal("0"));
        saveDB.setDeductMoney(new BigDecimal("0"));
        saveDB.setBackMoney(new BigDecimal("0"));
        saveDB.setUserCashMoney(new BigDecimal("0"));
        saveDB.setReduceMaxMoney(new BigDecimal("0"));

        //服务端 计算出的根据用户选择数量 应该退的金额  ,但需求要求 前台可修改. 所以暂时没用了.
        BigDecimal returnMoney= BigDecimal.ZERO ;
        //用户选择退几个
        int returnCount = 0 ;


        List<FmbExchangeCodes> fmbExchangeCodes = new ArrayList<>() ;

        //日历房逻辑
        if  (GOODS_TYPE_6==  orderQueryResult.getGoodsType() ) {


            int selectCount = input.getRoomNumber().intValue() ;

            if (orderQueryResult.getRemainReturnMaxMoney().compareTo(input.getReturnMoney())!=0 &&
                    input.getReturnMoney().compareTo(new BigDecimal(selectCount).multiply(orderQueryResult.getSkuGoodsPriceSubCoupon()))==1) {
                throw new BadLogicException("正常退款金额大于商品最大可退金额");
            }

            fmbExchangeCodes = queryExchangeCode(input.getOrderSn());

            final List<FmbExchangeCodes> collect2 = fmbExchangeCodes.stream().filter(code -> code.getStatus() == 0).collect(Collectors.toList());

//            final int codeCanUse = (int) fmbExchangeCodes.stream().filter(code -> code.getStatus() == 0).count();

//            if (input.getRoomNumber()>collect2.size()) {
            if (input.getRoomNumber()>orderQueryResult.getOrderGoods().getGoodsNumber()) {
                throw new BadLogicException("可退房间数量超过最大可退房间数");
            }

            final String returnCodes =
                    collect2.stream().limit(input.getRoomNumber()*orderQueryResult.getDayDiff()/orderQueryResult.getEachExchangeCodeTotalNightRoom()  ).map(FmbExchangeCodes::getExchangeCode).collect(Collectors.joining(
                            ","));

//            int dayDiff =
//                    FmbDateUtil.dayDiff(FmbDateUtil.localDateTime2String10( orderQueryResult.getOrderGoods().getLeaveTime()),
//                    FmbDateUtil.localDateTime2String10( orderQueryResult.getOrderGoods().getPlayTime())) ;
//            //每一个电子码代码的间夜
//            final int eachExchangeCode_jy = orderQueryResult.getOrderGoods().getGoodsNumber() * dayDiff / orderQueryResult.getHotelExchangeCode();
//            int returnRoomNum = eachExchangeCode_jy * input.getRoomNumber()/dayDiff ;

            saveDB.setGoodsNum(input.getRoomNumber());
            saveDB.setReturnCodes(returnCodes);


            if (selectCount < orderQueryResult.getHotelRemainMaxRoomCanCancel()) {
                returnMoney = orderQueryResult.getSkuGoodsPriceSubCoupon().multiply(new BigDecimal(selectCount)) ;
            } else if (selectCount == orderQueryResult.getHotelRemainMaxRoomCanCancel()) {
                // 订单原价 - 优惠券价格 - 不能退款的码的价格
                returnMoney =
                        orderQueryResult.getMoneyNoCouponAndShippingFee().subtract(orderQueryResult.getAlreadyReturnMoney()) .subtract( new BigDecimal(orderQueryResult.getHotelRoomNum()-selectCount).multiply(orderQueryResult.getSkuGoodsPriceSubCoupon()) )  ;

                lastCodeReturn = true ;
            }

            saveDB.setCouponMoney(orderQueryResult.getEachSkuCouponDivide().multiply(new BigDecimal(selectCount)));
            saveDB.setMaxMoney(orderQueryResult.getSkuGoodsPriceSubCoupon().multiply(new BigDecimal(selectCount)));
            if (lastCodeReturn) {
                saveDB.setCouponMoney(orderQueryResult.getCouponMoney().subtract (new BigDecimal(orderQueryResult.getHotelRoomNum()-selectCount).multiply(orderQueryResult.getEachSkuCouponDivide())) );
                saveDB.setMaxMoney(orderQueryResult.getSkuGoodsPrice().multiply(new BigDecimal(selectCount)).subtract(saveDB.getCouponMoney()));
            }

        }


        //房券逻辑
        if  (GOODS_TYPE_7==  orderQueryResult.getGoodsType() ) {
            final Set<String> inputCodeSet = input.getReserveCode().stream().collect(Collectors.toSet());
            final Set<String> codeSet = orderQueryResult.getHotelCodeCanReturn().stream().map(FmbReserveHotelCodes::getCode).collect(Collectors.toSet());

            for (String is : inputCodeSet) {
                if (!codeSet.contains(is)) {
                    throw new BadLogicException("房券号异常");
                }
            }



            int  tot = orderService.queryFmbOrderRelatedData(saveDB.getOrderSn()).getContainTimes().intValue() ;
            final boolean haveFullCode = orderQueryResult.getHotelCodeCanReturn().stream().filter(c -> c.getStatus() == 1 && c.getNumber().intValue() == tot).count() > 0;

            if (!haveFullCode) {
                throw new BadLogicException("无未使用房券可退");
            }

            //前台发起才校验
            if(userInfo!=null && userInfo.getUserType()== SESSIONUSER_FRONT  ){

                final boolean userCanStartHotelCodeReturn = haveFullCode && orderService.userCanStartHotelCodeReturn(saveDB.getOrderSn());
                if (!userCanStartHotelCodeReturn  ) {
                    throw new BadLogicException("不符合房券退款政策");
                }
            }



            final int selectCount = inputCodeSet.size();

            returnCount = inputCodeSet.size();


            if (orderQueryResult.getRemainReturnMaxMoney().compareTo(input.getReturnMoney())!=0 &&
                    input.getReturnMoney().compareTo(new BigDecimal(selectCount).multiply(orderQueryResult.getSkuGoodsPriceSubCoupon()))==1) {
                throw new BadLogicException("正常退款金额大于商品最大可退金额");
            }


            saveDB.setGoodsNum(selectCount);
            saveDB.setReturnCodes(input.getReserveCode().stream().collect(Collectors.joining(",")));


            if (selectCount < orderQueryResult.getHotelReserveCodeCanReturnTotal()) {
                returnMoney = orderQueryResult.getSkuGoodsPriceSubCoupon().multiply(new BigDecimal(selectCount)) ;
            } else if (selectCount == orderQueryResult.getHotelReserveCodeCanReturnTotal()) {
                // 订单原价 - 优惠券价格 - 不能退款的码的价格
                returnMoney =
                        orderQueryResult.getMoneyNoCouponAndShippingFee()
                                .subtract(orderQueryResult.getAlreadyReturnMoney())
                                .subtract( new BigDecimal(orderQueryResult.getHotelReserveCodeNotReturn()).multiply(orderQueryResult.getSkuGoodsPriceSubCoupon()) )  ;

                lastCodeReturn = true ;
            }

            saveDB.setCouponMoney(orderQueryResult.getEachSkuCouponDivide().multiply(new BigDecimal(selectCount)));
            saveDB.setMaxMoney(orderQueryResult.getSkuGoodsPriceSubCoupon().multiply(new BigDecimal(selectCount)));
            if (lastCodeReturn) {
                saveDB.setCouponMoney(orderQueryResult.getCouponMoney().subtract (new BigDecimal(orderQueryResult.getHotelReserveCodeNotReturn()).multiply(orderQueryResult.getEachSkuCouponDivide())) );
                saveDB.setMaxMoney(orderQueryResult.getSkuGoodsPrice().multiply(new BigDecimal(selectCount)).subtract(saveDB.getCouponMoney()));
            }

        }

//        final BigDecimal alreadyReturnMoney = orderQueryResult.getAlreadyReturnMoney();
        final BigDecimal remainReturnMaxMoney = orderQueryResult.getRemainReturnMaxMoney();

        if (input.getReturnClass()==0 && input.getReturnMoney().compareTo(remainReturnMaxMoney)==1 ){
            throw new BadLogicException("正常退款:退款金额大于剩余最大可退");
        }


        //已经 退还余额
        final BigDecimal returnCashMoney = orderQueryResult.getReturnedCashMoney() ;
//                orderQueryResult.getReturnsList().stream().filter(re-> re.getStatus()!=3).map(FmbOrderReturns::getUsedCashMoney).reduce(BigDecimal.ZERO,
//                        BigDecimal::add);


        returnMoneyMathCashFirst(input.getReturnMoney(), orderQueryResult.getOrderInfo().getRealpayMoney(),
                orderQueryResult.getOrderInfo().getCashMoney(), orderQueryResult.getAlreadyReturnMoney().subtract(returnCashMoney),
                returnCashMoney,saveDB);



        saveDB.setReturnClass(0);
        saveDB.setAdminUid(userInfo.getUserId());
        saveDB.setAdminDesc("后台发起正常退货");

        if (userInfo.getUserType()== SESSIONUSER_FRONT) {
            /**
             ******************************************************************
             *     退款单  AdminUid -1  代表前台用户发起的退款
             ******************************************************************
             **/
            saveDB.setAdminUid(-1);
            saveDB.setAdminDesc("前台用户退货");
        }

        saveDB.setStatus(0);
        if (input.isHotelOrderOfFromReserveReturn()) {
            //酒店预约单退款 退款单状态直接变为 1
            saveDB.setStatus(1);
            saveDB.setAdminDesc("后台发起正常退货,酒店预约单直接审核通过");
            //酒店预约单直接退 增加逻辑
            saveDB.setMaxMoney(saveDB.getMoney().add(saveDB.getUserCashMoney()));

        }


        saveDB.setThirdPartStatus(0);

        saveDB.setUploadFile("");
        if (input.getUploadFile() != null && !input.getUploadFile().isEmpty()) {
            saveDB.setUploadFile(input.getUploadFile().stream().collect(Collectors.joining(",")));
        }

        /**
         * ****************************************************************
         *         如果是酒店预约单退款 ,而且用户也没有补差价, 是不需要生成退款单的
         *         直接 交易关闭 退还次数 ,还发短信通知用户审核通过
         *         如果用户 需要补差价 需要生成退款单,而且
         ******************************************************************
         */
        if (input.isHotelOrderOfFromReserveReturn()) {

            HashMap<String, Object> rHash = new HashMap<>();

            ReqorderDetail dInput = new ReqorderDetail();
            dInput.setOrderSn(input.getOrderSn());
            dInput.setMess(userInfo.myInfo()+"在后台发起酒店预约单直接退货");
            dInput.setAdminReturn(1);
            dInput.setAdminUid(userInfo.getUserId());
//            dInput.setCancelType("fmbx_hotel_reserve_return_direct");

            //返回次数信息等
            orderService.cancelOrderByOrderSn(rHash,dInput,null,
                    isOrderCancelNotNeedReturnMoney(saveDB)?ADMIN001: FMBX_HOTEL_RESERVE_RETURN_NEED_PART_MONEY) ;

            final String returnOrderSmsContent = createReturnOrderSmsContent(orderQueryResult.getOrderInfo(), saveDB,
                    SMS_TEMPLATE_HOTEL_RESERVE_ORDER_RETURN_DIRECT_OK);
            logger.info("returnOrderSmsContent {} #[{}] ",saveDB.getMobile(),returnOrderSmsContent );
            FmbSmsUtil.sendSms(saveDB.getMobile(),returnOrderSmsContent,"fmbx_hotel_reserve_return_direct");

            if (isOrderCancelNotNeedReturnMoney(saveDB)) {

                resultMap.put("result",1) ;
                return ;
            }


        }


        //做最后的数据库操作
        final int rInt = self.saveReturnCore(input, userInfo, saveDB, orderQueryResult,BigDecimal.ZERO);
        resultMap.put("result",1) ;
        if (rInt==1) {
            //通知用户
            if (1==input.getSendSms()&& StringUtils.isNotBlank(input.getSmsTemplate())) {
                FmbScheduleUtil.doAsyncJob(() -> {
                    final String returnOrderSmsContent = createReturnOrderSmsContent(orderQueryResult.getOrderInfo(), saveDB, input.getSmsTemplate());
                    logger.info("returnOrderSmsContent {} #[{}] ",saveDB.getMobile(),returnOrderSmsContent );
                    FmbSmsUtil.sendSms(saveDB.getMobile(),returnOrderSmsContent,"fmbx_create_order_return");

                });
            }
        }


    }

    public boolean isOrderCancelNotNeedReturnMoney(FmbOrderReturns saveDB) {
        return saveDB.getMoney().add(saveDB.getUserCashMoney()).compareTo(BigDecimal.ZERO) == 0;
    }


    /**
     * 生成 退款单的核心逻辑
     * @param input  前端原始输入
     * @param userInfo 用户信息
     * @param returnDBInfo 构造好的 退款单信息
     * @param orderQueryResult 构造退款单时的查询结果
     * @param moreCash    额外增加余额数量
     * @return
     */
    @DSTransactional
    public int saveReturnCore(AdminOrder input, SessionUser userInfo, FmbOrderReturns returnDBInfo,
                              FmbOrderDetailQueryResult orderQueryResult, BigDecimal moreCash) {


        DynamicDataSourceContextHolder.push(DB_master);

        final StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("历史已退:"+orderQueryResult.getAlreadyReturnMoney()+",剩余最大可退:"+ orderQueryResult.getRemainReturnMaxMoney()+
                "元") ;
        stringBuffer.append("现金返还:"+returnDBInfo.getMoney()+",余额返还:"+returnDBInfo.getUserCashMoney()+",优惠券分摊:"+returnDBInfo.getCouponMoney()) ;

        final boolean saveReturn = orderReturnsService.save(returnDBInfo);

        if (saveReturn) {

            //如果是房券 而且勾选了电子码  ,补偿退款 也可以用
            // 冻结房券电子码
            if (GOODS_TYPE_7== orderQueryResult.getGoodsType()   &&  input.getReserveCode()!=null && !input.getReserveCode().isEmpty()) {

                RedissonLockService.doLockJob(FmbConstants.USER_RESOURCE_LOCK, returnDBInfo.getUid()+"", 5000, 4000,
                        () -> updateReserveCode2ReturnStatus(input, returnDBInfo),"系统繁忙");
            }

            //酒店 日历房正常退款
            if (GOODS_TYPE_6== orderQueryResult.getGoodsType() && input.getReturnClass()==0){

                Wrapper<FmbxHotelGoodsDetail> wrDetail = new LambdaUpdateWrapper<FmbxHotelGoodsDetail>()
                        .eq(FmbxHotelGoodsDetail::getRecId, orderQueryResult.getOrderGoods().getRecId())
                        .setSql(" room_number_return = room_number_return+ "+ input.getRoomNumber())
                        ;
                hotelGoodsDetailService.update(wrDetail);

                final String[] split = returnDBInfo.getReturnCodes().split(",");

//                orderQueryResult.getDayDiff()*input.getRoomNumber()/orderQueryResult.getEachExchangeCodeTotalNightRoom()

                //更新电子码状态
                for (Integer i = 0; i < split.length; i++) {

                    Wrapper<FmbExchangeCodes> wrCodeUpdate = new LambdaUpdateWrapper<FmbExchangeCodes>()
                            .eq(FmbExchangeCodes::getExchangeCode,split[i])
                            .eq(FmbExchangeCodes::getOrderSn,returnDBInfo.getOrderSn())
                            .set(FmbExchangeCodes::getStatus,2)
                            ;
                    exchangeCodesService.update(wrCodeUpdate) ;
                }

            }

            //增加返现操作  需要返还余额时 增加日志
            if (returnDBInfo.getUserCashMoney().compareTo(BigDecimal.ZERO)==1) {
                addCashLog(returnDBInfo,moreCash);
            }

            //增加  return note
            saveReturnNote(input, returnDBInfo);

            //增加 return action
            saveReturnAction(userInfo, returnDBInfo);

            if (input.getFrontReturnReserveCode() != null && 1==input.getFrontReturnReserveCode().intValue()) {

                FmbOrderActions action = new FmbOrderActions();
                action.setOrderSn(returnDBInfo.getOrderSn());
                //代表前台用户
                action.setAdminUid(-1);
                action.setOrderStatus(orderQueryResult.getOrderInfo().getOrderStatus());
                action.setShippingStatus(orderQueryResult.getOrderInfo().getShippingStatus());
                action.setPayStatus(orderQueryResult.getOrderInfo().getPayStatus());
                action.setActionType("");
                action.setActionNote("前台用户“"+userInfo.getUsername()+"-->"+userInfo.getUserId()+"”操作--申请退货");
                action.setLogTime(LocalDateTime.now());
                orderActionsService.save(action) ;

            }


            //处理已结算退款
            if (orderQueryResult.isExistsSettle()) {
                final FmbOrderGoods orderGoods = orderQueryResult.getOrderGoods();

                //如果是酒店预约单
                if( (orderQueryResult.getOrderInfo().getGoodsType() == 6 && orderQueryResult.getOrderInfo().getOrderType() == 2)){

                    FmbOrderReserveCancelSettle rerserveCancel = new FmbOrderReserveCancelSettle();

                    rerserveCancel.setType(1);
                    rerserveCancel.setOrderSn(orderQueryResult.getOrderInfo().getOrderSn());
                    rerserveCancel.setRecId(orderGoods.getRecId());
                    rerserveCancel.setAid(orderQueryResult.getOrderInfo().getAid());
                    rerserveCancel.setShopUserId(orderGoods.getShopUserId());
                    rerserveCancel.setSettleMoney(new BigDecimal(returnDBInfo.getGoodsNum()).multiply(orderGoods.getSettlePrice()));
                    rerserveCancel.setSettleId(0);
                    rerserveCancel.setCtime(LocalDateTime.now());

                    reserveCancelSettleService.save(rerserveCancel) ;

                }else {
                    //不是酒店预约单
                    FmbOrderReturnsSettle returnSettle = new FmbOrderReturnsSettle();
                    returnSettle.setReturnId(returnDBInfo.getReturnId());
                    returnSettle.setAid(orderQueryResult.getOrderInfo().getAid());

                    returnSettle.setShopUserId(orderGoods.getShopUserId());
                    returnSettle.setOrderSn(orderGoods.getOrderSn());
                    returnSettle.setRecId(orderGoods.getRecId());
                    returnSettle.setMoney(returnDBInfo.getMoney());

                    returnSettle.setSettleMoney(new BigDecimal("0"));
                    // 正常退
                    if (0==returnDBInfo.getReturnClass()) {
                        returnSettle.setSettleMoney(new BigDecimal(returnDBInfo.getGoodsNum()).multiply(orderGoods.getSettlePrice()));
                    }
                    returnSettle.setSettleId(0);
                    returnSettle.setStatus(0);

                    returnSettle.setCtime(LocalDateTime.now());

                    returnsSettleService.save(returnSettle) ;
                }


            }

        }

        return 1 ;
    }


    /**
     * 退款单被 拒绝
     * @param returnId
     */
    public void returnOrderReject(Integer returnId,String noteStr ,List<String> uploadFile){

        SessionUser userInfo = userUnionService.nowUserInfo();
        //退货单详情
        final FmbOrderReturns returnInfoOfDB = orderReturnsService.getById(returnId);

        if (returnInfoOfDB == null) {
            throw new BadLogicException("退款单不存在!"+returnId);
        }

        //完成拒绝动作
        FmbOrderReturns upDB = new FmbOrderReturns();
        upDB.setReturnId(returnId);
        upDB.setStatus(3);
        upDB.setReturnTime(LocalDateTime.now());
        final boolean b = orderReturnsService.updateById(upDB);

        final FmbOrderInfo orderInfo = orderService.queryFmbOrderInfo(returnInfoOfDB.getOrderSn());

        if (b) {

            if (GOODS_TYPE_7==orderInfo.getGoodsType()) {
                final List<String> codes = Arrays.asList(returnInfoOfDB.getReturnCodes().split(",")).stream().collect(Collectors.toList());

                updateReserveCode2Status(codes,returnInfoOfDB.getOrderSn(),1) ;

            }

            if (GOODS_TYPE_6== orderInfo.getGoodsType()&& returnInfoOfDB.getReturnClass()==0){
                Wrapper<FmbxHotelGoodsDetail> wrDetail = new LambdaUpdateWrapper<FmbxHotelGoodsDetail>()
                        .eq(FmbxHotelGoodsDetail::getOrderSn, returnInfoOfDB.getOrderSn())
                        .eq(FmbxHotelGoodsDetail::getRecId, returnInfoOfDB.getRecId())
                        .setSql(" room_number_return = room_number_return- "+ returnInfoOfDB.getGoodsNum())
                        ;
                hotelGoodsDetailService.update(wrDetail);
            }

            if(returnInfoOfDB.getUserCashMoney().compareTo(BigDecimal.ZERO)==1){
                unableCashLog(returnInfoOfDB) ;
            }


            FmbOrderReturnsNote note = new FmbOrderReturnsNote();
            note.setAdminUid(userInfo.getUserId());
            note.setOrderSn(returnInfoOfDB.getOrderSn());
            note.setRecId(returnInfoOfDB.getRecId());
            note.setGoodsId(returnInfoOfDB.getGoodsId());
            note.setReturnId(returnInfoOfDB.getReturnId());
            note.setReturnStatus(returnInfoOfDB.getStatus());
            note.setUploadFile("");
            note.setActionNote("");

            if (StringUtils.isNotBlank(noteStr)) {
                note.setActionNote(noteStr);
            }
            if (uploadFile!=null && !uploadFile.isEmpty()) {
                note.setUploadFile(uploadFile.stream().collect(Collectors.joining(",")));
            }
            note.setCreateTime(LocalDateTime.now());
            returnsNoteService.save(note) ;

            FmbOrderReturnsAction action = new FmbOrderReturnsAction();
            action.setAdminUid(userInfo.getUserId());
            action.setOrderSn(returnInfoOfDB.getOrderSn());
            action.setRecId(returnInfoOfDB.getRecId());
            action.setGoodsId(returnInfoOfDB.getGoodsId());
            action.setReturnId(returnInfoOfDB.getReturnId());
            action.setReturnStatus(returnInfoOfDB.getStatus());
            action.setActionNote( userInfo.myInfo()+ "操作--拒绝退货");
            action.setCreateTime(LocalDateTime.now());
            returnsActionService.save(action );


        }

    }


    /**
     * 退款单 审核通过
     * @param returnId
     */
    public void returnOrderConfirm(Integer returnId,String noteStr ,List<String> uploadFile){

        SessionUser userInfo = userUnionService.nowUserInfo();
        //退货单详情
        final FmbOrderReturns returnInfoOfDB = orderReturnsService.getById(returnId);

        if (returnInfoOfDB == null) {
            throw new BadLogicException("退款单不存在!"+returnId);
        }

        //完成  退货单变为审核通过
        FmbOrderReturns upDB = new FmbOrderReturns();
        upDB.setReturnId(returnId);
        upDB.setStatus(1);
//        upDB.setReturnTime(LocalDateTime.now());
        final boolean b = orderReturnsService.updateById(upDB);

        final FmbOrderInfo orderInfo = orderService.queryFmbOrderInfo(returnInfoOfDB.getOrderSn());

        if (b) {


            FmbOrderReturnsNote note = new FmbOrderReturnsNote();
            note.setAdminUid(userInfo.getUserId());
            note.setOrderSn(returnInfoOfDB.getOrderSn());
            note.setRecId(returnInfoOfDB.getRecId());
            note.setGoodsId(returnInfoOfDB.getGoodsId());
            note.setReturnId(returnInfoOfDB.getReturnId());
            note.setReturnStatus(returnInfoOfDB.getStatus());
            note.setUploadFile("");
            note.setActionNote("");

            if (StringUtils.isNotBlank(noteStr)) {
                note.setActionNote(noteStr);
            }
            if (uploadFile!=null && !uploadFile.isEmpty()) {
                note.setUploadFile(uploadFile.stream().collect(Collectors.joining(",")));
            }
            note.setCreateTime(LocalDateTime.now());
            returnsNoteService.save(note) ;

            FmbOrderReturnsAction action = new FmbOrderReturnsAction();
            action.setAdminUid(userInfo.getUserId());
            action.setOrderSn(returnInfoOfDB.getOrderSn());
            action.setRecId(returnInfoOfDB.getRecId());
            action.setGoodsId(returnInfoOfDB.getGoodsId());
            action.setReturnId(returnInfoOfDB.getReturnId());
            action.setReturnStatus(returnInfoOfDB.getStatus());
            action.setActionNote( userInfo.myInfo()+ "操作--审核通过");
            action.setCreateTime(LocalDateTime.now());
            returnsActionService.save(action );

        }

    }


    /**
     * 退款单 完成退货
     * @param returnId
     */
    public void returnOrderFinish(Integer returnId,String noteStr ,List<String> uploadFile){

        SessionUser userInfo = userUnionService.nowUserInfo();
        //退货单详情
        final FmbOrderReturns returnInfoOfDB = orderReturnsService.getById(returnId);

        if (returnInfoOfDB == null) {
            throw new BadLogicException("退款单不存在!"+returnId);
        }

        // 变为完成退货 更新退货完成时间
        FmbOrderReturns upDB = new FmbOrderReturns();
        upDB.setReturnId(returnId);
        upDB.setStatus(2);
        upDB.setReturnTime(LocalDateTime.now());
        final boolean b = orderReturnsService.updateById(upDB);

        final FmbOrderInfo orderInfo = orderService.queryFmbOrderInfo(returnInfoOfDB.getOrderSn());

        if (b) {

            if (GOODS_TYPE_7==orderInfo.getGoodsType()) {
                final List<String> codes = Arrays.asList(returnInfoOfDB.getReturnCodes().split(",")).stream().collect(Collectors.toList());

                //把电子码改为失效
                updateReserveCode2Status(codes,returnInfoOfDB.getOrderSn(),3) ;

                Wrapper<FmbReserveHotelCodes> wrCode = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                        .eq(FmbReserveHotelCodes::getOrderSn,returnInfoOfDB.getOrderSn())
                        .select(FmbReserveHotelCodes::getStatus,FmbReserveHotelCodes::getCode)
                        ;
                final List<FmbReserveHotelCodes> codeList = hotelCodesService.list(wrCode);

                final int countStatus3 = (int) codeList.stream().filter(code -> code.getStatus() == 3).count();

                //如果是所有电子码都冻结
                if (countStatus3==codeList.size()) {

                    Wrapper<FmbOrderReturns> wrRe = new LambdaQueryWrapper<FmbOrderReturns>()
                            .eq(FmbOrderReturns::getOrderSn,returnInfoOfDB.getOrderSn())
                            .select(FmbOrderReturns::getStatus,FmbOrderReturns::getMoney,FmbOrderReturns::getUserCashMoney)
                            ;
                    final List<FmbOrderReturns> listReturns = orderReturnsService.list(wrRe);

//                    且订单金额全部退完之后订单状态交易关闭
                    final BigDecimal reduceAll = listReturns.stream().filter(r -> r.getStatus() == 2 || r.getStatus() == 4).map(re -> re.getMoney().add(re.getUserCashMoney())).reduce(BigDecimal.ZERO, BigDecimal::add);

                    if (reduceAll.compareTo(orderInfo.getCashMoney().add(orderInfo.getRealpayMoney()))>=0) {
                        closeOrder(returnInfoOfDB.getOrderSn(),userInfo,orderInfo,returnId) ;
                    }
                }

            }

            if(returnInfoOfDB.getUserCashMoney().compareTo(BigDecimal.ZERO)==1){
                sendCash(returnInfoOfDB) ;
            }


            FmbOrderReturnsNote note = new FmbOrderReturnsNote();
            note.setAdminUid(userInfo.getUserId());
            note.setOrderSn(returnInfoOfDB.getOrderSn());
            note.setRecId(returnInfoOfDB.getRecId());
            note.setGoodsId(returnInfoOfDB.getGoodsId());
            note.setReturnId(returnInfoOfDB.getReturnId());
            note.setReturnStatus(returnInfoOfDB.getStatus());
            note.setUploadFile("");
            note.setActionNote("");

            if (StringUtils.isNotBlank(noteStr)) {
                note.setActionNote(noteStr);
            }
            if (uploadFile!=null && !uploadFile.isEmpty()) {
                note.setUploadFile(uploadFile.stream().collect(Collectors.joining(",")));
            }
            note.setCreateTime(LocalDateTime.now());
            returnsNoteService.save(note) ;

            FmbOrderReturnsAction action = new FmbOrderReturnsAction();
            action.setAdminUid(userInfo.getUserId());
            action.setOrderSn(returnInfoOfDB.getOrderSn());
            action.setRecId(returnInfoOfDB.getRecId());
            action.setGoodsId(returnInfoOfDB.getGoodsId());
            action.setReturnId(returnInfoOfDB.getReturnId());
            action.setReturnStatus(returnInfoOfDB.getStatus());
            action.setActionNote( userInfo.myInfo()+ "操作--拒绝退货");
            action.setCreateTime(LocalDateTime.now());
            returnsActionService.save(action );


        }

    }



    /**
     * 关闭订单
     * @param orderSn
     * @param userInfo
     * @param orderInfo
     * @param returnId
     */
    public void closeOrder(String orderSn, SessionUser userInfo, FmbOrderInfo orderInfo, Integer returnId) {

        Wrapper<FmbOrderInfo> wrOrder = new LambdaUpdateWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,orderSn)
                .set(FmbOrderInfo::getOrderStatus,6)
                ;
        final boolean update = fmbOrderInfoService.update(wrOrder);

        if (update) {

            FmbOrderActions action = new FmbOrderActions();
            action.setOrderSn(orderSn);
            //代表前台用户
            action.setAdminUid(userInfo!=null?userInfo.getUserId():-1);
            action.setOrderStatus(6);
            action.setShippingStatus(orderInfo.getShippingStatus());
            action.setPayStatus(orderInfo.getPayStatus());
            action.setActionType("");
            action.setActionNote("退货完成,关闭订单,退货单id:"+returnId);
            action.setLogTime(LocalDateTime.now());
            orderActionsService.save(action) ;

        }

    }


    public void unableCashLog(FmbOrderReturns returnDBInfo){

        Wrapper<FmbUserCashLog> wrU = new LambdaUpdateWrapper<FmbUserCashLog>()
                .eq(FmbUserCashLog::getOrderSn,returnDBInfo.getOrderSn())
                .eq(FmbUserCashLog::getReturnIds,returnDBInfo.getReturnId()+"")
                .set(FmbUserCashLog::getStatus,3)
                .set(FmbUserCashLog::getUtime,LocalDateTime.now())
                ;
        userCashLogService.update(wrU);

    }


    /**
     * 给用户 返还余额
     * @param returnInfoOfDB
     */
    public void sendCash(FmbOrderReturns returnInfoOfDB) {

        final FmbUserExtinfo fmbUserExtinfo = orderService.queryUserBalance(returnInfoOfDB.getUid());

        FmbUserCashChange changeDB = new FmbUserCashChange();

        if (fmbUserExtinfo==null) {
            FmbUserExtinfo db = new FmbUserExtinfo() ;
            db.setUid(returnInfoOfDB.getUid());
            db.setMoney(returnInfoOfDB.getUserCashMoney());
            db.setCtime(LocalDateTime.now());
            db.setUtime(LocalDateTime.now());
            userExtinfoService.save(db) ;

            changeDB.setMoney(new BigDecimal("0"));
            changeDB.setNewMoney(returnInfoOfDB.getUserCashMoney());
            changeDB.setChangeMoney(returnInfoOfDB.getUserCashMoney());
        }else {

            changeDB.setMoney(fmbUserExtinfo.getMoney());
            changeDB.setNewMoney(returnInfoOfDB.getUserCashMoney().add(fmbUserExtinfo.getMoney()));
            changeDB.setChangeMoney(returnInfoOfDB.getUserCashMoney());
            Wrapper<FmbUserExtinfo> wrU = new LambdaUpdateWrapper<FmbUserExtinfo>()
                    .eq(FmbUserExtinfo::getId,fmbUserExtinfo.getId())
                    .set(FmbUserExtinfo::getMoney,returnInfoOfDB.getUserCashMoney().add(fmbUserExtinfo.getMoney()))
                    ;
            userExtinfoService.update(wrU) ;


        }

        Wrapper<FmbUserCashLog> wrLog = new LambdaQueryWrapper<FmbUserCashLog>()
                .eq(FmbUserCashLog::getOrderSn,returnInfoOfDB.getOrderSn())
                .eq(FmbUserCashLog::getReturnIds,""+returnInfoOfDB.getReturnId())
                .select(FmbUserCashLog::getCashlogId,FmbUserCashLog::getStatus,FmbUserCashLog::getReturnIds)
                ;
        final FmbUserCashLog cashLog = userCashLogService.getOne(wrLog);

        //更新 log 为失效
        Wrapper<FmbUserCashLog> wrLogUpdate = new LambdaUpdateWrapper<FmbUserCashLog>()
                .eq(FmbUserCashLog::getCashlogId,cashLog.getCashlogId())
                .set(FmbUserCashLog::getStatus,3)
                .set(FmbUserCashLog::getUtime,LocalDateTime.now())
                ;
        userCashLogService.update(wrLogUpdate) ;



        changeDB.setUid(returnInfoOfDB.getUid());
        changeDB.setCashlogId(cashLog.getCashlogId());
        changeDB.setOrderSn(returnInfoOfDB.getOrderSn());

        changeDB.setNote("完成退款余额返还");
        changeDB.setCtime(LocalDateTime.now());


        cashChangeService.save(changeDB) ;


    }





    public void saveReturnAction(SessionUser userInfo, FmbOrderReturns returnDBInfo) {
        FmbOrderReturnsAction action = new FmbOrderReturnsAction();

        action.setAdminUid(returnDBInfo.getAdminUid());
        action.setOrderSn(returnDBInfo.getOrderSn());
        action.setRecId(returnDBInfo.getRecId());
        action.setGoodsId(returnDBInfo.getGoodsId());
        action.setReturnId(returnDBInfo.getReturnId());
        action.setReturnStatus(returnDBInfo.getStatus());

        if (returnDBInfo.getReturnClass()==0) {
            action.setActionNote( userInfo.myInfo()+ "操作--发起正常退货");
        }
        if (returnDBInfo.getReturnClass()==1) {
            action.setActionNote( userInfo.myInfo()+ "操作--发起补偿退货");
        }

        action.setCreateTime(LocalDateTime.now());

        returnsActionService.save(action );
    }

    public void saveReturnNote(AdminOrder input, FmbOrderReturns returnDBInfo) {
        FmbOrderReturnsNote note = new FmbOrderReturnsNote();
        note.setAdminUid(returnDBInfo.getAdminUid());
        note.setOrderSn(returnDBInfo.getOrderSn());
        note.setRecId(returnDBInfo.getRecId());
        note.setGoodsId(returnDBInfo.getGoodsId());
        note.setReturnId(returnDBInfo.getReturnId());
        note.setReturnStatus(returnDBInfo.getStatus());
        note.setUploadFile("");
        note.setActionNote("");

        if (StringUtils.isNotBlank(input.getInternalNote())) {
            note.setActionNote(input.getInternalNote());
        }
        if (input.getInternalUploadFile()!=null && !input.getInternalUploadFile().isEmpty()) {
            note.setUploadFile(input.getInternalUploadFile().stream().collect(Collectors.joining(",")));
        }
        note.setCreateTime(LocalDateTime.now());
        returnsNoteService.save(note) ;
    }

    public void updateReserveCode2ReturnStatus(AdminOrder input, FmbOrderReturns saveDB) {
        Wrapper<FmbReserveHotelCodes> wrCodeUpdate = new LambdaUpdateWrapper<FmbReserveHotelCodes>()
                .eq(FmbReserveHotelCodes::getOrderSn, saveDB.getOrderSn())
                .in(FmbReserveHotelCodes::getCode, input.getReserveCode())
                .set(FmbReserveHotelCodes::getStatus,2)
                ;
        hotelCodesService.update(wrCodeUpdate);
    }

    public void updateReserveCode2Status(List<String> codes ,String orderSn ,Integer status) {
        Wrapper<FmbReserveHotelCodes> wrCodeUpdate = new LambdaUpdateWrapper<FmbReserveHotelCodes>()
                .in(FmbReserveHotelCodes::getCode, codes)
                .eq(FmbReserveHotelCodes::getOrderSn, orderSn)
                .set(FmbReserveHotelCodes::getStatus,status)
                ;
        hotelCodesService.update(wrCodeUpdate);
    }


}
