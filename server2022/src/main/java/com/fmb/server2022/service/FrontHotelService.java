package com.fmb.server2022.service;

//NEW_IMPORT_DOMAIN


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.FrontActvHotelDomain;
import com.fmb.server2022.domain.FrontActvRecomDomain;
import com.fmb.server2022.domain.FrontHotelDomain;
import com.fmb.server2022.domain.FrontResSkuDomain;
import com.fmb.server2022.domain.FrontRoomDomain;
import com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain;
import com.fmb.server2022.domain.FrontRoomSkuDomain;
import com.fmb.server2022.domain.FrontRoomSkuOrderDomain;
import com.fmb.server2022.domain.FrontRoomSuiteDomain;
import com.fmb.server2022.fmbx.entity.FmbxActivityLike;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteStock;
import com.fmb.server2022.fmbx.service.IFmbxActivityLikeService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.fmb.server2022.mapper.FrontHotelDao;
import com.fmb.server2022.service.order.OrderOfHotelDateService;
import com.fmb.server2022.service.order.OrderOfHotelReserveService;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fmb.server2022.service.order.OrderService.buildReserveSkuBuyLimit;
import static java.time.temporal.ChronoUnit.DAYS;

@Service
public class FrontHotelService {
    private static Logger logger = LoggerFactory.getLogger(FrontHotelService.class);

    @Autowired
    JedisPool jedisPool ;
    @Autowired
    FrontHotelDao frontHotelDao;
    @Autowired
    IFmbxHotelReserveSkuService hotelReserveSkuService;
    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService;
    @Autowired
    IFmbxSuiteService suiteService ;
    @Autowired
    IFmbxSuiteStockService suiteStockService;
    @Autowired
    IFmbxActivityLikeService activityLikeService;
    @Autowired
    OrderOfHotelDateService orderOfHotelDateService ;
    @Autowired
    FrontHotelService self;
    @Autowired
    CouponService couponService;

    /**
     * 前台酒店详情
     */
    public void getHotelDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontHotelDomain> frontHotelDomainList = frontHotelDao.selectHotelDetail(input);
        if (frontHotelDomainList.size() == 0) {
            throw new BadLogicException("酒店不存在");
        }
        resultMap.put("hotel", frontHotelDomainList.get(0));
    }


    /**
     * 前台房型详情
     */
    public void getRoomSkuDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontRoomDomain> frontRoomDomainList = frontHotelDao.selectFrontRoomDetail(input);
        if (frontRoomDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        FrontRoomDomain room = frontRoomDomainList.get(0);
        //价格
        ReqHotel req = new ReqHotel();
        Set<Integer> skuIds = new HashSet();
        skuIds.add(room.getSkuId());
        req.setSkuIds(skuIds);
        List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
        if(priceInfo.size()>0){
            room.setMinGoodsPrice(priceInfo.get(0).getMinGoodsPrice());
        }
        resultMap.put("room", room);
        req = new ReqHotel();
        req.setBpsId(room.getBpsId());
        req.setRoomId(room.getRoomId());
        List<String> mediaUrl = new ArrayList<>();
        List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
        for(BpsMediaInfoDomain m : mediaInfo){
            mediaUrl.add(m.getUrl());
        }
        room.setMediaUrl(mediaUrl);
    }

    /**
     * 前台房劵房型详情
     */
    public void getResSkuDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontRoomSuiteDomain> frontSuiteDomainList = frontHotelDao.selectResSuite(input);
        if (frontSuiteDomainList.size() == 0) {
            throw new BadLogicException("套餐不存在");
        }
        FrontRoomSuiteDomain suite = frontSuiteDomainList.get(0);
        final Integer suiteId = suite.getSuiteId();
        List<FrontRoomDomain> frontRoomDomainList = self.queryFrontRoomDomains(suiteId);
        resultMap.put("roomList", frontRoomDomainList);
        resultMap.put("suite", suite);
        resultMap.put("skuId", input.getSkuId());

        final ArrayList<String> buyLimitStr = buildReserveSkuBuyLimit( suite.getFlagBuyLimit(), suite.getEachOrderMinNum(), suite.getEachOrderMaxNum(), suite.getMaxBuyAllNum());
        resultMap.put("buyLimitStr", buyLimitStr.stream().collect(Collectors.joining(",")));

    }

    public List<FrontRoomDomain> queryFrontRoomDomains(Integer suiteId) {
        ReqHotel req = new ReqHotel();
        req.setSuiteId(suiteId);
        List<FrontRoomDomain> frontRoomDomainList = frontHotelDao.selectResRoomDetail(req);
        if (frontRoomDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        for (FrontRoomDomain room : frontRoomDomainList) {
            req = new ReqHotel();
            req.setBpsId(room.getBpsId());
            req.setRoomId(room.getRoomId());
            List<String> mediaUrl = new ArrayList<>();
            List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
            for (BpsMediaInfoDomain m : mediaInfo) {
                mediaUrl.add(m.getUrl());
            }
            room.setMediaUrl(mediaUrl);
            room.setSuiteContentList(null);
        }
        return frontRoomDomainList;
    }

    /**
     * 日历房低价日历
     */
    public void selectHotelDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontActvHotelDomain> frontActvHotelDomainList = frontHotelDao.selectFrontActvHotel(input);
        if (frontActvHotelDomainList.size() == 0) {
            throw new BadLogicException("活动不存在");
        }
        List<FrontRoomSkuDomain> roomList = frontHotelDao.selectFrontRoomSku(input);

        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = chooseDate.plusDays(-3);
        LocalDate priceEndDate = chooseDate.plusDays(7);
        if(StringUtils.isNotEmpty(input.getLeaveDate())){
            LocalDate leaveDate = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            if (leaveDate.isAfter(priceEndDate)){
                priceEndDate = leaveDate.plusDays(1);
            }
        }
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        //查低价
        Set<Integer> skuIds = roomList.stream().map(FrontRoomSkuDomain::getSkuId).collect(Collectors.toSet());
        input.setSkuIds(skuIds);
        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelDatePrice(input);

        BigDecimal minGoodsPrice = BigDecimal.ZERO;
        if (skuIds.size()>0) {
            List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(input);
            if (priceInfo.get(0) != null) {
                minGoodsPrice = priceInfo.get(0).getMinGoodsPrice();
            }
        }
        //状态 价格
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            p.setMinGoodsPrice(minGoodsPrice);
            p.setShowStatus(p.getGoodsPrice().compareTo(p.getMinGoodsPrice())==0 ? 1 : 0);
            if(p.getStockStatus() != 1 || p.getStockNum() < 1){
                p.setShowStatus(2);//关房或无库存
            }
        }
        resultMap.put("xaid", input.getXaid());
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", priceEndDate);
        resultMap.put("playDate", input.getPlayDate());
        resultMap.put("leaveDate", input.getLeaveDate());
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    /**
     * 日历房选择日历
     */
    public void selectHotelSkuDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontRoomDomain> frontRoomList = frontHotelDao.selectFrontRoomDetail(input);
        if (frontRoomList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        Integer skuId = input.getSkuId();
        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = chooseDate.plusDays(0);
        LocalDate priceEndDate = chooseDate.plusDays(180);
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelSkuDatePrice(input);
        //查套餐
        Integer suiteId = 0;
        if (hotelRoomPriceList.size()>0){
            suiteId = hotelRoomPriceList.stream().filter(x->x.getSuiteId()>0).findFirst().get().getSuiteId() ;
        }
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime)
                .eq(FmbxSuite::getSuiteId,suiteId);
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);
        //查低价
        BigDecimal minGoodsPrice = BigDecimal.ZERO;
        ReqHotel req = new ReqHotel();
        Set<Integer> skuIds = new HashSet();
        skuIds.add(skuId);
        req.setSkuIds(skuIds);
        List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
        if (priceInfo.get(0) != null) {
            minGoodsPrice = priceInfo.get(0).getMinGoodsPrice();
        }
        //状态 价格
        boolean lastStatus = true;
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            p.setMinGoodsPrice(minGoodsPrice);
            p.setShowStatus(p.getGoodsPrice().compareTo(p.getMinGoodsPrice())==0 ? 1 : 0);
            if(p.getStockStatus() != 1 || p.getStockNum() < 1){
                p.setShowStatus(2);//关房或无库存
            }
            if (!this.isValidCheckinDate(p.getDateStr(),suiteInfo)){
                p.setShowStatus(2);//超出提前预定
            }
            if(lastStatus && p.getShowStatus()==2 && showStartDate.equals(FmbDateUtil.dateToLocalDate(p.getDateStr()))){
                showStartDate = showStartDate.plusDays(1);
            }else{
                lastStatus = false;
            }
        }
        resultMap.put("skuId", skuId);
        resultMap.put("nightMin", suiteInfo!=null?suiteInfo.getNightMin():0);
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", priceEndDate);
        resultMap.put("playDate", input.getPlayDate());
        resultMap.put("leaveDate", input.getLeaveDate());
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    private boolean isValidCheckinDate(String checkInDate, FmbxSuite suite) {
        //当前秒数 从凌晨0点算起的 秒数
        int seconds = DateTime.now().get(DateTimeFieldType.secondOfDay()) ;
//        logger.info("suite ##{}",suite);
        final String[] split = suite.getPreReserveTime().split(":");

        if (split.length!=2) {
            return false;
        }

        final int hour = Integer.parseInt(split[0]);
        final int min = Integer.parseInt(split[1]);
        if (hour<0 || hour>23) {
            return false;
        }
        if (min<0 || min>59) {
            return false;
        }

        int dayAdd = suite.getPreReserveDay() ;
        final int p = hour * 3600 + min * 60;
        if(seconds > p){
            dayAdd ++ ;
        }

        final LocalDate localDateAim = FmbDateUtil.dateToLocalDate(checkInDate);
        if (localDateAim == null) {
            return false;
        }
        //  假设今天为6月1日，设置提前1天，17:00前可订，则表示用户在今天17:00之前可以预订到6月2号入住的产品，在今天17:00之后能预订到6月3号入住的产品
        final LocalDate minDate = LocalDate.now().plus(dayAdd, DAYS);
        if (minDate.isAfter(localDateAim)) {
            return false;
        }

        return true ;
    }

    /**
     * 房劵选择日历
     */
    public void selectResSkuDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        //suiteId房劵,skuId日历房
        Integer skuId = input.getSkuId();
        Integer suiteId = input.getSuiteId();
        ReqHotel req = new ReqHotel();
        req.setSkuId(skuId);
        req.setType(7);
        List<FrontRoomDomain> frontRoomList = frontHotelDao.selectFrontRoomDetail(req);
        if (frontRoomList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        if (suiteId == null) {
            suiteId = frontRoomList.get(0)==null? 0 : frontRoomList.get(0).getSuiteId();
        }
        req = new ReqHotel();
        req.setSuiteId(suiteId);
        List<FrontRoomSuiteDomain> frontSuiteDomainList = frontHotelDao.selectResSuite(req);
        if (frontSuiteDomainList.size() == 0) {
            throw new BadLogicException("套餐不存在");
        }
        FrontRoomSuiteDomain suite = frontSuiteDomainList.get(0);
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime)
                .eq(FmbxSuite::getSuiteId,suiteId);
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);

        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = LocalDate.now();
        LocalDate priceEndDate = suite.getReserveChooseEndTime();
        if (priceEndDate.isBefore(showEndDate)){
            showEndDate = priceEndDate;
        }
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelSkuDatePrice(input);
        //状态 价格
        boolean lastStatus = true;
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            if(input.getResGoodPrice()==null) {
                p.setGoodsPrice(p.getGoodsPrice().subtract(suite.getGoodsPrice().divide(suite.getTotalNeight()==0 ? BigDecimal.ONE:new BigDecimal(suite.getTotalNeight()))));
            }else{
                p.setGoodsPrice(p.getGoodsPrice().subtract(input.getResGoodPrice()));
            }
            if(p.getGoodsPrice().compareTo(BigDecimal.ZERO)<0){
                p.setGoodsPrice(BigDecimal.ZERO);
            }
            if(p.getStockStatus() == 1 && p.getStockNum() >= FrontRoomSkuDatePriceDomain.REMAINDER_NUM){
                p.setShowStatus(3);//充足
            }else if(p.getStockStatus() == 1 && p.getStockNum() > 0){
                p.setShowStatus(4);
            }else{
                p.setShowStatus(2);
            }
            LocalDate pdate = FmbDateUtil.dateToLocalDate(p.getDateStr());
            if(suite.getInvalidReserveDateSet().contains(pdate) || suite.getInvalidReserveDateSet().contains(pdate.getDayOfWeek().getValue())){
                p.setShowStatus(2);
            }
            if (!this.isValidCheckinDate(p.getDateStr(),suiteInfo)){
                p.setShowStatus(2);//超出提前预定
            }
            if(lastStatus && p.getShowStatus()==2 && showStartDate.equals(FmbDateUtil.dateToLocalDate(p.getDateStr()))){
                showStartDate = showStartDate.plusDays(1);
            }else{
                lastStatus = false;
            }
        }
        resultMap.put("skuId", skuId);
        resultMap.put("nightMin", suite!=null?suite.getNightMin():0);
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", priceEndDate);
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    /**
     * 酒店活动详情
     */
    public void selectFrontActvHotel(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontActvHotelDomain> frontActvHotelDomainList = frontHotelDao.selectFrontActvHotel(input);
        if (frontActvHotelDomainList.size() == 0) {
            throw new BadLogicException("活动不存在");
        }
        //最低价格 状态（热销中，距开抢，距结束）? 已售数量 是否有日历房 是否有房劵
        //是否有日历房
        FrontActvHotelDomain frontActvHotelDomain = frontActvHotelDomainList.get(0);
        if (input.getLongitude()!=null && input.getLatitude()!=null) {
            Double d = FmbNumberUtil.GetDistance(input.getLongitude(),input.getLatitude(),frontActvHotelDomain.getLongitude(), frontActvHotelDomain.getLatitude());
            frontActvHotelDomain.setDistanceStr("距您直线距离约"+d+"米");
        }
        List<FrontRoomSkuDomain> roomList = frontHotelDao.selectFrontRoomSku(input);
        if(roomList.size()>0) {
            frontActvHotelDomain.setHasRoomSuite(1);
        }else{
            frontActvHotelDomain.setHasRoomSuite(0);
        }
        //是否有房劵
        List<FrontResSkuDomain> resList = frontHotelDao.selectFrontResSku(input);
        if(resList.size()>0) {
            frontActvHotelDomain.setHasReserveSuite(1);
        }else{
            frontActvHotelDomain.setHasReserveSuite(0);
        }
        //如果是房券 距开抢，距结束
        if (input.getType()!=null && input.getType().equals(FrontActvHotelDomain.RES_TYPE)){
            frontActvHotelDomain.setHasRoomSuite(0);
            if(resList.size()>0) {
                /*List<FrontResSkuDomain> startList = resList.stream().sorted(Comparator.comparing(FrontResSkuDomain::getSellStartTime)).collect(Collectors.toList());
                frontActvHotelDomain.setStartTime(startList.get(0).getSellStartTime());
                if(frontActvHotelDomain.getStartTimeDuration().getSeconds()>0){
                    frontActvHotelDomain.setStatus(2);
                }*/
                List<FrontResSkuDomain> endList = resList.stream().sorted(Comparator.comparing(FrontResSkuDomain::getSellEndTime).reversed()).collect(Collectors.toList());
                frontActvHotelDomain.setEndTime(endList.get(0).getSellEndTime());
                if(frontActvHotelDomain.getEndTimeDuration().getSeconds()>0 && frontActvHotelDomain.getEndTimeDuration().getSeconds()<(24 * 3600 * 7)){
                    frontActvHotelDomain.setStatus(3);
                }
                List<FrontResSkuDomain> showList = resList.stream().sorted(Comparator.comparing(FrontResSkuDomain::getSellCountDownShowFlag)).collect(Collectors.toList());
                if(showList.get(0).getSellCountDownShowFlag().intValue()==0){
                    frontActvHotelDomain.setStatus(1);
                }
            }
        }
        //销售量
        Integer roomSellNum = roomList.stream().mapToInt(FrontRoomSkuDomain::getSellNum).sum();
        Integer resSellNum = resList.stream().mapToInt(FrontResSkuDomain::getSellNum).sum();
        logger.info("roomSellNum={} resSellNum={}",roomSellNum,resSellNum);
        frontActvHotelDomain.setSaleNum(roomSellNum+resSellNum);
        //最低价格
        ReqHotel req = new ReqHotel();
        Set<Integer> skuIds = roomList.stream().map(FrontRoomSkuDomain::getSkuId).collect(Collectors.toSet());
        if (skuIds.size()>0) {
            req.setSkuIds(skuIds);
            List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
            if (priceInfo.get(0) != null) {
                frontActvHotelDomain.setGoodsPrice(priceInfo.get(0).getMinGoodsPrice());
                frontActvHotelDomain.setMarketPrice(priceInfo.get(0).getMinMarketPrice());
            }
        }

        frontActvHotelDomain.setBpsUrl("http://m.baidu.com");
        frontActvHotelDomain.setAidContentInfoUrl("http://m.fumubang.com");
        resultMap.put("xaid", frontActvHotelDomain.getXaid());
        resultMap.put("xaidStr", "产品编号：");
        resultMap.put("mediaUrl", frontActvHotelDomain.getMediaUrl());
        resultMap.put("activityDetail", frontActvHotelDomain);
        self.selectCustomerService(resultMap);
        //优惠券
        ReqCoupon reqCoupon = new ReqCoupon();
        BeanUtils.copyProperties(input,reqCoupon);
        couponService.getCouponByAid(resultMap,reqCoupon,nowUser);
        //精彩推荐
        List<FrontActvRecomDomain> activityRecommend = new ArrayList<>();
        for (int i=0;i<5;i++) {
            FrontActvRecomDomain domain = new FrontActvRecomDomain();
            domain.setXaid(105761);
            domain.setTitle("固安福朋喜来登酒店");
            domain.setIntro("宿高级房");
            domain.setSubTitle("享早餐+西式简餐/夏季特饮+北京野生动物园门票/大湖皮划艇/跑跑卡丁车+儿童活动+游");
            domain.setGoodsPrice(new BigDecimal(899));
            domain.setMarketPrice(new BigDecimal(1099));
            domain.setGoodsPriceUnit("起");
            domain.setMediaUrl("http://img3.fumubang.net/jupload/20221214/fmbupload_1671002134005.jpg");
            activityRecommend.add(domain);
        }
        resultMap.put("activityRecommend", activityRecommend);
        self.isFavorite(resultMap, input, nowUser);
    }


    /**
     * 预售房劵套餐列表
     */
    public void selectFrontResSku(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        if (input.getType()!=null && input.getType().equals(FrontActvHotelDomain.RES_TYPE)){
            resultMap.put("titleStr", "套餐抢购");
        }else{
            resultMap.put("titleStr", "预售房券");
        }
        resultMap.put("sloganStr", "优惠疯狂囤，时间灵活兑！");

        List<FrontResSkuDomain> frontSkuDomainList = frontHotelDao.selectFrontResSku(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("sku不存在");
        }
        for (FrontResSkuDomain sku : frontSkuDomainList) {
            sku.setType(input.getType());
            if (StringUtils.isNotEmpty(sku.getRoomIds())) {
                String[] roomIds = sku.getRoomIds().split(",");
                Set<String> roomIdSet = Arrays.stream(roomIds).collect(Collectors.toSet());
                ReqHotel req = new ReqHotel();
                req.setRoomIds(roomIdSet);
                List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
                if(mediaInfo.size()>0) {
                    sku.setMediaUrl(mediaInfo.get(0).getUrl());
                    sku.setMediaCount(mediaInfo.size());
                }
            }
        }
        resultMap.put("skuCount", frontSkuDomainList.size());
        resultMap.put("skuList", frontSkuDomainList);

    }


    /**
     * 日历房套餐列表
     */
    public void selectFrontRoomSku(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        resultMap.put("titleStr", "立即预定");
        Integer chooseDuration = 0;
        if (StringUtils.isNotEmpty(input.getPlayDate()) && StringUtils.isNotEmpty(input.getLeaveDate())) {
            LocalDate s = FmbDateUtil.dateToLocalDate(input.getPlayDate());
            LocalDate e = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            Period p = Period.between(s, e);
            chooseDuration = p.getDays();
        }

        List<FrontRoomSkuDomain> frontSkuDomainList = frontHotelDao.selectFrontRoomSku(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getSortValue,FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime)
                .eq(FmbxSuite::getXaid,input.getXaid());
        List<FmbxSuite> suiteList = suiteService.list(suiteWrapper);
        Map<Integer, FmbxSuite> suiteMap = suiteList.stream().collect(Collectors.toMap(FmbxSuite::getSuiteId, Function.identity()));
        if (suiteMap == null) {
            throw new BadLogicException("套餐不存在");
        }

        for(FrontRoomSkuDomain sku : frontSkuDomainList){
            //图片
            ReqHotel req = new ReqHotel();
            req.setRoomId(sku.getRoomId());
            List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
            if(mediaInfo.size()>0) {
                sku.setMediaUrl(mediaInfo.get(0).getUrl());
                sku.setMediaCount(mediaInfo.size());
            }
            //价格
            req = new ReqHotel();
            Set<Integer> skuIds = new HashSet();
            skuIds.add(sku.getSkuId());
            req.setSkuIds(skuIds);
            List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
            if(priceInfo.get(0) != null){
                sku.setGoodsPrice(priceInfo.get(0).getMinGoodsPrice());
                sku.setMarketPrice(priceInfo.get(0).getMinMarketPrice());
            }
            //状态 所选晚数是suite最小晚数整数倍,所选日期sku有房有库存
            FmbxSuite suite = suiteMap.get(sku.getSuiteId());
            boolean isIntTimesNight = suite.getNightMin() != null && suite.getNightMin() != 0 && chooseDuration % suite.getNightMin() == 0;
            Wrapper<FmbxSuiteStock> stockWrapper = new LambdaQueryWrapper<FmbxSuiteStock>()
                    .select(FmbxSuiteStock::getRoomId,FmbxSuiteStock::getTargetDate,FmbxSuiteStock::getStockNum,FmbxSuiteStock::getStatus)
                    .eq(FmbxSuiteStock::getRoomId,sku.getRoomId())
                    .between(FmbxSuiteStock::getTargetDate,input.getPlayDate(),input.getLeaveDate());
            List<FmbxSuiteStock> stockList = suiteStockService.list(stockWrapper);
            boolean hasRoom = true;
            st : for(FmbxSuiteStock stock : stockList){
                if(stock.getStatus() != 1 || stock.getStockNum() < 1 || !isValidCheckinDate(FmbDateUtil.FORMATTER10.format(stock.getTargetDate()),suite)){
                    hasRoom = false;
                    break st;
                }
            }
            if(isIntTimesNight && hasRoom) {
                sku.setStatus(FrontRoomSkuDomain.STATUS_ORDEROOM);
            }else{
                sku.setStatus(FrontRoomSkuDomain.STATUS_SCHEDULED);
            }
        }
        //按套餐分组
        Map<Integer, List<FrontRoomSkuDomain>> groupBy = frontSkuDomainList.stream().collect(Collectors.groupingBy(FrontRoomSkuDomain::getSuiteId, LinkedHashMap::new,Collectors.toList()));
        List<FrontRoomSuiteDomain> frontSuiteDomainList = new ArrayList<>();
        for(Map.Entry<Integer,List<FrontRoomSkuDomain>> ele : groupBy.entrySet()){
            FrontRoomSuiteDomain s = new FrontRoomSuiteDomain();
            s.setSuiteId(ele.getKey());
            s.setSkuList(ele.getValue());
            s.setSkuCount(s.getSkuList().size());
            s.setSuiteName(ele.getValue().get(0).getSuiteName());
            s.setSortValue(suiteMap.get(ele.getKey()).getSortValue());
            frontSuiteDomainList.add(s);
        }
        resultMap.put("suiteList", frontSuiteDomainList);

    }

    /**
     * 日历房提交订单sku信息
     */
    public static final DateTimeFormatter ZH_MD = DateTimeFormatter.ofPattern("MM月dd日");
    public void selectOrderRoomInfo(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        Integer chooseDuration = 0;
        if (StringUtils.isNotEmpty(input.getPlayDate()) && StringUtils.isNotEmpty(input.getLeaveDate())) {
            LocalDate s = FmbDateUtil.dateToLocalDate(input.getPlayDate());
            LocalDate e = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            Period p = Period.between(s, e);
            chooseDuration = p.getDays();
            resultMap.put("chooseDuration", chooseDuration);
            resultMap.put("chooseDurationStr", "共"+chooseDuration+"晚");
            resultMap.put("playDate", s);
            resultMap.put("leaveDate", e);
            resultMap.put("playDateStr", ZH_MD.format(s)+" "+(StringUtils.isNotBlank(FmbDateUtil.getNearDateZh(s))?FmbDateUtil.getNearDateZh(s):FmbDateUtil.getDayOfWeekZh(s)));
            resultMap.put("leaveDateStr", ZH_MD.format(e)+" "+(StringUtils.isNotBlank(FmbDateUtil.getNearDateZh(e))?FmbDateUtil.getNearDateZh(e):FmbDateUtil.getDayOfWeekZh(e)));
        }

        List<FrontRoomSkuOrderDomain> frontSkuDomainList = frontHotelDao.selectFrontRoomSkuBySkuId(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        resultMap.put("orderRoomInfo", frontSkuDomainList.get(0));
    }

    public void isFavorite(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser){
        resultMap.put("isFavorite", false);
        if (nowUser != null) {
//            throw new BadLogicException("用户不存在");

            Wrapper<FmbxActivityLike> wrapper = new LambdaQueryWrapper<FmbxActivityLike>()
                    .select(FmbxActivityLike::getId)
                    .eq(FmbxActivityLike::getXaid,input.getXaid())
                    .eq(FmbxActivityLike::getXatype,input.getType())
                    .eq(FmbxActivityLike::getUid,nowUser.getUserId());
            List<FmbxActivityLike> activityList = activityLikeService.list(wrapper);
            if(activityList!=null && activityList.size()>0){
                resultMap.put("isFavorite", true);
            }
        }

    }

    public void updateFavorite(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        if (nowUser == null) {
            throw new BadLogicException("用户不存在");
        }
        Wrapper<FmbxActivityLike> wrapper = new LambdaQueryWrapper<FmbxActivityLike>()
                .select(FmbxActivityLike::getId)
                .eq(FmbxActivityLike::getXaid,input.getXaid())
                .eq(FmbxActivityLike::getXatype,input.getType())
                .eq(FmbxActivityLike::getUid,nowUser.getUserId());
        List<FmbxActivityLike> activityList = activityLikeService.list(wrapper);
        if(activityList!=null && activityList.size()>0){
            activityLikeService.removeById(activityList.get(0));
            resultMap.put("isFavorite", false);
        }else{
            FmbxActivityLike d = new FmbxActivityLike();
            d.setUid(nowUser.getUserId());
            d.setXaid(input.getXaid());
            d.setXatype(input.getType()==null?1:input.getType());
            d.setCtime(LocalDateTime.now());
            activityLikeService.save(d);
            resultMap.put("isFavorite", true);
        }
    }

    public void selectCustomerService(HashMap<String, Object> resultMap){
        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DB_10);

            String value = jedis.get("fmb_customer_service_switch_key");
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, String.class);
            List<String> valueList = mapper.readValue(value, javaType);

            resultMap.put("customer_service", valueList);
            return ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }
}