package com.fmb.server2022.service;

//NEW_IMPORT_DOMAIN


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.FrontActvHotelDomain;
import com.fmb.server2022.domain.FrontActvRecomDomain;
import com.fmb.server2022.domain.FrontHotelDomain;
import com.fmb.server2022.domain.FrontResSkuDomain;
import com.fmb.server2022.domain.FrontRoomDomain;
import com.fmb.server2022.domain.FrontRoomSkuDatePriceDomain;
import com.fmb.server2022.domain.FrontRoomSkuDomain;
import com.fmb.server2022.domain.FrontRoomSkuOrderDomain;
import com.fmb.server2022.domain.FrontRoomSuiteDomain;
import com.fmb.server2022.fmb_new.entity.FmbActivityTag;
import com.fmb.server2022.fmb_new.entity.FmbProductIndex;
import com.fmb.server2022.fmb_new.entity.JishigouCommonDistrict;
import com.fmb.server2022.fmb_new.mapper.JishigouCommonDistrictMapper;
import com.fmb.server2022.fmb_new.service.IFmbActivityTagService;
import com.fmb.server2022.fmb_new.service.IFmbProductIndexService;
import com.fmb.server2022.fmb_new.service.IJishigouCommonDistrictService;
import com.fmb.server2022.fmbx.entity.*;
import com.fmb.server2022.fmbx.service.*;
import com.fmb.server2022.mapper.FrontHotelDao;
import com.fmb.server2022.service.order.OrderOfHotelDateService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.DB_master;
import static com.fmb.basic.FmbConstants.DB_slave;
import static com.fmb.server2022.service.order.OrderOfHotelDateService.roomNumGenGenMin;
import static com.fmb.server2022.service.order.OrderService.buildReserveSkuBuyLimit;
import static java.time.temporal.ChronoUnit.DAYS;

@Service
public class FrontHotelService {
    private static Logger logger = LoggerFactory.getLogger(FrontHotelService.class);

    @Autowired
    JedisPool jedisPool ;
    @Autowired
    FrontHotelDao frontHotelDao;
    @Autowired
    IFmbxHotelReserveSkuService hotelReserveSkuService;
    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService;
    @Autowired
    IFmbxSuiteService suiteService ;
    @Autowired
    IFmbxActivityService activityService ;
    @Autowired
    IFmbxActivityExtService activityExtService ;
    @Autowired
    IFmbxSuiteStockService suiteStockService;
    @Autowired
    IFmbxActivityLikeService activityLikeService;
    @Autowired
    OrderOfHotelDateService orderOfHotelDateService ;
    @Autowired
    FrontHotelService self;
    @Autowired
    CouponService couponService;
    @Autowired
    IFmbProductIndexService productIndexService;
    @Autowired
    IJishigouCommonDistrictService commonDistrictService;
    @Autowired
    IFmbGoodsCategoryService categoryService;
    @Autowired
    IFmbActivityTagService activityTagService;
    @Autowired
    IFmbHotelReserveInfoService fmbHotelReserveInfoService;
    @Autowired
    IFmbCpssrcService cpssrcService;

    @Autowired
    IFmbUserBrowseRecordService browseRecordService ;

    /**
     * 前台酒店详情
     */
    public void getHotelDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontHotelDomain> frontHotelDomainList = frontHotelDao.selectHotelDetail(input);
        if (frontHotelDomainList.size() == 0) {
            throw new BadLogicException("酒店不存在");
        }
        FrontHotelDomain hotel = frontHotelDomainList.get(0);
        ReqHotel req = new ReqHotel();
        req.setBpsId(hotel.getBpsId());
        List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
        if (mediaInfo.size()>0) {
            hotel.setBgImgUrl(mediaInfo.get(0).getUrl());
        }
        if (StringUtils.isNotBlank(hotel.getIntroduce())) {
            hotel.setIntroduce(hotel.getIntroduce().replaceAll("src=", "data-src="));
        }
        resultMap.put("hotel", hotel);
    }


    /**
     * 前台房型详情
     */
    public void getRoomSkuDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontRoomDomain> frontRoomDomainList = frontHotelDao.selectFrontRoomDetail(input);
        if (frontRoomDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        FrontRoomDomain room = frontRoomDomainList.get(0);
        room.buildBedTypeStr();
        //价格
        ReqHotel req = new ReqHotel();
        Set<Integer> skuIds = new HashSet();
        skuIds.add(room.getSkuId());
        req.setSkuIds(skuIds);
        LocalDateTime now = LocalDateTime.now();
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getPreReserveDay,
                        FmbxSuite::getPreReserveTime,FmbxSuite::getTotalNeight)
                .eq(FmbxSuite::getSuiteId,room.getSuiteId());
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);
        room.setTotalNeight(suiteInfo.getTotalNeight());
        if(StringUtils.isNotBlank(suiteInfo.getPreReserveTime()) && FmbDateUtil.compareHourMin(suiteInfo.getPreReserveTime(),now.getHour(),now.getMinute()) > 0) {
            req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suiteInfo.getPreReserveDay()+1));
        }else{
            req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suiteInfo.getPreReserveDay()));
        }
        List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
        if(priceInfo.size()>0){
            final FrontRoomSkuDatePriceDomain frontRoomSkuDatePriceDomain = priceInfo.get(0);
            if (frontRoomSkuDatePriceDomain != null) {
                room.setMinGoodsPrice(frontRoomSkuDatePriceDomain.getMinGoodsPrice());
            }
        }
        resultMap.put("room", room);
        req = new ReqHotel();
        req.setBpsId(room.getBpsId());
        req.setRoomId(room.getRoomId());
        List<String> mediaUrl = new ArrayList<>();
        List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
        for(BpsMediaInfoDomain m : mediaInfo){
            mediaUrl.add(m.getUrl());
        }
        room.setMediaUrl(mediaUrl);
    }

    /**
     * 前台房劵房型详情
     */
    public void getResSkuDetail(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        input.setNeedResSkuFlagSell(1);
        List<FrontRoomSuiteDomain> frontSuiteDomainList = frontHotelDao.selectResSuite(input);
        if (frontSuiteDomainList.size() == 0) {
            throw new BadLogicException("套餐不存在");
        }
        FrontRoomSuiteDomain suite = frontSuiteDomainList.get(0);
        final Integer suiteId = suite.getSuiteId();
        List<FrontRoomDomain> frontRoomDomainList = self.queryFrontRoomDomains(suiteId);
        for(FrontRoomDomain frontRoomDomain : frontRoomDomainList){
            frontRoomDomain.buildBedTypeStr();
        }
        //suite.setSuiteName(suite.getSkuName());
        if(frontRoomDomainList.size()>0){
            if (suite.getHotelReserveSplit()==1) {
                for (FrontRoomDomain room : frontRoomDomainList) {
                    if (input.getSkuId().equals(room.getResSkuId())) {
                        suite.setRoomName(room.getRoomName());
                        break;
                    }
                }
            }else if (suite.getHotelReserveSplit()==2) {
                suite.setRoomName(frontRoomDomainList.stream().map(FrontRoomDomain::getRoomName).collect(Collectors.joining("/")));
                for (FrontRoomDomain room : frontRoomDomainList) {
                    room.setResRoomName(suite.getRoomName());
                }
            }
        }
        resultMap.put("roomList", frontRoomDomainList);
        resultMap.put("suite", suite);
        resultMap.put("skuId", input.getSkuId());
        resultMap.put("HotelReserveSplit", suite.getHotelReserveSplit());

        final ArrayList<String> buyLimitStr = buildReserveSkuBuyLimit( suite.getFlagBuyLimit(), suite.getEachOrderMinNum(), suite.getEachOrderMaxNum(), suite.getMaxBuyAllNum());
        resultMap.put("buyLimitStr", buyLimitStr.stream().collect(Collectors.joining(",")));

    }



    public List<FrontRoomDomain> queryFrontRoomDomains(Integer suiteId) {
        return queryFrontRoomDomains(suiteId,true) ;
    }

    public List<FrontRoomDomain> queryFrontRoomDomains(Integer suiteId,boolean mediaQuery) {
        return queryFrontRoomDomains(suiteId,mediaQuery,true) ;
    }

    public List<FrontRoomDomain> queryFrontRoomDomains(Integer suiteId,boolean mediaQuery,boolean needReserveFlagSellCondition) {
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getHotelReserveSplit,FmbxSuite::getFlagHotelReserve)
                .eq(FmbxSuite::getSuiteId,suiteId);
        FmbxSuite suite = suiteService.getOne(suiteWrapper);

        ReqHotel req = new ReqHotel();
        req.setSuiteId(suiteId);
        req.setNeedReserveFlagSellCondition(needReserveFlagSellCondition);
        List<FrontRoomDomain> frontRoomDomainList = new ArrayList<>();
        if (suite.getHotelReserveSplit()==1) {
            frontRoomDomainList = frontHotelDao.selectResSplitRoomDetail(req);
        }else if (suite.getHotelReserveSplit()==2){
            frontRoomDomainList = frontHotelDao.selectResSingleRoomDetail(req);
        }
        if (frontRoomDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }

        if (mediaQuery) {
            for (FrontRoomDomain room : frontRoomDomainList) {
                req = new ReqHotel();
                req.setBpsId(room.getBpsId());
                req.setRoomId(room.getRoomId());
                List<String> mediaUrl = new ArrayList<>();
                List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
                for (BpsMediaInfoDomain m : mediaInfo) {
                    mediaUrl.add(m.getUrl());
                }
                room.setMediaUrl(mediaUrl);
                room.setSuiteContentList(null);
            }
        }


        return frontRoomDomainList;
    }

    /**
     * 日历房低价日历
     */
    public void selectHotelDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontActvHotelDomain> frontActvHotelDomainList = frontHotelDao.selectFrontActvHotel(input);
        if (frontActvHotelDomainList.size() == 0) {
            throw new BadLogicException("活动不存在");
        }
        input.setType(6);
        List<FrontRoomSkuDomain> roomList = frontHotelDao.selectFrontRoomSku(input);

        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = chooseDate.plusDays(-3);
        LocalDate priceEndDate = chooseDate.plusDays(7);
        if(StringUtils.isNotEmpty(input.getLeaveDate())){
            LocalDate leaveDate = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            if (leaveDate.isAfter(priceEndDate)){
                priceEndDate = leaveDate.plusDays(1);
            }
        }
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        //查低价
        Set<Integer> skuIds = roomList.stream().map(FrontRoomSkuDomain::getSkuId).collect(Collectors.toSet());
        input.setSkuIds(skuIds);
        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelDatePrice(input);

        BigDecimal minGoodsPrice = BigDecimal.ZERO;
        if (skuIds.size()>0) {
            List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(input);
            if (priceInfo.get(0) != null) {
                minGoodsPrice = priceInfo.get(0).getMinGoodsPrice();
            }
        }
        //状态 价格
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            p.setMinGoodsPrice(minGoodsPrice);
            p.setShowStatus(p.getGoodsPrice().compareTo(p.getMinGoodsPrice())==0 ? 1 : 0);
            if(p.getStockStatus() != 1 || p.getStockNum() < 1){
                p.setShowStatus(2);//关房或无库存
            }
        }
        resultMap.put("xaid", input.getXaid());
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", priceEndDate);
        resultMap.put("playDate", input.getPlayDate());
        resultMap.put("leaveDate", input.getLeaveDate());
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    /**
     * 日历房选择日历
     */
    public void selectHotelSkuDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        List<FrontRoomDomain> frontRoomList = frontHotelDao.selectFrontRoomDetail(input);
        if (frontRoomList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        Integer skuId = input.getSkuId();
        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = chooseDate.plusDays(0);
        LocalDate priceEndDate = chooseDate.plusDays(180);
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelSkuDatePrice(input);
        //查套餐
        Integer suiteId = 0;
        if (hotelRoomPriceList.size()>0){
            suiteId = hotelRoomPriceList.stream().filter(x->x.getSuiteId()>0).findFirst().get().getSuiteId() ;
        }
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getPreReserveDay,
                        FmbxSuite::getPreReserveTime,FmbxSuite::getTotalNeight)
                .eq(FmbxSuite::getSuiteId,suiteId);
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);
        //查低价
        /*
        BigDecimal minGoodsPrice = BigDecimal.ZERO;
        ReqHotel req = new ReqHotel();
        Set<Integer> skuIds = new HashSet();
        skuIds.add(skuId);
        req.setSkuIds(skuIds);
        LocalDateTime now = LocalDateTime.now();
        if(StringUtils.isNotBlank(suiteInfo.getPreReserveTime()) && FmbDateUtil.compareHourMin(suiteInfo.getPreReserveTime(),now.getHour(),now.getMinute()) > 0) {
            req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suiteInfo.getPreReserveDay()+1));
        }else{
            req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suiteInfo.getPreReserveDay()));
        }
        List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
        if (priceInfo.get(0) != null) {
            minGoodsPrice = priceInfo.get(0).getMinGoodsPrice();
        }*/
        //状态 价格
        boolean lastStatus = true;
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            //p.setMinGoodsPrice(minGoodsPrice);
            //p.setShowStatus(p.getGoodsPrice().compareTo(p.getMinGoodsPrice())==0 ? 1 : 0);
            p.setShowStatus(0);
            if(p.getStockStatus() == 1 && p.getStockNum() >= FrontRoomSkuDatePriceDomain.REMAINDER_NUM){
            }else if(p.getStockStatus() == 1 && p.getStockNum() > 0){
                p.setShowStatus(4);
            }else{
                p.setShowStatus(2);//关房或无库存
            }
            if (!this.isValidCheckinDate(p.getDateStr(),suiteInfo)){
                p.setShowStatus(2);//超出提前预定
            }
            if(lastStatus && p.getShowStatus()==2 && showStartDate.equals(FmbDateUtil.dateToLocalDate(p.getDateStr()))){
                showStartDate = showStartDate.plusDays(1);
            }else{
                lastStatus = false;
            }
        }
        resultMap.put("skuId", skuId);
        resultMap.put("nightMin", suiteInfo!=null?suiteInfo.getNightMin():0);
        resultMap.put("suiteTotalNeight", suiteInfo!=null?suiteInfo.getTotalNeight():0);
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", priceEndDate);
        resultMap.put("playDate", input.getPlayDate());
        resultMap.put("leaveDate", input.getLeaveDate());
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    private boolean isValidCheckinDate(String checkInDate, FmbxSuite suite) {
        //当前秒数 从凌晨0点算起的 秒数
        int seconds = DateTime.now().get(DateTimeFieldType.secondOfDay()) ;
//        logger.info("suite ##{}",suite);
        final String[] split = suite.getPreReserveTime().split(":");

        if (split.length!=2) {
            return false;
        }

        final int hour = Integer.parseInt(split[0]);
        final int min = Integer.parseInt(split[1]);
        if (hour<0 || hour>23) {
            return false;
        }
        if (min<0 || min>59) {
            return false;
        }

        int dayAdd = suite.getPreReserveDay() ;
        final int p = hour * 3600 + min * 60;
        if(seconds > p){
            dayAdd ++ ;
        }

        final LocalDate localDateAim = FmbDateUtil.dateToLocalDate(checkInDate);
        if (localDateAim == null) {
            return false;
        }
        //  假设今天为6月1日，设置提前1天，17:00前可订，则表示用户在今天17:00之前可以预订到6月2号入住的产品，在今天17:00之后能预订到6月3号入住的产品
        final LocalDate minDate = LocalDate.now().plus(dayAdd, DAYS);
        if (minDate.isAfter(localDateAim)) {
            return false;
        }

        return true ;
    }

    /**
     * 房劵选择日历
     */
    public void selectResSkuDatePrice(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        //suiteId房劵,skuId日历房
        Integer skuId = input.getSkuId();
        Integer suiteId = input.getSuiteId();
        ReqHotel req = new ReqHotel();
        req.setSkuId(skuId);
        req.setType(7);
        List<FrontRoomDomain> frontRoomList = frontHotelDao.selectFrontRoomDetail(req);
        if (frontRoomList.size() == 0) {
            throw new BadLogicException("该房型库存不足,请选择其他房型");
        }
        if (suiteId == null) {
            suiteId = frontRoomList.get(0)==null? 0 : frontRoomList.get(0).getSuiteId();
        }
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getPreReserveDay,
                        FmbxSuite::getPreReserveTime,FmbxSuite::getTotalNeight,FmbxSuite::getHotelReserveSplit)
                .eq(FmbxSuite::getSuiteId,suiteId);
        FmbxSuite suiteInfo = suiteService.getOne(suiteWrapper);
        if (logger.isInfoEnabled()) {
            logger.info("suiteid={} totalNight={} minNight={}",suiteInfo.getSuiteId(),suiteInfo.getTotalNeight(),suiteInfo.getNightMin());
        }
        req = new ReqHotel();
        req.setSuiteId(suiteId);
        if (suiteInfo.getHotelReserveSplit()!=null && suiteInfo.getHotelReserveSplit()==1) {
            req.setHotelDateSkuId(skuId);
        }else{
            req.setHotelDateSkuId(0);
        }
        req.setNeedResSkuFlagSell(0);
        List<FrontRoomSuiteDomain> frontSuiteDomainList = frontHotelDao.selectResSuite(req);
        if (frontSuiteDomainList.size() == 0) {
            throw new BadLogicException("套餐不存在");
        }
        FrontRoomSuiteDomain suite = frontSuiteDomainList.get(0);

        LocalDate chooseDate = FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr());
        if (StringUtils.isNotEmpty(input.getPlayDate())){
            chooseDate = FmbDateUtil.dateToLocalDate(input.getPlayDate());
        }
        LocalDate showStartDate = chooseDate.plusDays(0);
        LocalDate showEndDate = chooseDate.plusDays(180);
        LocalDate priceStartDate = LocalDate.now();
        LocalDate priceEndDate = suite.getReserveChooseEndTime();
        if (priceEndDate.isBefore(showEndDate)){
            showEndDate = priceEndDate;
        }
        input.setPriceStartDate(priceStartDate);
        input.setPriceEndDate(priceEndDate);

        List<FrontRoomSkuDatePriceDomain> hotelRoomPriceList = frontHotelDao.selectHotelSkuDatePrice(input);
        //状态 价格
        boolean lastStatus = true;
        boolean needPlusDate = false;
        for (FrontRoomSkuDatePriceDomain p : hotelRoomPriceList) {
            p.setOrderType(2);
            if(input.getResGoodPrice()==null) {
                p.setGoodsPrice(p.getGoodsPrice().subtract(suite.getGoodsPrice().divide(suite.getTotalNeight()==0 ? BigDecimal.ONE:new BigDecimal(suite.getTotalNeight()),2, RoundingMode.HALF_UP)));
            }else{
                p.setGoodsPrice(p.getGoodsPrice().subtract(input.getResGoodPrice()));
            }
            if(p.getGoodsPrice().compareTo(BigDecimal.ZERO)<0){
                p.setGoodsPrice(BigDecimal.ZERO);
            }
            if(p.getStockStatus() == 1 && p.getStockNum() >= FrontRoomSkuDatePriceDomain.REMAINDER_NUM){
                p.setShowStatus(3);//充足
            }else if(p.getStockStatus() == 1 && p.getStockNum() > 0){
                p.setShowStatus(4);
            }else{
                p.setShowStatus(2);
            }
            LocalDate pdate = FmbDateUtil.dateToLocalDate(p.getDateStr());
            if(suite.getInvalidReserveDateSet().contains(pdate) || suite.getInvalidReserveWeekDaySet().contains(pdate.getDayOfWeek().getValue())){
                p.setShowStatus(2);
            }
            if (!this.isValidCheckinDate(p.getDateStr(),suiteInfo)){
                p.setShowStatus(2);//超出提前预定
            }
            if(lastStatus && p.getShowStatus()==2 && showStartDate.equals(FmbDateUtil.dateToLocalDate(p.getDateStr()))){
                showStartDate = showStartDate.plusDays(1);
            }else{
                lastStatus = false;
            }
            if (p.getShowStatus()!=2){
                needPlusDate = true;
            }
        }

        if(hotelRoomPriceList.size()>0 && needPlusDate) {
            FrontRoomSkuDatePriceDomain cplast = new FrontRoomSkuDatePriceDomain();
            cplast.setDateStr(priceEndDate.plusDays(1).format(FmbDateUtil.FORMATTER10));
            cplast.setHoliday("");
            cplast.setShowStatus(2);
            cplast.setGoodsPrice(BigDecimal.ZERO);
            cplast.setChooseStatus(0);
            cplast.setStockNum(0);
            hotelRoomPriceList.add(cplast);
        }else{
            hotelRoomPriceList.clear();
        }
        resultMap.put("skuId", skuId);
        if (suite != null && suite.getCanDivide()==0) {
            resultMap.put("nightMin", suite.getNightMin());
            resultMap.put("suiteTotalNeight", suite.getTotalNeight());
        }else if (suite != null && suite.getCanDivide()==1){
            resultMap.put("nightMin", suite.getNightMin());
            resultMap.put("suiteTotalNeight", suite.getNightMin());
        }else{
            resultMap.put("nightMin", 0);
            resultMap.put("suiteTotalNeight", 0);
        }
        resultMap.put("showStartDate", showStartDate);
        resultMap.put("showEndDate", needPlusDate?showEndDate.plusDays(1):showEndDate);
        resultMap.put("priceStartDate", priceStartDate);
        resultMap.put("priceEndDate", needPlusDate?priceEndDate.plusDays(1):priceEndDate);
        resultMap.put("roomPrice", hotelRoomPriceList);
    }

    /**
     * 酒店活动详情
     */
    public void selectFrontActvHotel(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        input.setType(6);
        List<FrontActvHotelDomain> frontActvHotelDomainList = frontHotelDao.selectFrontActvHotel(input);
        if (frontActvHotelDomainList.size() == 0) {
            throw new BadLogicException("活动不存在");
        }
        //是否有日历房
        FrontActvHotelDomain frontActvHotelDomain = frontActvHotelDomainList.get(0);
        if (frontActvHotelDomain.getStatus()==2){
            throw new BadLogicException("该产品已下架");
        }
        frontActvHotelDomain.setGoodsType(input.getType());
        if (input.getLongitude()!=null && input.getLatitude()!=null) {
            Double d = FmbNumberUtil.GetDistance(input.getLongitude(),input.getLatitude(),frontActvHotelDomain.getLongitude(), frontActvHotelDomain.getLatitude());
            if (Double.valueOf(1000).compareTo(d)<0) {
                frontActvHotelDomain.setDistanceStr("距您直线距离约" + String.format("%.1f",d/1000) + "公里");
            }else {
                frontActvHotelDomain.setDistanceStr("距您直线距离约" + d + "米");
            }
        }
        ReqHotel param = new ReqHotel();
        BeanUtils.copyProperties(input,param);
        param.setType(6);
        List<FrontRoomSkuDomain> roomList = frontHotelDao.selectFrontRoomSku(input);
        if(roomList.size()>0) {
            frontActvHotelDomain.setHasRoomSuite(1);
        }else{
            frontActvHotelDomain.setHasRoomSuite(0);
        }
        //是否有房劵
        List<FrontResSkuDomain> resList = frontHotelDao.selectFrontResSku(input);
        if(resList.size()>0) {
            frontActvHotelDomain.setHasReserveSuite(1);
        }else{
            frontActvHotelDomain.setHasReserveSuite(0);
        }
        //如果是房券 距开抢，距结束  && input.getType().equals(FrontActvHotelDomain.RES_TYPE 不判断房券了
        if (input.getType()!=null){
            if(resList.size()>0) {
                frontActvHotelDomain.setStatus(1);
                /*List<FrontResSkuDomain> startList = resList.stream().sorted(Comparator.comparing(FrontResSkuDomain::getSellStartTime).reversed()).collect(Collectors.toList());
                frontActvHotelDomain.setStartTime(startList.get(0).getSellStartTime());
                if(frontActvHotelDomain.getStartTimeDuration().getSeconds()>0){
                    frontActvHotelDomain.setStatus(2);
                }*/
                List<FrontResSkuDomain> endList = resList.stream().sorted(Comparator.comparing(FrontResSkuDomain::getSellEndTime)).collect(Collectors.toList());
                for (FrontResSkuDomain end : endList) {
                    if (end.getSellCountDownShowFlag()==1) {
                        frontActvHotelDomain.setEndTime(end.getSellEndTime());
                        if (frontActvHotelDomain.getEndTimeDuration().getSeconds() > 0 && frontActvHotelDomain.getEndTimeDuration().getSeconds() < (24 * 3600 * 7)) {
                            frontActvHotelDomain.setStatus(3);
                        }
                        break;
                    }
                }
            }
        }
        Wrapper<FmbxActivity> aWrapper = new LambdaQueryWrapper<FmbxActivity>()
                .select(FmbxActivity::getMinMarketPrice,FmbxActivity::getMinGoodsPrice,FmbxActivity::getMinMarketPriceHotelReserve,FmbxActivity::getMinGoodsPriceHotelReserve
                        ,FmbxActivity::getSellNumTotal,FmbxActivity::getXaid,FmbxActivity::getFlagHotelDate,FmbxActivity::getFlagHotelReserve)
                .eq(FmbxActivity::getXaid,frontActvHotelDomain.getXaid());
        FmbxActivity fmbxActivity = activityService.getOne(aWrapper);
        frontActvHotelDomain.setSellStatus(2);
        if (fmbxActivity != null) {
            //最低价格
            /*if (input.getType() != null && input.getType().equals(FrontActvHotelDomain.ROOM_TYPE)){
                frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPrice());
                frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPrice());
            }else if (input.getType() != null && input.getType().equals(FrontActvHotelDomain.RES_TYPE)) {
                frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPriceHotelReserve());
                frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPriceHotelReserve());
            }
            if (BigDecimal.ZERO.compareTo(fmbxActivity.getMinGoodsPrice())==0){
                if (1==fmbxActivity.getFlagHotelDate()){
                    frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPrice());
                    frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPrice());
                }else if (1==fmbxActivity.getFlagHotelReserve()) {
                    frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPriceHotelReserve());
                    frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPriceHotelReserve());
                }
            }*/
            if(BigDecimal.ZERO.compareTo(fmbxActivity.getMinGoodsPrice())==0){
                if(BigDecimal.ZERO.compareTo(fmbxActivity.getMinGoodsPriceHotelReserve())==0) {
                    frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPrice());
                    frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPrice());
                }else{
                    frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPriceHotelReserve());
                    frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPriceHotelReserve());
                }
            }else{
                if(BigDecimal.ZERO.compareTo(fmbxActivity.getMinGoodsPriceHotelReserve())==0) {
                    frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPrice());
                    frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPrice());
                }else{
                    if(fmbxActivity.getMinGoodsPrice().compareTo(fmbxActivity.getMinGoodsPriceHotelReserve()) < 0) {
                        frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPrice());
                        frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPrice());
                    }else{
                        frontActvHotelDomain.setGoodsPrice(fmbxActivity.getMinGoodsPriceHotelReserve());
                        frontActvHotelDomain.setMarketPrice(fmbxActivity.getMinMarketPriceHotelReserve());
                    }
                }
            }
            //销售量
            frontActvHotelDomain.setSaleNum(fmbxActivity.getSellNumTotal());
        }
        Wrapper<FmbProductIndex> pWrapper = new LambdaQueryWrapper<FmbProductIndex>()
                .select(FmbProductIndex::getStatus,FmbProductIndex::getSellStat,FmbProductIndex::getFmbxFlagHotelReserve,FmbProductIndex::getFmbxFlagHotelDate)
                .eq(FmbProductIndex::getApId,frontActvHotelDomain.getXaid());
        FmbProductIndex fmbProductIndex = productIndexService.getOne(pWrapper);
        if (fmbProductIndex != null){
            /*boolean flag = false;
            if ((fmbProductIndex.getFmbxFlagHotelDate()==1 && FrontActvHotelDomain.ROOM_TYPE == input.getType())
                    || (fmbProductIndex.getFmbxFlagHotelReserve()==1 && FrontActvHotelDomain.RES_TYPE==input.getType())){
                flag = true;
            }
            if (flag && fmbProductIndex.getSellStat()!=16 && frontActvHotelDomain.getSaleNum()==0){
                frontActvHotelDomain.setSellStatus(2);
                frontActvHotelDomain.setStatus(0);
            }else if(flag && fmbProductIndex.getSellStat()!=16 && frontActvHotelDomain.getSaleNum()>0){
                frontActvHotelDomain.setSellStatus(3);
                frontActvHotelDomain.setStatus(0);
            }else if(!flag && frontActvHotelDomain.getSaleNum()==0){
                frontActvHotelDomain.setSellStatus(2);
                frontActvHotelDomain.setStatus(0);
            }else if(!flag && frontActvHotelDomain.getSaleNum()>0){
                frontActvHotelDomain.setSellStatus(3);
                frontActvHotelDomain.setStatus(0);
            }else{
                frontActvHotelDomain.setSellStatus(1);
            }*/
            if (fmbProductIndex.getSellStat()!=16 && frontActvHotelDomain.getSaleNum()==0){
                frontActvHotelDomain.setSellStatus(2);
                frontActvHotelDomain.setStatus(0);
            }else if(fmbProductIndex.getSellStat()!=16 && frontActvHotelDomain.getSaleNum()>0){
                frontActvHotelDomain.setSellStatus(3);
                frontActvHotelDomain.setStatus(0);
            }else {
                frontActvHotelDomain.setSellStatus(1);
            }
        }
        String now = FmbDateUtil.nowDateStr();
        frontActvHotelDomain.setBpsUrl(FmbServerConfig.mSiteDomainHttp()+"/webapp/#/main/hotel/info/"+frontActvHotelDomain.getXaid());
        frontActvHotelDomain.setAidContentInfoUrl(FmbServerConfig.mSiteDomainHttp()+"/webapp/#/main/app-richtext/act_"+frontActvHotelDomain.getXaid());
        frontActvHotelDomain.setAidContentInfoPhpUrl(FmbServerConfig.mSiteDomainHttp()+"/mobile/hd_detail/show_desc?aid="+frontActvHotelDomain.getXaid()+"&from=app");
        Wrapper<FmbActivityTag> wrTag = new LambdaQueryWrapper<FmbActivityTag>()
                .select(FmbActivityTag::getId, FmbActivityTag::getTagName)
                .eq(FmbActivityTag::getAid, input.getXaid())
                .and(LambdaQueryWrapper->LambdaQueryWrapper.eq(FmbActivityTag::getAlwaysShow,1).le(FmbActivityTag::getStartTime,now).ge(FmbActivityTag::getEndTime,now)
                        .or().eq(FmbActivityTag::getAlwaysShow,0));
        List<FmbActivityTag> tagdbList = activityTagService.list(wrTag);
        if (tagdbList.size()>0) {
            List<String> tagnameList = tagdbList.stream().sorted(Comparator.comparing(FmbActivityTag::getId)).map(FmbActivityTag::getTagName).collect(Collectors.toList());
            frontActvHotelDomain.setActvTag(tagnameList);
        }else{
            frontActvHotelDomain.setActvTag(Collections.EMPTY_LIST);
        }
        resultMap.put("xaid", frontActvHotelDomain.getXaid());
        resultMap.put("xaidStr", "产品编号：");
        resultMap.put("mediaUrl", frontActvHotelDomain.getMediaUrl());
        resultMap.put("activityDetail", frontActvHotelDomain);
        self.selectCustomerService(resultMap);
        //优惠券
        ReqCoupon reqCoupon = new ReqCoupon();
        BeanUtils.copyProperties(input,reqCoupon);
        couponService.getCouponByAid(resultMap,reqCoupon,nowUser);
        //精彩推荐
        resultMap.put("activityRecommend",self.getActivityRecommend(frontActvHotelDomain.getXaid()).values());
        self.isFavorite(resultMap, input, nowUser);
        FmbScheduleUtil.doAsyncJob(() -> {
                if (StringUtils.isNotBlank(input.getCpssrc())){
                    increaseViewcountByCpssrc(input.getCpssrc());
                }
                makeUserFootprint(input.getXaid(),nowUser) ;

        });
    }


    /**
     * 用户活动足迹 记录
     *
     * 没有就新增
     * 有就更新当天访问的最后时间
     * @param xaid
     * @param nowUser
     */
    public void makeUserFootprint(Integer xaid, NowUser nowUser) {

        if (nowUser != null && xaid!=null ) {

            DynamicDataSourceContextHolder.push(DB_slave);

            final LocalDateTime now = LocalDateTime.now();

            final String nowDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            Wrapper<FmbUserBrowseRecord> wrTest = new LambdaQueryWrapper<FmbUserBrowseRecord>()
                    .eq(FmbUserBrowseRecord::getAid, xaid)
                    .eq(FmbUserBrowseRecord::getUid,nowUser.getUserId())
                    .between(FmbUserBrowseRecord::getCtime,nowDate+" 00:00:00",nowDate+" 23:59:59")
                    .last(" limit 1  ")
                    ;
            final FmbUserBrowseRecord one = browseRecordService.getOne(wrTest);
            DynamicDataSourceContextHolder.push(DB_master);
            if (one ==null) {

                //新增
                FmbUserBrowseRecord record = new FmbUserBrowseRecord();
                record.setAid(xaid);
                record.setUid(nowUser.getUserId());

                record.setCtime(now);
                browseRecordService.save(record) ;

            }else {

                //更新 
                Wrapper<FmbUserBrowseRecord> wrUp = new LambdaUpdateWrapper<FmbUserBrowseRecord>()
                        .eq(FmbUserBrowseRecord::getId,one.getId())
                        .set(FmbUserBrowseRecord::getCtime,now)
                        ;
                browseRecordService.update(wrUp);
            }

        }

    }


    /**
     * 预售房劵套餐列表
     */
    public void selectFrontResSku(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        if (input.getType()!=null && input.getType().equals(FrontActvHotelDomain.RES_TYPE)){
            resultMap.put("titleStr", "立即抢购");
            resultMap.put("sloganStr", "抢订热门日期，早订早安心！");
        }else{
            resultMap.put("titleStr", "预售房券");
            resultMap.put("sloganStr", "优惠疯狂囤，时间灵活兑！");
        }

        List<FrontResSkuDomain> frontSkuDomainList = frontHotelDao.selectFrontResSku(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("sku不存在");
        }
        for (FrontResSkuDomain sku : frontSkuDomainList) {
            sku.setType(input.getType());
            if (StringUtils.isNotEmpty(sku.getRoomIds())) {
                String[] roomIds = sku.getRoomIds().split(",");
                Set<String> roomIdSet = Arrays.stream(roomIds).collect(Collectors.toSet());
                ReqHotel req = new ReqHotel();
                req.setRoomIds(roomIdSet);
                List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
                if(mediaInfo.size()>0) {
                    sku.setMediaUrl(mediaInfo.get(0).getUrl());
                    sku.setMediaCount(mediaInfo.size());
                }
            }
        }
        List<FrontResSkuDomain> sortedList = frontSkuDomainList.stream()
                .sorted(Comparator.comparing(FrontResSkuDomain::getSortValue).reversed())
                .collect(Collectors.toList());
        resultMap.put("skuCount", frontSkuDomainList.size());
        resultMap.put("skuList", sortedList);

    }


    @DS(DB_slave)
    public void selectFrontRoomSku(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        self.selectFrontRoomSku(resultMap,input,nowUser,true) ;
    }

    /**
     * 日历房套餐列表
     * @param resultMap
     * @param input
     * @param nowUser
     * @param setType6  这个值为false 时 可以搜索 套餐只设置了房券,没勾选日历房,这时虽然该套餐日历房不前台售卖,但后台改签可以选择该套餐的日历房 
     *                  这个值为true  时 只能搜索 套餐勾选了日历房的 日历房信息
     *      *
     */
    @DS(DB_slave)
    public void selectFrontRoomSku(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser,boolean setType6) {
        resultMap.put("titleStr", "立即预订");
        resultMap.put("sloganStr", "抢订热门日期，早订早安心！");
        Integer chooseDuration = 0;
        if (StringUtils.isNotEmpty(input.getPlayDate()) && StringUtils.isNotEmpty(input.getLeaveDate())) {
            LocalDate s = FmbDateUtil.dateToLocalDate(input.getPlayDate());
            LocalDate e = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            Period p = Period.between(s, e);
            chooseDuration = p.getDays();
        }
        if (setType6) {
            input.setType(6);
        }
        List<FrontRoomSkuDomain> frontSkuDomainList = frontHotelDao.selectFrontRoomSku(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        Wrapper<FmbxSuite> suiteWrapper = new LambdaQueryWrapper<FmbxSuite>()
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getNightMin,FmbxSuite::getSortValue,FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime
                ,FmbxSuite::getTotalNeight,FmbxSuite::getFlagHotelReserve,FmbxSuite::getFlagStandardHotel)
                .eq(FmbxSuite::getXaid,input.getXaid());
        List<FmbxSuite> suiteList = suiteService.list(suiteWrapper);
        Map<Integer, FmbxSuite> suiteMap = suiteList.stream().collect(Collectors.toMap(FmbxSuite::getSuiteId, Function.identity()));
        if (suiteMap == null) {
            throw new BadLogicException("套餐不存在");
        }

        for(FrontRoomSkuDomain sku : frontSkuDomainList){
            //状态 所选晚数是suite最小晚数整数倍,所选日期sku有房有库存
            FmbxSuite suite = suiteMap.get(sku.getSuiteId());
            //图片
            ReqHotel req = new ReqHotel();
            req.setRoomId(sku.getRoomId());
            List<BpsMediaInfoDomain> mediaInfo = frontHotelDao.selectBpsMediaInfo(req);
            if(mediaInfo.size()>0) {
                sku.setMediaUrl(mediaInfo.get(0).getUrl());
                sku.setMediaCount(mediaInfo.size());
            }
            //价格
            req = new ReqHotel();
            Set<Integer> skuIds = new HashSet();
            skuIds.add(sku.getSkuId());
            req.setSkuIds(skuIds);
            LocalDateTime now = LocalDateTime.now();
            if(StringUtils.isNotBlank(suite.getPreReserveTime()) && FmbDateUtil.compareHourMin(suite.getPreReserveTime(),now.getHour(),now.getMinute()) > 0) {
                req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suite.getPreReserveDay()+1));
            }else{
                req.setPriceStartDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.nowDateStr()).plusDays(suite.getPreReserveDay()));
            }
            List<FrontRoomSkuDatePriceDomain> priceInfo = frontHotelDao.selectMinPrice(req);
            if(priceInfo.get(0) != null){
                sku.setGoodsPrice(priceInfo.get(0).getMinGoodsPrice());
                sku.setMarketPrice(priceInfo.get(0).getMinMarketPrice());
            }
            boolean isIntTimesNight = suite.getNightMin() != null && suite.getNightMin() != 0 && chooseDuration % suite.getNightMin() == 0;

            int minRoom = 0 ;
            if (chooseDuration!=0 && isIntTimesNight) {
                minRoom = roomNumGenGenMin(suite.getTotalNeight(),chooseDuration) ;
            }

            List<FmbxSuiteStock> stockList = new ArrayList<>() ;
            if (StringUtils.isNotEmpty(input.getPlayDate()) && StringUtils.isNotEmpty(input.getLeaveDate())) {
                Wrapper<FmbxSuiteStock> stockWrapper = new LambdaQueryWrapper<FmbxSuiteStock>()
                        .select(FmbxSuiteStock::getRoomId,FmbxSuiteStock::getTargetDate,FmbxSuiteStock::getStockNum,FmbxSuiteStock::getStatus)
                        .eq(FmbxSuiteStock::getRoomId,sku.getRoomId())
                        .ge(StringUtils.isNotEmpty(input.getPlayDate()),FmbxSuiteStock::getTargetDate,input.getPlayDate())
                        .lt(StringUtils.isNotEmpty(input.getLeaveDate()),FmbxSuiteStock::getTargetDate,input.getLeaveDate());
                stockList = suiteStockService.list(stockWrapper);
            }

            boolean hasRoom = true;
//            logger.info("stockList={}",stockList);
//            logger.info("suite.getNightMin()={}  minRoom={} suiteid={} suite.getTotalNeight()={} chooseDuration={}",suite.getNightMin(),
//                    minRoom,
//                    suite.getSuiteId(),suite.getTotalNeight(),chooseDuration);
            if ((StringUtils.isNotEmpty(input.getPlayDate()) || StringUtils.isNotEmpty(input.getLeaveDate())) && stockList.size()==0){
                hasRoom = false;
            }
            st : for(FmbxSuiteStock stock : stockList){
                if(stock.getStatus() != 1 || stock.getStockNum() < 1 ||  (suite.getNightMin() != null && suite.getNightMin() != 0 && minRoom!=0 &&  stock.getStockNum() < minRoom)    || !isValidCheckinDate(FmbDateUtil.FORMATTER10.format(stock.getTargetDate()),suite)){
                    hasRoom = false;
                    break st;
                }
            }
            if(isIntTimesNight && hasRoom) {
                sku.setStatus(FrontRoomSkuDomain.STATUS_ORDEROOM);
            }else{
                sku.setStatus(FrontRoomSkuDomain.STATUS_SCHEDULED);
            }
        }
        //按套餐分组
        Map<Integer, List<FrontRoomSkuDomain>> groupBy = frontSkuDomainList.stream().collect(Collectors.groupingBy(FrontRoomSkuDomain::getSuiteId, HashMap::new,Collectors.toList()));
        List<FrontRoomSuiteDomain> frontSuiteDomainList = new ArrayList<>();
        for(Map.Entry<Integer,List<FrontRoomSkuDomain>> ele : groupBy.entrySet()){
            FrontRoomSuiteDomain s = new FrontRoomSuiteDomain();
            s.setSuiteId(ele.getKey());
            List<FrontRoomSkuDomain> sortSkuList = ele.getValue().stream().sorted(Comparator.comparing(FrontRoomSkuDomain::getSkuId))
                    .sorted(Comparator.comparing(FrontRoomSkuDomain::getSortValue).reversed())
                    .collect(Collectors.toList());
            s.setSkuList(sortSkuList);
            s.setSkuCount(s.getSkuList().size());
            s.setSuiteName(ele.getValue().get(0).getSuiteName());
            s.setSortValue(suiteMap.get(ele.getKey()).getSortValue());
            frontSuiteDomainList.add(s);
        }
        List<FrontRoomSuiteDomain> sortedList = frontSuiteDomainList.stream().sorted(Comparator.comparing(FrontRoomSuiteDomain::getSuiteId))
                .sorted(Comparator.comparing(FrontRoomSuiteDomain::getSortValue).reversed())
                .collect(Collectors.toList());
        resultMap.put("suiteList", sortedList);

    }

    /**
     * 日历房提交订单sku信息
     */
    public static final DateTimeFormatter ZH_MD = DateTimeFormatter.ofPattern("MM月dd日");
    public void selectOrderRoomInfo(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        Integer chooseDuration = 0;
        if (StringUtils.isNotEmpty(input.getPlayDate()) && StringUtils.isNotEmpty(input.getLeaveDate())) {
            LocalDate s = FmbDateUtil.dateToLocalDate(input.getPlayDate());
            LocalDate e = FmbDateUtil.dateToLocalDate(input.getLeaveDate());
            Period p = Period.between(s, e);
            chooseDuration = p.getDays();
            resultMap.put("chooseDuration", chooseDuration);
            resultMap.put("chooseDurationStr", "共"+chooseDuration+"晚");
            resultMap.put("playDate", s);
            resultMap.put("leaveDate", e);
            resultMap.put("playDateStr", ZH_MD.format(s)+" "+(StringUtils.isNotBlank(FmbDateUtil.getNearDateZh(s))?FmbDateUtil.getNearDateZh(s):FmbDateUtil.getDayOfWeekZh(s)));
            resultMap.put("leaveDateStr", ZH_MD.format(e)+" "+(StringUtils.isNotBlank(FmbDateUtil.getNearDateZh(e))?FmbDateUtil.getNearDateZh(e):FmbDateUtil.getDayOfWeekZh(e)));
        }

        List<FrontRoomSkuOrderDomain> frontSkuDomainList = frontHotelDao.selectFrontRoomSkuBySkuId(input);
        if (frontSkuDomainList.size() == 0) {
            throw new BadLogicException("房型不存在");
        }
        resultMap.put("orderRoomInfo", frontSkuDomainList.get(0));
        resultMap.put("userInfoTxt", FrontRoomSkuOrderDomain.userInfoMap);
    }

    public void isFavorite(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser){
        resultMap.put("isFavorite", false);
        if (nowUser != null) {
//            throw new BadLogicException("用户不存在");

            Wrapper<FmbxActivityLike> wrapper = new LambdaQueryWrapper<FmbxActivityLike>()
                    .select(FmbxActivityLike::getId)
                    .eq(FmbxActivityLike::getXaid,input.getXaid())
                    .eq(FmbxActivityLike::getUid,nowUser.getUserId());
            List<FmbxActivityLike> activityList = activityLikeService.list(wrapper);
            if(activityList!=null && activityList.size()>0){
                resultMap.put("isFavorite", true);
            }
        }

    }

    public void updateFavorite(HashMap<String, Object> resultMap, ReqHotel input, NowUser nowUser) {
        if (nowUser == null) {
            throw new BadLogicException("用户不存在");
        }
        Wrapper<FmbxActivityLike> wrapper = new LambdaQueryWrapper<FmbxActivityLike>()
                .select(FmbxActivityLike::getId)
                .eq(FmbxActivityLike::getXaid,input.getXaid())
                .eq(FmbxActivityLike::getUid,nowUser.getUserId());
        List<FmbxActivityLike> activityList = activityLikeService.list(wrapper);
        if(activityList!=null && activityList.size()>0){
            activityLikeService.removeById(activityList.get(0));
            resultMap.put("isFavorite", false);
        }else{
            FmbxActivityLike d = new FmbxActivityLike();
            d.setUid(nowUser.getUserId());
            d.setXaid(input.getXaid());
            d.setCtime(LocalDateTime.now());
            activityLikeService.save(d);
            resultMap.put("isFavorite", true);
        }
    }

    public void selectCustomerService(HashMap<String, Object> resultMap){
        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DB_10);

            String value = jedis.get("fmb_customer_service_switch_key");
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, String.class);
            List<String> valueList = mapper.readValue(value, javaType);

            resultMap.put("customer_service", valueList);
            return ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    public void getContent(HashMap<String, Object> resultMap, ReqHotel input) {
        String contentParam = input.getContentParam();
        String[] param = contentParam.split("_");
        if (param.length==2) {
            if ("act".equals(param[0])) {
                Wrapper<FmbxActivityExt> wrapper = new LambdaQueryWrapper<FmbxActivityExt>()
                        .select(FmbxActivityExt::getRichTxt)
                        .eq(FmbxActivityExt::getXaid, Integer.valueOf(param[1]));
                List<FmbxActivityExt> activityList = activityExtService.list(wrapper);
                if (activityList != null && activityList.size() > 0 && StringUtils.isNotBlank(activityList.get(0).getRichTxt())) {
                    resultMap.put("content", activityList.get(0).getRichTxt().replaceAll("src=", "data-src="));
                }
            }
        }
    }

    @Cacheable(cacheNames = "fmbCache#60", keyGenerator = "customKeyGenerator")
    public SortedMap<Integer,FrontActvRecomDomain> getActivityRecommend(Integer xaid){
        DynamicDataSourceContextHolder.push(DB_slave);
        Integer limit = 5;
        Wrapper<FmbxActivity> actWrapper = new LambdaQueryWrapper<FmbxActivity>()
                .select(FmbxActivity::getXaid,FmbxActivity::getCityId,FmbxActivity::getProvinces,FmbxActivity::getProvincesSub,FmbxActivity::getCategoryId)
                .eq(FmbxActivity::getXaid,xaid);
        FmbxActivity act = activityService.getOne(actWrapper);
        Wrapper<JishigouCommonDistrict> districtWrapper = new LambdaQueryWrapper<JishigouCommonDistrict>()
                .select(JishigouCommonDistrict::getId)
                .eq(JishigouCommonDistrict::getName,act.getProvinces());
        List<JishigouCommonDistrict> districtList = commonDistrictService.list(districtWrapper);
        SortedMap<Integer,FrontActvRecomDomain> activityRecommend = new TreeMap<>();
        Wrapper<FmbProductIndex> wrapper = new LambdaQueryWrapper<FmbProductIndex>()
                .select(FmbProductIndex::getApId,FmbProductIndex::getTitle,FmbProductIndex::getSubTitle,
                        FmbProductIndex::getSellMinPrice,FmbProductIndex::getMarketMinPrice,FmbProductIndex::getBanner,FmbProductIndex::getActivityGroupType,
                        FmbProductIndex::getFmbxActivityFlag,FmbProductIndex::getFmbxFlagHotelDate,FmbProductIndex::getFmbxFlagHotelReserve)
                .eq(FmbProductIndex::getSellStat, 16)
                .eq(FmbProductIndex::getStatus, 1)
                .ne(act!=null, FmbProductIndex::getApId,xaid)
                .eq(act!=null, FmbProductIndex::getCityId, act.getCityId())
                .eq(act!=null, FmbProductIndex::getCatId, act.getCategoryId())
                .in(districtList!=null && districtList.size()>0 ,FmbProductIndex::getProvincesId,districtList)
                .orderByDesc(FmbProductIndex::getSellNum)
                .last("limit "+limit);
        List<FmbProductIndex> plist = productIndexService.list(wrapper);
        int i= 0;
        Set<Integer> aidSet = new HashSet();
        for (FmbProductIndex p : plist) {
            //如果含酒店预约不推荐
            Wrapper<FmbHotelReserveInfo> wrReserve = new LambdaQueryWrapper<FmbHotelReserveInfo>()
                    .select(FmbHotelReserveInfo::getAid)
                    .eq(FmbHotelReserveInfo::getAid,p.getApId());
            List<FmbHotelReserveInfo> rlist = fmbHotelReserveInfoService.list(wrReserve);
            if (rlist.size()>0){
                continue;
            }
            i++;
            FrontActvRecomDomain domain = new FrontActvRecomDomain();
            BeanUtils.copyProperties(p,domain);
            domain.setXaid(p.getApId());
            domain.setIntro("");
            domain.setSubTitle(p.getSubTitle());
            domain.setGoodsPrice(new BigDecimal(p.getSellMinPrice().toString()));
            domain.setMarketPrice(new BigDecimal(p.getMarketMinPrice().toString()));
            domain.setMediaUrl(p.getBanner());
            activityRecommend.put(i,domain);
            aidSet.add(p.getApId());
        }
        if (i<limit){
            Wrapper<FmbGoodsCategory> cWrapper = new LambdaQueryWrapper<FmbGoodsCategory>()
                    .select(FmbGoodsCategory::getArrchildid)
                    .eq(FmbGoodsCategory::getParentid, 0)
                    .apply(act.getCategoryId()!=null, "FIND_IN_SET('"+act.getCategoryId()+"',arrchildid)");
            FmbGoodsCategory fmbGoodsCategory = categoryService.getOne(cWrapper);
            String[] arrChildid = {};
            if (fmbGoodsCategory!=null && StringUtils.isNotBlank(fmbGoodsCategory.getArrchildid())) {
                arrChildid = fmbGoodsCategory.getArrchildid().split(",");
            }
            limit = limit - i;
            wrapper = new LambdaQueryWrapper<FmbProductIndex>()
                    .select(FmbProductIndex::getApId,FmbProductIndex::getTitle,FmbProductIndex::getSubTitle,
                            FmbProductIndex::getSellMinPrice,FmbProductIndex::getMarketMinPrice,FmbProductIndex::getBanner,FmbProductIndex::getActivityGroupType,
                            FmbProductIndex::getFmbxActivityFlag,FmbProductIndex::getFmbxFlagHotelDate,FmbProductIndex::getFmbxFlagHotelReserve)
                    .eq(FmbProductIndex::getSellStat, 16)
                    .eq(FmbProductIndex::getStatus, 1)
                    .ne(act!=null, FmbProductIndex::getApId,xaid)
                    .eq(act!=null, FmbProductIndex::getCityId, act.getCityId())
                    .in(arrChildid.length>0, FmbProductIndex::getCatId, arrChildid)
                    .notIn(aidSet.size()>0,FmbProductIndex::getApId,aidSet)
                    .in(districtList!=null && districtList.size()>0 ,FmbProductIndex::getProvincesId,districtList)
                    .orderByDesc(FmbProductIndex::getSellNum)
                    .last("limit "+limit);
            plist = productIndexService.list(wrapper);
            for (FmbProductIndex p : plist) {
                //如果含酒店预约不推荐
                Wrapper<FmbHotelReserveInfo> wrReserve = new LambdaQueryWrapper<FmbHotelReserveInfo>()
                        .select(FmbHotelReserveInfo::getAid)
                        .eq(FmbHotelReserveInfo::getAid,p.getApId());
                List<FmbHotelReserveInfo> rlist = fmbHotelReserveInfoService.list(wrReserve);
                if (rlist.size()>0){
                    continue;
                }
                i++;
                FrontActvRecomDomain domain = new FrontActvRecomDomain();
                BeanUtils.copyProperties(p,domain);
                domain.setXaid(p.getApId());
                domain.setIntro("");
                domain.setSubTitle(p.getSubTitle());
                domain.setGoodsPrice(new BigDecimal(p.getSellMinPrice().toString()));
                domain.setMarketPrice(new BigDecimal(p.getMarketMinPrice().toString()));
                domain.setMediaUrl(p.getBanner());
                activityRecommend.put(i,domain);
            }

        }
        return activityRecommend;
    }

    public void increaseViewcountByCpssrc(String cpssrc){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "cpssrc_viewcount_hash";
            if (jedis.hexists(key,cpssrc)){
                jedis.hincrBy(key,cpssrc,1);
            }else{
                Wrapper<FmbCpssrc> wr = new LambdaQueryWrapper<FmbCpssrc>()
                        .select(FmbCpssrc::getViewCount)
                        .eq(FmbCpssrc::getCpssrcValue,cpssrc);
                List<FmbCpssrc> list = cpssrcService.list(wr);
                if (list.size()>0){
                    Integer value = list.get(0).getViewCount()+1;
                    jedis.hset(key,cpssrc,value.toString());
                }
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    public void updateCpssrcViewcount(){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "cpssrc_viewcount_hash";
            Map<String,String> cpssrcMap = jedis.hgetAll(key);
            for(Map.Entry<String,String> item : cpssrcMap.entrySet()){
                Wrapper<FmbCpssrc> wr = new LambdaQueryWrapper<FmbCpssrc>()
                        .select(FmbCpssrc::getCpssrcId, FmbCpssrc::getCpssrcValue, FmbCpssrc::getViewCount)
                        .eq(FmbCpssrc::getCpssrcValue,item.getKey());
                List<FmbCpssrc> list = cpssrcService.list(wr);
                if (list.size()>0){
                    FmbCpssrc fmbCpssrc = list.get(0);
                    fmbCpssrc.setViewCount(Integer.valueOf(item.getValue()));
                    cpssrcService.updateById(fmbCpssrc);
                }
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    public void cleanCpssrcViewcount(){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "cpssrc_viewcount_hash";
            Map<String,String> cpssrcMap = jedis.hgetAll(key);
            for(Map.Entry<String,String> item : cpssrcMap.entrySet()){
                jedis.hdel(key,item.getKey());
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}