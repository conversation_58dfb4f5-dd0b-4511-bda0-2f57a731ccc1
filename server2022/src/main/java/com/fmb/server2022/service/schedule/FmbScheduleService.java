package com.fmb.server2022.service.schedule;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.fmbx.service.IFmbxScheduleJobService;
import com.fmb.server2022.service.MailService;
import com.fmb.server2022.service.schedule.impel.ConsumeJob;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.fmb.basic.FmbConstants.DEFAULT_DATETIME;
import static com.fmb.basic.FmbConstants.REDIS_DEFAULT_DB;
import static com.fmb.util.FmbDateUtil.dateToLocalDatetime;

/**
 * 添加定时任务
 * 处理定时任务
 * 处理异步任务核心类
 */
@Service
public class FmbScheduleService implements ApplicationRunner , ApplicationContextAware {

    public static final int MAX_RECONNECT_NUM = 60;
    private static Logger logger = LoggerFactory.getLogger(FmbScheduleService.class);

    public static final String DELAY_SORT_SET = "fmbx:delaySortSet";
    public static final String RUN_QUEUE = "fmbx:runQueue";

    public static final String RUN_QUEUE2 = "fmb_queue_lists";


    /**
     * 一次读取16条
     */
    private static final int BATCH_COUNT = 16;

    /**
     * 1. lua 脚本的数组下标从1开始
     * 2. 使用 zrangebyscore 从KEYS[1]里 读取排序值从0到ARGV[1] 内的数据, 一次读取BATCH_COUNT条 数据 ,如果有数据
     * 3. 将zset里取出的数据 lpush 进一个队列(KEYS[1]) ,返回添加的数据个数
     */
    private static final String LUA_SCRIPT = "local resultArray = redis.call('zrangebyscore', KEYS[1], 0, ARGV[1], 'limit' , 0, " + BATCH_COUNT + ")\n" +
            "local cx = 0 \n" +
            "if #resultArray > 0 then\n" +
            "   for i=1,#resultArray do\n" +
            "       if redis.call('zrem', KEYS[1], resultArray[i]) > 0 then " +
            "           redis.call('lpush',KEYS[2],resultArray[i]) " +
            "           cx = cx+1 " +
            "       end" +
            "   end " +
            "end " +
            "return cx ";

    /**
     * 定时任务扫描 死循环开关
     */
    private static final AtomicBoolean runFlag = new AtomicBoolean(true);

    @Autowired
    IFmbxScheduleJobService scheduleJobService;

    @Autowired
    JedisPool jedisPool;

    ApplicationContext applicationContext ;


    /**
     * 异步执行的队列
     */
    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;



    /**
     * 死循环 查询定时任务
     * <p>
     * 从 zset(DELAY_SORT_SET)里取出所有比 以当前系统毫秒数 小的数据,没有就休息一秒
     * 并放入队列(RUN_QUEUE)
     */
    void doCheckScheduleZset() {

//        System.out.println("doCheckScheduleZset begin");

        ArrayList<String> keys = new ArrayList<>();
        //zset 名称
        keys.add(DELAY_SORT_SET);
        //list 队列名称 采用lpush  rpop
        keys.add(RUN_QUEUE);

        int loopCount = 0 ;

        int redisConnectError = 0 ;
        Jedis jedis = null;

        while (runFlag.get()) {

            Long eval = 0L;
            try {
                //初始化 redis 的连接
                if(loopCount==0 ){
//                    System.out.println("doCheckScheduleZset doConnect redis ----------------");
                    jedis = jedisPool.getResource();

                    //如果得不到就休息1秒
                    if (jedis == null) {
                        TimeUnit.MILLISECONDS.sleep(1000);
                        redisConnectError ++ ;
                        //如果20次连接不了redis 就发报警邮件
                        if(redisConnectError==20){
                            MailService.sendSystemErrorMail("doCheckScheduleZset不能连接redis") ;
                        }
                        logger.warn(" can_get_redis_connection  ");
                        continue;
                    }else {
                        redisConnectError =0  ;
                    }

                    jedis.select(REDIS_DEFAULT_DB);
                }

                ArrayList<String> args = new ArrayList<>();
                args.add("" + System.currentTimeMillis());
                eval = (Long) jedis.eval(LUA_SCRIPT, keys, args);

                //如果获取定时任务为空就休息1秒
                if (eval.intValue() == 0) {
                    TimeUnit.MILLISECONDS.sleep(1000);
//                    System.out.println(" empty zset ");
                    loopCount ++ ;
                }
                //超过 MAX_RECONNECT_NUM 这个数 就重新连接一次 redis
                if (loopCount>= MAX_RECONNECT_NUM) {
                    loopCount = 0  ;
                }
            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);

                //发生异常也重新连一下 redis
                loopCount = 0 ;
            } finally {
                if (jedis != null && loopCount==0) {
                    jedis.close();
                }
            }
        }
        //退出死循环 关闭 redis 连接
        if (jedis != null ) {
            jedis.close();
        }

    }

    /**
     * 添加定时执行任务
     * @param jobType 任务类型
     * @param id      业务id
     * @param map     其他业务参数
     * @param second  延迟的秒数
     * @return
     */
    public HashMap<String, Object> addJobWithSecond(String jobType, Integer id, Map<String, Object> map, int second) {
        return addJob(jobType, id, map, new Date(1000 * second + System.currentTimeMillis()));
    }

    /**
     * 添加定时执行任务
     * @param jobType 任务类型
     * @param id      业务id
     * @param map     其他业务参数
     * @param aim     要执行的时间
     * @return
     */
    public HashMap<String, Object> addJob(String jobType, Integer id, Map<String, Object> map, Date aim) {

        HashMap<String, Object> result = new HashMap<>();


        final FmbxScheduleJob jobDB = existSameTypeJobNeedRun(jobType, id);
        if (jobDB != null) {
            //如果任务已经存在 取消原有任务的执行

            final String aimDateStr = FmbDateUtil.toDateFullStr(aim);
            //任务完全相等 就直接返回数据库里的数据
            // 任务完全相等的条件 是  jobType和业务id ,且未来执行的时间(精确到秒)也完全相同
            // 不考虑map 参数
            if (aimDateStr.equals( jobDB.getScheduleRunTime().format(FmbDateUtil.FORMATTER_FULL))) {

                logger.info("existSameJobInfo jobType={} fmbxid={} ScheduleRunTime={}",jobType,id,aimDateStr);
                result.put("jobId", jobDB.getJobId());
                result.put("jobType", jobType);
                result.put("fmbxid", jobDB.getJobFmbxId());

                return  result;
            }

            cancelJobBecauseRepeat(jobDB);
        }
        //保存信息任务
        final LocalDateTime now = LocalDateTime.now();
        FmbxScheduleJob newJob = new FmbxScheduleJob();
        newJob.setJobType(jobType);
        newJob.setJobFmbxId(id);
        newJob.setJobExtPars(JSON.toJSONString(map));
        newJob.setStatus(0);
        newJob.setResult("");
        newJob.setScheduleRunTime(dateToLocalDatetime(aim));
        newJob.setCtime(now);
        final boolean save = scheduleJobService.save(newJob);
        if (save) {
            //把任务信息写入 zset
            enableJobJedis(newJob);
        }


        result.put("jobId", newJob.getJobId());
        result.put("jobType", jobType);
        result.put("fmbxid", newJob.getJobFmbxId());


        return result;

    }

    /**
     * 因任务重复 取消任务
     * @param jobDB
     */
    void cancelJobBecauseRepeat(FmbxScheduleJob jobDB) {


        Wrapper<FmbxScheduleJob> wrUpdate = new LambdaUpdateWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobId,jobDB.getJobId())
                .eq(FmbxScheduleJob::getStatus,jobDB.getStatus())
                .set(FmbxScheduleJob::getUtime,LocalDateTime.now())
                .set(FmbxScheduleJob::getStatus,4)
                .set(FmbxScheduleJob::getResult,"已经存在相同任务,取消执行")
                ;
        scheduleJobService.update(wrUpdate) ;

        unableJobJedis(jobDB);
    }

    /**
     * 手动取消任务
     * @param jobDB
     */
    void cancelJobWithManual(FmbxScheduleJob jobDB) {


        Wrapper<FmbxScheduleJob> wrUpdate = new LambdaUpdateWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobId,jobDB.getJobId())
                .eq(FmbxScheduleJob::getStatus,jobDB.getStatus())
                .set(FmbxScheduleJob::getUtime,LocalDateTime.now())
                .set(FmbxScheduleJob::getStatus,1)
                .set(FmbxScheduleJob::getResult,"手动取消执行")
                ;

        scheduleJobService.update(wrUpdate) ;
        unableJobJedis(jobDB);
    }

    /**
     * 定时任务写入 redis zset
     * @param newJob
     */
    boolean enableJobJedis(FmbxScheduleJob newJob) {

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);
            //zset里只放jobid
            final String jobid = newJob.getJobId() + "";
            return jedis.zadd(DELAY_SORT_SET, FmbDateUtil.getLocalDatetimeMillionSeconds(newJob.getScheduleRunTime()), jobid)==1;
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return false ;

    }

    /**
     * 从redis 里删除定时任务
     * @param jobDB
     */
    private boolean unableJobJedis(FmbxScheduleJob jobDB) {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            return  jedis.zrem(DELAY_SORT_SET, jobDB.getJobId() + "")==1;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return false ;

    }


    /**
     * 查询是否存在未执行的相同任务
     *
     * @param taskType
     * @param id
     * @return 存在就返回对象 否则返回空
     */
    @DS(FmbConstants.DB_slave)
    FmbxScheduleJob existSameTypeJobNeedRun(String taskType, Integer id) {

        Wrapper<FmbxScheduleJob> wr = new LambdaQueryWrapper<FmbxScheduleJob>()
                .select(FmbxScheduleJob::getJobId, FmbxScheduleJob::getJobType, FmbxScheduleJob::getJobFmbxId,FmbxScheduleJob::getStatus,
                        FmbxScheduleJob::getScheduleRunTime)
                .eq(FmbxScheduleJob::getJobType, taskType)
                .eq(FmbxScheduleJob::getJobFmbxId, id)
                .eq(FmbxScheduleJob::getStatus, 0)
                .last(" limit 1 ");
        return scheduleJobService.getOne(wr);
    }


    /**
     * 从队列里读出 任务id 放入线程池执行
     *
     * @param runSource
     *
     * "javaJob"  内部java 定时
     * "producer2" 解决其他 php
     *
     */
    void startConsumer(String runSource) {

//        System.out.println("startConsumer begin");

        int loopCount = 0 ;
        int redisConnectError = 0 ;

        Jedis jedis = null;
        //开启死循环
        for (; runFlag.get(); ) {

            try {
                if(loopCount==0 ){
//                    System.out.println(" doConnect redis ----------------");
                    jedis = jedisPool.getResource();

                    //如果得不到就休息1秒
                    if (jedis == null) {
                        TimeUnit.MILLISECONDS.sleep(1000);
                        redisConnectError ++ ;
                        //如果20次连接不了redis 就发报警邮件
                        if(redisConnectError==20){
                            MailService.sendSystemErrorMail("startConsumer 不能连接redis o--o!") ;
                        }
                        logger.warn(" can_get_redis_connection  ");
                        continue;
                    }else {
                        redisConnectError =0  ;
                    }

                    jedis.select(REDIS_DEFAULT_DB);
                }

                //通过阻塞方法查询队列是否有消息
                //此处是依赖redis 的堵塞读取 最多堵10秒
                //30秒里有消息就马上恢复
                List<String> v = null ;

                //来源是定时任务的
                if (RUN_QUEUE.equals(runSource)) {
                    v = jedis.brpop(10, RUN_QUEUE);

                    if (v != null && !v.isEmpty()) {
                        String jobId = v.get(1);

                        if (jobId != null) {
                            executor.submit(new FmbxRedisJobRunner(jobId) {
                                @Override
                                public void run() {
                                    doConsumeJob(getJobId());
                                }
                            });
                        } else {
                            logger.warn( " JOB_ID_IS_NULL ");
                        }
                    }else {
                        loopCount ++ ;
//                    System.out.println("not job" +loopCount);
                    }

                }


                //其他渠道 you可能 是php 放入的队列 可以扩展
                if (RUN_QUEUE2.equals(runSource)) {
                    v = jedis.brpop(10, RUN_QUEUE2);

                    if (v != null && !v.isEmpty()) {
                        String json = v.get(1);

                        if (json != null) {
                            executor.submit(new FmbxRedisJobRunner2(json) {
                                @Override
                                public void run() {
                                    doConsumeJob2(getJson());
                                }
                            });
                        } else {
                            logger.warn( " JOB_ID_IS_NULL ");
                        }
                    }else {
                        loopCount ++ ;
//                    System.out.println("not job" +loopCount);
                    }

                }




                if (loopCount>= 30) {
                    loopCount = 0  ;
                }

            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);

                //发生异常也重新连一下 redis
                loopCount = 0 ;

            } finally {
                if (jedis != null && loopCount==0) {
                    jedis.close();
                }
            }

        }


        if (jedis != null ) {
            jedis.close();
        }
    }





    /**
     * 真正的定时任务 业务执行方法
     * 当前方法已经在 线程池里执行
     * @param jobId
     */
    public void doConsumeJob(String jobId) {

        Integer nJobID = null ;
        try {
            nJobID = NumberUtil.parseInt(jobId);
        } catch (NumberFormatException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return;
        }

        final FmbxScheduleJob jobInfo = scheduleJobService.getById(nJobID);

        if (jobInfo == null) {
            logger.error("bad jobinfo nJobID="+nJobID);
            return;
        }
        try {
            final ConsumeJob bean = this.applicationContext.getBean(jobInfo.getJobType(), ConsumeJob.class);
            if (bean == null) {
                runFail(jobInfo, "doConsumeJob找不到处理类-_-!");
            } else {
                ConsumeResult consumeResult = bean.processJob(jobInfo);
                if (consumeResult!=null &&  consumeResult.isRunSuccess()) {
                    runOK(jobInfo, consumeResult.getResultMessage());
                } else {
                    runFail(jobInfo, consumeResult.getResultMessage());
                }
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            logger.error("errorid={} jobinfo={}" , errorid, jobInfo.toString());

            HashMap<String, Object> me = new HashMap<>();
            me.put("errorid", errorid);
            me.put("message", ex.getMessage());
            runFail(jobInfo, JSON.toJSONString(me));

        }

    }


    /**
     * 真正的定时任务 业务执行方法
     * 当前方法已经在 线程池里执行
     * @param json
     */
    public void doConsumeJob2(String json) {

        final JSONObject jsonObject = JSON.parseObject(json);

        if (jsonObject==null || !jsonObject.containsKey("type")) {
            logger.error("doConsumeJob2_bad_job_info",json);
            return  ;
        }
        final String type = jsonObject.getString("type");

        FmbxScheduleJob jobInfo = new FmbxScheduleJob();

        jobInfo.setJobType(type);
        jobInfo.setJobFmbxId((int) nextId());
        jobInfo.setJobExtPars(json);
        jobInfo.setStatus(0);
        jobInfo.setResult("");
        final LocalDateTime now = LocalDateTime.now();
        jobInfo.setScheduleRunTime(now);
        jobInfo.setUtime(DEFAULT_DATETIME);
        jobInfo.setCtime(LocalDateTime.now());

        scheduleJobService.save(jobInfo) ;


        try {
            final ConsumeJob bean = this.applicationContext.getBean(jobInfo.getJobType(), ConsumeJob.class);
            if (bean == null) {
                runFail(jobInfo, "doConsumeJob2找不到处理类");
            } else {
                ConsumeResult consumeResult = bean.processJob(jobInfo);
                if (consumeResult!=null &&  consumeResult.isRunSuccess()) {
                    runOK(jobInfo, consumeResult.getResultMessage());
                } else {
                    runFail(jobInfo, consumeResult.getResultMessage());
                }
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            logger.error("errorid={} jobinfo={}" , errorid, jobInfo.toString());

            HashMap<String, Object> me = new HashMap<>();
            me.put("errorid", errorid);
            me.put("message", ex.getMessage());
            runFail(jobInfo, JSON.toJSONString(me));
        }
    }

    public long nextId(){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            Long aLong = jedis.incr(FmbConstants.REDIS_GLOBAL_ID);
            return  aLong ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return System.currentTimeMillis()/1000;
    }



    /**
     * 数据库更新 任务执行结果  ,操作成功
     * @param jobInfo
     * @param message
     * @return
     */
    public boolean runOK(FmbxScheduleJob jobInfo, String message) {

        Wrapper<FmbxScheduleJob> wrUpdate = new LambdaUpdateWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobId,jobInfo.getJobId())
                .eq(FmbxScheduleJob::getStatus,jobInfo.getStatus())
                .set(FmbxScheduleJob::getUtime,LocalDateTime.now())
                .set(FmbxScheduleJob::getStatus,2)
                .set(FmbxScheduleJob::getResult,message)
                ;

        return scheduleJobService.update(wrUpdate) ;
    }


    /**
     * 数据库更新 任务执行结果  ,操作成功
     * @param jobInfo
     * @param message
     * @return
     */
    public boolean runFail(FmbxScheduleJob jobInfo, String message) {

        Wrapper<FmbxScheduleJob> wrUpdate = new LambdaUpdateWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobId,jobInfo.getJobId())
                .eq(FmbxScheduleJob::getStatus,jobInfo.getStatus())
                .set(FmbxScheduleJob::getUtime,LocalDateTime.now())
                .set(FmbxScheduleJob::getStatus,3)
                .set(FmbxScheduleJob::getResult,message)
                ;

        return scheduleJobService.update(wrUpdate) ;
    }




    /**
     * 启动后开启定时任务检查和消费者队列
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        //开启定时任务监控
        executor.submit(new Thread("doCheckZset") {
            @Override
            public void run() {
                doCheckScheduleZset();
            }
        });

        //开启消费
        executor.submit(new Thread("redisConsumerThread") {
            @Override
            public void run() {
                startConsumer(RUN_QUEUE);
            }
        });

        //开启消费
        executor.submit(new Thread("redisConsumerThread") {
            @Override
            public void run() {
                startConsumer(RUN_QUEUE2);
            }
        });

    }

    /**
     * 如果服务停止 就关闭 runFlag
     */
    @PreDestroy
    public void beforeDown() {
        System.out.println("beforeDown");
        runFlag.compareAndSet(true, false);
    }


    /**
     *
     * 根据任务类型和业务id 取消定时任务
     *
     * @param type  任务类型
     * @param jobId 任务id
     * @return
     */
    public boolean cancelJobBecauseRepeat(String type, int jobId) {

        final FmbxScheduleJob fmbxScheduleJob = existSameTypeJobNeedRun(type, jobId);
        if (fmbxScheduleJob != null) {
            cancelJobWithManual(fmbxScheduleJob);

            return true;
        }
        return false;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext ;
    }
}
