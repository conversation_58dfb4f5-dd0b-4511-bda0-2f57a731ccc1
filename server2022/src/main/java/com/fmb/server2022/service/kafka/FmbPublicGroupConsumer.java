package com.fmb.server2022.service.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.server2022.service.BpsService;
import com.fmb.server2022.service.FmbxBpService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class FmbPublicGroupConsumer {



    @Autowired
    FmbxBpService bpService ;

    @Autowired
    BpsService bpsService ;

    /**
     * 相当于异步完成 通知任务
     * @param record
     */
    @KafkaListener( id = "bpCheckNotify", topics = {FmbKafkaTopic.PUBLIC_GROUP},groupId = "default")
    public void bpCheckNotify(ConsumerRecord<String,String> record
    ) {

        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
//            String message = kafkaMessage.get();
            JSONObject jsonObject = JSON.parseObject(kafkaMessage.get()).getJSONObject("binfo");


            switch (jsonObject.getString("INNERGROUP")){

                case  FmbKafkaTopic.PUBLIC_GROUP_BP_NEED_CHECK :
                {
                    bpService.notifyBD(jsonObject.getInteger("bpId")) ;
                    break;
                }
                case  FmbKafkaTopic.PUBLIC_GROUP_BPS_FLAG_UPDATE :
                {
                    bpsService.updateBpsInfoStatus(jsonObject.getInteger("bpsid")) ;
                    break;
                }



                default:

            }




        }
    }


}
