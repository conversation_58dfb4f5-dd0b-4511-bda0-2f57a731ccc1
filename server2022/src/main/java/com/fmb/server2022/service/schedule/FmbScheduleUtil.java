package com.fmb.server2022.service.schedule;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.fmb.server2022.service.FmbAsyncJob;
import com.fmb.util.FmbDateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * FmbScheduleService 类的包裹实现  省得引入
 */
@Component
public class FmbScheduleUtil {

    private static Logger logger = LoggerFactory.getLogger(FmbScheduleUtil.class);

    private static FmbScheduleService fmbScheduleService;

    /**
     * 异步执行的队列
     */
    private static   ThreadPoolExecutor executor;



    public static final  String JOB_NAME_CANCELORDERJOB = "cancelOrderJob" ;
    public static final  String JOB_NAME_ORDER_CLOSE_IN_1_HOUR = "job_name_order_close_in_1_hour" ;
    public static final  String JOB_NAME_ORDER_SUCCESS_PAY = "pay_success_order" ;
    public static final  String JOB_NAME_SHOPUSER_MODIFY = "shopuser_modify" ;
    public static final  String JOB_NAME_ORDER_RETURN_PASS = "order_return_pass" ;
    public static final  String JOB_NAME_UPDATE_MYCPS_PRODUCT_PRICE = "update_mycps_product_price" ;
    public static final String JOB_NAME_SKU_SELL_START = "SKU_SELL_START";
    public static final String JOB_NAME_SKU_SELL_END = "SKU_SELL_END";

    @Autowired
    public void setFmbScheduleService(FmbScheduleService fmbScheduleService) {
        FmbScheduleUtil.fmbScheduleService = fmbScheduleService;
    }

    @Autowired
    @Qualifier("asyncServiceExecutor")
    public  void setExecutor(  ThreadPoolExecutor executor) {
        FmbScheduleUtil.executor = executor;
    }

    /**
     * 添加定时执行任务
     *
     * @param jobType 任务类型
     * @param id      业务id
     * @param map     其他业务参数
     * @param second  延迟的秒数
     * @return
     */
    public static HashMap<String, Object> addScheduleJob(String jobType, Integer id, Map<String, Object> map, int second) {
        return fmbScheduleService.addJobWithSecond(jobType, id, map, second);
    }

    public static HashMap<String, Object> addScheduleJob(String jobType, Integer id, Map<String, Object> map, Date date) {
        return fmbScheduleService.addJob(jobType, id, map, date);
    }
    public static HashMap<String, Object> addScheduleJob(String jobType, Integer id, Map<String, Object> map, String date) throws ParseException {
        return fmbScheduleService.addJob(jobType, id, map, FmbDateUtil.toFullDate(date));
    }

    public static HashMap<String, Object> addAsyncJobAndRunNow(String jobType, Integer id, Map<String, Object> map) {
        return fmbScheduleService.addJobWithSecond(jobType, id, map, 0);
    }

    public static  void doAsyncJob(FmbAsyncJob job){
        executor.execute(new Runnable() {
            @Override
            public void run() {
                logger.info("doAsyncJob_begin");
                job.doJob();
                logger.info("doAsyncJob_end");
            }
        });
    }

}
