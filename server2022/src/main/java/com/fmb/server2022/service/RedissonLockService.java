package com.fmb.server2022.service;

import com.fmb.basic.BadLogicException;
import com.fmb.util.SnowflakeIdWorker;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Service
public class RedissonLockService {

    private static Logger logger = LoggerFactory.getLogger(RedissonLockService.class);

    @Autowired
    static RedissonClient redissonClient ;


    @Autowired
    public  void setRedissonClient(RedissonClient redissonClient) {
        RedissonLockService.redissonClient = redissonClient;
    }

    /**
     *  对执行lockJob 的操作增加分布式redis锁操作
     *
     * @param lockKeyPre   分布式锁前缀
     * @param lockValue    分布式锁后缀,一般是资源的id 一类的
     * @param waitMilliseconds   锁等待时间,单位毫秒
     * @param leaseMilliseconds  锁过了这个毫秒数后就会释放
     * @param lockJob      锁定住资源后要做的工作
     * @param errorMessage      获取锁失败后返回异常的文本内容
     */
    public static void doLockJob(
            String lockKeyPre,
            String lockValue,
            int waitMilliseconds,
            int leaseMilliseconds,
            RedissonLockJob lockJob,
            String errorMessage
    ){
        //资源加锁
        RLock stockLock = redissonClient.getLock( lockKeyPre +lockValue );
        try {
            if (stockLock.tryLock(waitMilliseconds, leaseMilliseconds, TimeUnit.MILLISECONDS)) {
                try{
                    lockJob.doJob();
                }
                finally {
                    if (stockLock != null && stockLock.isLocked() && stockLock.isHeldByCurrentThread()) {
                        stockLock.unlock();
                    }else {
                        logger.info("not need_unlock #FALSE");
                    }
                }
            }else {
                throw new BadLogicException(errorMessage);
            }
        } catch (InterruptedException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
            throw new BadLogicException("系统异常,获取资源锁失败[2102]");
        }

    }

    /**
     *
     * @param lockKeyPre
     * @param lockValue
     * @param leaseMilliseconds 最长占用时间
     * @param lockJob
     */
    public static void doLockJobNoWait(
            String lockKeyPre,
            String lockValue,
            int leaseMilliseconds ,
            RedissonLockJob lockJob
    ) {
        //资源加锁
        final String lockName = lockKeyPre + lockValue;
        RLock stockLock = redissonClient.getLock(lockName);
        try {
            if (stockLock.tryLock(0,leaseMilliseconds,TimeUnit.MILLISECONDS)) {
                try {
                    lockJob.doJob();
                } finally {

                    if (stockLock != null && stockLock.isLocked() && stockLock.isHeldByCurrentThread()) {
                        stockLock.unlock();
                    }else {
                        logger.info("not need_unlock #FALSE");
                    }
                }
            } else {
                logger.warn("getLockFail {}",lockName);
            }
        } catch (InterruptedException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
            throw new BadLogicException("系统异常,获取资源锁失败[2111]");
        }
    }

}
