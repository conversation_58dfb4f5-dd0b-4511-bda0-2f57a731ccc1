package com.fmb.server2022.service;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbMycps;
import com.fmb.server2022.fmbx.entity.FmbMycpsCommissionOrder;
import com.fmb.server2022.fmbx.entity.FmbMycpsUser;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.service.IFmbMycpsCommissionOrderService;
import com.fmb.server2022.fmbx.service.IFmbMycpsService;
import com.fmb.server2022.fmbx.service.IFmbMycpsUserService;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbUsersService;
import com.fmb.util.SnowflakeIdWorker;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/6/27 10:15 下午
 */
@Service
public class MyCpsService {


    private static Logger logger = LoggerFactory.getLogger(MyCpsService.class);

    private final IFmbMycpsCommissionOrderService commissionOrderService ;
    private final IFmbMycpsService mycpsService ;
    private final IFmbOrderGoodsService goodsService;
    private final IFmbMycpsUserService mycpsUserService;
    private final IFmbUsersService usersService;


    private final JedisPool jedisPool ;

    @Autowired
    public MyCpsService(IFmbMycpsCommissionOrderService commissionOrderService, IFmbMycpsService mycpsService, IFmbOrderGoodsService goodsService, IFmbMycpsUserService mycpsUserService, IFmbUsersService usersService, JedisPool jedisPool) {
        this.commissionOrderService = commissionOrderService;
        this.mycpsService = mycpsService;
        this.goodsService = goodsService;
        this.mycpsUserService = mycpsUserService;
        this.usersService = usersService;
        this.jedisPool = jedisPool;
    }


    @DS(FmbConstants.DB_slave)
    public void read3(){
        final String bookFilePath = "/Users/<USER>/Downloads/分享官名单3.xlsx";
        final ExcelReader reader = ExcelUtil.getReader(bookFilePath);

        final List<Map<String, Object>> maps = reader.readAll();
        for (Map<String, Object> map : maps) {
            Wrapper<FmbMycpsUser> wr1 = new LambdaQueryWrapper<FmbMycpsUser>()
                    .eq(FmbMycpsUser::getMid,(Long)map.get("分享官ID"))
                    .eq(FmbMycpsUser::getStatus,1)
                    ;
            final FmbMycpsUser one = mycpsUserService.getOne(wr1);

            map.put("mobile", "");
            if (one != null) {
                final Integer uid = one.getUid();
                final FmbUsers byId = usersService.getById(uid);

                if (byId != null) {
                    map.put("mobile", byId.getPhoneNumber());
                }
            }
        }

        final ExcelWriter writer = ExcelUtil.getWriter(bookFilePath + "_" + System.currentTimeMillis() + ".xlsx");
        writer.write(maps) ;
        writer.close();

        reader.close();
    }

    public void readRedis2Excel(){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            final List<String> mycpsList = jedis.lrange("mycpsList", 0, -1);

            final ArrayList<MycpsExcelInfo> l = new ArrayList<>();
            for (String s : mycpsList) {
                final MycpsExcelInfo mycpsExcelInfo = JSON.parseObject(s, MycpsExcelInfo.class);
                l.add(mycpsExcelInfo);
            }

            final ExcelWriter writer = ExcelUtil.getWriter("/Users/<USER>/Downloads/分销数据/Result_汇总" + System.currentTimeMillis() + ".xlsx");

            writer.write(l);
            writer.close();

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    @DS(FmbConstants.DB_slave)
    public void doJob() {





        Wrapper<FmbMycps> wrMycps = new LambdaQueryWrapper<FmbMycps>()
                .select(FmbMycps::getMid,FmbMycps::getmLevel,FmbMycps::getMname,FmbMycps::getInviteMid)
                .eq(FmbMycps::getStatus,1)
                .eq(FmbMycps::getmMode,1)
                ;
        final List<FmbMycps> allMycps = mycpsService.list(wrMycps);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            final List<String> collect = allMycps.stream().map(JSON::toJSONString).collect(Collectors.toList());

            jedis.lpush("mycpsInfos",collect.toArray(new String[]{})) ;
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

//        Collections.shuffle(allMycps);
//
//        for (FmbMycps myCps : allMycps) {
//
//            RedissonLockService.doLockJob("mycpsLock", myCps.getMid() + "", 3000, 2000,
//                    () -> core1(coll, myCps), "系统繁忙");
//
//        }



    }

    @DS(FmbConstants.DB_slave)
    public void core1() {

        final ArrayList<Object> coll = new ArrayList<>();
        coll.add(0);
        coll.add(1);
        coll.add(2);
        coll.add(3);
        coll.add(4);
        Jedis jedis = null;

        jedis = jedisPool.getResource();
        jedis.select(FmbConstants.REDIS_DEFAULT_DB);

        try {

//            jedis.lpush("mycpsInfos",collect.toArray(new String[]{})) ;

            while (true) {

                if (!jedis.isConnected()) {
                    jedis = jedisPool.getResource();
                    jedis.select(FmbConstants.REDIS_DEFAULT_DB);
                }

                final String mycpsInfos = jedis.rpop("mycpsInfos");

                if (mycpsInfos == null) {
                    break;
                }

                //获取 分享官数据
                final FmbMycps myCps = JSON.parseObject(mycpsInfos, FmbMycps.class);

                final MycpsExcelInfo mycpsExcelInfo = new MycpsExcelInfo();

                //获取邀请人信息
                FmbMycps invite = null;
                if (myCps.getInviteMid() != null && myCps.getInviteMid() != 0) {
                    invite = mycpsService.getById(myCps.getInviteMid());
                }

                //找到分销商的 返佣模式的所有 佣金订单信息
                Wrapper<FmbMycpsCommissionOrder> wrOrder = new LambdaQueryWrapper<FmbMycpsCommissionOrder>()
                        .eq(FmbMycpsCommissionOrder::getMid, myCps.getMid())
                        .eq(FmbMycpsCommissionOrder::getOrderMode, 1)
                        .in(FmbMycpsCommissionOrder::getCommissionStatus, coll)
                        .select(FmbMycpsCommissionOrder::getOriMoney, FmbMycpsCommissionOrder::getOrderSn,
                                FmbMycpsCommissionOrder::getRealCommission,
                                FmbMycpsCommissionOrder::getOrderMode, FmbMycpsCommissionOrder::getType,
                                FmbMycpsCommissionOrder::getCommissionStatus);
                final List<FmbMycpsCommissionOrder> listOrders = commissionOrderService.list(wrOrder);

                //获取 去重后的 订单号
                final List<String> distOrders = listOrders.stream().map(FmbMycpsCommissionOrder::getOrderSn).distinct().collect(Collectors.toList());

                //找到对应 订单的 goods 信息
                List<FmbOrderGoods> listGoods = new ArrayList<>();

                if (!distOrders.isEmpty()) {
                    Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                            .in(FmbOrderGoods::getOrderSn, distOrders)
                            .select(FmbOrderGoods::getGoodsNumber, FmbOrderGoods::getGoodsPrice, FmbOrderGoods::getSettlePrice);
                    listGoods = goodsService.list(wrGoods);
                }

                //获取这些订单 商品金额
                final BigDecimal reduceGoods =
                        listGoods.stream().map(o -> o.getGoodsPrice().multiply(new BigDecimal(o.getGoodsNumber()))).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                //获取结算金额
                final BigDecimal reduceSettle =
                        listGoods.stream().map(o -> o.getSettlePrice().multiply(new BigDecimal(o.getGoodsNumber()))).reduce(BigDecimal.ZERO,
                                BigDecimal::add);

                //直佣已提现
                final BigDecimal reduce1 =
                        listOrders.stream().filter(o -> o.getType() == 1 && o.getCommissionStatus() == 4).map(FmbMycpsCommissionOrder::getRealCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
                //间佣已提现
                final BigDecimal reduce2 =
                        listOrders.stream().filter(o -> o.getType() == 2 && o.getCommissionStatus() == 4).map(FmbMycpsCommissionOrder::getRealCommission).reduce(BigDecimal.ZERO, BigDecimal::add);

                // 所有的直佣
                final BigDecimal reduce1All =
                        listOrders.stream().filter(o -> o.getType() == 1).map(FmbMycpsCommissionOrder::getRealCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
                //所有的间佣
                final BigDecimal reduce2All =
                        listOrders.stream().filter(o -> o.getType() == 2).map(FmbMycpsCommissionOrder::getRealCommission).reduce(BigDecimal.ZERO, BigDecimal::add);

//            final BigDecimal reduce3 =
//                    listOrders.stream().filter(o -> o.getType() == 3 && o.getCommissionStatus() == 4).map(FmbMycpsCommissionOrder::getRealCommission).reduce(BigDecimal.ZERO, BigDecimal::add);

                //获取 佣金订单里的 用户实际支付金额 并累加
                BigDecimal payMoney = new BigDecimal("0");
                final HashSet<String> ext = new HashSet<>();
                for (FmbMycpsCommissionOrder listOrder : listOrders) {
                    if (!ext.contains(listOrder.getOrderSn())) {
                        payMoney = payMoney.add(listOrder.getOriMoney());
                        ext.add(listOrder.getOrderSn());
                    }
                }

//            logger.info("mid={} level={}  inviteMid={} 直佣={} 间佣={} 额外奖励={}  reduceGoods={} reduceSettle={}  payMoney={}",myCps.getMid(),
//                    myCps.getmLevel(),
//                    myCps.getInviteMid(),reduce1
//                    ,reduce2,reduce3,reduceGoods,reduceSettle,payMoney);

                mycpsExcelInfo.setMid(myCps.getMid());
                mycpsExcelInfo.setMName(myCps.getMname());
                mycpsExcelInfo.setMLevel(myCps.getmLevel());
                if (invite != null) {
                    mycpsExcelInfo.setInviteMid(invite.getInviteMid());
                    mycpsExcelInfo.setInviteMName(invite.getMname());
                }

                mycpsExcelInfo.setReduce1(reduce1);
                mycpsExcelInfo.setReduce2(reduce2);

                mycpsExcelInfo.setReduce1All(reduce1All);
                mycpsExcelInfo.setReduce2All(reduce2All);
//            mycpsExcelInfo.setReduce3(reduce3);
                mycpsExcelInfo.setReduceGoods(reduceGoods);
                mycpsExcelInfo.setReduceSettle(reduceSettle);
                mycpsExcelInfo.setPayMoney(payMoney);

                final String o = JSON.toJSONString(mycpsExcelInfo);
                logger.info("{}", o);

                jedis.lpush("mycpsList", o);

                if (jedis.llen("mycpsInfos") == 0) {
                    break;
                }

            }

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    public void read5() {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            final String my123Info = jedis.rpop("my123Info");
            logger.info("#{}# {}", my123Info,my123Info==null);

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }
}
@Data
class MycpsExcelInfo{

    private Integer mid;
    private String mName;
    private Integer mLevel;
    private Integer inviteMid;
    private String inviteMName;

    BigDecimal reduce1 ;
    BigDecimal reduce1All ;
    BigDecimal reduce2 ;
    BigDecimal reduce2All ;

    BigDecimal reduceGoods ;
    BigDecimal reduceSettle ;
    BigDecimal payMoney ;

}
