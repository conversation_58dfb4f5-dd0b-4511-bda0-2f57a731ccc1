package com.fmb.server2022.service;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.BadAccessException;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.mapper.LoginDao;
import com.fmb.util.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.HashMap;
import java.util.Set;

import static com.fmb.basic.FmbConstants.*;

@Service
@Slf4j
public class SysTokenService {


    private static Logger logger = LoggerFactory.getLogger(SysTokenService.class);

    @Autowired
    LoginDao loginDao;

    @Autowired
    JedisPool jedisPool ;

    /**
     * 用户登录验证通过后 ,生成token,记录用户已登录的状态
     */
    public String generateToken(String username) {
        MDC.put("username", username);

        //生成token
        String token = SnowflakeIdWorker.getInstance().nextId()+"" ;
        //设置用户信息缓存
        //获取用户信息
        SessionUserInfoOfFmbAdmin info = getUserInfoByUsernameFromDB(username);
//        if (info == null) {
//            return "" ;
//        }
//        log.info("设置用户信息缓存:token={} , username={}, info={}", token, username, info);
        writeUserInfo2Cache(token, info);
        return token;
    }

    public SessionUserInfoOfFmbAdmin getUserInfo() {
        String token = MDC.get("token");
        return getUserInfoFromCache(token);
    }

    /**
     * 根据token查询用户信息
     * 如果token无效,会抛未登录的异常
     */
    public SessionUserInfoOfFmbAdmin getUserInfoFromCache(String token) {
        if (StringUtils.isBlank(token)) {
            throw new BadAccessException("无效的token");
        }
        SessionUserInfoOfFmbAdmin info = getUserInfoBytoken(token);
//        if (info == null) {
////            throw new BadAccessException("找不到对应token");
////        }
        return info;
    }


    /**
     * 退出登录时,将token置为无效
     */
    public boolean invalidateToken() {
        String token = MDC.get(TOKEN);
        if (StringUtils.isNotBlank(token)) {
           return   invalidate(token);
        }

        return false ;

    }

    /**
     * 根据 token 让用户登录的token失效
     * @param token
     */
    public boolean invalidate(String token) {

        boolean result = false ;

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);


            String value = jedis.get(TOKEN_PRE_OF_FMBADMIN + token);


            //删除 集合里的数据
            if(StringUtils.isNotBlank(value)){
                SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = JSON.parseObject(value, SessionUserInfoOfFmbAdmin.class);
                jedis.srem (TOKENSETS_PRE+ sessionUserInfoOfFmbAdmin.getUserId(),token) ;
            }

            Long del = jedis.del(TOKEN_PRE_OF_FMBADMIN + token);

            result = del>0 ;

            log.info(" invalidate_token={} opresult={}",token,result);

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return result ;


    }


    /**
     * 把用户信息 写入redis
     * @param token
     * @param info  包含了用户权限的用户信息
     */
    public void writeUserInfo2Cache(String token, SessionUserInfoOfFmbAdmin info) {

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);

            jedis.setex(TOKEN_PRE_OF_FMBADMIN +token, TOKEN_KEEP_SECONDS ,JSON.toJSONString(info)) ;

            //把用户的所有token 都存到redis setl里 ,后续禁用用户或者权限变更可以根据这个找到用户
            int userId = info.getUserId();
            jedis.sadd ( TOKENSETS_PRE+ userId,token) ;
            checkSameUserOtherTokens(jedis, token, userId);

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    public void checkSameUserOtherTokens(Jedis jedis, String token, int userId) {
        //顺便检查一下 该用户的所有token 看是否有效,删除无效的
        Set<String> smembers = jedis.smembers(TOKENSETS_PRE + userId);
        for (String tokencheck : smembers) {

            if (token.equals(tokencheck)) {
                continue;
            }
            if(!jedis.exists(TOKEN_PRE_OF_FMBADMIN +tokencheck)){
                jedis.del(TOKEN_PRE_OF_FMBADMIN +tokencheck) ;
                jedis.srem (TOKENSETS_PRE+ userId,tokencheck) ;
            }
        }
    }

    /**
     * 通过token值 从redis里读取用户 信息
     * @param token
     * @return
     */
    public SessionUserInfoOfFmbAdmin getUserInfoBytoken(String token) {


        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);
            String tokenKey = TOKEN_PRE_OF_FMBADMIN + token;
            String value = jedis.get(tokenKey);



            if(StringUtils.isBlank(value)){
                log.info(" cannot_get_token {}",tokenKey);
                return null ;
            }else {
                SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = JSON.parseObject(value, SessionUserInfoOfFmbAdmin.class);

                //保持session
                if (jedis.ttl(tokenKey)<TOKEN_REMAIN_SECONDS_LIMIT) {
                    jedis.expire(tokenKey,TOKEN_KEEP_SECONDS) ;
                }

                return sessionUserInfoOfFmbAdmin;
            }



        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return null ;
    }


    /**
     * 根据用户名 查询用户的 SessionUserInfoOfFmbAdmin
     * @param username
     * @return  有可能返回空 .要做非空判断
     */

    public SessionUserInfoOfFmbAdmin getUserInfoByUsernameFromDB(String username) throws  BadLogicException {
        SessionUserInfoOfFmbAdmin userInfo = loginDao.getUserInfo(username);
        if (userInfo == null) {
            throw new BadLogicException("获取用户信息失败username="+username+",请联系管理员查看当前用户状态") ;
        }
        //如果是管理员权限 就把所有的菜单和权限读出来
        if (userInfo.getRoleIds().contains(FmbConstants.ADMIN_ROLE_ID )) {
            //管理员,查出全部按钮和权限码
            userInfo.setMenuList(loginDao.getAllMenu());
            userInfo.setPermissionList(loginDao.getAllPermissionCode());
        }
        return userInfo;
    }


    /**
     *
     * 读取 系统返回 需要前端更新的数据
     *
     *
     * @return
     */
    public HashMap readstats() {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("needUpdateToken", 0);


        String token = MDC.get(TOKEN);
        if (StringUtils.isNotBlank(token)) {

            Jedis jedis = null;
            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);
                Boolean exists = jedis.exists(FmbConstants.TOKEN_NEED_UPDATE + token);

                //读取到后 删除相应的key
                if (exists) {
                    resultMap.put("needUpdateToken", 1);
                    jedis.del(FmbConstants.TOKEN_NEED_UPDATE + token) ;
                }
            } catch (Exception ex) {

                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);

            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }

        }


        return  resultMap ;
    }


}
