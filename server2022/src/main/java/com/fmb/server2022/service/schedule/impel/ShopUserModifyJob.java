package com.fmb.server2022.service.schedule.impel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.FmbxBpService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_SHOPUSER_MODIFY;

/**
 *  php 修改供应商(商家) 自动同步
 */
@Service(value = JOB_NAME_SHOPUSER_MODIFY)
public class ShopUserModifyJob implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(ShopUserModifyJob.class);

    @Autowired
    FmbxBpService bpService ;

    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {
            final JSONObject jsonObject = JSON.parseObject(job.getJobExtPars());
            if ("edit".equals( jsonObject.getString("operateType"))) {
                bpService.syncShopUserInfoFromPhp(jsonObject.getInteger("shopUserId"));
            }

        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
