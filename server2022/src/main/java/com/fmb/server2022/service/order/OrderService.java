package com.fmb.server2022.service.order;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.FrontApiRequestDomain;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderSysInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqConfirmOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.domain.*;
import com.fmb.server2022.domain.stock.SkuSellNumUpdate;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.entity.FmbOrderConfirm;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmb_new.service.IFmbOrderConfirmService;
import com.fmb.server2022.fmbx.entity.*;
import com.fmb.server2022.fmbx.service.*;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqFmbOrderConfirm;
import com.fmb.server2022.reqdomain.ReqFmbOrderHotel;
import com.fmb.server2022.reqdomain.ReqHotelMail;
import com.fmb.server2022.reqdomain.RoomNumStatus;
import com.fmb.server2022.service.CouponService;
import com.fmb.server2022.service.FrontHotelService;
import com.fmb.server2022.service.FrontSkuService;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.UserUnionService;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.server2022.service.schedule.impel.OrderSuccessPayDomain;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.FmbOrderInfoUtil;
import com.fmb.util.FmbPhpUtil;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import de.ailis.pherialize.Mixed;
import de.ailis.pherialize.MixedArray;
import de.ailis.pherialize.Pherialize;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.basic.user.SessionUser.SESSIONUSER_FRONT;
import static com.fmb.server2022.domain.ConfirmOrderMoneyGroup.GROUPTYPE_1;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.ORDER_SUCCESS_PAY;
import static com.fmb.server2022.service.order.KefuOrderService.FMBX_SECOND_CONFIRM;
import static com.fmb.server2022.service.order.OrderReturnService.FMBX_HOTEL_RESERVE_RETURN_NEED_PART_MONEY;
import static com.fmb.server2022.service.order.OrderReturnService.isOrderSuccessStatus;
import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_CANCELORDERJOB;
import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_ORDER_CLOSE_IN_1_HOUR;
import static com.fmb.util.FmbDateUtil.FORMATTER_HHmm;

@Service
public class OrderService {

    public static final String FULL_FORMAT = "yyyyMMddHHmmss";
    public static final String FRONT = "front";
    public static final String ADMIN001 = "admin001";
    public static final String CODE_ALERT = "退款中无法预约";

    @Autowired
    Configuration freeMarkerConfig ;

    //删除订单按钮
    private static final int BUTTON_TYPE_DEL = 1;
    //去支付
    private static final int BUTTON_TYPE_PAY = 2;
    //再次购买
    private static final int BUTTON_TYPE_RE_BUY = 3;
    //去预约
    private static final int BUTTON_TYPE_2_HOTEL_RESERVE = 4;
    //房券去退货
    private static final int BUTTON_TYPE_2_RETURN_SERVICE = 5;
    //取消订单
    private static final int BUTTON_TYPE_CANCEL = 6;
    // 退款单详情
    private static final int BUTTON_TYPE_RETURN_DETAIL = 7;

    //日历房
    public static final int GOODS_TYPE_6 = 6;
    //房券
    public static final int GOODS_TYPE_7 = 7;


    private static Logger logger = LoggerFactory.getLogger(OrderService.class);


    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    IFmbOrderRelevantLogService orderRelevantLogService;

    @Autowired
    IFmbOrderTradeSnapshotService snapshotService ;

    @Autowired
    IFmbxBpsRoomService bpsRoomService ;

    @Autowired
    IFmbOrderCancelService orderCancelService ;

    @Autowired
    IFmbOrderReturnsService orderReturnsService ;

    @Autowired
    IFmbOrderActionsService orderActionsService ;

    @Autowired
    FrontHotelService frontHotelService;

    @Autowired
    IFmbGoodsCategoryService goodsCategoryService;
    
    @Autowired
    OrderService self;

    @Autowired
    IFmbxBpsContentService bpsContentService ;

    @Autowired
    IFmbUsersService usersService ;


    @Autowired
    IFmbExchangeCodesService exchangeCodesService ;

    @Autowired
    OrderReturnService returnService ;

    @Autowired
    IFmbCouponUnableActivityService couponUnableActivityService;

    @Autowired
    IFmbShieldUsersService shieldUsersService;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbxSkuService skuService ;

    @Autowired
    IFmbOrderRelatedDataService relatedDataService ;

    @Autowired
    IFmbReserveHotelCodesService hotelCodesService;

    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;

    @Autowired
    IFmbxSuiteExtService suiteExtService ;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService ;

    @Autowired
    IFmbxSuiteService suiteService ;

    @Autowired
    FrontSkuService frontSkuService ;

    @Autowired
    IFmbPayOrdersService payOrdersService ;

    @Autowired
    IFmbxBpParterInfoService bpParterInfoService ;

    @Autowired
    IFmbxBpMainService bpMainService ;

    @Autowired
    IFmbxBpParterHotelInfoService parterHotelInfoService ;

    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    IFmbOrderExtInfoService orderExtInfoService ;

    @Autowired
    IFmbOrderGoodsService orderGoodsService ;

    @Autowired
    IFmbMycpsBindUserService bindUserService ;

    @Autowired
    IFmbHotelReserveOrderRecordService hotelReserveOrderRecordService ;

    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    IFmbUserCouponService couponService ;

    @Autowired
    IFmbCouponTypeService couponTypeService ;

    @Autowired
    IFmbUserExtinfoService userExtinfoService ;
    @Autowired
    IFmbUserCashLogService userCashLogService ;


    @Autowired
    IFmbxSuiteStockService suiteStockService ;

    @Autowired
    IFmbxSuiteRoomdatePriceService roomdatePriceService ;


    @Autowired
    SuiteService mySuiteService ;


    @Autowired
    JedisPool jedisPool ;

    @Autowired
    IFmbCouponLogService couponLogService ;


    @Autowired
    OrderOfHotelReserveService orderOfHotelReserveService ;

    @Autowired
    OrderOfHotelDateService orderOfHotelDateService ;

    @Autowired
    FrontSkuDao frontSkuDao ;

    @Autowired
    IFmbTemplateService templateService ;

    @Autowired
    IFmbxScheduleJobService scheduleJobService;

    @Autowired
    CouponService checkCouponService ;

    @Autowired
    KefuOrderService kefuOrderService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IFmbxHotelOrderConfirmService orderConfirmService ;

    @Autowired
    IFmbOrderConfirmService fmbOrderConfirmService;

    @Autowired
    OrderSuccessHandler orderSuccessHandler;

    @Autowired
    IFmbxHotelGoodsDetailService hotelGoodsDetailService;

    @Autowired
    private IFmbAdminService adminService;

    @Autowired
    IFmbMycpsCommissionOrderService mycpsCommissionOrderService;

    @Autowired
    IFmbMycpsService mycpsService;

//    IFmbxHotelNeedConfirmService needConfirmService


    public AbstractOrderservice getOrderService(int type){
        AbstractOrderservice orderservice =null;
        switch (type){
            case SKU_HOTEL_DATE:
                orderservice = orderOfHotelDateService ;
                break;
            case SKU_HOTEL_RESERVE:
                orderservice= orderOfHotelReserveService ;
                break;
        }
        return orderservice ;
    }


    public AbstractOrderservice getOrderServiceFromGoodsType(int type){
        AbstractOrderservice orderservice =null;
        switch (type){
            case SKU_HOTEL_DATE_GOODS_TYPE:
                orderservice = orderOfHotelDateService ;
                break;
            case SKU_HOTEL_RESERVE_GOODS_TYPE:
                orderservice= orderOfHotelReserveService ;
                break;
        }
        return orderservice ;
    }


    public final static Pattern MOBILE = Pattern.compile("1[3-9]\\d{9}");

    /**
     * 创建订单方法的 整体 入口
     *
     * @param resultMap
     * @param input
     * @param nowUser
     */
    public void createOrderMain(HashMap<String, Object> resultMap, ReqGenOrder input, NowUser nowUser) {

        //各种业务逻辑判断 ,并组织 价格数据到 input.getOrderSysInfo().getSkuDetails() 的价格信息里
//        input.getOrderSysInfo().getSkuDetails()
        self.checkCanGenOrder(input, nowUser);

        //该方法结束后就知道了用户购买的所有货物的价格信息了
        //存在 input.OrderSysInfo.goodsPriceAll 里
        self.sumPrice2OrderSysInfo(input,nowUser) ;


        //用户资源加锁
        RedissonLockService.doLockJob(FmbConstants.USER_RESOURCE_LOCK, nowUser.getUserId()+"", 3000, 2000,
                () -> self.lockUserResourceAndCreateOrder(input, nowUser),"系统繁忙");

        //运行到这里核心的订单已经生成可以做其他

        //如果直接订单支付成功了
        if (input.getOrderSysInfo().isOrderDirectSuccess()) {
            self.orderSuccessPay(input.getOrderSysInfo().getFmbOrderInfo().getOrderSn(),null) ;
        }
        //输出结果前 执行可以异步执行的任务
        self.doAsyncJob(input);

        //做输出操作
        self.buildOutInfo(resultMap,input) ;

    }


    /**
     * 注意这个方法是异步执行
     * @param input
     */
    public void doAsyncJob(ReqGenOrder input) {

        FmbScheduleUtil.doAsyncJob( ()->{
            doAsyncJobCore(input) ;
        } );

    }

    public void doAsyncJobCore(ReqGenOrder input) {

        logger.info("doAsyncJobCore_begin");

        final ArrayList<SkuSellNumUpdate> skuSellNumUpdates = new ArrayList<>();
        final FmbOrderInfo orderInfo = input.getOrderSysInfo().getFmbOrderInfo();

        //构造销量信息 skuSellNumUpdates
        for (SkuDetail detail : input.getOrderSysInfo().getSkuDetails()) {

            if (GOODS_TYPE_6== orderInfo.getGoodsType()) {
                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());
                skuSellNumUpdates.add(numUpdate) ;
            }
            if (GOODS_TYPE_7== orderInfo.getGoodsType()) {

                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());
                skuSellNumUpdates.add(numUpdate) ;
            }
        }

        //更新 销量
        self.updateSkuSellNum(skuSellNumUpdates,orderInfo) ;



        // 1. 如果库存减到 0 进行异步通知
        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).doFinalJobWhenCreateOrderOver(input,skuDetail);
        }

        //增加 定时取消任务
        if (!input.getOrderSysInfo().isOrderDirectSuccess()) {
            //增加定时任务检查 订单是否支付了 ,没有就取消订单
            HashMap<String, Object> map = new HashMap<>();
            FmbScheduleUtil.addScheduleJob(JOB_NAME_CANCELORDERJOB,input.getOrderSysInfo().getFmbOrderInfo().getOrderId(),map,
                    input.getOrderSysInfo().getCancelSecond()) ;

            int oneHour = 60*60 ;
            //增加 还有1小时交易关闭短信
            if (input.getOrderSysInfo().getCancelSecond()>oneHour) {
                HashMap<String, Object> par = new HashMap<>();
                par.put("aid", input.getOrderSysInfo().getActivity().getXaid());
                FmbScheduleUtil.addScheduleJob(JOB_NAME_ORDER_CLOSE_IN_1_HOUR,input.getOrderSysInfo().getFmbOrderInfo().getOrderId(),par,
                        input.getOrderSysInfo().getCancelSecond()-oneHour) ;
            }

        }

        //如果有mycps信息 调用接口读取信息
        self.doMycpsJob(orderInfo,input.getMycps()) ;


    }

    /**
     *
     * @param orderInfo
     * @param mycps cps 信息
     */
    public void doMycpsJob(FmbOrderInfo orderInfo, String mycps) {

        //酒店预约单不走分销逻辑
        if (orderInfo.getOrderType()!=null && 2== orderInfo.getOrderType().intValue()) {
            return ;
        }


        //如果携带 mycps参数
        if (StringUtils.isNotBlank(mycps)) {

            //请求php 接口 解析分销数据
            final HttpRequest post = HttpUtil.createPost("http://m.fumubang.com/mobile/ajax/get_mycps_info");
            post.form("mycps",mycps) ;
            final String signV = MD5Util.md5(mycps + PHP_MD5_SALT_KEY);
            post.form("sign", signV) ;
            try {
                final String body = post.execute().body();
                if (StringUtils.isNotBlank(body) && body.indexOf("mid")!=-1) {
                    // {"error_code":"0","msg":"","data":{"mid":"629","uid":"36609","date":"2023-03-10 10:56:12"}}

                    if (logger.isInfoEnabled()) {
                        logger.info("mycps_info {}",body);
                    }
                    
                    final JSONObject cpsData = JSON.parseObject(body).getJSONObject("data");
                    final int mid = Integer.parseInt(cpsData.getString("mid"));
                    final int uid = Integer.parseInt(cpsData.getString("uid"));
                    final String date = cpsData.getString("date");

                    final FmbOrderInfo updateMycps = new FmbOrderInfo();

                    updateMycps.setOrderId(orderInfo.getOrderId());
                    updateMycps.setMycpsId(mid);
                    final boolean update = orderInfoService.updateById(updateMycps);

                    if (update) {
                        pushRedisMessage2PhpCreatMyCpsOrder(orderInfo.getOrderSn(),orderInfo.getOrderId(),mycps,mid,uid) ;
                    }

                }
            }catch (Exception ex){
                logger.error("ordersn={} mycps={}",orderInfo.getOrderSn(),mycps);
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            }

        }else  {
            //不能是酒店预约单

            //如果是专属买家 
            Wrapper<FmbMycpsBindUser> wrBind = new LambdaQueryWrapper<FmbMycpsBindUser>()
                    .eq(FmbMycpsBindUser::getUid,orderInfo.getUid())
                    .eq(FmbMycpsBindUser::getBuyerStatus,1)
                    .select(FmbMycpsBindUser::getMid,FmbMycpsBindUser::getMycpsUid)
                    .last(" limit 1 ")
                    ;
            final FmbMycpsBindUser bindUser = bindUserService.getOne(wrBind);
            if (bindUser != null) {
                pushRedisMessage2PhpCreatMyCpsOrder(orderInfo.getOrderSn(),orderInfo.getOrderId(),"",bindUser.getMid(),bindUser.getMycpsUid()) ;
            }
        }

    }

    /**
     * 把分销信息 推给php
     * @param orderSn
     * @param orderId
     * @param mycps
     * @param mid
     * @param uid
     */
    void pushRedisMessage2PhpCreatMyCpsOrder(String orderSn, Integer orderId, String mycps, int mid, int uid) {
        logger.info("orderId={},orderSn={},mycps={}" ,orderId,orderSn,mycps);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DB_1);

            HashMap<String, Object> data = new HashMap<>();
            data.put("mycps_mid", mid);
            data.put("mycps_uid", uid);
            data.put("order_id", orderId);
            data.put("mycps", mycps);

            final String pushVal = "mycps-add_new_order-" + java.net.URLEncoder.encode(JSON.toJSONString(data), "utf-8");
            jedis.lpush("msg_queue_list", pushVal) ;
            logger.info("php_redis_lpush msg_queue_list  [{}]",pushVal);

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }

    /**
     * 如果是 分销订单支付成功 就推送消息到 php 队列
     * @param orderSn
     * @param orderId
     * @param mid
     */
    void pushRedisMessage2PhpCpsOrderSuccessPay(String orderSn, Integer orderId,  int mid ) {
        logger.info("orderId={},orderSn={}" ,orderId,orderSn);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DB_1);

            HashMap<String, Object> data = new HashMap<>();
            data.put("mid", mid);
            data.put("order_sn", orderSn);
            data.put("order_id", orderId);

            final String pushVal = "mycps-update_mycps_commission-" + java.net.URLEncoder.encode(JSON.toJSONString(data),"utf-8");
            jedis.lpush("msg_queue_list", pushVal) ;
            logger.info("php_redis_lpush msg_queue_list  [{}]",pushVal);
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }


    public void pushRedisMessage2PhpCpsOrderReturnPass(Integer returnID ) {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DB_1);

            HashMap<String, Object> data = new HashMap<>();
            data.put("return_id", returnID);

            final String pushVal = "mycps-orderReturn-" + java.net.URLEncoder.encode(JSON.toJSONString(data),"utf-8");
            jedis.lpush("msg_queue_list", pushVal) ;
            logger.info("php_redis_lpush msg_queue_list  [{}]",pushVal);
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }


    public void saveOrderInfoAction(FmbOrderInfo orderInfo, String note) {

        FmbOrderActions action = new FmbOrderActions();
        action.setOrderSn(orderInfo.getOrderSn());
        //代表前台用户
        action.setAdminUid(-1);
        action.setOrderStatus(orderInfo.getOrderStatus());
        action.setShippingStatus(orderInfo.getShippingStatus());
        action.setPayStatus(orderInfo.getPayStatus());
        action.setActionType("");
        action.setActionNote(note);
        action.setLogTime(LocalDateTime.now());
        orderActionsService.save(action) ;
    }

    /**
     * 增加交易快照 
     * @param input
     * @param orderInfo
     * @param skuSellNumUpdates
     */
    public void saveSnap(ReqGenOrder input, FmbOrderInfo orderInfo, ArrayList<SkuSellNumUpdate> skuSellNumUpdates) {

        /**
         * 交易快照是按 goods 个数存储的 .
         */
        //存储 交易快照
        for (SkuDetail detail : input.getOrderSysInfo().getSkuDetails()) {


            FmbOrderTradeSnapshot snapDB = new FmbOrderTradeSnapshot();
            snapDB.setRecId(detail.getRecId());
            snapDB.setOrderSn(orderInfo.getOrderSn());
            snapDB.setUid(orderInfo.getUid());
            snapDB.setBasicInfo("");

            if (GOODS_TYPE_6== orderInfo.getGoodsType()) {

                HashMap<String, Object> mapSn = new HashMap<>();
                ReqHotel reqHotel = new ReqHotel();
                reqHotel.setSkuId(detail.getReqGenSku().getSkuId());
                frontHotelService.getRoomSkuDetail(mapSn,reqHotel,null);

                mapSn.put("suiteBuyNote","");
                final Integer suiteId = detail.getSuite().getSuiteId();

                final FmbxSuiteExt buyNoteDB = querySuiteBuyNote(suiteId);

                if (buyNoteDB != null) {
                    mapSn.put("suiteBuyNote",buyNoteDB.getBuyNote());
                }

                snapDB.setContent(JSONUtil.toJsonStr(mapSn));

                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());

                skuSellNumUpdates.add(numUpdate) ;

            }
            if (GOODS_TYPE_7== orderInfo.getGoodsType()) {
                HashMap<String, Object> mapSn = new HashMap<>();
                ReqHotel reqHotel = new ReqHotel();
                reqHotel.setSkuId(detail.getReqGenSku().getSkuId());
                frontHotelService.getResSkuDetail(mapSn,reqHotel,null);


                List<FrontRoomDomain> frontRoomDomainList = (List<FrontRoomDomain>) mapSn.get("roomList");
                Integer vHotelReserveSplit = (Integer) mapSn.get("HotelReserveSplit");

                //如果是 拆分房券 需要把套餐信息里的房型 只保留一个房型,其他的删除
                // 否则订单详情里读取交易快照会有多个房型信息展示
                if (vHotelReserveSplit==1) {

                    final Iterator<FrontRoomDomain> iterator = frontRoomDomainList.iterator();
                    while (iterator.hasNext()) {
                        final FrontRoomDomain room = iterator.next();
                        if (!reqHotel.getSkuId().equals(room.getResSkuId())) {
                            iterator.remove();
                        }
                    }
                }


                mapSn.put("suiteBuyNote","");

                mapSn.put("reserveRoomInfo",detail.getReserveRoomInfo());
                mapSn.put("reserveSplit",vHotelReserveSplit);
                mapSn.put("reserveRoomIds",detail.getReserveRoomIds());

                final Integer suiteId = detail.getSuite().getSuiteId();


                final FmbxSuiteExt buyNoteDB = querySuiteBuyNote(suiteId);

                if (buyNoteDB != null) {
                    mapSn.put("suiteBuyNote",buyNoteDB.getBuyNote());
                }

                snapDB.setContent(JSONUtil.toJsonStr(mapSn));

                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());

                skuSellNumUpdates.add(numUpdate) ;
            }

            //用这个字段标记 交易快照的版本
            snapDB.setAppContent(""+FmbConstants.ORDER_SNAP_VERSION);
            snapDB.setAppContentNew("");
            //把整个下单计算数据都存储上
            snapDB.setSummary(JSONUtil.toJsonStr(input));
            snapDB.setNearScenerys("");
            //从订单里直接 读取 goodstype
            snapDB.setGoodsType(orderInfo.getGoodsType());
            snapDB.setCtime(LocalDateTime.now());
            //新的下单系统 标记
            snapDB.setIsOldData(2);
            snapshotService.save(snapDB) ;

        }
    }

    public FmbxSuiteExt querySuiteBuyNote(Integer suiteId) {
        Wrapper<FmbxSuiteExt> wrExt = new LambdaQueryWrapper<FmbxSuiteExt>()
                .eq(FmbxSuiteExt::getSuiteId, suiteId)
                .select(FmbxSuiteExt::getBuyNote)
                ;
        final FmbxSuiteExt buyNoteDB = suiteExtService.getOne(wrExt);
        return buyNoteDB;
    }

    public FmbxSuiteExt querySuiteInteriorNote(Integer suiteId) {
        Wrapper<FmbxSuiteExt> wrExt = new LambdaQueryWrapper<FmbxSuiteExt>()
                .eq(FmbxSuiteExt::getSuiteId, suiteId)
                .select(FmbxSuiteExt::getInteriorNote)
                ;
        final FmbxSuiteExt InteriorNoteDB = suiteExtService.getOne(wrExt);
        return InteriorNoteDB;
    }

    public void updateSkuSellNum(ArrayList<SkuSellNumUpdate> skuSellNumUpdates, FmbOrderInfo orderInfo) {

        for (SkuSellNumUpdate s : skuSellNumUpdates) {
            getOrderServiceFromGoodsType(s.getGoodSType()).updateSkuSellNum(s ,orderInfo) ;
        }

    }

    /**
     * 构造返回结果
     * @param resultMap
     * @param input
     */
    public void buildOutInfo(HashMap<String, Object> resultMap, ReqGenOrder input) {
        resultMap.put("ordersn",input.getOrderSysInfo().getFmbOrderInfo().getOrderSn() ) ;
        resultMap.put("paySuccess",input.getOrderSysInfo().isOrderDirectSuccess()?1:0) ;

    }

    /**
     * 从这里开始因为要锁定用户的权益 并 生成订单.
     * 所以要整体放在一个事务里
     * @param input
     * @param nowUser
     */
    @DS(DB_master)
    @DSTransactional
    public void lockUserResourceAndCreateOrder(ReqGenOrder input, NowUser nowUser) {
        //使用优惠券 并计算优惠券 最终可以抵扣多少金额 并放入input.OrderSysInfo.couponMoney
        self.processDiscountsInfo(input, nowUser.getUserId()) ;
        //使用余额
        self.processUserBalance(input, nowUser) ;
        //生成订单前 做最后的金额计算
        self.processFinalMoneyInfo(input) ;
        //创建订单和 goods
        self.createOrderAndGoods(input, nowUser) ;
        //扣减库存
        self.subStock(input,nowUser) ;
        //把用户 下单过程中使用的各种权益做各种扣减
        self.changeUserRightToUsedStatus(input,nowUser) ;


        final ArrayList<SkuSellNumUpdate> skuSellNumUpdates = new ArrayList<>();
        final FmbOrderInfo orderInfo = input.getOrderSysInfo().getFmbOrderInfo();
        //存储交易快照
        self.saveSnap(input, orderInfo, skuSellNumUpdates);

        //存储 orderAction
        self.saveOrderInfoAction(orderInfo, "用户下单");



    }

    /**
     * 当创建订单和扣减权益完成
     * 做收尾工作
     * @param input
     */
    public void doFinalJobWhenCreateOrderOver(ReqGenOrder input) {



    }

    /**
     * 把用户 下单过程中使用的各种权益做各种扣减
     *
     * @param input
     * @param nowUser
     */
    public void changeUserRightToUsedStatus(ReqGenOrder input, NowUser nowUser) {

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();


        //扣减优惠券
        if (orderSysInfo.isUseCoupon()) {

            self.useCoupon(nowUser, orderSysInfo);

        }

        //扣减用户  余额
        if(orderSysInfo.isUseBalance()){

            self.useBalance(nowUser, orderSysInfo);

        }


        if (orderSysInfo.isUseHotelCode()){

            self.useHotelCode(input, nowUser, orderSysInfo);

        }

    }

    /**
     * 修改房券状态
     * 扣减次数
     * @param input
     * @param nowUser
     * @param orderSysInfo
     */
    public void useHotelCode(ReqGenOrder input, NowUser nowUser, OrderSysInfo orderSysInfo) {

        //增加酒店预约单
        FmbHotelReserveOrderRecord orderDB = new FmbHotelReserveOrderRecord();
        orderDB.setGeneralOrderSn(orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn());
        orderDB.setHotelOrderSn(orderSysInfo.getFmbOrderInfo().getOrderSn());
        orderDB.setUid(nowUser.getUserId());
        orderDB.setMobile(input.getMobile());
        orderDB.setReceiver("");
        FmbReserveHotelCodes hotelCode = orderSysInfo.getHotelCode();
        orderDB.setCode(hotelCode.getCode());
        orderDB.setSettlePrice(orderSysInfo.getReserveCodeFromOrderGoods().getSettlePrice());
        orderDB.setGeneralAid(orderSysInfo.getReserveCodeFromOrderInfo().getAid());
        orderDB.setHotelAid(orderSysInfo.getFmbOrderInfo().getAid());
        orderDB.setGeneralTicketId(orderSysInfo.getReserveCodeFromOrderGoods().getGoodsId());
        orderDB.setHotelTicketId(orderSysInfo.getSkuMains().get(0).getSkuId());
        orderDB.setPackageId(0);
        final Integer skuNumber = input.getReqGenSkus().get(0).getSkuNumber();
        orderDB.setGoodsNumber(skuNumber);
        orderDB.setReserveNum(orderSysInfo.getReserveTotalNightRoom());
        orderDB.setUseNum(orderSysInfo.getReserveTotalNightRoom());
        orderDB.setShopUserId(orderSysInfo.getSkuMains().get(0).getBpId());
        final List<HotelSkuDatePriceDomain> priceList = orderSysInfo.getSkuDetails().get(0).getPriceList();
        orderDB.setPlayTime(FmbDateUtil.dateToLocalDate( input.getReqGenSkus().get(0).getCheckInDate()));
        orderDB.setLeaveTime( FmbDateUtil.dateToLocalDate( input.getReqGenSkus().get(0).getCheckOutDate()));
        orderDB.setHouseType("");
        orderDB.setBedType("");
        orderDB.setAdminUid(0);
        orderDB.setConfirmSn("");
        orderDB.setOperName("");
        orderDB.setStatus(1);
        final LocalDateTime now = LocalDateTime.now();

        orderDB.setCtime(now);
        orderDB.setCancelTime(DEFAULT_DATETIME);
        orderDB.setOperType(0);
        orderDB.setSmsType(0);

        hotelReserveOrderRecordService.save(orderDB) ;


        //扣减次数

        boolean updateCodeResult = false ;
        for (int i = 0; i < 3; i++) {
            int aimNum = hotelCode.getNumber() - orderSysInfo.getReserveTotalNightRoom();

            if(aimNum<0){
                throw new BadLogicException("房券使用异常[2154]");
            }

            Wrapper<FmbReserveHotelCodes> upWr2 = new LambdaUpdateWrapper<FmbReserveHotelCodes>()

                    .eq(FmbReserveHotelCodes::getCodeId, hotelCode.getCodeId())
                    .eq(FmbReserveHotelCodes::getNumber, hotelCode.getNumber())
                    .set(FmbReserveHotelCodes::getNumber, aimNum)
                    .set(aimNum == 0, FmbReserveHotelCodes::getStatus, 3)
                    .set(FmbReserveHotelCodes::getUtime, now)
                    .set(FmbReserveHotelCodes::getUseTime, now);
            updateCodeResult = hotelCodesService.update(upWr2);

            if (updateCodeResult) {
                break;
            } else {
                hotelCode = self.queryHotelCode(hotelCode.getCode());
            }
        }

        if (!updateCodeResult) {
            throw new BadLogicException("房券使用异常[2156]");
        }

        logger.info("---before queryHotelCodeIsFinish {}" ,orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn());
        //如果房券用完了 就更新 related 表的 结束标记
        if (queryHotelCodeIsFinish(orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn())) {
            Wrapper<FmbOrderRelatedData> wrRelatedUp = new LambdaUpdateWrapper<FmbOrderRelatedData>()
                    .eq(FmbOrderRelatedData::getOrderSn,orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn())
                    .set(FmbOrderRelatedData::getReserveEndStatus,1)
                    ;
            relatedDataService.update(wrRelatedUp) ;
        }


    }

    /**
     * 修改余额
     * @param nowUser
     * @param orderSysInfo
     */
    public void useBalance(NowUser nowUser, OrderSysInfo orderSysInfo) {
        final LocalDateTime now = LocalDateTime.now();
        Wrapper<FmbUserExtinfo> wr = new LambdaUpdateWrapper<FmbUserExtinfo>()

                .eq(FmbUserExtinfo::getId, orderSysInfo.getUserBalance().getId())
                .set(FmbUserExtinfo::getMoney, orderSysInfo.getUserBalance().getMoney().subtract(orderSysInfo.getActUserBalance()))
                .set(FmbUserExtinfo::getUtime, now)
                ;
        if (!userExtinfoService.update(wr)) {
            throw new BadLogicException("余额使用失败[1614]");
        }

        FmbUserCashLog dbCashLog = new FmbUserCashLog();
        dbCashLog.setUid(nowUser.getUserId());
        dbCashLog.setOrderSn(orderSysInfo.getFmbOrderInfo().getOrderSn());
        dbCashLog.setMoney(orderSysInfo.getActUserBalance().multiply(new BigDecimal("-1")));
        dbCashLog.setOpenid("");
        dbCashLog.setSourceType(51);
        dbCashLog.setDirectionType(2);
        dbCashLog.setReturnIds("");
        dbCashLog.setNumber(0);
        dbCashLog.setStatus(2);
        dbCashLog.setValidTime(DEFAULT_DATETIME);
        dbCashLog.setCtime(now);
        dbCashLog.setUtime(DEFAULT_DATETIME);
        dbCashLog.setAdminUid(0);
        dbCashLog.setNote("订单号"+ orderSysInfo.getFmbOrderInfo().getOrderSn()+"下单抵扣"+ orderSysInfo.getActUserBalance().doubleValue()+"元,为冻结中");

        userCashLogService.save(dbCashLog);
    }

    /**
     * 使用优惠券
     * @param nowUser
     * @param orderSysInfo
     */
    public void useCoupon(NowUser nowUser, OrderSysInfo orderSysInfo) {
        Wrapper<FmbUserCoupon> wrap = new LambdaUpdateWrapper<FmbUserCoupon>()
                .eq(FmbUserCoupon::getCouponSn, orderSysInfo.getFrontCouponDomain().getCouponSn())
                .eq(FmbUserCoupon::getUseStatus,0)
                .set(FmbUserCoupon::getUseStatus,1)
                .set(FmbUserCoupon::getUseTime,LocalDateTime.now())
                .set(FmbUserCoupon::getOrderSn, orderSysInfo.getFmbOrderInfo().getOrderSn())
                ;
        final boolean update = couponService.update(wrap);

        if (!update) {
            throw new BadLogicException("优惠券使用失败[1612]");
        }

        FmbCouponLog couponLog = new FmbCouponLog();
        couponLog.setCouponSn(orderSysInfo.getFrontCouponDomain().getCouponSn());
        couponLog.setLogType("order_used");
        couponLog.setAdminUid(nowUser.getUserId());
        couponLog.setLogNote("下单绑定");
        couponLog.setLogTime(LocalDateTime.now());
        couponLogService.save(couponLog) ;
    }

    /**
     * //生成订单前 做最后的金额计算
     * @param input
     */
    public void processFinalMoneyInfo(ReqGenOrder input) {
        input.getOrderSysInfo().buildMoneyInfoBeforeCreateOrder() ;
    }

    /**
     * 处理用户使用余额
     * @param input
     * @param nowUser
     */
    public void processUserBalance(ReqGenOrder input, NowUser nowUser) {
        //使用余额
        if ( input.shouldUseBalance()) {

            final int userId = nowUser.getUserId();
            final FmbUserExtinfo userBalance = self.queryUserBalance(userId);
            input.getOrderSysInfo().setUserBalance(userBalance);

            //要使用余额 但账户里却没有
            if (userBalance.getMoney().compareTo(BigDecimal.ZERO)!=1) {
                throw new BadLogicException("余额异常[1540]");
            }

        }
    }

    public FmbUserExtinfo queryUserBalance(int userId) {
        Wrapper<FmbUserExtinfo> wr = new LambdaQueryWrapper<FmbUserExtinfo>()
                .eq(FmbUserExtinfo::getUid, userId)
                ;

        final FmbUserExtinfo userBalance = userExtinfoService.getOne(wr);
        return userBalance;
    }

    /**
     * 处理各种优惠
     * @param input
     * @param userId
     */
    public void processDiscountsInfo(ReqGenOrder input, int userId) {
        if ( input.shouldUseCoupon() ) {

            if (!self.couponAidCheck(input.getXaid())) {
                throw new BadLogicException(input.getXaid()+"不能使用优惠券[1578]");
            }

//            final String couponSn = input.getCouponInfo().get(0);
            HashMap<String, Object> par = new HashMap<>();
            par.put("uid", userId);
            par.put("coupon_sn", input.getCouponSn());
            List<FrontCouponDomain> lists = frontSkuDao.selectFrontCoupon(par);
            if (lists.isEmpty()) {
                throw new BadLogicException("优惠券状态异常[1501]");
            }
            final FrontCouponDomain frontCouponDomain = lists.get(0);

            final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
            final FmbxActivity activity = orderSysInfo.getActivity();

            self.couponCoreCheck(input, activity, frontCouponDomain, input.getXaid(), orderSysInfo.getGoodsPriceAll(),orderSysInfo);

            //把优惠券信息放入 orderSysInfo
            orderSysInfo.setFrontCouponDomain(frontCouponDomain);
            orderSysInfo.setUseCoupon(true);

        }
    }

    @DS(DB_slave)
    public boolean couponCategoryCheck(String limitCate, Integer categoryOfActivity) {

        if (StringUtils.isNotBlank(limitCate)) {

            //反序列化出分类信息
            final Mixed unserialize = Pherialize.unserialize(limitCate);
            final MixedArray array = (MixedArray) unserialize.getValue();
            final List<Integer> collect = array.values().stream().map(o -> Integer.parseInt( o.toString())).collect(Collectors.toList());

            List<FmbGoodsCategory> fmbGoodsCategories = new ArrayList<>() ;
            if (!collect.isEmpty()) {
                fmbGoodsCategories = suiteDao.queryCategoryInfo(collect);
            }

            if (!fmbGoodsCategories.isEmpty()) {
                if (!fmbGoodsCategories.stream().map(c->c.getCateId().toString()).collect(Collectors.toSet()).contains(categoryOfActivity.toString())) {
                    return false ;
                }
            }
        }
        return true ;
    }


    /**
     * 返回6位 随机数字字符串
     * @return
     */
    public String createRandomCode(){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            Long aLong = jedis.incrBy(FmbConstants.REDIS_RANDOM, RandomUtils.nextInt(1001, 9999));
            aLong = 100000 +  aLong%900000L ;
            return  aLong.toString() ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return StringUtils.leftPad( new StringBuffer().append( System.currentTimeMillis()%900000 ).reverse().toString() ,6,"0");
    }

    /**
     * 创建订单并扣减库存
     * @param input
     * @param nowUser
     */
    public void createOrderAndGoods(ReqGenOrder input, NowUser nowUser) {

        final String paySn = SnowflakeIdWorker.getInstance().nextId() + "";

        self.savePayOrderTable(input, paySn);

        FmbOrderInfo fmbOrderInfo = self.saveOrderInfo(nowUser, paySn,input);
        if (fmbOrderInfo == null) {
            logger.error("genOrderError 查询fmb_pay_orders paySn={} ,可查看用户的请求参数",paySn);
            throw new BadLogicException("生成订单异常[1301]");
        }
        self.saveOrderGoods(input,nowUser,fmbOrderInfo) ;
        input.getOrderSysInfo().setFmbOrderInfo(fmbOrderInfo);

    }

    /**
     * 扣库存
     * @param input
     * @param nowUser
     */
    public void subStock(ReqGenOrder input, NowUser nowUser) {

        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).subStock(input,nowUser,skuDetail) ;
        }
    }

    /**
     * 保存goods
     * @param input
     * @param nowUser
     * @param orderInfo
     */
    public void saveOrderGoods(ReqGenOrder input, NowUser nowUser, FmbOrderInfo orderInfo) {
        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).saveOrderGoods(nowUser, orderInfo, skuDetail,input) ;
        }
    }

    public void savePayOrderTable(ReqGenOrder input, String paySn) {



        FmbPayOrders pay = new FmbPayOrders();

        pay.setPaySn(paySn);
        pay.setMoneyOrderSn("");
        pay.setTradeNo("");
        pay.setRequestIp(MDC.get(FmbConstants.USER_IP));
        pay.setReturnIp("");
        pay.setNotifyIp("");
        pay.setRequestText(JSONUtil.toJsonStr(input));
        pay.setResponseText("");
        pay.setPayType("");
        pay.setReferer("");
        pay.setIsSuccess(0);
        pay.setCtime(LocalDateTime.now());
        payOrdersService.save(pay);
    }


    public FmbOrderInfo saveOrderInfo(NowUser nowUser, String paySn, ReqGenOrder input) {
        String orderSn = createOrderSn();

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();

        final FmbxActivity activity = orderSysInfo.getActivity();

        FmbOrderInfo order = new FmbOrderInfo();
        order.setOrderSn(orderSn);
        order.setChangeToOrder("");
        order.setBeforeOrderSn("");
        order.setOrderName( activity.getTitle());
        order.setPaySn("");
        order.setMyPaySn(paySn);
        order.setThirdSn("");
        order.setBuyerEmail("");
        order.setReceiverEmail("");
        order.setUid(nowUser.getUserInfo().getUid());
        order.setMycpsId(0);

        order.setShippingStatus(0);
        order.setTraceStatus(0);
        order.setPayStatus(0);
        order.setConfirmStatus(0);
        order.setConfirmRecheckStatus(0);
        order.setOccupyStatus(0);
        order.setPayClear(0);
        order.setMycpsSettleStatus(0);

        order.setAid(activity.getXaid());
        order.setOrderCityId(activity.getCityId());
        order.setUserCityId(input.getSiteId()==null?1:input.getSiteId());
        order.setShopUserId(activity.getBpId());
        order.setShopWarehouseId(0);
        order.setGoodsGroupId(0);

        order.setActivityType(1);
        order.setShapeType(1);

        //使用房券的单子 OrderType 标记为2
        if (orderSysInfo.isUseHotelCode()) {
            order.setOrderType(2);
        }else {
            order.setOrderType(1);
        }


        order.setChangeType(0);


        order.setCountry(1);
        order.setsDistrictId(0);
        order.setProvince(0);
        order.setCity(0);
        order.setArea(0);
        order.setStreet(0);
        order.setAddress("");
        order.setZipcode("");
        order.setTel("");
        order.setMobile(input.getMobile());
        order.setEmail("");
        order.setWeixin("");
        order.setPostscript(input.getNote()==null?"":StringUtils.left(input.getNote(),512));

        HashMap<String, Object> extInfo = new HashMap<>();

        order.setReceiver("");
        //酒店订单
        if (orderSysInfo.isHaveHotelDateSku()) {
            final Optional<SkuDetail> first = orderSysInfo.getSkuDetails().stream().filter(d -> d.getRoomSku() != null).findFirst();
            if (first.isPresent()) {
                extInfo.put("hotelUserName",first.get().getReqGenSku().getHotelUserName()) ;
                order.setReceiver(first.get().getReqGenSku().getHotelUserName().stream().collect(Collectors.joining(",")));
            }
            extInfo.put("hotelDate",1) ;

            order.setGoodsType(GOODS_TYPE_6);


        }
        //购买房券
        if (orderSysInfo.isHaveHotelReserveSku()) {
            extInfo.put("buyHotelCode",1) ;

            final Optional<SkuDetail> first = orderSysInfo.getSkuDetails().stream().filter(d -> d.getReserveSku() != null).findFirst();
            if (first.isPresent()) {
                extInfo.put("hotelUserName",first.get().getReqGenSku().getHotelUserName()) ;
                order.setReceiver(first.get().getReqGenSku().getHotelUserName().stream().collect(Collectors.joining(",")));

                extInfo.put("hotelReserveSplit",first.get().getSuite().getHotelReserveSplit()) ;
//                first.get().getReserveSku().

            }

            order.setGoodsType(GOODS_TYPE_7);
        }
        //使用房券
        if (orderSysInfo.isUseHotelCode()) {
            extInfo.put("useHotelCode",1) ;
            extInfo.put("hotelCode",orderSysInfo.getHotelCode().getCode()) ;
        }


        order.setExtInfo(JSONUtil.toJsonStr(extInfo));
        order.setCertInfo("");
        order.setInvType(0);
        order.setInvPayee("");
        order.setInvContent("");
        order.setAdultNum(0);
        order.setChildNum(0);
        order.setMoney(orderSysInfo.getGoodsPriceAll());
        order.setShippingFee(orderSysInfo.getShippingFee());
        order.setShippingMethod(0);
        order.setPaySource("");
        order.setPayNote("");
        order.setEditorNote("");
        order.setReferer(input.orderType());
        order.setOrderReferer(1);

        if (input.checkFMB_APP()) {
            order.setAppname("fmb");
        }else {
            order.setAppname("");
        }
        order.setCreateTime(LocalDateTime.now());
        order.setConfirmTime(DEFAULT_DATETIME);
        order.setPayTime(DEFAULT_DATETIME);
        order.setConfirmUpdateTime(DEFAULT_DATETIME);
        order.setShippingTime(DEFAULT_DATETIME);
        order.setPlayTime(DEFAULT_DATETIME);
        order.setUseCoupon(orderSysInfo.isUseCoupon()?1:0);
        order.setCouponMoney(orderSysInfo.getCouponMoney().intValue());
        order.setCulturalCouponMoney(0);
        order.setCashMoney(orderSysInfo.getActUserBalance());
        if (orderSysInfo.isRateCoupon()) {
            order.setDiscountRate(orderSysInfo.getFrontCouponDomain().getDiscountRate());
            order.setCouponMoney(0);
        }else {
            order.setDiscountRate(0);
        }

        order.setAppSubMoney(0);
        //这里是用户真正要支付的金额
        final BigDecimal goodsPriceAllFinal = orderSysInfo.getGoodsPriceAllFinal();
        order.setRealpayMoney(goodsPriceAllFinal);

        //如果用户需要支付0元 就直接订单交易成功了
        if (goodsPriceAllFinal.compareTo(BigDecimal.ZERO)==0) {
            order.setOrderStatus(5);
            input.getOrderSysInfo().setOrderDirectSuccess(true);
        }else {
            order.setOrderStatus(0);
        }

        order.setCpsName("");
        if (StringUtils.isNotBlank(input.getCpssrc())) {
            order.setCpsName(input.getCpssrc());
        }

        order.setCpsCookie("");
        order.setVersion(input.getVersion());
        order.setIsDelete(0);

        //防止订单号重复
        for (int i = 0; i < 5; i++) {
            try {
                    boolean save = orderInfoService.save(order);
                    if (save) {
                        return order ;
                    }
            }catch (Exception ex){

                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);

                if (!ex.getMessage().contains("Duplicate entry")) {
                    return null ;
                }
            }
            order.setOrderSn(createOrderSn());
        }

        return null;
    }

    public String createOrderSn() {
        return DateTime.now().toString(FULL_FORMAT) + createRandomCode();
    }

    public void sumPrice2OrderSysInfo(ReqGenOrder input, NowUser nowUser) {

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            orderSysInfo.setGoodsPriceAll(orderSysInfo.getGoodsPriceAll().add(skuDetail.getGoodsPriceAll()));
            orderSysInfo.setSettlePriceAll(orderSysInfo.getSettlePriceAll().add(skuDetail.getSettlePriceAll()));
        }

    }

    /**
     * 判断是否可以下单
     *
     * @param input
     * @param nowUser
     */
    @DS(FmbConstants.DB_slave)
    public void checkCanGenOrder(ReqGenOrder input, NowUser nowUser) {

        if (nowUser == null) {
            throw new BadLogicException("需要登录后操作[100]");
        }
        if (input.getReqGenSkus()==null || input.getReqGenSkus().isEmpty()) {
            throw new BadLogicException("sku信息非法[80]");
        }
        if (  !isMobile(input.getMobile())) {
            throw new BadLogicException(input.getMobile()+",手机号码错误[82]");
        }

        final Map<String, List<ReqGenSku>> reqSkuMaps =
                input.getReqGenSkus().stream().collect(Collectors.groupingBy(x->x.getSkuId().toString()));

        //请求的sku信息 按理说是 按skuid 分组是 唯一的
        //如果不唯一说明有问题
        if (reqSkuMaps.size()!=input.getReqGenSkus().size()) {
            throw new BadLogicException("sku信息非法[84]");
        }

        Wrapper<FmbShieldUsers> wr = new LambdaQueryWrapper<FmbShieldUsers>()
                .eq(FmbShieldUsers::getUid, nowUser.getUserInfo().getUid());
        //用户屏蔽查询
        if (shieldUsersService.count(wr) > 0) {
            throw new BadLogicException("用户状态异常[110]");
        }

        final FmbxActivity activity = self.queryFmbxActivity(input.getXaid());
        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
        orderSysInfo.setActivity(activity);

        if (activity == null) {
            throw new BadLogicException("产品信息异常[130]");
        }

        if (input.shouldCheckActivity()) {
            //活动层面校验
            self.checkActivity(activity,input) ;
        }

        //房券基础校验
        if(input.useHotelReserveCode()){
            self.checkHotelReserveCode(input.getHotelReserveCode(),nowUser.getUserId(),orderSysInfo,"checkCanGenOrder",input.getReqGenSkus()) ;
        }


        self.buildSkuDetails(input, reqSkuMaps);

        for (SkuDetail skuDetail : orderSysInfo.getSkuDetails()) {

            if (skuDetail.getReqGenSku().getSkuNumber() == null || skuDetail.getReqGenSku().getSkuNumber() < 1) {
                throw new BadLogicException("skuid=" + skuDetail.getReqGenSku().getSkuId() + "购买数量异常.[163]");
            }
            skuDetail.setSkuNumber(skuDetail.getReqGenSku().getSkuNumber());

            self.baseCheckOfEachSkuType(skuDetail,nowUser,input) ;
            self.buildSkuSinglePriceOfEachType(skuDetail,nowUser,input) ;
            //构造 订单自动取消秒数
            orderSysInfo.setCancelSecond(getOrderService(skuDetail.getSkuType()).getCancelSecond(skuDetail)) ;

        }


        //房券数量校验  这里要使用到  skuDetail.getPriceList()
        // 所以需要放在   buildSkuSinglePriceOfEachType 后面
        if (input.useHotelReserveCode()) {
            final SkuDetail skuDetail = input.getOrderSysInfo().getSkuDetails().get(0);

            final int nightNum = skuDetail.getPriceList().size();
            //想要预定的总总间数    间数*晚数
            final int totalNightRoom = skuDetail.getSkuNumber() * nightNum;
            //该房券拥有的间夜数
            final Integer cardHaveNumNow = input.getOrderSysInfo().getHotelCode().getNumber();

            if (totalNightRoom>cardHaveNumNow) {
                throw new BadLogicException("房券数量不足[2109]");
            }

            input.getOrderSysInfo().setReserveTotalNightRoom(totalNightRoom);


            //预约星期限制 和 日期限制
            final FmbxHotelReserveSku reserveCodeSourceSku = orderOfHotelReserveService.querySkuReserve(input.getOrderSysInfo().getHotelCode().getTicketId());
            final String invalidReserveWeekDays = reserveCodeSourceSku.getInvalidReserveWeekDays();
            final String invalidReserveDate = reserveCodeSourceSku.getInvalidReserveDate();

            //限制星期几 不能约
            Set<Integer> collectWeek = new HashSet<>() ;
            //限制 哪些天不能约
            Set<String> collectDate = new HashSet<>() ;
            if (StringUtils.isNotBlank(invalidReserveWeekDays)) {
                collectWeek = Stream.of(invalidReserveWeekDays.split(",")).map(x -> Integer.parseInt(x)).collect(Collectors.toSet());
            }
            if (StringUtils.isNotBlank(invalidReserveDate)) {
                collectDate = Stream.of(invalidReserveDate.split(",")).map(x -> x.toString().trim()).collect(Collectors.toSet());
            }

            //做校验
            for (HotelSkuDatePriceDomain moneyItem : input.getOrderSysInfo().getSkuDetails().get(0).getPriceList()) {

                String d  =FmbDateUtil.toDate10Str(moneyItem.getDatecol()) ;

                if (collectWeek.contains(8) &&  reserveDateIsLimit(d, collectDate)) {
                    throw new BadLogicException("库存不足[3490]");
                }
                if (reserveWeekIsLimit( DateTime.parse(d).getDayOfWeek(),collectWeek)) {
                    final int dayOfWeek = DateTime.parse(d).getDayOfWeek();
//                    throw new BadLogicException(d+"是星期"+(( dayOfWeek !=7 )?dayOfWeek+"":"日")+"不能预约[3491]");
                    throw new BadLogicException("库存不足[3491]");
                }

            }
        }

    }



    /**
     * 校验房券
     * @param code
     * @param uid
     * @param orderSysInfo
     * @param from
     * @param reqGenSkus
     */
    public void checkHotelReserveCode(String code, int uid, OrderSysInfo orderSysInfo, String from, List<ReqGenSku> reqGenSkus) {

        logger.info("code=[{}] from=[{}]",code,from);

        if (StringUtils.isNotBlank(code)) {
            if( !code.startsWith("H") || !code.substring(1).matches("\\d{8}")){
                throw new BadLogicException("房券码异常[2302]");
            }
        }


        final FmbReserveHotelCodes codeDB = self.queryHotelCode(code);
        if (codeDB == null) {
            throw new BadLogicException("房券状态异常[2312]");
        }

        //changeOrder 改签时不交易 房券 的状态
        if (   !"changeOrder".equals(from) && (   codeDB.getStatus()!=1 || codeDB.getNumber()<1  ) ) {
            throw new BadLogicException("房券状态异常[2315]");
        }

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,codeDB.getOrderSn())
                .select(FmbOrderInfo::getOrderId,FmbOrderInfo::getOrderSn,FmbOrderInfo::getUid,FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getMoney,
                        FmbOrderInfo::getAid
                        )
                ;
        FmbOrderInfo reserveCodeFromOrderInfo = orderInfoService.getOne(wrOrder);




        if (reqGenSkus != null) {


            Wrapper<FmbxHotelReserveSku> wrReserveSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSkuId,codeDB.getTicketId())
                    .select(FmbxHotelReserveSku::getReserveValidityStartTime,FmbxHotelReserveSku::getReserveValidityEndTime
                            ,FmbxHotelReserveSku::getReserveChooseStartTime,FmbxHotelReserveSku::getReserveChooseEndTime
                    )
                    ;
            final FmbxHotelReserveSku nowReserveSkuInfo = reserveSkuService.getOne(wrReserveSku);

//            logger.info("nowReserveSkuInfo={}", JSON.toJSONString( nowReserveSkuInfo));
            final LocalDateTime now = LocalDateTime.now();
            if (nowReserveSkuInfo.getReserveValidityStartTime()!=null  && nowReserveSkuInfo.getReserveValidityEndTime()!=null &&
                    !( now.isAfter(nowReserveSkuInfo.getReserveValidityStartTime()) && now.isBefore(nowReserveSkuInfo.getReserveValidityEndTime())   )   ) {
                throw new BadLogicException("房券预约有效期异常[6710]");
            }


            final ReqGenSku reqGenSku = reqGenSkus.get(0);

            final String checkInDate = reqGenSku.getCheckInDate();
            final String checkOutDate = reqGenSku.getCheckOutDate();
            LocalDate localDateIn = FmbDateUtil.dateToLocalDate(checkInDate);
            final LocalDate localDateOut = FmbDateUtil.dateToLocalDate(checkOutDate);

            if (localDateIn==null  || localDateOut==null) {
                throw new BadLogicException("入住日期错误[7712]");
            }

            if (!localDateIn.isBefore(localDateOut)) {
                throw new BadLogicException("入住日期错误[7713]");
            }

            final LocalDateTime reserveChooseStartTime = nowReserveSkuInfo.getReserveChooseStartTime();
            final LocalDateTime reserveChooseEndTime = nowReserveSkuInfo.getReserveChooseEndTime();

            while (localDateIn.isBefore(localDateOut)){
                if (localDateIn.isBefore(reserveChooseStartTime.toLocalDate()) || localDateIn.isAfter(reserveChooseEndTime.toLocalDate())) {
                    throw new BadLogicException("入住日期无库存[7714]");
                }
                localDateIn = localDateIn.plus(1, ChronoUnit.DAYS);
            }

        }



        if (reserveCodeFromOrderInfo == null  ) {
            throw new BadLogicException("房券状态异常[2317]");
        }
        if ( reserveCodeFromOrderInfo.getUid().intValue()!=uid) {
            throw new BadLogicException("房券用户状态异常[2339]");
        }

        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn,codeDB.getOrderSn())
                .eq(FmbOrderGoods::getGoodsType, SKU_HOTEL_RESERVE_GOODS_TYPE)
                .eq(FmbOrderGoods::getGoodsId, codeDB.getTicketId())
                .select(
                        FmbOrderGoods::getRecId
                        , FmbOrderGoods::getGoodExt
                        , FmbOrderGoods::getGoodsId
                        , FmbOrderGoods::getGoodsNumber
                        , FmbOrderGoods::getGoodsType
                        , FmbOrderGoods::getGoodsPrice
                        , FmbOrderGoods::getSettlePrice
                        , FmbOrderGoods::getMarketPrice
                )
                ;
        FmbOrderGoods reserveCodeFromOrderGoods = orderGoodsService.getOne(goodsWr);
        if (reserveCodeFromOrderGoods == null) {
            throw new BadLogicException("房券状态异常[2319]");
        }

        orderSysInfo.setReserveCodeFromOrderInfo(reserveCodeFromOrderInfo);
        orderSysInfo.setReserveCodeFromOrderGoods(reserveCodeFromOrderGoods);

        final String goodExt = reserveCodeFromOrderGoods.getGoodExt();
        FmbxSuite suite = JSONUtil.parse(goodExt).getByPath("skuDetail.suite", FmbxSuite.class);

        orderSysInfo.setReserveSuiteSplit(suite.getHotelReserveSplit());
        //用 现有的套餐信息替换
        suite = orderOfHotelReserveService.queryFmbxSuite(suite.getSuiteId());

        orderSysInfo.setReserveCodeSourceSuite(suite);

        orderSysInfo.setHotelCode(codeDB);

        final FmbxHotelReserveSku reserveCodeSourceSku = orderOfHotelReserveService.querySkuReserve(codeDB.getTicketId());
        orderSysInfo.setReserveCodeSourceSku(reserveCodeSourceSku);


        orderSysInfo.setUseHotelCode(true);

        final Integer totalNeight = suite.getTotalNeight();
        final BigDecimal goodsPrice = reserveCodeFromOrderGoods.getGoodsPrice();

        final BigDecimal hotelCodeEachNightPay = goodsPrice.divide(new BigDecimal(totalNeight),2, RoundingMode.HALF_UP);
        orderSysInfo.setHotelCodeEachNightPay(hotelCodeEachNightPay);



    }

    public FmbReserveHotelCodes queryHotelCode(String code) {
        return queryHotelCode(code,1) ;
    }

    /**
     * 查询房券 根据 condition
     * @param condition
     * @param type 1 -根据code 查询
     *             2 -根据订单号查
     * @return
     */
    public FmbReserveHotelCodes queryHotelCode(String condition,int type) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq(1==type, FmbReserveHotelCodes::getCode, condition)
                .eq(2==type, FmbReserveHotelCodes::getOrderSn, condition)
                .select(
                        FmbReserveHotelCodes::getCodeId
                        , FmbReserveHotelCodes::getCode
                        , FmbReserveHotelCodes::getOrderSn
                        , FmbReserveHotelCodes::getNumber
                        , FmbReserveHotelCodes::getStatus
                        ,FmbReserveHotelCodes::getTicketId

                );

        final FmbReserveHotelCodes codeDB = hotelCodesService.getOne(wr);
        return codeDB;
    }


    public List<FmbReserveHotelCodes> queryHotelCodeList(String condition, int type) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq(1==type, FmbReserveHotelCodes::getCode, condition)
                .eq(2==type, FmbReserveHotelCodes::getOrderSn, condition)
                .select(
                        FmbReserveHotelCodes::getCodeId
                        , FmbReserveHotelCodes::getCode
                        , FmbReserveHotelCodes::getOrderSn
                        , FmbReserveHotelCodes::getNumber
                        , FmbReserveHotelCodes::getStatus
                        ,FmbReserveHotelCodes::getTicketId
                        , FmbReserveHotelCodes::getStatus
                );

        final List<FmbReserveHotelCodes> list = hotelCodesService.list(wr);
        return list;
    }


    /**
     * 房券订单是否有 可用房券
     * @param condition
     * @return
     */
    public boolean queryHotelCodeIsFinish(String condition) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq( FmbReserveHotelCodes::getOrderSn, condition)
                .gt(FmbReserveHotelCodes::getNumber,0)
                .eq(FmbReserveHotelCodes::getStatus,1)
                ;

        return hotelCodesService.count(wr) == 0  ;
    }




    /**
     * 把用户的输入的sku 信息 构造为 Skudetail 对象
     * 方便各种sku 分不同service 处理
     * @param input
     * @param reqSkuMaps
     */
    public void buildSkuDetails(ReqGenOrder input, Map<String, List<ReqGenSku>> reqSkuMaps) {
        //查询  基础sku信息
        final List<FmbxSku> skuMains = self.queryFmbxSkus(input.getReqGenSkus());
        input.getOrderSysInfo().getSkuMains().addAll(skuMains) ;

        //如果系统查到的 sku数量 为0  或者 用户输入sku数量对不上 就说明sku信息不正确
        if (skuMains.size()==0 || skuMains.size()!= input.getReqGenSkus().size()){
            throw new BadLogicException("sku数据异常[140]");
        }

        final Map<String, List<FmbxSku>> skuMainOfDBMap = skuMains.stream().collect(Collectors.groupingBy(x -> x.getSkuId().toString()));

        //通过这个循环 把具体的sku 信息都放入 OrderSysInfo 的 skuDetails里
        for (FmbxSku skuMain : input.getOrderSysInfo().getSkuMains()) {

           getOrderService(skuMain.getSkuType().intValue()).buildSkuDetail(input, reqSkuMaps, skuMainOfDBMap, skuMain);
        }


        //通过 房券预约走这个检查
        if (input.useHotelReserveCode() ) {
            //酒店预约下单只能 传入酒店日历房的skuid
            for (FmbxSku skuMain : input.getOrderSysInfo().getSkuMains()) {
                if (FmbConstants.SKU_HOTEL_DATE!=  skuMain.getSkuType().intValue()) {
                    throw new BadLogicException("酒店预约下单sku数据异常[2150]");
                }
            }

            //只能传一个 酒店日历房的 sku id
            if (input.getOrderSysInfo().getSkuMains().size()!=1) {
                throw new BadLogicException("酒店预约下单sku数据异常[2152]");
            }

        }


    }

    public List<FmbxSku> queryFmbxSkus(List<ReqGenSku> input) {
        Wrapper<FmbxSku> wrSku = new LambdaQueryWrapper<FmbxSku>()
                .in(FmbxSku::getSkuId, input.stream().map(x->x.getSkuId()).collect(Collectors.toList()))
                .select(FmbxSku::getSkuId,FmbxSku::getSkuName,FmbxSku::getXaid,FmbxSku::getBpsId,FmbxSku::getBpId,FmbxSku::getSkuType)
                ;
        final List<FmbxSku> skuMains = skuService.list(wrSku) ;
        return skuMains;
    }
    public FmbxSku queryFmbxSkus(Integer skuid) {
        Wrapper<FmbxSku> wrSku = new LambdaQueryWrapper<FmbxSku>()
                .eq(FmbxSku::getSkuId,skuid)
                .select(FmbxSku::getSkuId,FmbxSku::getSkuName,FmbxSku::getXaid,FmbxSku::getBpsId,FmbxSku::getBpId,FmbxSku::getSkuType)
                ;
        final FmbxSku skuMains = skuService.getOne(wrSku);
        return skuMains;
    }

    /**
     * 判断是否和合法的手机号
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        return StringUtils.isNotBlank(mobile) &&  Validator.isMatchRegex(MOBILE,mobile);
    }

    /**
     * 活动层面校验
     * @param activity
     * @param input
     */
    @DS(FmbConstants.DB_slave)
    public void checkActivity(FmbxActivity activity, ReqGenOrder input) {

        final String[] platIds = activity.getPlatId().split(",");
        final HashSet<String> setPlatIds = new HashSet<>();
        for (String platId : platIds) {
            setPlatIds.add(platId) ;
        }

        //如果不是全平台  售卖平台校验
//        if (!setPlatIds.contains("0")) {
////            显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
//            if (input.checkH5() && !setPlatIds.contains("3")) {
//                throw new BadLogicException("当前平台购买受限[901]");
//            }
//            else if (input.checkWxApp() && !setPlatIds.contains("10")) {
//                throw new BadLogicException("当前平台购买受限[904]");
//            }
//            else if (input.checkH5() && ( !setPlatIds.contains("1")  && !setPlatIds.contains("2") ) ) {
//                throw new BadLogicException("当前平台购买受限[907]");
//            }
//        }


        //活动删除状态校验
//          `flag_delete` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '删除:0-正常,1-被删除',
        if (1== activity.getFlagDelete().intValue()) {
            throw new BadLogicException("aid="+activity.getXaid()+",当前活动不能购买[910]");
        }


        //活动上下架状态校验
//          `flag_publish` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '上下架状态:0-待发布,1-已上架,2-已下架',
        if (1!= activity.getFlagPublish().intValue()) {
            throw new BadLogicException("aid="+activity.getXaid()+",当前活动发布状态异常[914]");
        }

    }



    @DS(FmbConstants.DB_slave)
    public FmbxActivity queryFmbxActivity(Integer aid) {
        Wrapper<FmbxActivity> aWr = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid, aid)
                .select(FmbxActivity::getXaid,FmbxActivity::getBpsId,FmbxActivity::getBpId,FmbxActivity::getBpcId,
                        FmbxActivity::getCategoryId,FmbxActivity::getTicketType,FmbxActivity::getTitle,FmbxActivity::getPlatId,

                        FmbxActivity::getCityId,FmbxActivity::getSalesCityIds,FmbxActivity::getBusinessType,FmbxActivity::getIsConfirm,
                        FmbxActivity::getFlagDelete,FmbxActivity::getFlagHaveValidSku,FmbxActivity::getFlagPublish)
                ;
        //查出活动信息
        final FmbxActivity activity = activityService.getOne(aWr);
        return activity;
    }

    /**
     * 做基础的sku 校验
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    public void baseCheckOfEachSkuType(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {
        getOrderService(skuDetail.getSkuType()).baseCheck(skuDetail,nowUser,input) ;
    }

    /**
     * 通用的价格构造方法
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    public void buildSkuSinglePriceOfEachType(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {
        getOrderService(skuDetail.getSkuType()).buildPrice(skuDetail,input);
    }


    /**
     * 用户成功支付时 给用户分发相关权益
     * @param orderSn
     * @param orderSuccessPayDomain  支付的信息
     *                               如果为空 目前是下单直接就成功的
     */
    @DSTransactional
    @DS(DB_master)
    public void orderSuccessPay(String orderSn,OrderSuccessPayDomain orderSuccessPayDomain){

//        logger.info("orderSuccessPay orderSn={} orderSuccessPayDomain={}",orderSn,JSON.toJSONString(orderSuccessPayDomain));
        Wrapper<FmbOrderInfo> wrOrderInfo = buildFmbOrderInfoWrapper(orderSn) ;
        final FmbOrderInfo orderInfoDB = orderInfoService.getOne(wrOrderInfo);

        final FmbxActivity activity = queryFmbxActivity(orderInfoDB.getAid());

        if (orderInfoDB == null) {
            throw new BadLogicException("订单不存在,order_sn"+orderSn) ;
        }

        //把订单改为交易成功
        FmbOrderInfo orderUpdateDB = new FmbOrderInfo();
        orderUpdateDB.setOrderId(orderInfoDB.getOrderId());
        orderUpdateDB.setPayTime(LocalDateTime.now());

        /**
         * 完全支付完成 标记
         *
         * 用户支付了部分 这个值保留为 false
         * 用户全额支付后 这个值变为true
         *
         */
        boolean payOver = false ;


        //当订单状态为未支付 或者  代付尾款
        // 三方支付走这里
        final int iOrderStatus = orderInfoDB.getOrderStatus().intValue();
        //当订单状态为未支付时,三方支付走这里

        //通过各种三方交易平台返回
        if (orderSuccessPayDomain != null) {
            if (0 == iOrderStatus || 7 == iOrderStatus) {

                orderUpdateDB.setPaySource(orderSuccessPayDomain.getPayType());
                final BigDecimal bigDecimalNotifyPay = new BigDecimal(orderSuccessPayDomain.getMoney());


                //正好完全支付
                if (bigDecimalNotifyPay.compareTo(BigDecimal.ZERO) > 0 &&
                        orderInfoDB.getRealpayMoney().compareTo(bigDecimalNotifyPay) == 0) {
                    payOver = true;
                    orderUpdateDB.setPayStatus(2);
                } else if (bigDecimalNotifyPay.compareTo(BigDecimal.ZERO) > 0 &&
                        orderInfoDB.getRealpayMoney().compareTo(bigDecimalNotifyPay) > 0) {

                    payOver = false;

                    orderUpdateDB.setRealpayMoney(orderInfoDB.getRealpayMoney().subtract(bigDecimalNotifyPay));

                    orderUpdateDB.setOrderStatus(7);

                    orderUpdateDB.setPayStatus(1);
                }


                orderUpdateDB.setPaySn(orderSuccessPayDomain.getPay_sn());

            }else
            {
                logger.warn("payMoneyOnBadStatus_{}_{} {}",orderSuccessPayDomain.getOrderSn(),iOrderStatus,orderSuccessPayDomain);
            }
        }else{

            //0元付 这里(可能是优惠券完全抵扣了金额,也可能B端分销....反正就是没走三方支付 )
            orderUpdateDB.setPaySource("nopay");
//            orderUpdateDB.setOrderStatus(5);
            orderUpdateDB.setPayStatus(2);

            payOver = true ;
        }



        if (payOver) {

            /**
             *      如果完全支付完成 而且订单是 待付尾款状态 需要把订单的实际支付金额改为全部支付金额
             *      譬如订单总额 10000元
             *      第一次支付8000元后 realpaymoney 改为 2000, orderstatus 改为7 待付尾款
             *      第二次支付2000元后 realpaymoney 改为 10000, orderstatus 改为8或者5
             */
            if (7 == iOrderStatus) {
                orderUpdateDB.setRealpayMoney(FmbOrderInfoUtil.oldOrderRealPayMoney(orderInfoDB));
            }

            //更新 orderStatus
            int goodsType = orderInfoDB.getGoodsType();
            int isConfirm = activity.getIsConfirm();
            if (goodsType == GOODS_TYPE_6) {
                //需要二次确认
                if (isConfirm == 0) {
                    orderUpdateDB.setOrderStatus(5);
                } else {
                    orderUpdateDB.setOrderStatus(8);
                }
            } else {
                orderUpdateDB.setOrderStatus(5);
            }

            //如果是房券订单 将二次确认状态改为 有货保留
            if (GOODS_TYPE_7 == orderInfoDB.getGoodsType().intValue()) {
                orderUpdateDB.setConfirmStatus(2);
                orderUpdateDB.setConfirmRecheckStatus(1);
            }
        }


//        logger.info("orderUpdateDB={}",JSON.toJSONString(orderUpdateDB));
        final boolean updateResult = orderInfoService.updateById(orderUpdateDB);

        //完全支付完成 而且 数据库 更新成功后
        if (updateResult && payOver ) {

            //订单支付成功后 做各种后续操作
            // 有的操作需要及时处理 就放在下面直接运行.
            // 时效性不强的 通过kafka 消息系统触发

            HashMap<String, Object> par = new HashMap<>();
            par.put("OrderId", orderInfoDB.getOrderId());
            par.put("PayStatus", orderUpdateDB.getPayStatus());
            par.put("OrderStatus", orderUpdateDB.getOrderStatus());
            par.put("ConfirmStatus", orderUpdateDB.getConfirmStatus());
            par.put("GoodsType", orderUpdateDB.getGoodsType());


            FmbKafkaProducerService.send(ORDER_SUCCESS_PAY,orderInfoDB.getOrderId()+"",par);


            if (logger.isInfoEnabled()) {
                logger.info("kafka send ORDER_SUCCESS_PAY {}",orderInfoDB.getOrderId()+"");
            }



            Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                    .eq(FmbOrderGoods::getOrderSn,orderSn)
                    .select( FmbOrderGoods::getRecId, FmbOrderGoods::getGoodsId,FmbOrderGoods::getGoodsType, FmbOrderGoods::getGoodsNumber,
                            FmbOrderGoods::getLeaveTime,FmbOrderGoods::getPlayTime,
                            FmbOrderGoods::getGoodExt)
                    ;
            final List<FmbOrderGoods> orderGoods = orderGoodsService.list(wrGoods);

            //创建用户的 权益
            for (FmbOrderGoods orderGood : orderGoods) {
                getOrderServiceFromGoodsType(orderGood.getGoodsType()).createUserAsset(orderGood,orderInfoDB) ;
            }

            //分销订单 推消息到redis php处理
            if (orderInfoDB.getMycpsId()!=null && orderInfoDB.getMycpsId()>0) {
                pushRedisMessage2PhpCpsOrderSuccessPay(orderInfoDB.getOrderSn(),orderInfoDB.getOrderId(),orderInfoDB.getMycpsId());
            }

            //如果使用了优惠券 更新优惠券状态为 已使用
            if (orderInfoDB.getUseCoupon()==1) {

                Wrapper<FmbUserCoupon> wrCou = new LambdaUpdateWrapper<FmbUserCoupon>()
                        .eq(FmbUserCoupon::getOrderSn,orderInfoDB.getOrderSn())
                        .set(FmbUserCoupon::getUseStatus,2)
                        .set(FmbUserCoupon::getUseTime,LocalDateTime.now())
                        ;
                couponService.update(wrCou) ;

            }
            //存储 orderAction
            FmbOrderInfo orderAc = new FmbOrderInfo();

            orderAc.setOrderSn(orderInfoDB.getOrderSn());
            orderAc.setOrderStatus(orderUpdateDB.getOrderStatus());
            orderAc.setShippingStatus(orderInfoDB.getShippingStatus());
            orderAc.setPayStatus(orderUpdateDB.getPayStatus());

            self.saveOrderInfoAction(orderAc, "用户支付");







        }

    }


    public void buildOrderStatus(FmbOrderInfo orderUpdateDB, Integer goodsType, Integer orderType, Integer isConfirm) {

        if (goodsType==GOODS_TYPE_6) {
            //需要二次确认
            if (isConfirm==0) {
                orderUpdateDB.setOrderStatus(5);
            }else {
                orderUpdateDB.setOrderStatus(8);
            }
        }else {
            orderUpdateDB.setOrderStatus(5);
        }


    }

    /**
     *
     * @param orderId
     * @param cancelType 取消来源
     */
    @DSTransactional
    @DS(DB_master)
    public void cancelOrder(  Integer orderId, String cancelType){

        self.cancelOrder(orderId,cancelType,1,null,null) ;
    }


    /**
     *
     * @param orderId    订单id   conditionType为1时提供
     * @param source
     *        auto_cancel  超时未支付 ,自动取
     *        front        前台用户取消
     *        fmbx_hotel_reserve_return_direct      后台直接发起酒店预约单退款
     *
     * @param conditionType   取消条件
     *                        1-订单id
     *                        2-订单号
     * @param orderSn       订单号   conditionType为2提供
     * @param input         目前 conditionType为2提供 一些其他取消的信息
     * @return     返还是否取消成功
     */
    public boolean cancelOrder(Integer orderId, String source, int conditionType, String orderSn, ReqorderDetail input){

        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(conditionType==1, FmbOrderInfo::getOrderId,orderId)
                .eq(conditionType==2, FmbOrderInfo::getOrderSn,orderSn)
                .select( FmbOrderInfo::getOrderId , FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getAid,
                        FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getUseCoupon,
                        FmbOrderInfo::getCashMoney,
                        FmbOrderInfo::getOrderStatus,
                        FmbOrderInfo::getShippingStatus,
                        FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getUid)
                ;
        final FmbOrderInfo fmbOrderInfo = orderInfoService.getOne(wrOrderInfo);

        if (fmbOrderInfo == null) {
            throw new BadLogicException("订单不存在,orderId"+orderId) ;
        }

        //230222  目前只对 未支付的操作
        //230331  增加 订单后台发起 酒店预约单退款
        if (fmbOrderInfo.getOrderStatus()==0 || ( input!=null && input.isAdminReturn()  && GOODS_TYPE_6==fmbOrderInfo.getGoodsType() && 2==fmbOrderInfo.getOrderType() )  ) {

            //返还 优惠券 余额 和房券次数
            self.cancelOrderInfo(fmbOrderInfo,source) ;

            Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                    .eq(FmbOrderGoods::getOrderSn,fmbOrderInfo.getOrderSn())
                    .select(FmbOrderGoods::getGoodsId,FmbOrderGoods::getGoodsType, FmbOrderGoods::getGoodsNumber, FmbOrderGoods::getGoodExt)
                    ;
            final List<FmbOrderGoods> orderGoods = orderGoodsService.list(wrGoods);

            //返还库存 并更新售卖数量
            for (FmbOrderGoods orderGood : orderGoods) {
                getOrderServiceFromGoodsType(orderGood.getGoodsType()).cancelUserGoodsReturnStockAndUpdateSellNum(orderGood,fmbOrderInfo) ;
            }

            //记录退款操作
            self.saveReturnOrderRecord(input, fmbOrderInfo);

        }


        return true ;

    }

    public void saveReturnOrderRecord(ReqorderDetail input, FmbOrderInfo fmbOrderInfo) {
        // FmbOrderCancel 表 添加记录
        FmbOrderCancel cancelDb = new FmbOrderCancel();

        cancelDb.setOrderSn(fmbOrderInfo.getOrderSn());

        if (input != null) {
            cancelDb.setCancelReason(input.getCancelId());
            cancelDb.setCancelDetail(input.getMess());
            cancelDb.setAdminUid(-1);

            if (input.isAdminReturn()) {
                cancelDb.setCancelReason(11);
                cancelDb.setCancelDetail(input.getMess());
                cancelDb.setAdminUid(input.getAdminUid());
            }

        }else {
            cancelDb.setCancelReason(0);
            cancelDb.setCancelDetail("系统自动取消");
            cancelDb.setAdminUid(0);
        }
        cancelDb.setCtime(LocalDateTime.now());
        orderCancelService.save(cancelDb);

        //FmbOrderActions 添加 记录
        FmbOrderActions action  = new FmbOrderActions();

        action.setOrderSn(fmbOrderInfo.getOrderSn());
        if (input != null) {
            //代表前台用户
            action.setAdminUid(-1);
            action.setActionNote( input.apiType()+ "用户取消订单");

            if (input.isAdminReturn()) {
                action.setAdminUid(input.getAdminUid());
                action.setActionNote( input.getMess());
            }

        }else {
            action.setAdminUid(0);
            action.setActionNote("系统取消订单");
        }
        action.setOrderStatus(6);
        action.setShippingStatus(fmbOrderInfo.getShippingStatus());
        action.setPayStatus(fmbOrderInfo.getPayStatus());
        action.setActionType("");

        action.setLogTime(LocalDateTime.now());

        orderActionsService.save(action) ;
    }


    /**
     * 取消订单 , 订单表的操作
     * @param fmbOrderInfo
     * @param source
     */
    public void cancelOrderInfo(FmbOrderInfo fmbOrderInfo, String source) {

        boolean closeOrder  = true ;
        if (FMBX_HOTEL_RESERVE_RETURN_NEED_PART_MONEY.equals(source)) {
            closeOrder  =false ;
        }

        if (closeOrder) {
            // 更新订单状态
            FmbOrderInfo orderUpdateDB = new FmbOrderInfo();
            orderUpdateDB.setOrderId(fmbOrderInfo.getOrderId());
            orderUpdateDB.setOrderStatus(6);
            orderInfoService.updateById(orderUpdateDB);
        }


        RedissonLockService.doLockJob(FmbConstants.USER_RESOURCE_LOCK, fmbOrderInfo.getUid() + "", 5000, 4000,
                () -> self.returnUserAssert(fmbOrderInfo, source), "系统繁忙");

    }

    /**
     * 返还 用户的优惠券和余额
     * @param fmbOrderInfo
     * @param auto_cancel
     */
    public void returnUserAssert(FmbOrderInfo fmbOrderInfo, String auto_cancel) {

        final Integer uid = fmbOrderInfo.getUid();
        //退还优惠券
        if (1== fmbOrderInfo.getUseCoupon().intValue()) {

            self.returnCoupon(fmbOrderInfo, auto_cancel, uid);

        }

        //需要给用户退余额
        if (fmbOrderInfo.getCashMoney().compareTo(BigDecimal.ZERO)==1) {

          self.returnUserBalance(fmbOrderInfo, uid);

        }

        //酒店预约
        if(GOODS_TYPE_6==fmbOrderInfo.getGoodsType().intValue() &&   2==fmbOrderInfo.getOrderType()){

          self.returnHotelCode(fmbOrderInfo.getOrderSn());

        }

    }

    /**
     * 酒店预约单 退还次数
     * @param ordersn
     */
    public void returnHotelCode(String ordersn) {
        Wrapper<FmbHotelReserveOrderRecord> wr = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                .eq(FmbHotelReserveOrderRecord::getHotelOrderSn, ordersn)
                .select(FmbHotelReserveOrderRecord::getStatus,FmbHotelReserveOrderRecord::getOrId,FmbHotelReserveOrderRecord::getReserveNum
                ,FmbHotelReserveOrderRecord::getCode,FmbHotelReserveOrderRecord::getGeneralOrderSn
                )
                ;
        final FmbHotelReserveOrderRecord orderRecordServiceOne = hotelReserveOrderRecordService.getOne(wr);
        final FmbOrderRelatedData fmbOrderRelatedData = queryFmbOrderRelatedData(orderRecordServiceOne.getGeneralOrderSn());


        //对于已经预约的 关闭预约记录 返还次数
        if (1==orderRecordServiceOne.getStatus().intValue()) {
            FmbHotelReserveOrderRecord rDBUpdate = new FmbHotelReserveOrderRecord();
            rDBUpdate.setOrId(orderRecordServiceOne.getOrId());
            rDBUpdate.setStatus(2);
            rDBUpdate.setCancelTime(LocalDateTime.now());
            hotelReserveOrderRecordService.updateById(rDBUpdate) ;

//            Wrapper<FmbReserveHotelCodes> codeWr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
//                    .eq(FmbReserveHotelCodes::getCode,orderRecordServiceOne.getCode())
//                    .eq(FmbReserveHotelCodes::getOrderSn,orderRecordServiceOne.getGeneralOrderSn())
//                    .select(FmbReserveHotelCodes::getCodeId,FmbReserveHotelCodes::getStatus,FmbReserveHotelCodes::getNumber)
//                    ;
//            final FmbReserveHotelCodes aimCodeInfo = hotelCodesService.getOne(codeWr);

            boolean returnCodeFlag =false ;

            for (int i = 0; i < 3; i++) {
                FmbReserveHotelCodes aimCodeInfo = queryHotelCode(orderRecordServiceOne.getCode()) ;
                //返还次数

                int aimNum = aimCodeInfo.getNumber() + orderRecordServiceOne.getReserveNum();

                //返回次数超过应包含 就返回
                if (aimNum > fmbOrderRelatedData.getContainTimes()) {
                    logger.error("returnHotelCode_more_than_should_have");
                    return ;
                }


                Wrapper<FmbReserveHotelCodes> upWr2 = new LambdaUpdateWrapper<FmbReserveHotelCodes>()

                        .eq(FmbReserveHotelCodes::getCodeId,aimCodeInfo.getCodeId())
                        .eq(FmbReserveHotelCodes::getNumber,aimCodeInfo.getNumber())
                        .set(FmbReserveHotelCodes::getNumber,aimNum)
                        .set(aimNum>0,FmbReserveHotelCodes::getStatus,1)
                        .set(FmbReserveHotelCodes::getUseTime,LocalDateTime.now())
                        ;
                returnCodeFlag =  hotelCodesService.update(upWr2) ;

                if (returnCodeFlag) {
                    break;
                }
            }
            if (!returnCodeFlag) {
                throw new BadLogicException("返还房券失败[2158]");
            }

            //更新related 预约状态
            final boolean hotelCodeIsFinish = queryHotelCodeIsFinish(orderRecordServiceOne.getGeneralOrderSn());

            Wrapper<FmbOrderRelatedData> wrRelatedUp = new LambdaUpdateWrapper<FmbOrderRelatedData>()
                    .eq(FmbOrderRelatedData::getOrderSn,orderRecordServiceOne.getGeneralOrderSn())
                    .set(FmbOrderRelatedData::getReserveEndStatus,hotelCodeIsFinish?1:0)
                    ;
            relatedDataService.update(wrRelatedUp) ;
        }
    }


    /**
     * 房券退款 准备
     * @param orderSn
     */
    public void queryHotelCodeNowInfo(String orderSn){

        DynamicDataSourceContextHolder.push(DB_slave);
        FmbOrderRelatedData fmbOrderRelatedData = queryFmbOrderRelatedData(orderSn);
        List<FmbReserveHotelCodes> codes = queryHotelCodeList(orderSn, 2);
        final FmbOrderInfo orderInfo = queryFmbOrderInfo(orderSn);
        final List<FmbOrderGoods> fmbOrderGoods = queryFmbOrderGoods(orderSn);
        final BigDecimal goodsPrice = fmbOrderGoods.get(0).getGoodsPrice();

        //总间夜
        final int totalNumber = fmbOrderRelatedData.getContainTimes() * fmbOrderRelatedData.getNumber();
        //剩余间夜
        final Integer sum = codes.stream().filter(c -> c.getStatus() == 1).map(FmbReserveHotelCodes::getNumber).reduce(0,Integer::sum) ;

        final BigDecimal allMoney = orderInfo.getMoney();
        final Integer couponMoney = orderInfo.getCouponMoney();
        final Integer discountRate = orderInfo.getDiscountRate();
        final BigDecimal realpayMoney = orderInfo.getRealpayMoney();

    }


    public void returnUserBalance(FmbOrderInfo fmbOrderInfo, Integer uid) {
        boolean updateBalance = false ;
        for (int i = 0; i < 3; i++) {

            Wrapper<FmbUserExtinfo> wrE = new LambdaQueryWrapper<FmbUserExtinfo>()
                    .eq(FmbUserExtinfo::getUid, uid)
                    .select(FmbUserExtinfo::getId,FmbUserExtinfo::getMoney)
                    ;

            final FmbUserExtinfo userBalance = userExtinfoService.getOne(wrE);

            if (userBalance == null) {
                throw new BadLogicException("uid="+ uid +"用户余额信息异常[1988]");
            }

            Wrapper<FmbUserExtinfo> wr = new LambdaUpdateWrapper<FmbUserExtinfo>()
                    .eq(FmbUserExtinfo::getId,userBalance.getId())
                    .eq(FmbUserExtinfo::getMoney,userBalance.getMoney())
                    .set(FmbUserExtinfo::getMoney, fmbOrderInfo.getCashMoney().add(userBalance.getMoney()))
                    .set(FmbUserExtinfo::getUtime,LocalDateTime.now())
                    ;
            updateBalance = userExtinfoService.update(wr);
            if (updateBalance) {
                break;
            }
        }
        if (!updateBalance) {
            throw new BadLogicException("uid="+ uid +",返还余额更新失败[1644]");
        }

        Wrapper<FmbUserCashLog> cashLogWr = new LambdaQueryWrapper<FmbUserCashLog>()
                .eq(FmbUserCashLog::getUid, uid)
                .eq(FmbUserCashLog::getOrderSn, fmbOrderInfo.getOrderSn())
                .select(FmbUserCashLog::getCashlogId)
                ;
        final FmbUserCashLog userCashLog = userCashLogService.getOne(cashLogWr);

        if (userCashLog==null) {
            throw new BadLogicException("用户返现流水信息异常");
        }

        userCashLog.setStatus(3);
        userCashLog.setUtime(LocalDateTime.now());

        //把使用余额 log 的状态改为失效
        userCashLogService.updateById(userCashLog) ;
    }

    public void returnCoupon(FmbOrderInfo fmbOrderInfo, String auto_cancel, Integer uid) {
        Wrapper<FmbUserCoupon> wrCoupon = new LambdaQueryWrapper<FmbUserCoupon>()
                .eq(FmbUserCoupon::getOrderSn, fmbOrderInfo.getOrderSn())
                .eq(FmbUserCoupon::getUid,uid)
                .select(FmbUserCoupon::getCouponSn)
                ;
        final FmbUserCoupon updateAim = couponService.getOne(wrCoupon);
        updateAim.setUseStatus(0);
        updateAim.setOrderSn("");
        updateAim.setUseTime(DEFAULT_DATETIME);
        couponService.updateById(updateAim) ;

        FmbCouponLog couponLog = new FmbCouponLog();
        couponLog.setCouponSn(updateAim.getCouponSn());
        couponLog.setLogType(auto_cancel);
        couponLog.setAdminUid(uid);
        HashMap<String, Object> map = new HashMap<>();
        map.put("reason", "订单超时未支付,优惠券返回");
        couponLog.setLogNote(JSON.toJSONString(map));
        couponLog.setLogTime(LocalDateTime.now());
        couponLogService.save(couponLog) ;
    }


    /**
     *
     *  确认订单 优惠券和房券 价格计算
     * @param uid 用户uid
     * @param input  选择优惠券时的 sku信息
     * @param resultMap  map 对象 , 根据aimOperate 不同 在这个map里 会返回对应的 数据 
     * @param aimOperate
         1.从确认订单 自动选择最优 优惠券调用该方法  "confirmOrder"
         2.从确认订单,用户要自己选择能用优惠券  "userChooseCoupon"

     */
    public void chooseBestCoupon(Integer uid, @Valid ReqConfirmOrder input, HashMap<String, Object> resultMap,String aimOperate){

        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.countGoodsPrice(input.getReqGenSkus(),input.getHotelReserveCode(),resultMap);


        confirmOrderMoneyInfo.setMainTitle("费用明细");
//        confirmOrderMoneyInfo.setSubTitle("费用明细xxx");

        HashMap<String, Object> par = new HashMap<>();
        par.put("uid", uid);
        List<FrontCouponDomain> lists = frontSkuDao.selectFrontCoupon(par);


        final Integer xaid = input.getXaid();
        //如果是用户自己 要选择优惠券 就默认返回空数组
        if ("userChooseCoupon".equals(aimOperate)) {
            resultMap.put("couponList",new ArrayList<>()) ;
        }

        final OrderSysInfo orderSysInfo = new OrderSysInfo();

        //只要用户不是 确认不使用优惠券 都要走优惠券筛选逻辑
        if (input.getUserChooseNotUseCoupon()!=1) {
            final FmbxActivity activity = queryFmbxActivity(xaid);
            boolean activityIsNotInCouponUnableAndNotYearCard = self.couponAidCheck(xaid);
            if (activityIsNotInCouponUnableAndNotYearCard) {

                for (FrontCouponDomain couponDomain : lists) {
                    try {
//                    self.processEachCoupon(input, activity, couponDomain,confirmOrderMoneyInfo, xaid) ;

                        self.couponCoreCheck(input, activity, couponDomain, xaid, confirmOrderMoneyInfo.countAllGoodsMoney(), orderSysInfo);
                    } catch (BadLogicException e) {
                        //如果 有的优惠券不满足使用条件 就标记为 不能使用
                        logger.info("getCouponSn={} ,errorMessage={}",couponDomain.getCouponSn(),e.getMessage());
                        couponDomain.setCanUse(false);
                    }
                }

                if ("userChooseCoupon".equals(aimOperate)) {
                    resultMap.put("couponList",lists) ;
                }

                if ("confirmOrder".equals(aimOperate) &&  StringUtils.isNotBlank( input.getCouponSn())) {
                    if (lists.stream().filter(co->input.getCouponSn().equals( co.getCouponSn()) && co.isCanUse() ).count()!=1) {
                        throw new BadLogicException("优惠券不合法[2412]");
                    }
                    //只保留用户指定的
                    lists = lists.stream().filter(co->input.getCouponSn().equals( co.getCouponSn()) && co.isCanUse() ).collect(Collectors.toList()) ;
                }

//            final List<FrontCouponDomain> canUseCoupons = lists.stream().filter(FrontCouponDomain::isCanUse).sorted((a, b) -> {
//                return b.getDiscountMoney().compareTo(a.getDiscountMoney());
//            }).collect(Collectors.toList());
//            logger.info("canUseCoupons={}",JSON.toJSONString(canUseCoupons,true));

                //找到 抵扣金额最高的 优惠券
                final Optional<FrontCouponDomain> first = lists.stream().filter(FrontCouponDomain::isCanUse).sorted((a, b) -> {
                    return b.getDiscountMoney().compareTo(a.getDiscountMoney());
                }).findFirst();

                if (first.isPresent()) {
                    final FrontCouponDomain reCoupon = first.get();

                    //只有不使用房券的时候才挑选最优优惠券
                    if (StringUtils.isBlank (input.getHotelReserveCode())){
                        confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" , reCoupon.getDiscountMoney() ) ;
                        confirmOrderMoneyInfo.setCouponSn(reCoupon.getCouponSn());
                        confirmOrderMoneyInfo.setCouponDiscountMoney(reCoupon.getDiscountMoney());
                    }
                }
            }
        }



        //如果在使用 酒店预约码
        if (StringUtils.isNotBlank (input.getHotelReserveCode())) {
            self.checkHotelReserveCode(input.getHotelReserveCode(),uid, orderSysInfo,"chooseBestCoupon",input.getReqGenSkus()) ;




            //日历房确认信息
            final List<ConfirmOrderMoneyItem> collect = confirmOrderMoneyInfo.getGroups().stream().filter(x -> x.getGroupType() == GROUPTYPE_1).map(g -> g.getItemList()).flatMap(x -> x.stream()).collect(Collectors.toList());


            final FmbxHotelReserveSku reserveCodeSourceSku = orderSysInfo.getReserveCodeSourceSku();
            final String invalidReserveWeekDays = reserveCodeSourceSku.getInvalidReserveWeekDays();
            final String invalidReserveDate = reserveCodeSourceSku.getInvalidReserveDate();

            Set<Integer> collectWeek = new HashSet<>();
            if (StringUtils.isNotBlank(invalidReserveWeekDays)) {
                collectWeek = Stream.of(invalidReserveWeekDays.split(",")).map(x -> Integer.parseInt(x)).collect(Collectors.toSet());
            }
            Set<String> collectDate = new HashSet<>() ;
            if (StringUtils.isNotBlank(invalidReserveDate)) {
                collectDate =
                        Stream.of(invalidReserveDate.split(",")).map(x -> x.toString().trim()).collect(Collectors.toSet());
            }

            for (ConfirmOrderMoneyItem moneyItem : collect) {

                final String leftTitle = moneyItem.getLeftTitle();
                //排除日期要检查 collectWeek里有8
                if (collectWeek.contains(8) &&  reserveDateIsLimit(leftTitle, collectDate)) {
                    throw new BadLogicException(leftTitle +"不能预约[3490]");
                }
                if (reserveWeekIsLimit( DateTime.parse(leftTitle).getDayOfWeek(),collectWeek)) {

                    final int dayOfWeek = DateTime.parse(leftTitle).getDayOfWeek();
                    throw new BadLogicException(leftTitle+"是星期"+(( dayOfWeek !=7 )?dayOfWeek+"":"日")+"不能预约[3491]");

                }

            }

            final Integer totalCodeNight = orderSysInfo.getReserveCodeSourceSuite().getTotalNeight();
            final BigDecimal goodsPrice = orderSysInfo.getReserveCodeFromOrderGoods().getGoodsPrice();

            final BigDecimal hotelCodeEachNightPay = goodsPrice.divide(new BigDecimal(totalCodeNight),2, RoundingMode.HALF_UP);
            logger.info("totalCodeNight={} ,goodsPrice={} ,hotelCodeEachNightPay = {}",totalCodeNight,goodsPrice,hotelCodeEachNightPay);

            BigDecimal totalHotelCodeSubMoney = BigDecimal.ZERO;
            logger.info(JSON.toJSONString(collect));

            final int totalWantNightOfHotel = collect.size() * collect.get(0).getSkuNumberN();
            if (totalWantNightOfHotel>totalCodeNight) {
                throw new BadLogicException("入住间夜数大于房券间夜数[5638]");
            }

            for (ConfirmOrderMoneyItem confirmOrderMoneyItem : collect) {
                logger.info("confirmOrderMoneyItem.getPriceBig()={}",confirmOrderMoneyItem.getPriceBig());
                // 如果日历房单间夜价格 大于等于 房券单间夜价格  房券抵扣金额就是就是房券本身
                if (confirmOrderMoneyItem.getPriceBig().compareTo(hotelCodeEachNightPay)!=-1) {
                    totalHotelCodeSubMoney =
                            totalHotelCodeSubMoney.add(hotelCodeEachNightPay.multiply(new BigDecimal(  confirmOrderMoneyItem.getSkuNumberN()))) ;


                }else {
                    //如果房券金额比 日历房大 ,抵扣金额 就是日历房金额
                    totalHotelCodeSubMoney =
                            totalHotelCodeSubMoney.add(confirmOrderMoneyItem.getPriceBig().multiply(new BigDecimal(  confirmOrderMoneyItem.getSkuNumberN()))) ;
                }

//                logger.info(" confirmOrderMoneyItem.getPriceBig() = {} ,totalHotelCodeSubMoney={} hotelCodeEachNightPay={}",
//                        confirmOrderMoneyItem.getPriceBig(),
//                        totalHotelCodeSubMoney,hotelCodeEachNightPay);
            }
            confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠","房券抵扣",totalHotelCodeSubMoney);

//            System.out.println("00000000000");
//            System.out.println(JSONUtil.toJsonStr(confirmOrderMoneyInfo));

            //使用房券时需要知道可选的 入离时间和 间数
            HashMap<String, Object> ruleInfo = new HashMap<>();
            self.buildHotelCodeUseRoleInfo(orderSysInfo, ruleInfo);

            final int dayDiff = FmbDateUtil.dayDiff(input.getReqGenSkus().get(0).getCheckOutDate(), input.getReqGenSkus().get(0).getCheckInDate());

//            logger.info("dayDiff={}",dayDiff);
//            logger.info("ruleInfo={}",JSONUtil.toJsonStr(ruleInfo));

            List dr  = (List) ((Map) ruleInfo.get("hotelCodeInfo")).get("dateAndRoomNumInfo");

            ArrayList<Integer> canUse = null ;
            for (Object o : dr) {
                HashMap<String, Object> map = (HashMap<String, Object>) o;
                if (map.get("nightNum").toString().equals(dayDiff+"")) {
                    canUse = (ArrayList<Integer>) map.get("roomNumberCanUse");
                }
            }
            if (canUse == null) {
                throw new BadLogicException("入离日期异常[4589]");
            }

            final Set<String> roomSet = canUse.stream().map(x -> x.toString()).collect(Collectors.toSet());

            final Integer canDivide = orderSysInfo.getReserveCodeSourceSku().getCanDivide();

            final ConfirmOrderMoneyGroup groupInfo = confirmOrderMoneyInfo.getGroups().stream().filter(x -> x.getGroupType() == GROUPTYPE_1).findFirst().get();

            //在合理范围内的 间数才能被标记为可用的.
            for (RoomNumStatus roomNumStatus : confirmOrderMoneyInfo.getRoomNumStatus()) {

                if (canDivide==0) {
                    if(roomSet.contains(roomNumStatus.getRoomNum()+"")&& roomNumStatus.getStatus()==1){
                        roomNumStatus.setStatus(1);
                    }else {
                        roomNumStatus.setStatus(0);
                    }
                }else {
                    if(roomSet.contains(roomNumStatus.getRoomNum()+"") && roomNumStatus.getRoomNum()<=groupInfo.getMaxStock()){
                        roomNumStatus.setStatus(1);
                    }else {
                        roomNumStatus.setStatus(0);
                    }
                }


                roomNumStatus.setSelected(0);
                if(roomNumStatus.getStatus()==1  && roomNumStatus.getRoomNum()==input.getReqGenSkus().get(0).getSkuNumberSource()){
                    roomNumStatus.setSelected(1);
                }

            }

            final List<RoomNumStatus> statusList = confirmOrderMoneyInfo.getRoomNumStatus().stream().filter(x -> x.getStatus() == 1).collect(Collectors.toList());

            if (!statusList.isEmpty()) {
                confirmOrderMoneyInfo.setMinRoomNum(statusList.get(0).getRoomNum());
                confirmOrderMoneyInfo.setMaxRoomNum(statusList.get(statusList.size()-1).getRoomNum());
            }

        }


//        logger.info("--->confirmOrderMoneyInfo={}",JSON.toJSONString(confirmOrderMoneyInfo,true));

        confirmOrderMoneyInfo.setTotalMoney( confirmOrderMoneyInfo.countAllGoodsMoney());
        resultMap.put("consumeList",confirmOrderMoneyInfo) ;

    }

    /**
     * 使用房券时因为 考虑到房券的是否可拆分, 以及剩余次数问题,需要把可选晚数和对应的可选间数返回
     * @param orderSysInfo
     * @param resultMap
     */
    public void buildHotelCodeUseRoleInfo(OrderSysInfo orderSysInfo, HashMap<String, Object> resultMap) {
//        orderSysInfo.setReserveCodeFromOrderInfo(reserveCodeFromOrderInfo);
//        orderSysInfo.setReserveCodeFromOrderGoods(reserveCodeFromOrderGoods);
//
//        FmbxSuite suite = JSONUtil.parse(reserveCodeFromOrderGoods.getGoodExt()).getByPath("suite", FmbxSuite.class);
//        orderSysInfo.setReserveCodeSourceSuite(suite);
//        orderSysInfo.setHotelCode(codeDB);
//        orderSysInfo.setUseHotelCode(true);

        final String goodExt = orderSysInfo.getReserveCodeFromOrderGoods().getGoodExt();
        FmbxHotelReserveSku reserveSku = JSONUtil.parse(goodExt).getByPath("skuDetail.reserveSku", FmbxHotelReserveSku.class);

        final Integer totalRemain = orderSysInfo.getHotelCode().getNumber();
        final Integer status = orderSysInfo.getHotelCode().getStatus() ;
        final Integer canDivide = reserveSku.getCanDivide();
        final Integer nightMin = orderSysInfo.getReserveCodeSourceSuite().getNightMin();

        logger.info("totalRemain={},status={},canDivide={},nightMin={}",totalRemain,status,canDivide,nightMin);

        HashMap<String, Object> hotelCodeInfo = new HashMap<>();
        hotelCodeInfo.put("totalRemain", totalRemain);
        hotelCodeInfo.put("canDivide", canDivide);
        hotelCodeInfo.put("nightMin", nightMin);

        resultMap.put("hotelCodeInfo",hotelCodeInfo) ;

        final ArrayList<HashMap<String, Object>> dr = new ArrayList<>();
        hotelCodeInfo.put("dateAndRoomNumInfo",dr) ;
        //可拆分
        if (1==canDivide) {

            for (Integer i = 1; i <= totalRemain; i++) {
                if (totalRemain%i==0) {

                    final int nightNum = totalRemain / i;

                    if (nightNum>=nightMin) {
                        HashMap<String, Object> data = new HashMap<>();
                        data.put("nightNum", nightNum);
                        final ArrayList<Integer> roomNumberCanUse = new ArrayList<>();
                        data.put("roomNumberCanUse", roomNumberCanUse);

                        for (int roomNumCheck = 1; roomNumCheck <= 10; roomNumCheck++) {
                            if (nightNum * roomNumCheck <=totalRemain) {
                                roomNumberCanUse.add(roomNumCheck);
                            }
                        }
                        dr.add(data) ;
                    }
                }
            }
        }else {

            for (Integer i = 1; i <= totalRemain; i++) {
                if (totalRemain%i==0) {

                    final int nightNum = totalRemain / i;

                    if (nightNum>=nightMin) {
                        HashMap<String, Object> data = new HashMap<>();
                        data.put("nightNum", nightNum);
                        final ArrayList<Integer> roomNumberCanUse = new ArrayList<>();
                        data.put("roomNumberCanUse", roomNumberCanUse);

                        for (int roomNumCheck = 1; roomNumCheck <= 10; roomNumCheck++) {
                            if (nightNum * roomNumCheck ==totalRemain) {
                                roomNumberCanUse.add(roomNumCheck);
                            }
                        }
                        dr.add(data) ;
                    }
                }
            }


        }

    }

    /**
     * 判断一个活动是否在 fmb_coupon_unable_activity 里 和 年卡里
     * 因为 这2种实际上 都不能使用优惠券
     * @param xaid
     * @return
     */
    @Cacheable(value = "couponAidCheck#30", keyGenerator = "customKeyGenerator")
    public boolean couponAidCheck(Integer xaid) {
        HashSet<String> aidsOfYearCard = self.queryYearCardAids();

        if (aidsOfYearCard.contains(xaid.toString())) {
            return false ;
        }

        Wrapper<FmbCouponUnableActivity> wrAc = new LambdaQueryWrapper<FmbCouponUnableActivity>()
                .eq(FmbCouponUnableActivity::getAid, xaid)
                ;
        if (couponUnableActivityService.count(wrAc)>0) {
            return false ;
        }

        return true;
    }

    public void processEachCoupon(FrontApiRequestDomain input, FmbxActivity activity, FrontCouponDomain couponDomain, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, Integer xaid) {

        BigDecimal priceAll = confirmOrderMoneyInfo.countAllGoodsMoney();

        self.couponCoreCheck(input, activity, couponDomain, xaid, priceAll, null);
    }

    /**
     * 优惠券 能否合法使用的核心逻辑, 以下情况会调用
     * 1. 系统计算用户最优优惠券
     * 2. 下单校验优惠券
     * 3. 用户自主选择可用优惠券列表
     * @param input  主要区分平台信息
     * @param activity 活动信息
     * @param couponDomain 优惠券和券组信息
     * @param xaid   活动id
     * @param priceAll
     * @param orderSysInfo   下单校验优惠券传入非空对象 ,其他情况为空
     */
    public void couponCoreCheck(FrontApiRequestDomain input, FmbxActivity activity, FrontCouponDomain couponDomain, Integer xaid,
             BigDecimal priceAll, OrderSysInfo orderSysInfo) {
        int platValue = couponDomain.getPlatId() ;

        if (checkCouponService.isNotValidCoupon(xaid, couponDomain.getCouponId().intValue())) {
            throw new BadLogicException("优惠券黑白名单校验失败[1502]");
        }

        if (0!= platValue && 1!= platValue) {

            if (platValue ==2 && !input.checkFMB_APP()) {
                throw new BadLogicException("优惠券平台限制异常[1506]");
            }else
            if (platValue ==3 && !input.checkH5()) {
                throw new BadLogicException("优惠券平台限制异常[1507]");
            }else
            if (platValue ==4 && !(input.checkFMB_APP() || input.checkWxApp())) {
                throw new BadLogicException("优惠券平台限制异常[1509]");
            }else
            if (platValue ==5 && !input.checkWxApp()) {
                throw new BadLogicException("优惠券平台限制异常[1511]");
            }
        }

        if (1== couponDomain.getLimitType().intValue() && !xaid.toString().equals(couponDomain.getActivityId().toString())) {
            throw new BadLogicException("优惠券不满足活动限制[1513]");
        }

        //限制品类
        if (2== couponDomain.getLimitType().intValue() &&!self.couponCategoryCheck(couponDomain.getLimitCate(), activity.getCategoryId())) {
            throw new BadLogicException("优惠券分类限制异常[1504]");
        }

        if (1== couponDomain.getCouponType().intValue() ) {

            //商品金额 小于 代金券金额
            if ( priceAll.compareTo( BigDecimal.valueOf ( couponDomain.getCouponMoney()))==-1){
                throw new BadLogicException("优惠券金额异常[1510]");
            }

            if (orderSysInfo != null) {
                orderSysInfo.setCouponMoney( BigDecimal.valueOf ( couponDomain.getCouponMoney()));
            }
        }
        else if (2== couponDomain.getCouponType().intValue() ) { //满减券

            //商品金额 需要大于折扣券金额
            if ( priceAll.compareTo( BigDecimal.valueOf ( couponDomain.getMinAmount()))==-1){
                throw new BadLogicException("优惠券金额异常[1514]");
            }
            if (orderSysInfo != null) {
                orderSysInfo.setCouponMoney(  BigDecimal.valueOf( couponDomain.getCouponMoney()));
            }


        }else if (4== couponDomain.getCouponType().intValue() ) { //折扣券

            //商品金额 需要大于折扣券金额
            if ( priceAll.compareTo(BigDecimal.valueOf(  couponDomain.getMinAmount()))==-1){
                throw new BadLogicException("优惠券金额异常[1516]");
            }
            //折扣券
            couponDomain.setDiscountMoney(priceAll.multiply(BigDecimal.valueOf(100- couponDomain.getDiscountRate()).divide(new BigDecimal(
                    "100"))));

            if (orderSysInfo != null) {

                orderSysInfo.setCouponMoney( orderSysInfo.getGoodsPriceAll().multiply(BigDecimal.valueOf(100-couponDomain.getDiscountRate()).divide(new BigDecimal("100")))  );
                orderSysInfo.setRateCoupon(true);
            }


        }
    }

    /**
     * 根据 用输入 的sku信息 查询原始价格
     * @param reqGenSkus
     * @param hotelReserveCode
     * @param resultMap
     * @return
     */
    public ConfirmOrderMoneyInfo countGoodsPrice(List<ReqGenSku> reqGenSkus, String hotelReserveCode, HashMap<String, Object> resultMap) {

        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = new ConfirmOrderMoneyInfo();


        final List<FmbxSku> fmbxSkus = self.queryFmbxSkus(reqGenSkus);

        final Map<String, List<FmbxSku>> collect = fmbxSkus.stream().collect(Collectors.groupingBy(x -> x.getSkuId().toString()));

        BigDecimal bigDecimalAll = BigDecimal.ZERO;
        for (ReqGenSku genSkus : reqGenSkus) {
            final FmbxSku fmbxSku = collect.get(genSkus.getSkuId().toString()).get(0);
            //构造价格明细
            final ConfirmOrderMoneyGroup group = getOrderService(fmbxSku.getSkuType()).buildPriceWhenUseCoupon(fmbxSku, genSkus,
                    "confirmOrder",confirmOrderMoneyInfo,hotelReserveCode);

//            for (ConfirmOrderMoneyItem i : group.getItemList()) {
//                bigDecimalAll.add(i.getPriceBig()) ;
//            }
            //取第一组的 价格数据的描述 作为 整体明细的副标题
            if (StringUtils.isNotBlank(group.getSkuDesc())) {
                confirmOrderMoneyInfo.setSubTitle(group.getSkuDesc());
            }
            //从下一层提取数据到 confirmOrderMoneyInfo
            if (fmbxSku.getSkuType()==1 && confirmOrderMoneyInfo.getRoomNumStatus().isEmpty() ) {
                confirmOrderMoneyInfo.getRoomNumStatus().addAll(  group.getRoomNumStatus());

                final List<RoomNumStatus> allOk = group.getRoomNumStatus().stream().filter(s -> s.getStatus() == 1).collect(Collectors.toList());
                if (!allOk.isEmpty()) {
                    confirmOrderMoneyInfo.setMinRoomNum(allOk.get(0).getRoomNum());
                    confirmOrderMoneyInfo.setMaxRoomNum(allOk.get(allOk.size()-1).getRoomNum());
                }


                group.getRoomNumStatus().clear();
            }

            confirmOrderMoneyInfo.getGroups().add(group) ;
            confirmOrderMoneyInfo.setFmbxSku(fmbxSku) ;
        }

        return confirmOrderMoneyInfo;
    }


    @Cacheable(value = "queryYearCardAids#120", keyGenerator = "customKeyGenerator")
    public HashSet<String> queryYearCardAids(){
        //cityIds = 1,2,3 //查全国、北京、上海的所有年卡活动
        Wrapper<FmbTemplate> wr = new LambdaQueryWrapper<FmbTemplate>()
                .eq(FmbTemplate::getSectionName,"year_card")
                .eq(FmbTemplate::getModuleName, "new_activity_config")
                .in(FmbTemplate::getCityId, new Integer[]{1, 2, 3})
                .select(
                        FmbTemplate::getContent,
                        FmbTemplate::getSectionName,
                        FmbTemplate::getModuleName,
                        FmbTemplate::getCityId
                );
        List<FmbTemplate> data = templateService.list(wr) ;

        HashSet<String> aidList=new HashSet<String>();
        //循环处理数据
        for (FmbTemplate item : data ){

            final ArrayList<HashMap<String, Object>> lists = FmbPhpUtil.unserialize2ListOfMap(item.getContent());

            for (HashMap<String, Object> map : lists) {
//                System.out.println(JSON.toJSONString(map));
                if (map.containsKey("aid")){
                    if (map.containsKey("prepare") &&  StringUtils.isNotBlank(  map.get("prepare").toString())) {
                        Date prepare = FmbDateUtil.str2Date(map.get("prepare").toString());
                        if (prepare != null) {
                            if (prepare.getTime() <System.currentTimeMillis() ) {
                                aidList.add(map.get("aid").toString()) ;
                            }
                        }
                    }else  {
                        aidList.add(map.get("aid").toString()) ;
                    }
                }
            }
        }
        return aidList;
    }

    /**
     * 订单详情接口
     * @param resultMap
     * @param input
     * @param nowUser
     * @param from
     *   1. "front" 前台
     */
    @DS(DB_master)
    public void orderDetail(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser,String from) {

        final String orderSn = input.getOrderSn();

        if (FRONT.equals(from) &&   nowUser == null) {
            throw new BadLogicException("用户需要登录[3104]");
        }

        FmbOrderInfo orderInfo = null ;
        if (FRONT.equals(from)){
            orderInfo = self.queryFmbOrderInfo(orderSn);
        }else
        if (ADMIN001.equals(from)){
            orderInfo = self.queryFmbOrderInfoFromAdmin001(orderSn);
        }

        if (orderInfo == null) {
            throw new BadLogicException("订单数据异常[3101]");
        }
        if (FRONT.equals(from) &&   nowUser.getUserId() != orderInfo.getUid().intValue()) {
            throw new BadLogicException("用户信息异常[3106]");
        }


        //查询goods 信息
        List<FmbOrderGoods> goodsList =null ;
        //前台尽量少返回
        if (FRONT.equals(from) ) {
            goodsList = self.queryFmbOrderGoods(orderSn);
        }
        if (ADMIN001.equals(from) ) {
            goodsList = self.queryFmbOrderGoodsAdmin(orderSn);
            List<String> ordersns = Stream.of(orderSn).collect(Collectors.toList());
            Map<String,Integer> invoiceMap = self.hasInvoiceByOrderSns(ordersns);
            goodsList.forEach(e->e.setHasInvoice(invoiceMap.get(orderSn)==null ? 0 : invoiceMap.get(orderSn)));
            resultMap.put("goodsList",goodsList) ;
            resultMap.put("notePrefix",LocalDateTime.now().format(DateTimeFormatter.ofPattern("M/dd"))+"--") ;
            self.getFmbMycpsCommissionOrder(resultMap,orderSn,nowUser,from);
        }

        final ArrayList<OrderDetailGoodsInfo> goodsInfos = new ArrayList<>();
        //构造价格信息列表 和 确认订单时的 结构一样
        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.createConfirmOrderMoneyInfoInOrderDetail(orderInfo, goodsList, goodsInfos);

        final Integer orderStatus = orderInfo.getOrderStatus();
        OrderDetailInfo orderDetailInfo = self.buildOrderStatusAndButtonInfo(orderInfo, orderStatus);

        //构造前台 展示的 购买人信息 和 购买物品信息
        // buyInfo 在后续代码里添加
        HashMap<String, Object> orderExtInfo = new HashMap<>();
        HashMap<String, Object> buyInfo = new HashMap<>();
        if ( ( GOODS_TYPE_6== orderInfo.getGoodsType())){
            orderExtInfo.put("title", "入住信息");
            final ArrayList<HashMap<String, Object>> listExts = new ArrayList<>();
            orderExtInfo.put("lists", listExts);
            putItemInfo(listExts, "入住人",orderInfo.getReceiver() );
            putItemInfo(listExts, "联系电话",orderInfo.getMobile() );

        }
        if ( ( GOODS_TYPE_7== orderInfo.getGoodsType())){
            orderExtInfo.put("title", "联系人信息");
            final ArrayList<HashMap<String, Object>> listExts = new ArrayList<>();
            orderExtInfo.put("lists", listExts);

            putItemInfo(listExts, "联系人",orderInfo.getReceiver() );
            putItemInfo(listExts, "联系电话",orderInfo.getMobile() );
        }

        resultMap.put("orderExtInfo",orderExtInfo) ;
        resultMap.put("buyInfo",buyInfo) ;


        //房券是否可拆分 字段
        int  canDivide = 0 ;
        //把套餐信息从 交易快照里读出来
        if ( ( GOODS_TYPE_6== orderInfo.getGoodsType() || GOODS_TYPE_7== orderInfo.getGoodsType())){

            Wrapper<FmbOrderTradeSnapshot> wrSn = new LambdaQueryWrapper<FmbOrderTradeSnapshot>()
                    .eq(FmbOrderTradeSnapshot::getOrderSn,orderSn)
                    .select(FmbOrderTradeSnapshot::getContent);
            final FmbOrderTradeSnapshot one = snapshotService.getOne(wrSn);

            resultMap.put("suiteInfo",new JSONObject()) ;
            if (one != null) {
                final JSONObject jsonObject = JSON.parseObject(one.getContent());
                resultMap.put("suiteInfo",jsonObject) ;
            }

            final FmbOrderGoods goods = goodsList.get(0);

            final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());
            final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
            final Integer bpsId = skuDetail.getFmbxSku().getBpsId();
            FmbxBps bps = self.queryBps(bpsId);

            resultMap.put("hotelBasicInfo",bps) ;
            resultMap.put("skuDetail",skuDetail) ;

            if (ADMIN001.equals(from)) {
                FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(skuDetail.getSuite().getSuiteId());
                if (  GOODS_TYPE_7== orderInfo.getGoodsType()){
                    final FmbxHotelReserveSku reserveSku = orderOfHotelReserveService.querySkuReserve(goods.getGoodsId());
                    suite.setIsReturn(reserveSku.getIsReturn());
                    suite.setIsAutoReturn(reserveSku.getIsAutoReturn());
                    suite.setReturnPolicy(reserveSku.getReturnPolicy());
                }
                FmbxSuiteExt suiteExt = self.querySuiteInteriorNote(skuDetail.getSuite().getSuiteId());
                resultMap.put("suiteRealtime", suite);
                resultMap.put("suiteExtRealtime", suiteExt);
            }

            if (GOODS_TYPE_7== orderInfo.getGoodsType()) {
                canDivide = skuDetail.getReserveSku().getCanDivide() ;
            }

            if (  GOODS_TYPE_6== orderInfo.getGoodsType()){
                final FmbxBpsRoom room = bpsRoomService.getById(skuDetail.getRoomSku().getRoomId());
                resultMap.put("roomInfo" , room) ;


                final OrderDetailGoodsInfo goodsInfo = goodsInfos.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_6).findFirst().get();


                // buyInfo 添加
                buyInfo.put("title", orderInfo.getOrderName());
                final ArrayList<HashMap<String, Object>> buyInfoExts = new ArrayList<>();
                buyInfo.put("lists", buyInfoExts);

                putItemInfo(buyInfoExts, "套餐名称",goodsInfo.getSuiteName()+"-"+room.getRoomName() );
                putItemInfo(buyInfoExts, "房间数量",goodsInfo.getRoomNum()+"" );
                putItemInfo(buyInfoExts, "入离日期",goodsInfo.getCheckInOutDateStr()+"(共"+goodsInfo.getTotalNight()+"晚)" );

            }

            if (  GOODS_TYPE_7== orderInfo.getGoodsType()){

                final OrderDetailGoodsInfo goodsInfo = goodsInfos.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_7).findFirst().get();

                // buyInfo 数据添加
                buyInfo.put("title", orderInfo.getOrderName());
                final ArrayList<HashMap<String, Object>> buyInfoExts = new ArrayList<>();
                buyInfo.put("lists", buyInfoExts);

                if (skuDetail.getReserveSku().getHotelDateSkuId()>0) {
                    putItemInfo(buyInfoExts, "套餐名称",goodsInfo.getSuiteName() +" ("+skuDetail.getReserveRoomInfo()+")" );
                }else{
                    putItemInfo(buyInfoExts, "套餐名称",goodsInfo.getSuiteName() +FmbConstants.RESERVE_SKU_NAME_SINGLE_POSTFIX );
                }


                putItemInfo(buyInfoExts, "数量",goodsInfo.getSkuNumber()+"" );
                putItemInfo(buyInfoExts, "有效期",goodsInfo.getValidStartEndDate() );

            }

        }


        final List<FmbOrderReturns> fmbOrderReturns = returnService.queryOrderReturns(orderSn);
        final List<FmbOrderReturns> inReturnOrders = fmbOrderReturns.stream().filter(r -> r.getStatus().intValue() == 0 || r.getStatus().intValue() == 1).collect(Collectors.toList());
        //是否存在未完成的退款单子
        final boolean existUnFinishOrderReturn =inReturnOrders.size()>0 ;

        boolean orderHadReturns =  fmbOrderReturns.stream().filter(r->r.getStatus()!=3).count()>0 ;

        HashMap<String, Object> returnInfo = new HashMap<>();

        resultMap.put("returnInfo",returnInfo) ;


        //酒店预约单
        if (isOrderFromHotelReserveCode(orderInfo)) {
            resultMap.put("reserveOKTip",
                    "预约提交后，是否预约成功以父母邦的二次确认短信为准。若预约有问题，我们会在24小时内(周末及节假日除外)与您电话联系，如您长时间未收到短信通知，或有其他需求请联系客服"
                            +FmbServerConfig.getServiceTel()+"(8:00-18:30/天)") ;

            Wrapper<FmbHotelReserveOrderRecord> wrRe = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                    .eq(FmbHotelReserveOrderRecord::getHotelOrderSn,orderInfo.getOrderSn())
                    //.select(FmbHotelReserveOrderRecord::getGeneralOrderSn)
                    ;
            final FmbHotelReserveOrderRecord fmbHotelReserveOrderRecord = hotelReserveOrderRecordService.getOne(wrRe);

            resultMap.put("generalOrderSn",fmbHotelReserveOrderRecord.getGeneralOrderSn()) ;
            if (ADMIN001.equals(from) ) {
                resultMap.put("fmbHotelReserveOrderRecord", fmbHotelReserveOrderRecord);
            }
        }

        //买房券 房券信息列表
        if (GOODS_TYPE_7== orderInfo.getGoodsType()) {

            List<FmbReserveHotelCodes> codes = orderDetailQueryHotelCode(orderSn, canDivide);

            //房券是否用完了
            boolean codeCanUse  = codes.stream().filter(x->x.getNumber()>0 && x.getStatus()==1 ).count()>0 ;

//            FmbOrderRelatedData data = queryFmbOrderRelatedData(orderSn);
//            final boolean existUnFinishOrderReturn = returnService.isExistUnFinishOrderReturn(orderSn);


            final FmbOrderGoods goods = goodsList.get(0);

            final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());
            final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
            final Integer skuId = skuDetail.getReserveSku().getSkuId();


            boolean userCanStartHotelCodeReturn = false ;

            if (orderInfo.getOrderStatus()!=0) {
                final FmbxHotelReserveSku reserveSku = orderOfHotelReserveService.querySkuReserve(goods.getGoodsId());
                final FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());

                final boolean haveFullCode = codes.stream().filter(c -> c.getStatus() == 1 && c.getNumber().intValue() == suite.getTotalNeight()).count() > 0;

                userCanStartHotelCodeReturn =  haveFullCode &&  userCanStartHotelCodeReturn(orderInfo.getOrderSn()) ;
            }


            final boolean showReturnButton = userCanStartHotelCodeReturn  && !existUnFinishOrderReturn;


            //如果发现 所有的房券都没有次数了
            final ArrayList<HashMap<String, Object>> buttonsList = orderDetailInfo.getButtonsList();
            if (!codeCanUse) {
                logger.info("房券用完了~~~");
                removeButton(buttonsList, BUTTON_TYPE_2_HOTEL_RESERVE);
            }
            // 如果没有房券可以退款了 删除退款按钮
            if (!showReturnButton) {
                logger.info("房券不能发起退款了~");
                removeButton(buttonsList, BUTTON_TYPE_2_RETURN_SERVICE);
            }



            String alertStr = checkReserveSkuReserveDateInfo(skuId );

            for (FmbReserveHotelCodes code : codes) {

                if (2==code.getStatus()) {
                    code.setAlertStr(CODE_ALERT);
                }else {
                    code.setAlertStr(alertStr);
                }
                if (ADMIN001.equals(from) ) {
                    code.setCodeUrl(FmbServerConfig.mSiteDomainHttp() + "/hotel_card/hotel_card_page/hotel_card_record?general_order_sn=" + code.getOrderSn() + "&general_ticket_id=" + code.getTicketId() + "&code=" + code.getCode());
                }
            }

            resultMap.put("hotelCode", codes) ;
            final FmbOrderRelatedData relatedData = self.queryFmbOrderRelatedData(orderSn);
            resultMap.put("hotelCodeRelatedDate",relatedData) ;
        }

        if (fmbOrderReturns.size()>0) {
            addButtonInfo(orderDetailInfo.getButtonsList(),BUTTON_TYPE_RETURN_DETAIL);
            removeButton(orderDetailInfo.getButtonsList(), BUTTON_TYPE_2_RETURN_SERVICE);

            returnInfo.put("order_sn",orderSn) ;
            returnInfo.put("goods_id",fmbOrderReturns.get(0).getGoodsId()) ;

            if (ADMIN001.equals(from)) {
                returnInfo.put("returnListSize", fmbOrderReturns.size());
                returnInfo.put("returnListStatus", fmbOrderReturns.stream().map(r -> FmbOrderReturns.statusMap.get(r.getStatus())).collect(Collectors.joining("/")));
                returnInfo.put("returnListUrl", "/tickets/orders/hotel_returns_list?order_sn=" + orderSn + "&goods_id=" + fmbOrderReturns.get(0).getGoodsId() + "&row_index=0&goods_type=4");



            }
        }

        if (ADMIN001.equals(from))
        {

            HashMap<String, Object> t2 = new HashMap<>();

            resultMap.put("changeOrderInfos",orderInfoOfChange(orderInfo.getOrderSn())) ;

            // 酒店普通日历房订单增加 改签标记
            resultMap.put("orderCanChange",0) ;
            if (isOrderSuccessStatus(orderInfo.getOrderStatus()) &&  orderInfo.getGoodsType()==GOODS_TYPE_6 && !orderHadReturns ) {
                resultMap.put("orderCanChange",1) ;
            }

            returnInfo.put("canReturnReserveHotleDirect",0) ;
            if(!existUnFinishOrderReturn &&  orderInfo.getGoodsType()==GOODS_TYPE_6 &&   orderInfo.getOrderType()==2 && isOrderSuccessStatus(orderInfo.getOrderStatus()) ){
                returnInfo.put("canReturnReserveHotleDirect",1) ;
                returnInfo.put("canReturnReserveHotleDirectDesc","酒店预约单可以直接发起退款") ;
            }



            returnInfo.put("canReturn",
                    (!isOrderSuccessStatus(orderInfo.getOrderStatus()) || existUnFinishOrderReturn)?0:1) ;
            returnInfo.put("inReturnDesc","") ;
            if (existUnFinishOrderReturn) {
                final Integer returnStatus = inReturnOrders.get(0).getStatus();
                if (returnStatus==0) {
                    returnInfo.put("inReturnDesc","待审核") ;
                }else
                if (returnStatus==1) {
                    returnInfo.put("inReturnDesc","审核通过") ;
                }
            }
        }


        FmbOrderInfoOfAdmin fmbOrderInfoOfAdmin = new FmbOrderInfoOfAdmin();
        BeanUtils.copyProperties(orderInfo,fmbOrderInfoOfAdmin);

        final Integer goodsType = fmbOrderInfoOfAdmin.getGoodsType();
        fmbOrderInfoOfAdmin.setSkipType( (goodsType==6|| goodsType==7)  ? 6 : fmbOrderInfoOfAdmin.getGoodsType() );


        resultMap.put("orderInfo",fmbOrderInfoOfAdmin) ;
        resultMap.put("status",orderDetailInfo) ;
        resultMap.put("goodsInfos",goodsInfos) ;
        resultMap.put("confirmOrderMoneyInfo",confirmOrderMoneyInfo) ;

        //后台订单查询
        if ( ADMIN001.equals(from) ) {
            self.queryAdminOrderInfo(resultMap, input, nowUser, orderSn, orderInfo);
        }
    }

    public boolean userCanStartHotelCodeReturn(String orderSn) {

        final Integer goodsId = queryFmbOrderGoods(orderSn).get(0).getGoodsId();

        final FmbxHotelReserveSku reserveSku =reserveSkuService.getById(goodsId) ;

//        final FmbxHotelReserveSku reserveSku = orderOfHotelReserveService.querySkuReserve(goodsId);
//        final FmbxSuite suite = suiteService.getById(reserveSku.getSuiteId());

        //  `is_return` tinyint(2) NOT NULL DEFAULT '1' COMMENT '退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)',
        if (reserveSku.getIsAutoReturn()==0 || reserveSku.getIsReturn()==0) {
            return false ;
        }

        //房券sku
//          `reserve_choose_start_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '可预约入住开始时间',
//  `reserve_choose_end_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '可预约入住结束时间',

        final LocalDateTime reserveChooseStartTime = reserveSku.getReserveChooseStartTime();
        final LocalDateTime reserveChooseEndTime = reserveSku.getReserveChooseEndTime();

//        1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)',
        if (1 == reserveSku.getIsReturn()) {

            int day = 0;
            if (reserveSku.getReturnValidDay() != null && reserveSku.getReturnValidDay().intValue() >= 0) {
                day = reserveSku.getReturnValidDay().intValue();
            }

            long mill =
                    FmbDateUtil.getLocalDatetimeMillionSeconds(reserveChooseEndTime) - day * 3600 * 24 * 1000L - (3600 * 24 * 1000L - 1000L * LocalTime.parse(reserveSku.getReturnValidTime(), FORMATTER_HHmm).toSecondOfDay());
            if (System.currentTimeMillis() > mill) {
                return false;
            }

        } else if (2 == reserveSku.getIsReturn()) {
            if (System.currentTimeMillis() < FmbDateUtil.getLocalDatetimeMillionSeconds(reserveChooseEndTime)) {
                return false;
            }
        } else if (3 == reserveSku.getIsReturn()) {
            if (System.currentTimeMillis() < FmbDateUtil.getLocalDatetimeMillionSeconds(reserveChooseStartTime)) {
                return false;
            }
        }

        return true ;
//        return  codes.stream().filter(c -> c.getStatus() == 1 && c.getNumber().intValue() == total).count() > 0 ;

    }

    public void removeButton(ArrayList<HashMap<String, Object>> buttonsList, int buttonType2ReturnService) {

        final Iterator<HashMap<String, Object>> iterator = buttonsList.iterator();

        while (iterator.hasNext()) {
            final HashMap<String, Object> next = iterator.next();
            if ((buttonType2ReturnService + "").equals(next.get("type").toString())) {
//                        按钮里就不需要去预约按钮了
                iterator.remove();
            }
        }
    }

    public List<FmbReserveHotelCodes> orderDetailQueryHotelCode(String orderSn, int canDivide) {
        List<FmbReserveHotelCodes> codes = queryHotelCodeList(orderSn, 2);
        FmbOrderRelatedData data = queryFmbOrderRelatedData(orderSn);
        int total = 0;
        if(data != null){
            total = data.getContainTimes();
        }
        int sortRank = 1000 ;
        for (FmbReserveHotelCodes code : codes) {
            if(code.getNumber()>0 && code.getStatus()==1){
                code.setCodeDesc("余"+code.getNumber()+"间夜,"+(canDivide ==1?"可拆分":"不可拆分"));
                code.setCodeAllDesc(FmbReserveHotelCodes.statusMap.get(code.getStatus())+",间夜总"+total+"余"+code.getNumber()+","+(canDivide ==1?"可拆分":"不可拆分"));
                code.setSortRank(sortRank);
            }else if(code.getStatus()==2){
                code.setCodeDesc("余"+code.getNumber()+"间夜,"+(canDivide ==1?"可拆分":"不可拆分")+",退款中");
                code.setCodeAllDesc(FmbReserveHotelCodes.statusMap.get(code.getStatus())+",间夜总"+total+"余"+code.getNumber()+","+(canDivide ==1?"可拆分":"不可拆分")+",退款中");
                code.setSortRank(500);
            }
            else {
                code.setCodeDesc("");
                code.setCodeAllDesc(FmbReserveHotelCodes.statusMap.get(code.getStatus())+",间夜总"+total+"余"+code.getNumber()+","+(canDivide ==1?
                        "可拆分":
                        "不可拆分"));
                code.setSortRank(0);
            }

            sortRank -- ;
        }

        //把用完的 code放到后面去
        codes =  codes.stream().sorted((a,b)->{ return b.getSortRank().compareTo(a.getSortRank()) ; })  .collect(Collectors.toList()) ;
        return codes;
    }

    @DS(DB_slave)
    public void queryAdminOrderInfo(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser, String orderSn,
                          FmbOrderInfo orderInfo) {
        final Integer uid = orderInfo.getUid();
        final FmbUsers fmbUser = self.queryFmbUser(uid);
        final FmbxActivity activity = self.queryFmbxActivity(orderInfo.getAid());

        resultMap.put("fmbUser",fmbUser) ;
        resultMap.put("activity",activity) ;

        final List<AdminOrderActionDomain> orderActions = self.selectAdminOrderAction(orderSn);

        final FmbxBpParterInfo bpParterInfo = bpParterInfoService.getById(orderInfo.getShopUserId());

        resultMap.put("bpParterInfo",bpParterInfo) ;
        resultMap.put("bpMain",bpMainService.getById(orderInfo.getShopUserId())) ;

        if (GOODS_TYPE_6== orderInfo.getGoodsType()) {
            final FmbxBpParterHotelInfo bpParterHotelInfo = parterHotelInfoService.getById(orderInfo.getShopUserId());
            resultMap.put("bpParterHotelInfo",bpParterHotelInfo) ;
            Wrapper<FmbxHotelGoodsDetail> detailWrapper = new LambdaQueryWrapper<FmbxHotelGoodsDetail>()
                    .eq(FmbxHotelGoodsDetail::getOrderSn, input.getOrderSn());
            List<FmbxHotelGoodsDetail> detailList = hotelGoodsDetailService.list(detailWrapper);
            resultMap.put("hotelGoodsDetail",detailList) ;
            if (activity.getIsConfirm()!=0){
                //订房单
                ReqFmbOrderHotel reqFmbOrderHotel = new ReqFmbOrderHotel();
                BeanUtils.copyProperties(input,reqFmbOrderHotel);
                kefuOrderService.getFmbOrderHotel(resultMap,reqFmbOrderHotel);
            }
            FmbOrderInfoOfAdmin fmbOrderInfoOfAdmin = (FmbOrderInfoOfAdmin)resultMap.get("orderInfo");
            if (activity.getIsConfirm()==1 || activity.getIsConfirm()==3){
                fmbOrderInfoOfAdmin.setIsShowConfirm(1);
            }else  if (activity.getIsConfirm()==2 && fmbOrderInfoOfAdmin!=null && fmbOrderInfoOfAdmin.getConfirmStatus()!=0){
                fmbOrderInfoOfAdmin.setIsShowConfirm(1);
            }else{
                fmbOrderInfoOfAdmin.setIsShowConfirm(0);
            }

            Wrapper<FmbOrderConfirm> wrFmbOrderConfirm = new LambdaQueryWrapper<FmbOrderConfirm>()
                    .eq(FmbOrderConfirm::getOrderSn, orderSn);
            List<FmbOrderConfirm> confirmList = fmbOrderConfirmService.list(wrFmbOrderConfirm);
            List<ReqFmbOrderConfirm> reConfirmList = new ArrayList<>();
            for(FmbOrderConfirm confirm : confirmList){
                ReqFmbOrderConfirm reqFmbOrderConfirm = new ReqFmbOrderConfirm();
                BeanUtils.copyProperties(confirm,reqFmbOrderConfirm);
                if (reqFmbOrderConfirm.getAdminUid() != null) {
                    LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid, reqFmbOrderConfirm.getAdminUid())
                            .select(FmbAdmin::getName);
                    FmbAdmin dbadmin = adminService.getOne(wr1);
                    if (dbadmin != null && StringUtils.isNotBlank(dbadmin.getName())) {
                        reqFmbOrderConfirm.setAdminName(dbadmin.getName());
                    }else if (-2==reqFmbOrderConfirm.getAdminUid()){
                        reqFmbOrderConfirm.setAdminName("商户");
                    }
                }
                reConfirmList.add(reqFmbOrderConfirm);
            }
            resultMap.put("fmbOrderConfirmList",reConfirmList);
            resultMap.put("orderConfirmStatusStr", ReqHotelMail.confirmStatusMap.get(orderInfo.getConfirmStatus()));
            resultMap.put("orderConfirmStatusList", ReqHotelMail.getAllConfirmStatusList());
            List<FmbOrderGoods> fmbOrderGoods = orderSuccessHandler.queryFmbOrderGoods(orderInfo.getOrderSn());
            String smsContent = orderSuccessHandler.getSmsContent(orderInfo, activity, fmbOrderGoods,FMBX_SECOND_CONFIRM);
            resultMap.put("orderConfirmSmsContent", smsContent);
        }

        resultMap.put("orderActions",orderActions) ;
        resultMap.put("netOrCom", FmbServerConfig.netOrCom()) ;

        HashMap<String, Object> extStr = new HashMap<>();

        extStr.put("activityCityStr", "") ;
        if (FMB_SITE.containsKey(""+activity.getCityId())) {
            extStr.put("activityCityStr", FMB_SITE.get(""+activity.getCityId())) ;
        }
        extStr.put("userCityStr", "") ;
        if (FMB_SITE.containsKey(""+ orderInfo.getUserCityId())) {
            extStr.put("userCityStr", FMB_SITE.get(""+ orderInfo.getUserCityId())) ;
        }

        resultMap.put("extStr",extStr) ;
    }

    private void putItemInfo(ArrayList<HashMap<String, Object>> listExts, String key, String hotelUserName) {
        HashMap<String, Object> tempMap = new HashMap<>();
        tempMap.put("key", key) ;
        tempMap.put("val", hotelUserName) ;
        listExts.add(tempMap) ;
    }

    /**
     * 订单详情页面里 构造 价格信息 数据结构
     * @param orderInfo
     * @param goodsList
     * @param goodsInfos
     * @return
     */
    public ConfirmOrderMoneyInfo createConfirmOrderMoneyInfoInOrderDetail(FmbOrderInfo orderInfo, List<FmbOrderGoods> goodsList,
                                                                     ArrayList<OrderDetailGoodsInfo> goodsInfos) {
        //费用明细信息 构造 开始
        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = new ConfirmOrderMoneyInfo() ;
        confirmOrderMoneyInfo.setTotalMoney(orderInfo.getRealpayMoney());
        confirmOrderMoneyInfo.setCouponSn("");

        confirmOrderMoneyInfo.setMainTitle("费用明细");

        /**
         * 这个地方 后期可能有问题
         * 下单逻辑里 为了后期扩展 可以一个订单下 多个不同skuid
         * 对应的 就是 ordergoods里的 多条记录
         * 这个地方就是循环 ordergoods 然后构造订单详情里的 费用明细 .
         * 费用明细标题头部的副标题 只有一个
         * 目前按照 取第一个 ordergoods里的 数据 进行构造该费用明细副标题
         */
        for (FmbOrderGoods goods : goodsList) {
            OrderDetailGoodsInfo info =   getOrderServiceFromGoodsType(goods.getGoodsType()).readGoodsInfoFromGoodsExt(goods,
                    confirmOrderMoneyInfo, orderInfo) ;
            goodsInfos.add(info) ;
        }

        if(orderInfo.getCouponMoney()>0 || orderInfo.getDiscountRate()>0){
            if(orderInfo.getDiscountRate()>0){
                //如果使用折扣券 ,因为 数据库表里的优惠券金额是整数,所以金额可能不正确,得自己计算优惠券到底用了多少钱
                final BigDecimal discountMoney = orderInfo.getMoney().multiply(new BigDecimal("100").subtract(new BigDecimal(orderInfo.getDiscountRate())).divide(new BigDecimal("100")));
                confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" ,discountMoney) ;
            }else if(orderInfo.getCouponMoney()>0) {
                confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" ,BigDecimal.valueOf(orderInfo.getCouponMoney())) ;
            }
        }

        // 余额使用了多少
        if (orderInfo.getCashMoney().compareTo(BigDecimal.ZERO)==1) {
            confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "余额" , orderInfo.getCashMoney()) ;
        }
        return confirmOrderMoneyInfo;
    }

    private void addButtonInfo(ArrayList<HashMap<String, Object>> buttonsList, int type) {
        HashMap<String, Object> buttonInfo = new HashMap<>();
        buttonInfo.put("type", type);

        switch (type){

            case BUTTON_TYPE_DEL :
                buttonInfo.put("buttonTxt", "删除订单");
                break;
            case BUTTON_TYPE_PAY:
                buttonInfo.put("buttonTxt", "去支付");
                break;
            case BUTTON_TYPE_RE_BUY:
                buttonInfo.put("buttonTxt", "再次购买");
                break;
            case BUTTON_TYPE_2_HOTEL_RESERVE:
                buttonInfo.put("buttonTxt", "去预约");
                break;
            case BUTTON_TYPE_2_RETURN_SERVICE:
                buttonInfo.put("buttonTxt", "申请退款");
                break;
            case BUTTON_TYPE_CANCEL:
                buttonInfo.put("buttonTxt", "取消订单");
                break;
            case BUTTON_TYPE_RETURN_DETAIL:
                buttonInfo.put("buttonTxt", "查看退款");
                break;

            default:
        }


        buttonsList.add(buttonInfo) ;
    }

    public List<FmbOrderGoods> queryFmbOrderGoods(String orderSn) {
        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn)
                .select(FmbOrderGoods::getRecId,FmbOrderGoods::getPlayTime,FmbOrderGoods::getLeaveTime,
                FmbOrderGoods::getGoodsType,FmbOrderGoods::getGoodsId
                ,FmbOrderGoods::getGoodsPrice,FmbOrderGoods::getGoodsNumber,FmbOrderGoods::getGoodExt
                )
                ;
        //找到goods 信息
        final List<FmbOrderGoods> goodsList = orderGoodsService.list(goodsWr);
        return goodsList;
    }
    public List<FmbOrderGoods> queryFmbOrderGoodsAdmin(String orderSn) {
        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn)
                ;
        //找到goods 信息
        final List<FmbOrderGoods> goodsList = orderGoodsService.list(goodsWr);
        return goodsList;
    }

    public List<FmbOrderActions> queryFmbOrderActions(String orderSn) {
        Wrapper<FmbOrderActions> awr = new LambdaQueryWrapper<FmbOrderActions>()
                .eq(FmbOrderActions::getOrderSn, orderSn)
                .select(FmbOrderActions::getLogTime,FmbOrderActions::getActionNote,FmbOrderActions::getPayStatus,
                        FmbOrderActions::getShippingStatus,FmbOrderActions::getOrderStatus,FmbOrderActions::getAdminUid)
                ;
        final List<FmbOrderActions> orderActions = orderActionsService.list(awr);
        return orderActions;
    }


    /**
     * 前台 普通查询
     * @param orderSn
     * @return
     */
    public FmbOrderInfo queryFmbOrderInfo(String orderSn) {

        Wrapper<FmbOrderInfo> wrOrder = buildFmbOrderInfoWrapper(orderSn);
        final FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrder);
        return orderInfo;
    }

    public Wrapper<FmbOrderInfo> buildFmbOrderInfoWrapper(String orderSn) {
        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn)
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn,FmbOrderInfo::getPaySn
                        ,FmbOrderInfo::getOrderName,FmbOrderInfo::getShopUserId
                        , FmbOrderInfo::getOrderStatus, FmbOrderInfo::getConfirmStatus,FmbOrderInfo::getPayStatus
                        , FmbOrderInfo::getMoney, FmbOrderInfo::getCouponMoney, FmbOrderInfo::getShippingFee,
                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getCashMoney,FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getDiscountRate,FmbOrderInfo::getExtInfo,
                        FmbOrderInfo::getOrderCityId ,FmbOrderInfo::getPaySource,FmbOrderInfo::getOrderType
                        , FmbOrderInfo::getAid,FmbOrderInfo::getReferer,FmbOrderInfo::getOrderReferer
                        , FmbOrderInfo::getReceiver, FmbOrderInfo::getMobile,FmbOrderInfo::getUid
                        ,FmbOrderInfo::getCreateTime,FmbOrderInfo::getIsDelete,FmbOrderInfo::getPostscript
                        ,FmbOrderInfo::getCulturalCouponMoney,
                        FmbOrderInfo::getAppSubMoney,FmbOrderInfo::getShippingStatus,
                        FmbOrderInfo::getChangeToOrder,FmbOrderInfo::getBeforeOrderSn ,FmbOrderInfo::getUseCoupon,FmbOrderInfo::getMycpsId


                )
                ;
        return wrOrder;
    }

    /**
     * 后台查询 订单详情
     * @param orderSn
     * @return
     */
    @DS(DB_slave)
    public FmbOrderInfo queryFmbOrderInfoFromAdmin001(String orderSn) {

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn)
//                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn
//                        , FmbOrderInfo::getOrderStatus, FmbOrderInfo::getConfirmStatus
//                        , FmbOrderInfo::getMoney, FmbOrderInfo::getCouponMoney, FmbOrderInfo::getShippingFee,
//                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getCashMoney,
//                        FmbOrderInfo::getDiscountRate,FmbOrderInfo::getExtInfo,
//                        FmbOrderInfo::getOrderCityId ,FmbOrderInfo::getPaySource,FmbOrderInfo::getOrderType,FmbOrderInfo::getPaySource
//                        , FmbOrderInfo::getAid,FmbOrderInfo::getOrderName,FmbOrderInfo::getAddress,FmbOrderInfo::getReceiver
//                        , FmbOrderInfo::getReceiver, FmbOrderInfo::getMobile,FmbOrderInfo::getUid,FmbOrderInfo::getGoodsType
//                        ,FmbOrderInfo::getCreateTime,FmbOrderInfo::getIsDelete
//
//                        //                            后台多的字段
//                        ,FmbOrderInfo::getEditorNote
//                )
                ;
        final FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrder);
        Wrapper<FmbOrderExtInfo> wrOrderExt = new LambdaQueryWrapper<FmbOrderExtInfo>()
                .eq(FmbOrderExtInfo::getOrderSn, orderSn);
        final FmbOrderExtInfo orderExt = orderExtInfoService.getOne(wrOrderExt);

        if (orderExt!=null && StringUtils.isNotBlank(orderExt.getNote())){
            orderInfo.setShopNote(orderExt.getNote());
        }else{
            orderInfo.setShopNote("无");
        }
        return orderInfo;
    }



    public FmbOrderRelatedData queryFmbOrderRelatedData(String orderSn) {
        Wrapper<FmbOrderRelatedData> wrRel = new LambdaQueryWrapper<FmbOrderRelatedData>()
                .eq(FmbOrderRelatedData::getOrderSn, orderSn)
                .select(FmbOrderRelatedData::getContainTimes,FmbOrderRelatedData::getNumber,FmbOrderRelatedData::getReserveEndStatus);
        final FmbOrderRelatedData relatedData = relatedDataService.getOne(wrRel);
        return relatedData;
    }

    public FmbUsers queryFmbUser(Integer uid) {
        Wrapper<FmbUsers> userWr = new LambdaQueryWrapper<FmbUsers>()
                .eq(FmbUsers::getUid, uid)
                .select(FmbUsers::getUid,FmbUsers::getPhoneNumber,FmbUsers::getUsername,FmbUsers::getRegdate)
                ;
        final FmbUsers fmbUser = usersService.getOne(userWr);
        return fmbUser;
    }

    /**
     * 返回订单信息和 按钮信息
     * @param orderInfo
     * @param orderStatus
     * @return
     */
    public OrderDetailInfo buildOrderStatusAndButtonInfo(FmbOrderInfo orderInfo, Integer orderStatus) {

        final ArrayList<HashMap<String, Object>> buttonsList = new ArrayList<>();

        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();

        // `order_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态。0.未付款 1.等待发货 2.已发货 3.退货中 4.已退货 5.交易成功 6.交易关闭 7.支付尾款 8.支付成功后续处理中',
        switch (orderStatus.intValue())
        {
            case 0:
                orderDetailInfo.setStatusStr( "待付款");
                final FmbxScheduleJob s = self.queryPayEndTime(orderInfo.getOrderId());

                if (s!=null) {
                    final long lm = FmbDateUtil.localDateTime2Date(s.getScheduleRunTime()).getTime() - System.currentTimeMillis();
                    orderDetailInfo.setPayRemainSeconds((int) (lm/1000));
                    orderDetailInfo.setStatusDetail("");
                }

                addButtonInfo(buttonsList, BUTTON_TYPE_PAY);
                //待支付
                    if (orderInfo.getIsDelete()==0) {
                        addButtonInfo(buttonsList, BUTTON_TYPE_CANCEL);
                    }


                break;
            case 1:
                orderDetailInfo.setStatusStr( "等待发货");
                break;
            case 2:
                orderDetailInfo.setStatusStr( "已发货");
                break;
            case 3:
                orderDetailInfo.setStatusStr( "退货中");
                break;
            case 4:
                orderDetailInfo.setStatusStr( "已退货");
                break;
            case 5:
                orderDetailInfo.setStatusStr( "交易成功");
                orderDetailInfo.setStatusDetail("您的订单已交易成功。");

                //用房券生成的 酒店日历房
                if ( GOODS_TYPE_7== orderInfo.getGoodsType() ) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_2_HOTEL_RESERVE);

                    addButtonInfo(buttonsList, BUTTON_TYPE_2_RETURN_SERVICE);
                }
                if (orderInfo.getIsDelete()==0) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_DEL);
                }



                break;
            case 6:
                orderDetailInfo.setStatusStr( "交易关闭");
                orderDetailInfo.setStatusDetail("您的订单已交易关闭。");

                if (orderInfo.getIsDelete()==0) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_DEL);
                }

                if(showReBuyButton(orderInfo)){
                    addButtonInfo(buttonsList, BUTTON_TYPE_RE_BUY);
                }



                break;
            case 7:

                addButtonInfo(buttonsList, BUTTON_TYPE_PAY);

                orderDetailInfo.setStatusStr( "支付尾款");
                orderDetailInfo.setStatusDetail("订单需补尾款，请尽快支付。");
                break;
            case 8:

                orderDetailInfo.setStatusStr( "处理中");
                orderDetailInfo.setStatusDetail( "订单仍在确认中，确认成功后会短信通知您。");

                break;
        }

        orderDetailInfo.getButtonsList().addAll(buttonsList) ;
        return orderDetailInfo;
    }

    private boolean showReBuyButton(FmbOrderInfo orderInfo) {

        //酒店预约单 就不要显示再次购买按钮了
        if (GOODS_TYPE_6 ==orderInfo.getGoodsType() && 2==orderInfo.getOrderType()) {
            return false ;
        }

        return true ;
    }



    /**
     * 查询 订单自动关闭任务表 找到自动关闭任务的结束时间和现在对比
     * @param orderId
     * @return
     */
    public FmbxScheduleJob queryPayEndTime(Integer orderId) {

        Wrapper<FmbxScheduleJob> wr = new LambdaQueryWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobType,JOB_NAME_CANCELORDERJOB)
                .eq(FmbxScheduleJob::getJobFmbxId,orderId)
                .select(FmbxScheduleJob::getScheduleRunTime)
                .orderByDesc(FmbxScheduleJob::getJobId)
                .last(" limit 1 ")
                ;
        final FmbxScheduleJob one = scheduleJobService.getOne(wr);
//        if (one==null) {
//            throw new BadLogicException("订单自动关闭时间异常[3112]");
//        }

        return one ;


    }


    /**
     *
     * @param userId
     * @param phoneNumber
     * @return
     */
    public Map queryPreOrderReceiver(int userId, String phoneNumber) {
        Map user = new HashMap();
        //查询最后一个酒店订单
        Wrapper<FmbOrderInfo> wr = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getUid,userId)
//                .in(FmbOrderInfo::getGoodsType, Arrays.asList( new Integer[]{4, 6, 15}))
                .in(FmbOrderInfo::getOrderStatus, Arrays.asList( new Integer[]{1,2,5,8}))
                .select(
                        FmbOrderInfo::getMobile,
                        FmbOrderInfo::getOrderSn,
                        FmbOrderInfo::getReceiver
                )
                .orderByDesc(FmbOrderInfo::getOrderId)
                .last(" limit 1");
        FmbOrderInfo orderInfo = orderInfoService.getOne(wr);
//        System.out.println(JSON.toJSONString(orderInfo));
        //查询最后一个订单

        if(null != orderInfo){
            user.put("mobile",orderInfo.getMobile());

            if (orderInfo.getReceiver().indexOf(",")!=-1) {
                user.put("receiver",orderInfo.getReceiver().split(",")[0]);
            }else {
                user.put("receiver",orderInfo.getReceiver());
            }
//            user.put("orderSn",orderInfo.getOrderSn());
        } else {
            user.put("mobile",phoneNumber);
            user.put("receiver","");
//            user.put("orderSn","");
        }
        return user  ;
    }

    public void confirmOrder(HashMap<String, Object> resultMap, NowUser nowUser,ReqConfirmOrder input) {


        if (nowUser == null) {
            throw new BadLogicException("需要登录后操作[100]");
        }

        self.chooseBestCoupon(nowUser.getUserId(),input,resultMap,"confirmOrder");

        resultMap.put("preOrderInfo",queryPreOrderReceiver(nowUser.getUserId(),nowUser.getUserInfo().getPhoneNumber())) ;


        //处理余额
        final FmbUserExtinfo fmbUserExtinfo = self.queryUserBalance(nowUser.getUserId());
        BigDecimal userBalance = BigDecimal.ZERO ;
        BigDecimal balanceCanUse = BigDecimal.ZERO ;
        resultMap.put("balanceShow",0) ;
        resultMap.put("balanceTip", BALANCE_TIP) ;
        resultMap.put("balanceHave",BigDecimal.ZERO) ;
        resultMap.put("balanceCanUse",balanceCanUse) ;
        if (fmbUserExtinfo != null) {
            userBalance = fmbUserExtinfo.getMoney() ;
            resultMap.put("balanceHave",userBalance) ;
            if (userBalance.compareTo(BigDecimal.ZERO)==1) {
                resultMap.put("balanceShow",1) ;
            }
        }

        final FmbxActivity activity = queryFmbxActivity(input.getXaid());
        resultMap.put("activityTitle",activity.getTitle()) ;

        ReqHotel inputBasic = new ReqHotel();
        final ReqGenSku reqGenSku = input.getReqGenSkus().get(0);
        final Integer skuId = reqGenSku.getSkuId();
        inputBasic.setSkuId(skuId);
        inputBasic.setPlayDate(reqGenSku.getCheckInDate());
        inputBasic.setLeaveDate(reqGenSku.getCheckOutDate());

        BeanUtils.copyProperties(input,inputBasic);

        HashMap<String, Object> basicInfo = new HashMap<>();
        resultMap.put("basicInfo", basicInfo);

        ConfirmOrderMoneyInfo consumeList = (ConfirmOrderMoneyInfo) resultMap.get("consumeList");


        resultMap.put("skutype",consumeList.getFmbxSku().getSkuType()) ;

        FmbxSuite suiteInfo = null ;
        if (consumeList.getFmbxSku().getSkuType()==SKU_HOTEL_DATE) {
            frontHotelService.selectOrderRoomInfo(basicInfo, inputBasic, nowUser);

            //查询 套餐信息
            Wrapper<FmbxSuiteRoomSku> wrRoom = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                    .eq(FmbxSuiteRoomSku::getSkuId,skuId)
                    .select(FmbxSuiteRoomSku::getSuiteId)
                    ;
            final FmbxSuiteRoomSku one = roomSkuService.getOne(wrRoom);
            Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
                    .eq(FmbxSuite::getSuiteId,one.getSuiteId())
                    .select(FmbxSuite::getIsInvoice,FmbxSuite::getSuiteId);
            suiteInfo = suiteService.getOne(wrSuite);

        }else if(consumeList.getFmbxSku().getSkuType()==SKU_HOTEL_RESERVE){

            final FmbxHotelReserveSku reserveSku = orderOfHotelReserveService.querySkuReserve(consumeList.getFmbxSku().getSkuId());
            final FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());

            String roomName = "" ;
            if (reserveSku.getHotelDateSkuId()>0) {
                final Integer roomId = reserveSku.getRoomId();
                final FmbxBpsRoom bpsRoom = queryRoomName(roomId);
                roomName = bpsRoom.getRoomName();
                reserveSku.setSkuName(reserveSku.getSkuName());
            }
            else if (reserveSku.getHotelDateSkuId()==0) {
                final List<Map> maps = suiteDao.selctRoomInfoOfSuite(reserveSku.getSuiteId());
                roomName = maps.stream().map(v -> (String) v.get("room_name")).collect(Collectors.joining("/"));
                reserveSku.setSkuName(reserveSku.getSkuName()+FmbConstants.RESERVE_SKU_NAME_SINGLE_POSTFIX);
            }

            final String yxq = FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseStartTime()) + "至" + FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseEndTime());

            String buyNumberStr = "" ;

            final ArrayList<String> buyLimitStr = buildReserveSkuBuyLimit( reserveSku.getFlagBuyLimit(), reserveSku.getEachOrderMinNum(), reserveSku.getEachOrderMaxNum(), reserveSku.getMaxBuyAllNum());

            basicInfo.put("buyLimitStr", buyLimitStr.stream().collect(Collectors.joining(",")));
            //入住有效期
            basicInfo.put("validReserveDateInfo", yxq);
            basicInfo.put("sku", reserveSku);
            basicInfo.put("suite", suite);
            basicInfo.put("userInfoTxt", FrontRoomSkuOrderDomain.userInfoMap);
            FrontResSkuDomain res = new FrontResSkuDomain();
            res.setSuiteContent(suite.getSuiteContent());
            res.setNightMin(suite.getNightMin());
            res.setTotalNeight(suite.getTotalNeight());
            final List<SuiteContent> suiteContentList = res.getSuiteContentGroupList();
            if (suiteContentList != null) {
                basicInfo.put("suiteContent", suiteContentList);

                //确认订单页 住 食 享 修改"住"信息, 把房型名字写入
                for (SuiteContent suiteContent : suiteContentList) {
                    if( "住".equals(suiteContent.getType())){
                        suiteContent.setIntro(roomName);
                    }
                }

                suite.setSuiteContent("");
            }

            //查询 套餐信息
            Wrapper<FmbxHotelReserveSku> wrRoom = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSkuId,skuId)
                    .select(FmbxHotelReserveSku::getSuiteId)
                    ;
            final FmbxHotelReserveSku one = reserveSkuService.getOne(wrRoom);
            Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
                    .eq(FmbxSuite::getSuiteId,one.getSuiteId())
                    .select(FmbxSuite::getIsInvoice,FmbxSuite::getSuiteId);
            suiteInfo = suiteService.getOne(wrSuite);


        }

        //处理发票信息
        if (suiteInfo != null) {
//            '发票政策:0-不支持开票,1-父母邦开票,2-商家开票',
            final Integer isInvoice = suiteInfo.getIsInvoice();

            HashMap<String, Object> invoice = new HashMap<>();

            if (0==isInvoice) {
                invoice.put("flagInvoice",0) ;
                invoice.put("invoiceTxt","") ;
            }else if (1==isInvoice) {
                invoice.put("flagInvoice",1) ;
                invoice.put("invoiceTxt","消费或入住后在个人中心-我的发票开具") ;

                //根据房券或日历房

            }else if (2==isInvoice) {
                invoice.put("flagInvoice",1) ;
                invoice.put("invoiceTxt","消费后离店时请前往商户前台咨询开具") ;

            }

            resultMap.put("invoice",invoice) ;

        }

        logger.info("consumeList={}",consumeList);

        final List<ConfirmOrderMoneyGroup> groups = consumeList.getGroups();

        final BigDecimal totalMoneyWithOutUseBalance = consumeList.getTotalMoney();


        boolean canUseBalance = totalMoneyWithOutUseBalance.compareTo(new BigDecimal("1")) == 1;

        /**
         * 酒店房券不能使用余额
         */
        if (StringUtils.isNotBlank (input.getHotelReserveCode()) && consumeList.getFmbxSku().getSkuType()==SKU_HOTEL_DATE) {
            canUseBalance = false ;
        }

        // 商品金额大于1 且 用户余额大于0
        if (  canUseBalance &&  userBalance.compareTo(BigDecimal.ZERO)==1) {

            /**
             * ***********************
             * 目前确认订单没有考虑运费  *
             * ***********************
             */
            //使用余额 用户至少需要支付1元

            if (totalMoneyWithOutUseBalance.subtract(userBalance).compareTo(new BigDecimal("1"))==1) {
                balanceCanUse = userBalance ;
            }else {
                balanceCanUse = totalMoneyWithOutUseBalance.subtract(new BigDecimal("1")) ;
            }

            resultMap.put("balanceCanUse",balanceCanUse) ;

            if (1==input.getUseBalance()) {
                consumeList.addNewGroupOfDiscount("优惠","余额",balanceCanUse);
                consumeList.setTotalMoney(totalMoneyWithOutUseBalance.subtract(balanceCanUse));
            }
        }

        if (!canUseBalance) {
            resultMap.put("balanceShow",0) ;
        }


    }

    public FmbxBpsRoom queryRoomName(Integer roomId) {
        Wrapper<FmbxBpsRoom> wrRoom = new LambdaQueryWrapper<FmbxBpsRoom>()
                .eq(FmbxBpsRoom::getRoomId, roomId)
                .select(FmbxBpsRoom::getRoomName,FmbxBpsRoom::getRoomId)
                ;
        final FmbxBpsRoom bpsRoom = bpsRoomService.getOne(wrRoom);
        return bpsRoom;
    }


    /**
     * 构造房券 购买数量限制信息
     * @param flagBuyLimit
     * @param eachOrderMinNum
     * @param eachOrderMaxNum
     * @param maxBuyAllNum
     * @return
     */
    public static ArrayList<String> buildReserveSkuBuyLimit( Integer flagBuyLimit, Integer eachOrderMinNum, Integer eachOrderMaxNum, Integer maxBuyAllNum) {
        final ArrayList<String> buyLimitStr = new ArrayList<>();
        if (flagBuyLimit ==1) {
            if (eachOrderMinNum.intValue() != 1) {
                buyLimitStr.add("每单最少购买" + eachOrderMinNum.intValue());
            }
            if (eachOrderMaxNum.intValue() != 9999) {
                buyLimitStr.add("每单最多购买" + eachOrderMaxNum.intValue());
            }
            if (maxBuyAllNum.intValue() != 9999) {
                buyLimitStr.add("每个用户最多购买" + maxBuyAllNum.intValue());
            }
        }
        return buyLimitStr;
    }


    public void orderSuccessPayFromRedis(OrderSuccessPayDomain orderSuccessPayDomain) {


        self.orderSuccessPay(orderSuccessPayDomain.getOrderSn(),orderSuccessPayDomain);
    }

    /**
     * 拿房券进入 酒店日历房 确认订单
     * @param resultMap
     * @param nowUser
     * @param input
     */
    @DS(DB_slave)
    public void hotelCodeQueryInConfirmPage(HashMap<String, Object> resultMap, NowUser nowUser, ReqConfirmOrder input) {

        final OrderSysInfo orderSysInfo = new OrderSysInfo();
        //查询房券信息
        self.checkHotelReserveCode(input.getHotelReserveCode(),nowUser.getUserId(), orderSysInfo,"hotelCodeQueryInConfirmPage",null) ;




        //查询上一个订单 收货人和电话
        resultMap.put("preOrderInfo",queryPreOrderReceiver(nowUser.getUserId(),nowUser.getUserInfo().getPhoneNumber())) ;

        HashMap<String, Object> basicInfo = new HashMap<>();
        resultMap.put("basicInfo", basicInfo);

        //查询房券sku 和套餐
        final FmbxHotelReserveSku reserveSku =
                orderOfHotelReserveService.querySkuReserve(orderSysInfo.getHotelCode().getTicketId());
        final FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());



        //查询 当前套餐对应的 日历房sku 的房型列表
        List<FrontRoomDomain> frontRoomDomainList =  frontHotelService.queryFrontRoomDomains(reserveSku.getSuiteId(),true,false);

        resultMap.put("hotelReserveSplit",2) ;
        resultMap.put("hotelReserveSplitRoomId",0) ;

        //拆分房券
        if ( orderSysInfo.getReserveSuiteSplit()==1) {

            //过滤 房型 ,需要比购买的房券价格高的房型才能展示出来
            Wrapper<FmbxHotelReserveSku> wrReserve = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                    .eq(FmbxHotelReserveSku::getSuiteId,suite.getSuiteId())
                    .gt(FmbxHotelReserveSku::getHotelDateSkuId,0)
                    .eq(FmbxHotelReserveSku::getStatus,1)
                    .ge(FmbxHotelReserveSku::getGoodsPrice,orderSysInfo.getReserveCodeFromOrderGoods().getGoodsPrice())
                    .select(FmbxHotelReserveSku::getRoomId,FmbxHotelReserveSku::getHotelDateSkuId)
                    ;
            final Set<Integer> roomIdSet =
                    reserveSkuService.list(wrReserve).stream().map(FmbxHotelReserveSku::getRoomId).collect(Collectors.toSet());

            final Iterator<FrontRoomDomain> iterator = frontRoomDomainList.iterator();

            //过滤不符合要求的房型
            while (iterator.hasNext()) {
                final FrontRoomDomain roomDomain = iterator.next();
                if (!roomIdSet.contains(roomDomain.getRoomId())) {
                    iterator.remove();
                }
            }



            resultMap.put("hotelReserveSplit",orderSysInfo.getReserveSuiteSplit()) ;
            resultMap.put("hotelReserveSplitRoomId",orderSysInfo.getReserveCodeSourceSku().getRoomId()) ;

        }

        SuiteContent s0 = new SuiteContent();
        s0.setType("住");
        s0.setCount(orderSysInfo.getReserveCodeSourceSuite().getTotalNeight());
        s0.setCountUnit("晚");



        if ( orderSysInfo.getReserveSuiteSplit()==1) {
            final FmbxBpsRoom fmbxBpsRoom = queryRoomName(orderSysInfo.getReserveCodeSourceSku().getRoomId());
            suite.setName(suite.getName()+" ("+fmbxBpsRoom.getRoomName()+")");

            s0.setIntro(fmbxBpsRoom.getRoomName());

        }else{
            suite.setName(suite.getName()+FmbConstants.RESERVE_SKU_NAME_SINGLE_POSTFIX);

            String rooms = frontRoomDomainList.stream().map(FrontRoomDomain::getRoomName).collect(Collectors.joining("/"));
            s0.setIntro(rooms);
        }





        //并返回
        resultMap.put("roomList", frontRoomDomainList);
        resultMap.put("hotelCodeEachNightPayed", orderSysInfo.getHotelCodeEachNightPay());
        resultMap.put("hotelCodeSkuId", reserveSku.getSkuId());


        //查询活动 信息
        final FmbxActivity activity = queryFmbxActivity(orderSysInfo.getReserveCodeFromOrderInfo().getAid());

        Wrapper<FmbxBpsContent> wrContent = new LambdaQueryWrapper<FmbxBpsContent>()
                .eq(FmbxBpsContent::getBpsId,activity.getBpsId())
                .select(FmbxBpsContent::getPolicy);
        //查询政策信息
        final FmbxBpsContent bpsContent = bpsContentService.getOne(wrContent);

        HashMap<String, Object> policy = new HashMap<>();
        policy.put("policy", bpsContent.getPolicy());
        policy.put("riskWarningTip", suite.getRiskWarningTip());



        basicInfo.put("orderRoomInfo", policy);
        basicInfo.put("suite", suite);
        final List<SuiteContent> suiteContentList = FrontRoomSkuDomain.getSuiteContentsGroup(suite.getSuiteContent());
        if (suiteContentList != null) {
            basicInfo.put("suiteContent", suiteContentList);

            final long count = suiteContentList.stream().filter(s -> "住".equals(s.getType())).count();
            if (count==0) {
                suiteContentList.add(0,s0);
            }

            suite.setSuiteContent("");
        }

        self.buildHotelCodeUseRoleInfo(orderSysInfo,resultMap) ;

    }




    /**
     * 查询商户信息
     * @param bpsId
     * @return
     */
    public FmbxBps queryBps(Integer bpsId) {
        LambdaQueryWrapper<FmbxBps> wrp = new LambdaQueryWrapper<FmbxBps>();
        wrp
                .eq(FmbxBps::getBpsId, bpsId)
                .select(FmbxBps::getName, FmbxBps::getPhone, FmbxBps::getAddress, FmbxBps::getProvinces, FmbxBps::getArea, FmbxBps::getLatitude, FmbxBps::getLongitude
                ,FmbxBps::getPlayTime,FmbxBps::getLeaveTime,
                        FmbxBps::getStarLevel
                )
        ;

        FmbxBps bps = bpsService.getOne(wrp);
        return bps;
    }



    /**
     * 订单操作记录
     * @return
     */

    public List<AdminOrderActionDomain> selectAdminOrderAction(String ordersn) {

        HashMap<String, Object> p = new HashMap<>();
        p.put("ordersn", ordersn);

        List<AdminOrderActionDomain> lists = frontSkuDao.selectAdminOrderAction(p);

        return lists ;

    }


    public boolean saveOrderNote(String orderSn, String note){



        Wrapper<FmbOrderInfo> upW = new LambdaUpdateWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,orderSn)
                .setSql(" editor_note = concat(editor_note,'\r\n','"+FmbDateUtil.nowDateTimeStr()+":"+note.replace("'","\"")+"')   ")
                ;
        return  orderInfoService.update(upW);

    }




    public HashMap<String, Object> getOrderHotelEmailTemp(ReqFmbOrderHotel fmbOrderHotel) {

        //设置参数
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        ReqorderDetail inputx = new ReqorderDetail();
        inputx.setAppid("");
        inputx.setOpVersion("");
        inputx.setVersion("");
        inputx.setOrderSn(fmbOrderHotel.getOrderSn());
        self.orderDetail(resultMap, inputx,null,ADMIN001);

        FmbOrderInfo orderInfo = (FmbOrderInfo) resultMap.get("orderInfo");

        //生成二维码
        /*UploadFileOutInfo qrCodePath = UploadFileUtil.prepareFilePath(FmbServerConfig.getFmbImgDir(), FmbServerConfig.getFmbImgUrl(), "qrCode", "png");
        QrCodeUtil.generate(String.valueOf(orderInfo.getOrderId()), 300, 300, FileUtil.file(qrCodePath.getFilePath()));
        String qrCodeUrl = qrCodePath.getOutUrl();

        resultMap.put("qrCodeUrl",qrCodeUrl);*/
        resultMap.put("dfdStatusStr",ReqFmbOrderHotel.formTypeMap.get(fmbOrderHotel.getFormType())); // 1新订 2改签 3变更 4取消 5其他
        resultMap.put("dfdStatus",fmbOrderHotel.getFormType());
        resultMap.put("ticketComment",fmbOrderHotel.getTicketComment());
        resultMap.put("fmbComment",fmbOrderHotel.getFmbComment());
        resultMap.put("adminName",fmbOrderHotel.getAdminName());
        resultMap.put("adminDate",fmbOrderHotel.getNowTimeStr());
        resultMap.put("serviceTel",FmbServerConfig.getServiceTel());//客服电话
        resultMap.put("serviceFax",FmbServerConfig.getServiceFax());//客服电话

        FreeMarkerConfigurer freeMarkerConfigurer = new FreeMarkerConfigurer();
        freeMarkerConfigurer.setConfiguration(freeMarkerConfig);
        String mailText = "";
        try {
            Template template = freeMarkerConfigurer.getConfiguration().getTemplate("hotelFrom.ftl");
            mailText = FreeMarkerTemplateUtils.processTemplateIntoString(template,resultMap);
        } catch (IOException | TemplateException ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        resultMap.put("mailText",mailText);
        return resultMap;
    }


    /**
     * 检查    week是否在 房券的限制预约星期里
     * @param week 星期   1-星期1   7-星期日
     * @param limitWeek  房券限制的星期
     * @return
     */
    public  boolean reserveWeekIsLimit( Integer week,  Set<Integer> limitWeek){

        return  limitWeek.contains(week) ;


    }

    /**
     * 检查  dateStr  是否在 房券的限制预约日期里
     * @param dateStr 核查日期
     * @param limitsDate  房券限制的日期
     * @return
     */
    public  boolean reserveDateIsLimit(String dateStr,   Set<String> limitsDate){

        return  (limitsDate.contains(dateStr)) ;


    }


    /**
     *
     * @param resultMap
     * @param input
     * @param nowUser
     * @param source
     *             auto_cancel  超时未支付 ,自动取
     *             front        前台用户取消
     *             admin001     后台
     *             fmbx_hotel_reserve_return_need_part_money      后台直接发起酒店预约单退款而且需要补差价
     *
     */
    @DSTransactional
    @DS(DB_master)
    public void cancelOrderByOrderSn(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser, String source) {

        if (FRONT.equals(source) && nowUser==null ) {
            throw new BadLogicException("需要登录[3489]");
        }

        resultMap.put("result",0) ;

        final boolean cancelOrder = self.cancelOrder(null, source, 2, input.getOrderSn(),input);
        if (cancelOrder) {
            resultMap.put("result",1) ;
        }
    }


    public void roomNumberCal(HashMap<String, Object> resultMap, ReqorderDetail input) {
        final ArrayList<RoomNumStatus> list = OrderOfHotelDateService.roomNumGen(input.getSuiteNightTotal(), input.getNightNum());

        logger.info("list={}",list);
        final List<RoomNumStatus> collect = list.stream().filter(roomNumStatus -> roomNumStatus.getStatus() == 1).collect(Collectors.toList());

        resultMap.put("roomNumMin", 0) ;
        resultMap.put("message", "") ;
        if (input.getNightNum()% input.getSuiteMinNight()!=0){
            resultMap.put("message", "入住晚数必须是"+input.getSuiteMinNight()+"的倍数") ;
        }

//        resultMap.put("roomNumInfo", collect) ;
        if (!collect.isEmpty()) {
            resultMap.put("roomNumMin", collect.get(0).getRoomNum()) ;
        }

    }

    @DS(DB_slave)
    public void hotelOrderCode(HashMap<String, Object> resultMap, String orderSn, NowUser nowUser, String from) {

        if (FRONT.equals(from) &&   nowUser == null) {
            throw new BadLogicException("用户需要登录[3104]");
        }
        FmbOrderInfo orderInfo = null ;
        if (FRONT.equals(from)){
            orderInfo = self.queryFmbOrderInfo(orderSn);
        }
        if (FRONT.equals(from) &&   nowUser.getUserId() != orderInfo.getUid().intValue()) {
            throw new BadLogicException("用户信息异常[3100]");
        }

        final List<FmbOrderGoods> goodsList = self.queryFmbOrderGoods(orderSn);

        if (goodsList.isEmpty()) {
            throw new BadLogicException("订单不存在[3823]");
        }

        final FmbOrderGoods goods = goodsList.get(0);

        final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());
        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

        int  canDivide = 0 ;
        ArrayList<JSONObject> jsonObjects = new ArrayList<>() ;

        if (GOODS_TYPE_7== goods.getGoodsType()) {
            canDivide = skuDetail.getReserveSku().getCanDivide() ;
            List<FmbReserveHotelCodes> codes = orderDetailQueryHotelCode(orderSn, canDivide);

            final Integer skuId = skuDetail.getReserveSku().getSkuId();


            String alertStr = checkReserveSkuReserveDateInfo(skuId );

            jsonObjects = new ArrayList<>();
            for (FmbReserveHotelCodes code : codes) {
                JSONObject jn = new JSONObject();
                jn.put("codeDesc",code.getCodeDesc());
                jn.put("status",code.getStatus());
                jn.put("code",code.getCode());

                if (2==code.getStatus()) {
                    jn.put("alertStr",CODE_ALERT);
                }else {
                    jn.put("alertStr",alertStr);
                }

                jsonObjects.add(jn);
            }
        }
        resultMap.put("hotelCode", jsonObjects) ;

    }

    /**
     * 根据 房券skuId 查询 预约有效期 ,如果异常就返回 异常描述文本,正常返回 ""
     * @param skuId
     * @return
     */
    private String checkReserveSkuReserveDateInfo(Integer skuId) {
        String alertStr = "" ;
        Wrapper<FmbxHotelReserveSku> wrRoom = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId, skuId)
                .select(FmbxHotelReserveSku::getReserveValidityStartTime,FmbxHotelReserveSku::getReserveValidityEndTime)
                ;
        final FmbxHotelReserveSku nowReserveSkuInfo = reserveSkuService.getOne(wrRoom);

        final LocalDateTime now = LocalDateTime.now();

//        logger.info("nowReserveSkuInfo={}",nowReserveSkuInfo);
        if ( nowReserveSkuInfo!=null &&    nowReserveSkuInfo.getReserveValidityStartTime()!=null  && nowReserveSkuInfo.getReserveValidityEndTime()!=null &&
                !( now.isAfter(nowReserveSkuInfo.getReserveValidityStartTime()) && now.isBefore(nowReserveSkuInfo.getReserveValidityEndTime())   )   ) {
            alertStr = "该房券已过操作有效期无法预约" ;
        }
        return alertStr;
    }

    public void getFmbMycpsCommissionOrder(HashMap<String, Object> resultMap, String orderSn, NowUser nowUser, String from) {
        final List<FmbMycpsCommissionOrder> mycpsOrderList = self.queryFmbMycpsCommissionOrder(orderSn);
        for (FmbMycpsCommissionOrder order : mycpsOrderList){
            Wrapper<FmbMycps> wrMycps = new LambdaQueryWrapper<FmbMycps>()
                    .eq(FmbMycps::getMid, order.getMid())
                    .select(FmbMycps::getMname);
            FmbMycps fmbMycps = mycpsService.getOne(wrMycps);
            Wrapper<FmbUsers> wrUser = new LambdaQueryWrapper<FmbUsers>()
                    .eq(FmbUsers::getUid, order.getUid())
                    .select(FmbUsers::getUsername);
            FmbUsers fmbUsers = usersService.getOne(wrUser);
            String desc = FmbMycpsCommissionOrder.typeMap.get(order.getType())+(order.getType()==1||order.getType()==2?"分销商":"")
                    +":" + fmbMycps.getMname()+":" + fmbUsers.getUsername()+" 佣金金额:¥"+ order.getRealCommission()
                    +" 佣金"+FmbMycpsCommissionOrder.statusMap.get(order.getCommissionStatus())
                    +" 佣金解冻时间："+ (order.getValidTime()!=null? order.getValidTime().format(FmbDateUtil.FORMATTER_FULL):"");
            order.setMycpsDesc(desc);
        }
        resultMap.put("mycpsCommissionOrder", mycpsOrderList) ;
    }

    public List<FmbMycpsCommissionOrder> queryFmbMycpsCommissionOrder(String orderSn) {
        Wrapper<FmbMycpsCommissionOrder> wrRecords = new LambdaQueryWrapper<FmbMycpsCommissionOrder>()
                .eq(FmbMycpsCommissionOrder::getOrderSn, orderSn)
                .select(FmbMycpsCommissionOrder::getMid, FmbMycpsCommissionOrder::getUid, FmbMycpsCommissionOrder::getOrderSn,
                        FmbMycpsCommissionOrder::getOrderMode, FmbMycpsCommissionOrder::getCommissionRate, FmbMycpsCommissionOrder::getType,
                        FmbMycpsCommissionOrder::getRealCommission, FmbMycpsCommissionOrder::getCommissionStatus, FmbMycpsCommissionOrder::getValidTime)
                .orderByAsc(FmbMycpsCommissionOrder::getType);
        final List<FmbMycpsCommissionOrder> mycpsList = mycpsCommissionOrderService.list(wrRecords);
        return mycpsList;
    }

    public Map<String, Integer> hasInvoiceByOrderSns(List<String> ordersnList) {
        Map<String, Integer> orderSnInvoiceMap = new HashMap<String, Integer>();
        String fmbKefuUrl = StringUtils.isBlank(FmbServerConfig.getFmbKefuUrl())?"":FmbServerConfig.getFmbKefuUrl();
        //请求客服接口 是否有发票
        final HttpRequest post = HttpUtil.createPost(fmbKefuUrl+"/fmbcc/api/kefusheet/query_invoice");
        try {
            String param = "ordersnList="+java.net.URLEncoder.encode(JSONArray.toJSONString(ordersnList),"utf-8")+"&version=2.0";
            post.form("ordersnList", JSONArray.toJSONString(ordersnList)) ;
            post.form("version", "2.0") ;
            final String signV = MD5Util.md5(param + "4dx3MATuBGZdOzFIH5W3DSJdEUCdQZRo");
            post.form("sign", signV) ;
            post.form("appid", 638482017) ;
            final String body = post.execute().body();
            if (StringUtils.isNotBlank(body) && body.indexOf("success")!=-1) {
                // {{"info":[{"has_invoice":false,"order_sn":"20220902133236786834"},{"has_invoice":true,"order_sn":"20220228103719825332"}],"result":"success"}
                final JSONObject data = JSON.parseObject(body);
                JSONArray invoiceArr = data.getJSONArray("info");
                for (int i = 0; i < invoiceArr.size(); i++) {
                    JSONObject invoicejson = JSON.parseObject(invoiceArr.getString(i));
                    String order_sn = invoicejson.getString("order_sn");
                    Boolean has_invoice = invoicejson.getBoolean("has_invoice");
                    orderSnInvoiceMap.put(order_sn,has_invoice?1:0);
                }

            }
        }catch (Exception ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        return orderSnInvoiceMap;
    }

    /**
     * ****************************************************************
     *   酒店房券退款页面
     ******************************************************************
     **/
    public void hotelReserveCodeReturn(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser, String from) {
        String orderSn = input.getOrderSn() ;

        if (StringUtils.isBlank(orderSn)) {
            throw new BadLogicException("订单号异常[3194]");
        }

        if (FRONT.equals(from) &&   nowUser == null) {
            throw new BadLogicException("用户需要登录[3104]");
        }
        FmbOrderInfo orderInfo = null ;
        if (FRONT.equals(from)){
            orderInfo = self.queryFmbOrderInfo(orderSn);
        }
        if (FRONT.equals(from) &&   nowUser.getUserId() != orderInfo.getUid().intValue()) {
            throw new BadLogicException("用户信息异常[3100]");
        }

        if (GOODS_TYPE_7!= orderInfo.getGoodsType()) {
            throw new BadLogicException("不是房券预约订单[3207]");
        }


        final FmbOrderDetailQueryResult orderQueryResult = returnService.orderReturnBeginQuery(orderSn, null);

        final List<FmbReserveHotelCodes> codes = orderQueryResult.getHotelCodeCanReturn();
        resultMap.put("codes", codes) ;

        final int size = codes.size();

        final BigDecimal remainReturnMaxMoney = orderQueryResult.getRemainReturnMaxMoney();

        final BigDecimal returnCashMoney = orderQueryResult.getReturnedCashMoney() ;

        final ArrayList<HashMap<String, Object>> returnMoneyList = new ArrayList<>();

        resultMap.put("returnMoneyList", returnMoneyList) ;
        for (int i = 1; i <= size; i++) {

            FmbOrderReturns saveDB = new FmbOrderReturns();
            BigDecimal inputMoney = new BigDecimal(i).multiply(orderQueryResult.getSkuGoodsPriceSubCoupon());
            //
            if(inputMoney.compareTo(remainReturnMaxMoney)==1){
                inputMoney = remainReturnMaxMoney ;
            }

            returnService.returnMoneyMathCashFirst(inputMoney, orderQueryResult.getOrderInfo().getRealpayMoney(),
                    orderQueryResult.getOrderInfo().getCashMoney(), orderQueryResult.getAlreadyReturnMoney().subtract(returnCashMoney),
                    returnCashMoney,saveDB);

            HashMap<String, Object> r = new HashMap<>();
            r.put("money", saveDB.getMoney());
            r.put("cash", saveDB.getUserCashMoney());
            r.put("submitReturnMoney", saveDB.getMoney().add(saveDB.getUserCashMoney()));
            r.put("selected", i);
            if(i==orderQueryResult.getOrderGoods().getGoodsNumber()){
                r.put("coupon", orderQueryResult.getOrderInfo().getCouponSubMoney());
            }else {
                r.put("coupon", orderQueryResult.getEachSkuCouponDivide().multiply(new BigDecimal(i)));
            }

            returnMoneyList.add(r) ;
        }


        resultMap.put("new_reason_list",NEW_REASON_LIST);
        resultMap.put("reason_list_me",REASON_LIST_ME);
        resultMap.put("reason_list_shop",REASON_LIST_SHOP);
        resultMap.put("return_instructions",RETURN_INSTRUCTIONS);

    }

    public void hotelReserveCodeReturnSubmit(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser, String from) {

        String orderSn = input.getOrderSn() ;

        if (StringUtils.isBlank(orderSn)) {
            throw new BadLogicException("订单号异常[3194]");
        }
        if (FRONT.equals(from) &&   nowUser == null) {
            throw new BadLogicException("用户需要登录[3104]");
        }
        FmbOrderInfo orderInfo = null ;
        if (FRONT.equals(from)){
            orderInfo = self.queryFmbOrderInfo(orderSn);
        }
        if (FRONT.equals(from) &&   nowUser.getUserId() != orderInfo.getUid().intValue()) {
            throw new BadLogicException("用户信息异常[3100]");
        }

        if (GOODS_TYPE_7!= orderInfo.getGoodsType()) {
            throw new BadLogicException("不是房券预约订单[3207]");
        }

        final AdminOrder adminOrder = new AdminOrder();

        adminOrder.setReturnClass(0);
        adminOrder.setReturnMoney(input.getReturnMoney());
        adminOrder.setReasonTxt("前台用户发起房券退款");
        adminOrder.setUploadFile(new ArrayList<String>());
        adminOrder.setInternalUploadFile(new ArrayList<String>());
        adminOrder.setReceiver(orderInfo.getReceiver());
        adminOrder.setMobile(orderInfo.getMobile());
        adminOrder.setSendSms(0);
        adminOrder.setSmsTemplate("");
        adminOrder.setInternalNote("房券发起退货");
        adminOrder.setReserveCode(input.getReserveCode());
        adminOrder.setOrderSn(orderSn);
        adminOrder.setReason("");
        adminOrder.setFrontReturnReserveCode(1);

        if(StringUtils.isNotBlank(input.getReason())){
            adminOrder.setReason(input.getReason());
        }

        adminOrder.setReasonTxt("");
        if(StringUtils.isNotBlank(input.getReasonUserInput())){
            adminOrder.setReasonTxt(input.getReasonUserInput());
        }

        SessionUser userInfo = new SessionUser();
        userInfo.setUserId(orderInfo.getUid());
        userInfo.setUsername(orderInfo.getReceiver());
        userInfo.setUserType(SESSIONUSER_FRONT);

        returnService.normalReturn(adminOrder,resultMap,userInfo);


    }

    /**
     * *****************************************************************
     * 订单1小时后 将关闭 发短信 提醒支付
    * *****************************************************************
     **/
    public void sendSmsOrderWillCloseIn1Hour(Integer orderId) {
        //【父母邦】呵呵，您购买的：sam酒店图文视频，订单号：20230413204610966331 还未付款，请尽快完成支付。否则订单将在1小时后关闭，届时若仍需购买，请重新下单。更多详情：http://fmb.so/v61

        final FmbOrderInfo orderInfoDB = orderInfoService.getById(orderId);

        if (orderInfoDB != null && orderInfoDB.getOrderStatus()==0  ) {

            String smsContent = "{收货人姓名}，您购买的：{活动标题}，订单号：{订单号} 还未付款，请尽快完成支付。否则订单将在1小时后关闭，届时若仍需购买，请重新下单。更多详情：http://fmb.so/8nnh" ;

            HashMap<String, String> info = new HashMap<>();
            info.put("{订单号}",  orderInfoDB.getOrderSn());
            info.put("{活动标题}", orderInfoDB.getOrderName());
            info.put("{应付金额}",  FmbNumberUtil.bigDecimal2Str( orderInfoDB.getRealpayMoney()));
            info.put("{手机号}",  orderInfoDB.getMobile());

            info.put("{kefu_phone}", FmbServerConfig.getServiceTel());
            info.put("{收货人姓名}",  orderInfoDB.getReceiver());

            final String[] aim = new String[info.size()];
            final String[] result = new String[info.size()];

            int i = 0 ;
            for (Map.Entry<String, String> stringEntry : info.entrySet()) {
                aim[i] = stringEntry.getKey() ;
                result[i] = stringEntry.getValue() ;
                i++ ;
            }
            smsContent  = StringUtils.replaceEach(smsContent,aim,result) ;

            FmbSmsUtil.sendSms(orderInfoDB.getMobile(),smsContent,"orderWillCloseIn1Hour");


        }


    }


    /**
     * 开始改签  返回改签选项
     * @param resultMap
     * @param input
     * @param admin001
     */
    public void orderChangeBegin(HashMap<String, Object> resultMap, AdminOrder input, String admin001) {

        DynamicDataSourceContextHolder.push(DB_slave);
        //查询订单信息
        final FmbOrderInfo orderInfo = queryFmbOrderInfo(input.getOrderSn());

        //查询goods信息
        List<FmbOrderGoods> goodsList = self.queryFmbOrderGoodsAdmin(input.getOrderSn());

        if (goodsList.isEmpty()) {
            throw new BadLogicException("goods信息异常!");
        }

        final FmbOrderGoods goods = goodsList.get(0);

        final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());
        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

        ReqHotel reqHotel = new ReqHotel();
        reqHotel.setXaid(orderInfo.getAid());
        //设置默认查询日期
//        if (StringUtils.isBlank(input.getPlayDate()) && StringUtils.isBlank(input.getLeaveDate())) {
//            reqHotel.setPlayDate(goods.getPlayDate());
//            reqHotel.setLeaveDate(goods.getLeaveDate());
//        }else
        if (StringUtils.isNotBlank(input.getPlayDate()) && StringUtils.isNotBlank(input.getLeaveDate())) {
            reqHotel.setPlayDate(input.getPlayDate());
            reqHotel.setLeaveDate(input.getLeaveDate());
        }

        HashMap<String, Object> sourceOrderInfo = new HashMap<>();

        //订单原始数据 回显
        sourceOrderInfo.put("playDate",goods.getPlayDate());
        sourceOrderInfo.put("leaveDate",goods.getLeaveDate());
        sourceOrderInfo.put("goodsNumber",goods.getGoodsNumber());
        sourceOrderInfo.put("goodsId",goods.getGoodsId());
        sourceOrderInfo.put("skuName",skuDetail.getRoomSku().getSkuName());
        sourceOrderInfo.put("money",orderInfo.getMoney());

        if (isOrderFromHotelReserveCode(orderInfo)) {

            final BigDecimal reduce = goodsList.stream().map(g -> g.getGoodsPrice().multiply(new BigDecimal(g.getGoodsNumber()))).reduce(BigDecimal.ZERO,
                    BigDecimal::add);

            sourceOrderInfo.put("money",reduce);
        }

        final ArrayList<String> receivers = new ArrayList<>();

        final String[] split = orderInfo.getReceiver().split(",");
        for (String s : split) {
            receivers.add(s) ;
        }

        sourceOrderInfo.put("receiver", receivers);

        resultMap.put("sourceOrderInfo",sourceOrderInfo);

        //酒店
        if (orderInfo.getGoodsType()==6 ) {
            HashMap<String, Object> skuInfo = new HashMap<>();

            if (isOrderFromHotelReserveCode(orderInfo)) {
                //查询可售卖的sku 信息
                frontHotelService.selectFrontRoomSku(skuInfo,reqHotel,null,false);
            }else{
                //查询可售卖的sku 信息
                frontHotelService.selectFrontRoomSku(skuInfo,reqHotel,null);
            }


            List<FrontRoomSuiteDomain> roomSkus  = (List<FrontRoomSuiteDomain>) skuInfo.get("suiteList");
            List<FrontRoomSuiteDomain> roomSkusProcess  = new ArrayList<>() ;

            //过滤 scheduled 状态的sku
            for (FrontRoomSuiteDomain skus : roomSkus) {
                final FrontRoomSuiteDomain nSkus = new FrontRoomSuiteDomain();

                nSkus.setSuiteId(skus.getSuiteId());
                nSkus.setSuiteName(skus.getSuiteName());

                final ArrayList<FrontRoomSkuDomain> skuList = new ArrayList<>();
                nSkus.setSkuList(skuList);

                for (FrontRoomSkuDomain s : skus.getSkuList()) {
                    if (!"scheduled".equals(s.getStatus())) {
                        skuList.add(s) ;
                    }
                }

                roomSkusProcess.add(nSkus);
            }

            processRoomSkuWhenOrderIsReserve(roomSkusProcess,orderInfo,goods,input.getOrderSn()) ;

            resultMap.put("suiteList",roomSkusProcess);
        }


    }

    public boolean isOrderFromHotelReserveCode(FmbOrderInfo orderInfo) {
        return orderInfo.getOrderType()==2 && orderInfo.getGoodsType()==GOODS_TYPE_6;
    }

    /**
     * 酒店预约单改签 筛选
     * @param roomSkusProcess
     * @param orderInfo
     * @param goods
     * @param orderSn
     */
    public void processRoomSkuWhenOrderIsReserve(List<FrontRoomSuiteDomain> roomSkusProcess, FmbOrderInfo orderInfo, FmbOrderGoods goods
            ,String orderSn) {


        if (isOrderFromHotelReserveCode(orderInfo)) {

            //找到旧订单的 日历房信息
            final FmbxSuiteRoomSku fmbxSuiteRoomSku = orderOfHotelDateService.querySkuHotelDate(goods.getGoodsId());

            //找到 套餐信息
            final FmbxSuite suite = orderOfHotelDateService.queryFmbxSuite(fmbxSuiteRoomSku.getSuiteId());

            //查询 当前套餐对应的 日历房sku 的房型列表
            List<FrontRoomDomain> frontRoomDomainList =  frontHotelService.queryFrontRoomDomains(fmbxSuiteRoomSku.getSuiteId(),false);

            final FmbxHotelReserveSku reserveSku = queryFmbxHotelReserveSkuFromHotelOrderSn(orderSn);

            if (suite.getHotelReserveSplit()==1) {

                //过滤 房型 ,需要比购买的房券价格高的房型才能展示出来
                Wrapper<FmbxHotelReserveSku> wrReserve = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                        .eq(FmbxHotelReserveSku::getSuiteId,suite.getSuiteId())
                        .gt(FmbxHotelReserveSku::getHotelDateSkuId,0)
                        .eq(FmbxHotelReserveSku::getStatus,1)
                        .ge(FmbxHotelReserveSku::getGoodsPrice,reserveSku.getGoodsPrice())
                        .select(FmbxHotelReserveSku::getRoomId,FmbxHotelReserveSku::getHotelDateSkuId)
                        ;
                final Set<Integer> roomIdSet =
                        reserveSkuService.list(wrReserve).stream().map(FmbxHotelReserveSku::getRoomId).collect(Collectors.toSet());

                final Iterator<FrontRoomDomain> iterator = frontRoomDomainList.iterator();

                //过滤不符合要求的房型
                while (iterator.hasNext()) {
                    final FrontRoomDomain roomDomain = iterator.next();
                    if (!roomIdSet.contains(roomDomain.getRoomId())) {
                        iterator.remove();
                    }
                }
            }

            final Set<Integer> roomIds = frontRoomDomainList.stream().map(FrontRoomDomain::getRoomId).collect(Collectors.toSet());

            final Iterator<FrontRoomSuiteDomain> suiteIterator = roomSkusProcess.iterator();

            while (suiteIterator.hasNext()) {
                final FrontRoomSuiteDomain suiteIt = suiteIterator.next();

                if (suiteIt.getSuiteId().intValue()!=suite.getSuiteId().intValue()) {
                    suiteIterator.remove();
                    continue;
                }
                final Iterator<FrontRoomSkuDomain> domainIterator = suiteIt.getSkuList().iterator();
                while (domainIterator.hasNext()){
                    final FrontRoomSkuDomain next1 = domainIterator.next();
                    if (!roomIds.contains(next1.getRoomId())) {
                        domainIterator.remove();
                    }
                }
            }
        }

    }

    /**
     * 改签预览
     * @param resultMap
     * @param reqSku
     * @param admin001
     */
    public void orderChangePreview(HashMap<String, Object> resultMap, ReqGenSku reqSku, String admin001) {

        List<ReqGenSku> reqGenSkus = new ArrayList<>() ;
        reqGenSkus.add(reqSku);



        final FmbOrderInfo orderInfo = queryFmbOrderInfo(reqSku.getSourceOrderSn());

        final FmbxSuiteRoomSku suiteRoomSku = roomSkuService.getOne(new LambdaQueryWrapper<FmbxSuiteRoomSku>().eq(FmbxSuiteRoomSku::getSkuId, reqSku.getSkuId()).select(FmbxSuiteRoomSku::getSuiteId));
        final FmbxSuite suitInfo = suiteService.getOne(new LambdaQueryWrapper<FmbxSuite>().eq(FmbxSuite::getSuiteId, suiteRoomSku.getSuiteId()).select(FmbxSuite::getTotalNeight, FmbxSuite::getNightMin));

        final int dayDiff = FmbDateUtil.dayDiff(reqSku.getCheckOutDate(), reqSku.getCheckInDate());

//        resultMap.put("dayDiff",dayDiff) ;
        resultMap.put("newOrderSuitInfo",suitInfo) ;

        final int reqTotal = reqSku.getSkuNumber() * dayDiff;


        resultMap.put("message","success") ;
        resultMap.put("result",1) ;

        if (reqSku.getSkuNumber()!=null &&   reqSku.getSkuNumber()<=0) {
            resultMap.put("result",0) ;
            resultMap.put("message","房间数异常") ;
        }

        if (!(reqTotal%suitInfo.getTotalNeight() ==0 && dayDiff%suitInfo.getNightMin()==0)) {
            resultMap.put("result",0) ;
            resultMap.put("message",
                    "改签后订单不符合套餐设定,改签后总间数为"+reqTotal+",  套餐要求总间夜为"+suitInfo.getTotalNeight()+"的倍数,最小入住晚数为"+suitInfo.getNightMin());
        }else{
            HashMap<String, Object> tMap = new HashMap<>();
            ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.countGoodsPrice(reqGenSkus,"",tMap);

            final List<ConfirmOrderMoneyGroup> groups = confirmOrderMoneyInfo.getGroups().stream().filter(g -> g.getGroupType() == 1).collect(Collectors.toList());
//            resultMap.put("moneyGroups",groups) ;
            final BigDecimal totalAll = groups.get(0).getItemList().stream().map(ConfirmOrderMoneyItem::getPriceBigAll).reduce(BigDecimal.ZERO, BigDecimal::add);
            resultMap.put("newOrderMoney",totalAll) ;
            resultMap.put("oldOrderMoney",orderInfo.getMoney()) ;

            //查询goods信息
            List<FmbOrderGoods> goodsList = self.queryFmbOrderGoodsAdmin(orderInfo.getOrderSn());

            resultMap.put("money",totalAll.subtract(orderInfo.getMoney())) ;


            //酒店预约单
            if ( isOrderFromHotelReserveCode(orderInfo)) {

                final BigDecimal reduce = goodsList.stream().map(g -> g.getGoodsPrice().multiply(new BigDecimal(g.getGoodsNumber()))).reduce(BigDecimal.ZERO,
                        BigDecimal::add);

                resultMap.put("oldOrderMoney",reduce) ;
                resultMap.put("money",totalAll.subtract(reduce)) ;
            }


        }

    }



    /**
     * 改签提交 .生成改签结果
     * @param resultMap
     * @param input
     * @param admin001
     */
    public void orderChangeSubmit(HashMap<String, Object> resultMap, ReqGenSku input, String admin001) {

        DynamicDataSourceContextHolder.push(DB_slave);

        if ( input.getSkuNumber()!=null &&   input.getSkuNumber()<=0) {
            throw new BadLogicException("房间数异常") ;
        }


        input.setNotCheckSkuNumber(1);
        final SessionUser sessionUser = userUnionService.nowUserInfo();

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, input.getSourceOrderSn())
                ;

        final FmbOrderInfo oldOrderDB = queryFmbOrderInfo(input.getSourceOrderSn());


        //查询goods信息
        List<FmbOrderGoods> goodsList = self.queryFmbOrderGoodsAdmin(input.getSourceOrderSn());

        if (goodsList.isEmpty()) {
            throw new BadLogicException("goods信息异常!");
        }





        final FmbOrderGoods goodsOld = goodsList.get(0);

        if (input.getNotPayMoneyDiff() != null && 1==input.getNotPayMoneyDiff()  && input.getSkuNumber().intValue()!=goodsOld.getGoodsNumber()  ) {
            throw new BadLogicException("选择免补差价时不能修改数量");
        }
        
//        resultMap.put("newOrderDB",oldOrderDB) ;
//        final BigDecimal bigDecimal = FmbOrderInfoUtil.oldOrderRealPayMoney(oldOrderDB);
//        //优惠券用了多少钱
//        final BigDecimal orderCouponMoney = FmbOrderInfoUtil.orderCouponMoney(oldOrderDB);


        List<ReqGenSku> reqGenSkus = new ArrayList<>() ;
        reqGenSkus.add(input);

        HashMap<String, Object> tMap = new HashMap<>();
        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.countGoodsPrice(reqGenSkus,"",tMap);
        final List<ConfirmOrderMoneyGroup> groups = confirmOrderMoneyInfo.getGroups().stream().filter(g -> g.getGroupType() == 1).collect(Collectors.toList());
//        resultMap.put("moneyGroups",groups) ;

        //改签后 货物总额 
        final BigDecimal totalAll = groups.get(0).getItemList().stream().map(ConfirmOrderMoneyItem::getPriceBigAll).reduce(BigDecimal.ZERO, BigDecimal::add);



        //查询订单信息
        final FmbOrderInfo newOrderDB = orderInfoService.getOne(wrOrder);

        newOrderDB.setOrderId(null);

        String orderSn = createOrderSn();
        newOrderDB.setOrderSn(orderSn);
        newOrderDB.setBeforeOrderSn(input.getSourceOrderSn());
        newOrderDB.setPaySn("");
        newOrderDB.setMoney(totalAll);

        if (newOrderDB.getDiscountRate()>0) {
            newOrderDB.setCouponMoney(FmbOrderInfoUtil.orderCouponMoney(newOrderDB).intValue());
        }
        //计算改签后总价格
        newOrderDB.setRealpayMoney(FmbOrderInfoUtil.oldOrderRealPayMoney(newOrderDB));

        resultMap.put("result",1) ;
        resultMap.put("newOrderSn",newOrderDB.getOrderSn()) ;


        final FmbxSuiteRoomSku newFmbxSuiteRoomSku = orderOfHotelDateService.querySkuHotelDate(input.getSkuId());
        final FmbxSku newFmbxSku = queryFmbxSkus(input.getSkuId());

        if (newFmbxSku.getXaid().intValue()!=oldOrderDB.getAid().intValue()) {
            throw new BadLogicException("不能跨活动改签");
        }


        OrderSysInfo orderSysInfo = new OrderSysInfo() ;

        final int dayDiffInput = FmbDateUtil.dayDiff(input.getCheckOutDate(), input.getCheckInDate());
        final int dayDiffInputOld = FmbDateUtil.dayDiff(goodsOld.getLeaveDate(),goodsOld.getPlayDate());

        //免补差价
        if (input.getNotPayMoneyDiff()!=null && input.getNotPayMoneyDiff()==1){

            if(dayDiffInput!=dayDiffInputOld ||  input.getSkuNumber().intValue()!= goodsOld.getGoodsNumber().intValue()){
                throw new BadLogicException("选择免补差价时不能修改间夜数和晚数");
            }

        }


        //酒店预约单 改签规则校验
        final String orderSn1 = oldOrderDB.getOrderSn();
        if ( isOrderFromHotelReserveCode(oldOrderDB)) {

            //老订单的 日历房sku 信息
            final FmbxSuiteRoomSku oldRoomSku = orderOfHotelDateService.querySkuHotelDate(goodsOld.getGoodsId());


            if (newFmbxSuiteRoomSku.getSuiteId().intValue()!=oldRoomSku.getSuiteId().intValue()) {
                throw new BadLogicException("预约单改签必须是同一套餐下的sku");
            }



            if(dayDiffInput*input.getSkuNumber().intValue()!=dayDiffInputOld*goodsOld.getGoodsNumber().intValue()){
                throw new BadLogicException("预约单改签消耗间夜不能改变");
            }


            final LambdaQueryWrapper<FmbHotelReserveOrderRecord> wrRecord = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                    .eq(FmbHotelReserveOrderRecord::getHotelOrderSn, orderSn1)
                    .select(FmbHotelReserveOrderRecord::getGeneralOrderSn, FmbHotelReserveOrderRecord::getCode,FmbHotelReserveOrderRecord::getGeneralTicketId);

            //房券预约记录 里获取 房券skuid
            final FmbHotelReserveOrderRecord hotelCodeInfo = hotelReserveOrderRecordService.getOne(wrRecord);
//
//            //获取房券信息
//            Wrapper<FmbxHotelReserveSku> wrResSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
//                    .eq(FmbxHotelReserveSku::getSkuId,hotelCodeInfo.getGeneralTicketId())
//                    .select(FmbxHotelReserveSku::getSuiteId,FmbxHotelReserveSku::getSkuId,FmbxHotelReserveSku::getGoodsPrice)
//                    ;
//            final FmbxHotelReserveSku reserveSku = reserveSkuService.getOne(wrResSku);
//
//            final FmbOrderGoods  reserveOrderGoods = queryFmbOrderGoods(hotelCodeInfo.getGeneralOrderSn()).get(0);
//
//            //房券对应的套餐
//            final FmbxSuite suiteReserve = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());
//
//            List<FmbxHotelReserveSku> reserveSkusValid = new ArrayList<>() ;
//            //找到同套餐的 所有房券信息
//            Wrapper<FmbxHotelReserveSku> wrResList = new LambdaQueryWrapper<FmbxHotelReserveSku>()
//                    .eq(FmbxHotelReserveSku::getSuiteId,reserveSku.getSuiteId())
//                    .eq(FmbxHotelReserveSku::getStatus,1)
//                    .eq(FmbxHotelReserveSku::getFlagSell,1)
//                    //是否拆分房券 查询条件是动态的
//                    .gt(suiteReserve.getHotelReserveSplit()==1,FmbxHotelReserveSku::getHotelDateSkuId,0)
//                    .eq(suiteReserve.getHotelReserveSplit()==2,FmbxHotelReserveSku::getHotelDateSkuId,0)
//                    //选择的字段
//                    .select(FmbxHotelReserveSku::getGoodsPrice,FmbxHotelReserveSku::getHotelDateSkuId,FmbxHotelReserveSku::getStatus,FmbxHotelReserveSku::getFlagSell
//                            ,FmbxHotelReserveSku::getSkuId,FmbxHotelReserveSku::getRoomId
//                    )
//                    ;
//            reserveSkusValid = reserveSkuService.list(wrResList);
//
//
//            if (suiteReserve.getHotelReserveSplit()==1) {
//
//                //拆分房券找到价格 持平或更高的sku
//                reserveSkusValid  =
//                        reserveSkusValid.stream().filter(r->r.getGoodsPrice().compareTo(reserveSku.getGoodsPrice())>=0).collect(Collectors.toList()) ;
//
//                //找到这些日历房的skuid
//                final Set<Integer> roomSkuidSet = reserveSkusValid.stream().map(FmbxHotelReserveSku::getHotelDateSkuId).collect(Collectors.toSet());
//
//                if (!roomSkuidSet.contains(input.getSkuId())) {
//                    throw new BadLogicException("房型必须是房券价格平价或者高于原房券价格的房型");
//                }
//            }


            checkHotelReserveCode(hotelCodeInfo.getCode(),oldOrderDB.getUid(),orderSysInfo,"changeOrder",reqGenSkus);



        }


        final List<HotelSkuDatePriceDomain> priceDomainList = orderOfHotelDateService.buildPriceList(input.getCheckInDate(),
                input.getCheckOutDate(), input.getSkuNumber(), input.getSkuId(),false);
        FmbxSuite suiteNew = orderOfHotelDateService.queryFmbxSuite(newFmbxSuiteRoomSku.getSuiteId());

        final FmbOrderTradeSnapshot oldSnap = snapshotService.getOne(new LambdaUpdateWrapper<FmbOrderTradeSnapshot>().eq(FmbOrderTradeSnapshot::getOrderSn,
                orderSn1));

        {
            //旧订单的交易快照 content 值修改
            String strContent =  buildSnapContent(input.getSkuId(),newFmbxSuiteRoomSku.getSuiteId()) ;
            oldSnap.setContent(strContent);

            String strSummary =  buildSnapSummary(oldSnap.getSummary(),input,newFmbxSku,newFmbxSuiteRoomSku,suiteNew,priceDomainList) ;

            oldSnap.setSummary(strSummary);

        }


        //酒店预约单改签 计算实际订单金额
        if (isOrderFromHotelReserveCode(newOrderDB)) {

            BigDecimal newOlderShouldPay = BigDecimal.ZERO ;
            for (HotelSkuDatePriceDomain domain : priceDomainList) {
                if ( new BigDecimal( domain.getGoodsPrice()).compareTo(orderSysInfo.getHotelCodeEachNightPay())>=0) {
                    newOlderShouldPay =
                            newOlderShouldPay.add(new BigDecimal( domain.getGoodsPrice()).subtract(orderSysInfo.getHotelCodeEachNightPay())) ;
                }
            }

            final BigDecimal newOlderShouldPayAll = newOlderShouldPay.multiply(new BigDecimal(input.getSkuNumber()));
            //设置 酒店预约单一共要补的差价
            newOrderDB.setMoney(newOlderShouldPayAll);
            newOrderDB.setRealpayMoney(newOlderShouldPayAll);


        }


        final FmbxSuiteRoomSku oldRoomSku = orderOfHotelDateService.querySkuHotelDate(goodsOld.getGoodsId());




        self.orderChangeSubmitCoreJob(oldOrderDB,newOrderDB,input,sessionUser,goodsOld  ,newFmbxSuiteRoomSku ,newFmbxSku,priceDomainList,
                oldSnap,suiteNew,orderSysInfo , oldRoomSku) ;

    }

    public FmbxHotelReserveSku queryFmbxHotelReserveSkuFromHotelOrderSn(String orderSn1) {
        final LambdaQueryWrapper<FmbHotelReserveOrderRecord> wrRecord = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                .eq(FmbHotelReserveOrderRecord::getHotelOrderSn, orderSn1)
                .select(FmbHotelReserveOrderRecord::getGeneralOrderSn, FmbHotelReserveOrderRecord::getCode,FmbHotelReserveOrderRecord::getGeneralTicketId);

        //房券预约记录 里获取 房券skuid
        final FmbHotelReserveOrderRecord hotelCodeInfo = hotelReserveOrderRecordService.getOne(wrRecord);

        //获取房券信息
        Wrapper<FmbxHotelReserveSku> wrResSku = new LambdaQueryWrapper<FmbxHotelReserveSku>()
                .eq(FmbxHotelReserveSku::getSkuId,hotelCodeInfo.getGeneralTicketId())
                .select(FmbxHotelReserveSku::getSuiteId,FmbxHotelReserveSku::getSkuId,FmbxHotelReserveSku::getGoodsPrice)
                ;
        final FmbxHotelReserveSku reserveSku = reserveSkuService.getOne(wrResSku);
        return reserveSku;
    }


    /**
     * @param oldOrderDB
     * @param newOrderDB
     * @param input
     * @param sessionUser
     * @param goodsOldWillSaveAsNewGoods
     * @param newFmbxSuiteRoomSku
     * @param newFmbxSku
     * @param priceDomainList
     * @param oldSnap
     * @param suiteNew
     * @param orderSysInfo
     * @param oldRoomSku
     */
    @DSTransactional
    @DS(DB_master)
    public void orderChangeSubmitCoreJob(FmbOrderInfo oldOrderDB, FmbOrderInfo newOrderDB, ReqGenSku input, SessionUser sessionUser,
                                         FmbOrderGoods goodsOldWillSaveAsNewGoods, FmbxSuiteRoomSku newFmbxSuiteRoomSku
            , FmbxSku newFmbxSku, List<HotelSkuDatePriceDomain> priceDomainList, FmbOrderTradeSnapshot oldSnap, FmbxSuite suiteNew, OrderSysInfo orderSysInfo, FmbxSuiteRoomSku oldRoomSku) {


        final FmbOrderGoods goodsOldBak = new FmbOrderGoods();
        BeanUtils.copyProperties(goodsOldWillSaveAsNewGoods,goodsOldBak);


        //更新改签前后的库存
        {

            LocalDate parse1 = goodsOldBak.getPlayTime().toLocalDate();
            final LocalDate parse2 = goodsOldBak.getLeaveTime().toLocalDate();

            final ArrayList<RoomStockNumberUpdate> stockList = new ArrayList<>();
            //旧房型 返回库存
            while (parse1.isBefore(parse2)) {

                RoomStockNumberUpdate ru = new RoomStockNumberUpdate();
                ru.setRoomId(oldRoomSku.getRoomId());
                ru.setTargetDate(parse1);
                ru.setChange(goodsOldBak.getGoodsNumber());
                ru.setCheckStock(false);

                stockList.add(ru) ;
                parse1 = parse1.plusDays(1);
            }

            //新订单 扣减库存
            for (HotelSkuDatePriceDomain priceDomain : priceDomainList) {
                RoomStockNumberUpdate ru = new RoomStockNumberUpdate();
                ru.setRoomId(newFmbxSuiteRoomSku.getRoomId());
                ru.setTargetDate(FmbDateUtil.dateToLocalDate(FmbDateUtil.toDate10Str(priceDomain.getDatecol() )) );
                ru.setChange(input.getSkuNumber()*-1);
                ru.setCheckStock(true);

                stockList.add(ru) ;
            }

            for (RoomStockNumberUpdate stockNumberUpdate : stockList) {

                Wrapper<FmbxSuiteStock> wrStock = new LambdaQueryWrapper<FmbxSuiteStock>()
                        .eq(FmbxSuiteStock::getTargetDate,stockNumberUpdate.getTargetDate())
                        .eq(FmbxSuiteStock::getRoomId,stockNumberUpdate.getRoomId())
                        .select(FmbxSuiteStock::getStockId,FmbxSuiteStock::getVersionNum,FmbxSuiteStock::getStockNum,FmbxSuiteStock::getStatus)
                        ;
                final FmbxSuiteStock one = suiteStockService.getOne(wrStock);
                if (one != null) {

                    final int stockNum = one.getStockNum() + stockNumberUpdate.getChange();
                    if (stockNumberUpdate.isCheckStock()) {
                        if (one.getStatus()!=1) {
                            throw new BadLogicException("改签后"+stockNumberUpdate.getTargetDate()+"房态异常");
                        }
                        if (stockNum<0){
                            throw new BadLogicException("改签后"+stockNumberUpdate.getTargetDate()+"库存不足");
                        }
                    }

                    one.setVersionNum(one.getVersionNum()+1);

                    one.setStockNum(stockNum);
                    suiteStockService.updateById(one) ;

                }else {
                    throw new BadLogicException("库存异常");
                }

            }



        }


        //需要补差价
        boolean boolPayMoreMoney = false ;
        //需要补多少
        BigDecimal payMoreMoney = BigDecimal.ZERO ;


        //一共退多少钱
        BigDecimal returnTotal = BigDecimal.ZERO ;

        //需要返回余额
        boolean boolReturnCash = false ;
        BigDecimal returnCash = BigDecimal.ZERO ;

        //需要返还现金
        boolean boolReturnMoney = false ;
        BigDecimal returnMoney = BigDecimal.ZERO ;


        final int compare = oldOrderDB.getRealpayMoney().compareTo(newOrderDB.getRealpayMoney());

        //需要补差价
        if (compare==-1) {
            boolPayMoreMoney = true ;
            payMoreMoney = newOrderDB.getRealpayMoney().subtract(oldOrderDB.getRealpayMoney()) ;

        }
        //需要退款
        if (compare==1) {
            returnTotal = oldOrderDB.getRealpayMoney().subtract(newOrderDB.getRealpayMoney());

            //退款金额 小于 原订单使用的余额
            if (returnTotal.compareTo(oldOrderDB.getCashMoney())==-1) {
                boolReturnCash = true ;
                returnCash = returnTotal ;
                newOrderDB.setCashMoney(newOrderDB.getCashMoney().subtract(returnTotal));
            }else {
                //老订单使用了余额 但不够退款总额
                if (oldOrderDB.getCashMoney().compareTo(BigDecimal.ZERO)==1) {
                    boolReturnCash = true ;
                    boolReturnMoney = true ;
                    returnCash = oldOrderDB.getCashMoney() ;
                    returnMoney = returnTotal.subtract(returnCash) ;
                }else {
                    //没使用余额
                    boolReturnMoney = true ;
                    returnMoney = returnTotal ;
                }
            }
        }

        final String paySn = SnowflakeIdWorker.getInstance().nextId() + "";

        //保存payorder
        FmbPayOrders pay = new FmbPayOrders();

        pay.setPaySn(paySn);
        pay.setMoneyOrderSn("");
        pay.setTradeNo("");
        pay.setRequestIp(MDC.get(FmbConstants.USER_IP));
        pay.setReturnIp("");
        pay.setNotifyIp("");
        pay.setRequestText(JSONUtil.toJsonStr(input));
        pay.setResponseText("");
        pay.setPayType("");
        pay.setReferer("");
        pay.setIsSuccess(0);
        pay.setCtime(LocalDateTime.now());
        payOrdersService.save(pay);


        newOrderDB.setMyPaySn(paySn);
        newOrderDB.setConfirmStatus(0);

        //需要补差价
        if (boolPayMoreMoney) {
            //如果 是免补差价
            if (input.getNotPayMoneyDiff()!=null &&  input.getNotPayMoneyDiff()==1) {
                newOrderDB.setOrderStatus(8);

                //恢复原有订单的 金额数据
                newOrderDB.setMoney(oldOrderDB.getMoney());
                newOrderDB.setDiscountRate(oldOrderDB.getDiscountRate());
                newOrderDB.setCouponMoney(oldOrderDB.getCouponMoney());
                newOrderDB.setCashMoney(oldOrderDB.getCashMoney());
                newOrderDB.setRealpayMoney(oldOrderDB.getRealpayMoney());

            }else {

                //补尾款金额直接放在 RealpayMoney
                newOrderDB.setRealpayMoney(payMoreMoney);
                newOrderDB.setOrderStatus(7);
                //付款中
                newOrderDB.setPayStatus(1);
            }
        }else {
            newOrderDB.setOrderStatus(8);
            newOrderDB.setRealpayMoney(FmbOrderInfoUtil.oldOrderRealPayMoney(newOrderDB));
        }

        //用了折扣券 就把couponMoney设置为 0
        if (newOrderDB.getDiscountRate()>0) {
            newOrderDB.setCouponMoney(0);
        }

        //新订单保存
        orderInfoService.save(newOrderDB) ;



        //保存goods
        goodsOldWillSaveAsNewGoods.setRecId(null);
        goodsOldWillSaveAsNewGoods.setOrderSn(newOrderDB.getOrderSn());


        goodsOldWillSaveAsNewGoods.setGoodsId(input.getSkuId()) ;
        goodsOldWillSaveAsNewGoods.setGoodsNumber(input.getSkuNumber());

        //免补差价时 不需要什么修改 保留原有goods里的价格信息
        if (input.getNotPayMoneyDiff()!=null &&  input.getNotPayMoneyDiff()==1  ) {

        }else{

            BigDecimal bigDecimalMarket = BigDecimal.ZERO;
            BigDecimal bigDecimalGoods = BigDecimal.ZERO;
            BigDecimal bigDecimalSettle = BigDecimal.ZERO;
            for (HotelSkuDatePriceDomain priceDomain : priceDomainList) {
                bigDecimalMarket = bigDecimalMarket.add(new BigDecimal(priceDomain.getMarketPrice())) ;
                bigDecimalGoods = bigDecimalGoods.add(new BigDecimal(priceDomain.getGoodsPrice())) ;
                bigDecimalSettle = bigDecimalSettle.add(new BigDecimal(priceDomain.getSettlePrice())) ;
            }

            goodsOldWillSaveAsNewGoods.setMarketPrice(bigDecimalMarket);
            goodsOldWillSaveAsNewGoods.setGoodsPrice(bigDecimalGoods);
            goodsOldWillSaveAsNewGoods.setSettlePrice(bigDecimalSettle);
        }
        goodsOldWillSaveAsNewGoods.setPlayTime(FmbDateUtil.dateToLocalDate(input.getCheckInDate()).atStartOfDay());
        goodsOldWillSaveAsNewGoods.setLeaveTime(FmbDateUtil.dateToLocalDate(input.getCheckOutDate()).atStartOfDay());




        //构造  goods 的 GoodExt
        //运行到这里  goodsOldWillSaveAsNewGoods 里的价格 已经是计算好的新订单价格
        String extInfoNew =   changeGoodsExtInfo(goodsOldWillSaveAsNewGoods.getGoodExt(),newFmbxSku,newFmbxSuiteRoomSku,priceDomainList,
                input,suiteNew,goodsOldWillSaveAsNewGoods) ;
        goodsOldWillSaveAsNewGoods.setGoodExt(extInfoNew);

        orderGoodsService.save(goodsOldWillSaveAsNewGoods) ;


        //更新电子码
        {

            Wrapper<FmbExchangeCodes> wrCode = new LambdaQueryWrapper<FmbExchangeCodes>()
                    .eq(FmbExchangeCodes::getOrderSn,oldOrderDB.getOrderSn())
                    ;
            final List<FmbExchangeCodes> listOldCode = exchangeCodesService.list(wrCode);


            if (!listOldCode.isEmpty()) {
                //如果 改签完后 直接交易成功需要补 电子码给用户
                if (isOrderSuccessStatus(newOrderDB.getOrderStatus())) {

                    FmbExchangeCodes nCode = new FmbExchangeCodes();

                    BeanUtils.copyProperties(listOldCode.get(0),nCode);
                    nCode.setCodeId(null);

                    Integer codeNum = 0;
                    int dayDiff = FmbDateUtil.dayDiff(FmbDateUtil.localDateTime2String10(goodsOldWillSaveAsNewGoods.getLeaveTime()),
                            FmbDateUtil.localDateTime2String10(goodsOldWillSaveAsNewGoods.getPlayTime()));
                    Integer totalNightOfSuite = suiteNew.getTotalNeight();

                    //最多电子码个数
                    codeNum = dayDiff * goodsOldWillSaveAsNewGoods.getGoodsNumber() / totalNightOfSuite;
                    //如果电子码个数超过间数 一定是用间数  ,因为退款只能按间数退
                    if (codeNum > goodsOldWillSaveAsNewGoods.getGoodsNumber()) {
                        codeNum = goodsOldWillSaveAsNewGoods.getGoodsNumber();
                    }

                    final HashSet<String> codesStr = new HashSet<>();

                    while (codesStr.size() < codeNum) {
                        final String  exchangeCode= RandomUtil.randomNumbers(8);

                        if (exchangeCodesService.count(new LambdaQueryWrapper<FmbExchangeCodes>().eq(FmbExchangeCodes::getExchangeCode,
                                exchangeCode))==0) {
                            codesStr.add(exchangeCode) ;
                        }
                    }

                    for (String code : codesStr) {

                        nCode.setCodeId(null);
                        nCode.setExchangeCode(code);
                        nCode.setExchangePwd(code);

                        nCode.setOrderSn(newOrderDB.getOrderSn());
                        nCode.setRecId(goodsOldWillSaveAsNewGoods.getRecId());

                        exchangeCodesService.save(nCode) ;

                    }

                }


                for (FmbExchangeCodes fmbExchangeCodes : listOldCode) {

                    FmbExchangeCodes wrUpCode = new FmbExchangeCodes();
                    wrUpCode.setCodeId(fmbExchangeCodes.getCodeId());
                    wrUpCode.setStatus(2);
                    exchangeCodesService.updateById(wrUpCode) ;
                }
            }


        }



        //修改 fmbx_hotel_order_confirm
        {
            if (isOrderSuccessStatus(newOrderDB.getOrderStatus())) {
                orderSuccessHandler.saveConfirmInfoFromOrderChange(newOrderDB,goodsOldWillSaveAsNewGoods) ;
            }

            //操作旧订单的  酒店二次确认表
            Wrapper<FmbxHotelOrderConfirm> wrConfirm = new LambdaUpdateWrapper<FmbxHotelOrderConfirm>()
                    .eq(FmbxHotelOrderConfirm::getOrderSn,oldOrderDB.getOrderSn())
                    .set(FmbxHotelOrderConfirm::getConfirmStatus,4)
                    .set(FmbxHotelOrderConfirm::getOrderStatus,6)
                    ;
            orderConfirmService.update(wrConfirm) ;

        }



        //保存  fmbx_hotel_goods_detail
        {

            List<FmbxHotelGoodsDetail> listOld  =new ArrayList<>() ;
            if (input.getNotPayMoneyDiff()!=null &&  input.getNotPayMoneyDiff()==1){
                Wrapper<FmbxHotelGoodsDetail> wrD = new LambdaQueryWrapper<FmbxHotelGoodsDetail>()
                        .eq(FmbxHotelGoodsDetail::getOrderSn,oldOrderDB.getOrderSn())
                        .select(FmbxHotelGoodsDetail::getRoomDate,FmbxHotelGoodsDetail::getGoodsPrice,FmbxHotelGoodsDetail::getSettlePrice)
                        .orderByAsc(FmbxHotelGoodsDetail::getRoomDate)
                        ;
                listOld = hotelGoodsDetailService.list(wrD);
            }

            for (int i = 0; i < priceDomainList.size(); i++) {
                HotelSkuDatePriceDomain priceDomain = priceDomainList.get(i) ;
//            }
//
//            for (HotelSkuDatePriceDomain priceDomain : priceDomainList) {

                FmbxHotelGoodsDetail goodsDetail = new FmbxHotelGoodsDetail();

                goodsDetail.setRecId(goodsOldWillSaveAsNewGoods.getRecId());
                goodsDetail.setOrderSn(newOrderDB.getOrderSn());
                goodsDetail.setRoomDate(  FmbDateUtil.dateToLocalDate( FmbDateUtil.toDate10Str( priceDomain.getDatecol())) );
                goodsDetail.setRoomNumber(goodsOldWillSaveAsNewGoods.getGoodsNumber());
                goodsDetail.setMarketPrice( new BigDecimal( priceDomain.getMarketPrice()));


                if (input.getNotPayMoneyDiff()!=null &&  input.getNotPayMoneyDiff()==1){
                    goodsDetail.setGoodsPrice( listOld.get(i).getGoodsPrice());
                    goodsDetail.setSettlePrice(listOld.get(i).getSettlePrice());
                    // listOld
                }else {
                    goodsDetail.setGoodsPrice( new BigDecimal( priceDomain.getGoodsPrice()));
                    goodsDetail.setSettlePrice(new BigDecimal(priceDomain.getSettlePrice()));
                }


                goodsDetail.setRoomId(newFmbxSuiteRoomSku.getRoomId());
                goodsDetail.setRoomName(newFmbxSuiteRoomSku.getRoomName());
                goodsDetail.setSuiteId(newFmbxSuiteRoomSku.getSuiteId());
                goodsDetail.setSuiteName(suiteNew.getName());
                goodsDetail.setRoomNumberReturn(0);

                hotelGoodsDetailService.save(goodsDetail) ;

            }

        }



        //酒店预约单 修改预约记录
        if (isOrderFromHotelReserveCode(oldOrderDB)) {

            FmbHotelReserveOrderRecord reserveRecord = null ;

            Wrapper<FmbHotelReserveOrderRecord> wrReRecord = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                    .eq(FmbHotelReserveOrderRecord::getHotelOrderSn,oldOrderDB.getOrderSn())
                    ;
            final FmbHotelReserveOrderRecord recordInsert = hotelReserveOrderRecordService.getOne(wrReRecord);

            FmbHotelReserveOrderRecord up1 = new FmbHotelReserveOrderRecord();
            up1.setOrId(recordInsert.getOrId());
            up1.setStatus(2);
            up1.setCancelTime(LocalDateTime.now());

            hotelReserveOrderRecordService.updateById(up1) ;


            recordInsert.setOrId(null);
            recordInsert.setHotelOrderSn(newOrderDB.getOrderSn());

            recordInsert.setHotelTicketId(goodsOldWillSaveAsNewGoods.getGoodsId());

            recordInsert.setPlayTime(goodsOldWillSaveAsNewGoods.getPlayTime().toLocalDate());
            recordInsert.setLeaveTime(goodsOldWillSaveAsNewGoods.getLeaveTime().toLocalDate());

            hotelReserveOrderRecordService.save(recordInsert) ;

        }


        //保存交易快照

        oldSnap.setSnapshotId(null);
        oldSnap.setOrderSn(newOrderDB.getOrderSn());
        oldSnap.setRecId(goodsOldWillSaveAsNewGoods.getRecId());
        oldSnap.setCtime(LocalDateTime.now());



        snapshotService.save(oldSnap) ;


//        System.out.println(goodsOld.getExtInfo());

        //relevantLog 添加
        FmbOrderRelevantLog relevantLog = new FmbOrderRelevantLog();
        relevantLog.setOrderSn(newOrderDB.getOrderSn());
        relevantLog.setOrderRelevantType("change_order");
        relevantLog.setOrderRelevantTypeId(newOrderDB.getOrderId());
        relevantLog.setOrderRelevantTypeStatus(newOrderDB.getOrderStatus());
        relevantLog.setOrderRelevantNote(input.getReasonTxt());
        relevantLog.setOrderRelevantIdentifier("order_change_status");
        relevantLog.setOrderChangeType("");
        relevantLog.setAdminUid(sessionUser.getUserId());
        relevantLog.setOrderRelevantLogTime(LocalDateTime.now());
        orderRelevantLogService.save(relevantLog) ;


        //更新 老订单 使用的 优惠券
        if (oldOrderDB.getDiscountRate()>0 || oldOrderDB.getCouponMoney()>0) {

            final FmbUserCoupon userCoupon = couponService.getOne(new LambdaQueryWrapper<FmbUserCoupon>().eq(FmbUserCoupon::getOrderSn, oldOrderDB.getOrderSn()).select(FmbUserCoupon::getCouponSn));

            Wrapper<FmbUserCoupon> wrap = new LambdaUpdateWrapper<FmbUserCoupon>()
                    .eq(FmbUserCoupon::getCouponSn, userCoupon.getCouponSn())
                    .set(FmbUserCoupon::getUseTime,LocalDateTime.now())
                    .set(FmbUserCoupon::getOrderSn, newOrderDB.getOrderSn())
                    ;
            couponService.update(wrap);

            FmbCouponLog couponLog = new FmbCouponLog();
            couponLog.setCouponSn(userCoupon.getCouponSn());
            couponLog.setLogType("order_changed");
            couponLog.setAdminUid(newOrderDB.getUid());
            couponLog.setLogNote("订单改签绑定");
            couponLog.setLogTime(LocalDateTime.now());
            couponLogService.save(couponLog) ;

        }


        //更新老订单
        Wrapper<FmbOrderInfo> wrOld = new LambdaUpdateWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,oldOrderDB.getOrderSn())
                .set(FmbOrderInfo::getOrderStatus,6)
                .set(FmbOrderInfo::getChangeType,1)
                .set(FmbOrderInfo::getChangeToOrder,newOrderDB.getOrderSn())
                ;
        orderInfoService.update(wrOld) ;


        //旧订单增加 订单操作记录
        {
            FmbOrderActions action = new FmbOrderActions();
            action.setOrderSn( oldOrderDB.getOrderSn());
            //代表前台用户
            action.setAdminUid(sessionUser.getUserId());
            action.setOrderStatus(6);
            action.setShippingStatus(oldOrderDB.getShippingStatus());
            action.setPayStatus(oldOrderDB.getPayStatus());
            action.setActionType("");
            action.setActionNote("由订单["+oldOrderDB.getOrderSn()+"]改签为订单["+newOrderDB.getOrderSn()+"]");
            action.setLogTime(LocalDateTime.now());
            orderActionsService.save(action) ;
        }

        //新订单 增加操作记录1
        {
            FmbOrderActions action = new FmbOrderActions();
            action.setOrderSn( newOrderDB .getOrderSn());
            //代表前台用户
            action.setAdminUid(sessionUser.getUserId());
            action.setOrderStatus(newOrderDB.getOrderStatus());
            action.setShippingStatus(newOrderDB.getShippingStatus());
            action.setPayStatus(newOrderDB.getPayStatus());
            action.setActionType("");
            String notPayMore = "" ;
            if(input.getNotPayMoneyDiff()!=null &&   input.getNotPayMoneyDiff()==1 ){
                notPayMore = "改签选择'免补差价',免补"+payMoreMoney;
            }

            action.setActionNote("由订单["+oldOrderDB.getOrderSn()+"]改签过来的订单["+newOrderDB.getOrderSn()+"]"+notPayMore);
            action.setLogTime(LocalDateTime.now());
            orderActionsService.save(action) ;
        }
        //新订单 增加操作记录2
        {
            FmbOrderActions action = new FmbOrderActions();
            action.setOrderSn( newOrderDB .getOrderSn());
            //代表前台用户
            action.setAdminUid(sessionUser.getUserId());
            action.setOrderStatus(newOrderDB.getOrderStatus());
            action.setShippingStatus(newOrderDB.getShippingStatus());
            action.setPayStatus(newOrderDB.getPayStatus());
            action.setActionType("");
            action.setActionNote("改签过来的订单["+newOrderDB.getOrderSn()+"]同时插入商品["+input.getSkuId()+"]");
            action.setLogTime(LocalDateTime.now());
            orderActionsService.save(action) ;
        }




        //需要退款
        if (boolReturnCash || boolReturnMoney) {
            FmbOrderReturns returnDBInsert = new FmbOrderReturns();

            returnDBInsert.setOrderSn(oldOrderDB.getOrderSn());
            returnDBInsert.setRecId(goodsOldBak.getRecId());
            returnDBInsert.setGoodsId(goodsOldBak.getGoodsId());
            returnDBInsert.setGoodsNum(goodsOldBak.getGoodsNumber());
            returnDBInsert.setReturnType(0);
            returnDBInsert.setReturnClass(0);
            returnDBInsert.setAdminUid(sessionUser.getUserId());
            returnDBInsert.setAdminDesc("");
            returnDBInsert.setUid(oldOrderDB.getUid());
            returnDBInsert.setReason("改签退款");
            returnDBInsert.setReasonTxt("");
            returnDBInsert.setUploadFile("");
            returnDBInsert.setMoney(new BigDecimal("0"));
            if (boolReturnMoney) {
                returnDBInsert.setMoney(returnMoney);
            }

            returnDBInsert.setMaxMoney(returnTotal);
            returnDBInsert.setFmbReturnMoney(new BigDecimal("0"));
            returnDBInsert.setShopReturnMoney(new BigDecimal("0"));
            returnDBInsert.setDivideCouponMoney(new BigDecimal("0"));
            returnDBInsert.setReturnCodes("");
            returnDBInsert.setReceiverTxt("");
            returnDBInsert.setReceiver(oldOrderDB.getReceiver());
            returnDBInsert.setBuyerAccount("");
            returnDBInsert.setCountry(0);
            returnDBInsert.setProvince(0);
            returnDBInsert.setCity(0);
            returnDBInsert.setArea(0);
            returnDBInsert.setStreet(0);
            returnDBInsert.setAddress("");
            returnDBInsert.setZipcode("");
            returnDBInsert.setTel("");
            returnDBInsert.setMobile(oldOrderDB.getMobile());
            returnDBInsert.setEmail("");
            returnDBInsert.setShippingName("");
            returnDBInsert.setShippingCode("");
            returnDBInsert.setShippingFee(new BigDecimal("0"));

            returnDBInsert.setStatus(1);
            returnDBInsert.setThirdPartStatus(0);
            returnDBInsert.setIsReturn(0);
            returnDBInsert.setReturnTime(DEFAULT_DATETIME);
            returnDBInsert.setApplyTime(LocalDateTime.now());
            returnDBInsert.setCouponMoney(new BigDecimal("0"));
            returnDBInsert.setCulturalCouponMoney(new BigDecimal("0"));
            returnDBInsert.setUsedCashMoney(new BigDecimal("0"));
            returnDBInsert.setDeductMoney(new BigDecimal("0"));
            returnDBInsert.setBackMoney(new BigDecimal("0"));

            returnDBInsert.setUserCashMoney(new BigDecimal("0"));
            returnDBInsert.setCashNote("");
            if (boolReturnCash) {
                returnDBInsert.setUserCashMoney(returnCash);
                returnDBInsert.setCashNote("返还余额"+FmbNumberUtil.bigDecimal2Str(returnCash));
            }

            returnDBInsert.setReduceMaxMoney(new BigDecimal("0"));
            returnDBInsert.setIdentyCids("");
            returnDBInsert.setReturnGroup("");
            returnDBInsert.setOperTypes("");
            returnDBInsert.setOrderTypes("");

            returnDBInsert.setIsUserReturn(0);
            returnDBInsert.setIsOvertimeReturn(0);

            orderReturnsService.save(returnDBInsert) ;
        }

        // 改签直接成功 需要发改签成功短信
        {
            if (isOrderSuccessStatus(newOrderDB.getOrderStatus())) {
                orderSuccessHandler.sendSmsWhenSuccess(newOrderDB.getOrderId()) ;
            }
        }

    }

    public String buildSnapContent(Integer skuId,Integer suiteId ) {


        HashMap<String, Object> mapSn = new HashMap<>();
        ReqHotel reqHotel = new ReqHotel();
        reqHotel.setSkuId(skuId);
        frontHotelService.getRoomSkuDetail(mapSn,reqHotel,null);

        mapSn.put("suiteBuyNote","");
//        final Integer suiteId = detail.getSuite().getSuiteId();

        final FmbxSuiteExt buyNoteDB = querySuiteBuyNote(suiteId);

        if (buyNoteDB != null) {
            mapSn.put("suiteBuyNote",buyNoteDB.getBuyNote());
        }

        return JSONUtil.toJsonStr(mapSn) ;

    }

    public String buildSnapSummary(String summary, ReqGenSku input, FmbxSku newFmbxSku, FmbxSuiteRoomSku newFmbxSuiteRoomSku, FmbxSuite suiteNew, List<HotelSkuDatePriceDomain> priceDomainList) {

        cn.hutool.json.JSON json = JSONUtil.parse(summary);
        OrderSysInfo orderSysInfo = json.getByPath("orderSysInfo", OrderSysInfo.class);
        cn.hutool.json.JSONArray array = (cn.hutool.json.JSONArray) json.getByPath("reqGenSkus");
        final List<ReqGenSku> reqGenSkus = array.toList(ReqGenSku.class);

        ReqGenSku reqGenSku2 = new ReqGenSku() ;

        if (!reqGenSkus.isEmpty()) {
            final ReqGenSku reqGenSku = reqGenSkus.get(0);

            reqGenSku.setSkuId(input.getSkuId());
            reqGenSku.setCheckInDate(input.getCheckInDate());
            reqGenSku.setCheckOutDate(input.getCheckOutDate());
            reqGenSku.setSkuNumber(input.getSkuNumber());

            BeanUtils.copyProperties(reqGenSku,reqGenSku2);
        }



        if (orderSysInfo.getSkuMains() != null) {
            orderSysInfo.getSkuMains().clear();
            orderSysInfo.getSkuMains().add(newFmbxSku);

        }

        final List<SkuDetail> skuDetails = orderSysInfo.getSkuDetails();
        if (skuDetails != null && !skuDetails.isEmpty()) {
            final SkuDetail skuDetail = skuDetails.get(0);

            skuDetail.setReqGenSku(reqGenSku2);

            skuDetail.setFmbxSku(newFmbxSku);
            skuDetail.setRoomSku(newFmbxSuiteRoomSku);
            skuDetail.setSuite(suiteNew);

            if (skuDetail.getPriceList()!=null) {
                skuDetail.getPriceList().clear();
                skuDetail.getPriceList().addAll(priceDomainList) ;
            }


            BigDecimal bigDecimalMarket = BigDecimal.ZERO;
            BigDecimal bigDecimalGoods = BigDecimal.ZERO;
            BigDecimal bigDecimalSettle = BigDecimal.ZERO;
            for (HotelSkuDatePriceDomain priceDomain : priceDomainList) {
                bigDecimalMarket = bigDecimalMarket.add(new BigDecimal(priceDomain.getMarketPrice())) ;
                bigDecimalGoods = bigDecimalGoods.add(new BigDecimal(priceDomain.getGoodsPrice())) ;
                bigDecimalSettle = bigDecimalSettle.add(new BigDecimal(priceDomain.getSettlePrice())) ;
            }

            skuDetail.setMarketPrice(bigDecimalMarket);
            skuDetail.setGoodsPrice(bigDecimalGoods);
            skuDetail.setGoodsPriceNoSub(bigDecimalGoods);
            skuDetail.setSettlePrice(bigDecimalSettle);

            skuDetail.setGoodsPriceAll(bigDecimalGoods.multiply(new BigDecimal(input.getSkuNumber())));
            skuDetail.setSettlePriceAll(bigDecimalSettle.multiply(new BigDecimal(input.getSkuNumber())));



        }


        json.putByPath("reqGenSkus",reqGenSkus);
        json.putByPath("orderSysInfo",orderSysInfo);


        return JSONUtil.toJsonStr(json) ;
    }


    public String changeGoodsExtInfo(String extInfo, FmbxSku newFmbxSku, FmbxSuiteRoomSku newFmbxSuiteRoomSku,
                                     List<HotelSkuDatePriceDomain> priceDomainList, ReqGenSku input, FmbxSuite suiteNew, FmbOrderGoods goodsOldWillSaveAsNewGoods) {


        final cn.hutool.json.JSON json = JSONUtil.parse(extInfo);

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

        final ReqGenSku reqGenSku = skuDetail.getReqGenSku();
        reqGenSku.setSkuId(input.getSkuId());
        reqGenSku.setCheckInDate(input.getCheckInDate());
        reqGenSku.setCheckOutDate(input.getCheckOutDate());
        reqGenSku.setSkuNumber(input.getSkuNumber());

        skuDetail.setFmbxSku(newFmbxSku);
        skuDetail.setSuite(suiteNew);
        skuDetail.setRoomSku(newFmbxSuiteRoomSku);

        skuDetail.setPriceList(priceDomainList);

        //免补差价
        if (input.getNotPayMoneyDiff()!=null &&  input.getNotPayMoneyDiff()==1) {

        }else {

            //非免补差价
            skuDetail.setGoodsPrice(goodsOldWillSaveAsNewGoods.getGoodsPrice());
            skuDetail.setGoodsPriceNoSub(goodsOldWillSaveAsNewGoods.getGoodsPrice());
            skuDetail.setGoodsPriceAll(goodsOldWillSaveAsNewGoods.getGoodsPrice().multiply(new BigDecimal(goodsOldWillSaveAsNewGoods.getGoodsNumber())));

            skuDetail.setSettlePrice(goodsOldWillSaveAsNewGoods.getSettlePrice());
            skuDetail.setSettlePriceAll(goodsOldWillSaveAsNewGoods.getSettlePrice().multiply(new BigDecimal(goodsOldWillSaveAsNewGoods.getGoodsNumber())));

        }


        json.putByPath("skuDetail",skuDetail);

        return json.toJSONString(0) ;

    }

    public void orderInfoOfChange(HashMap<String, Object> resultMap, ReqGenSku input){

        resultMap.put("changeOrderInfos",orderInfoOfChange(input.getSourceOrderSn())) ;
    }

    /**
     * 根据 订单号 查询 该订单所有的 上下游改签订单
     * @param sourceOrderSn
     * @return
     */
    public List<OrderChangeInfo> orderInfoOfChange(String sourceOrderSn){

        // 改签次序	订单号	订单状态	改签原因	支付方式	交易流水号	订单金额	支付金额	现金退款	余额退款
//        final String sourceOrderSn = input.getSourceOrderSn();

        final ArrayList<OrderChangeInfo> rList  = new ArrayList<>();
        final ArrayList<FmbOrderInfo> all  = new ArrayList<>();
        //向前查询
        final ArrayList<FmbOrderInfo> pre  = new ArrayList<>();
        //向后查询
        final ArrayList<FmbOrderInfo> next  = new ArrayList<>();

        final FmbOrderInfo orderInfo = queryFmbOrderInfo(sourceOrderSn);

        String orderSnPre = orderInfo.getBeforeOrderSn() ;
        String orderSnNext = orderInfo.getChangeToOrder() ;

        while (StringUtils.isNotBlank(orderSnPre)){
            final FmbOrderInfo p = queryFmbOrderInfo(orderSnPre);
            pre.add(p) ;
            orderSnPre = p.getBeforeOrderSn() ;
        }

        //前订单翻转
        Collections.reverse(pre) ;
        //合并进 all
        all.addAll(pre) ;
        //插入当前订单
        all.add(orderInfo) ;


        while (StringUtils.isNotBlank(orderSnNext)){
            final FmbOrderInfo n = queryFmbOrderInfo(orderSnNext);
            next.add(n) ;
            orderSnNext = n.getChangeToOrder() ;
        }
        //插入向后的订单
        all.addAll(next) ;

        //如果只有当前订单 说明这个订单就不存在改签链条
        if (all.size()<2){
            return rList;
        }


        for (FmbOrderInfo fmbOrderInfo : all) {
            rList.add(OrderChangeInfo.toOrderChangeInfo(fmbOrderInfo)) ;
        }

        // SerialNumber 处理
        rList.get(0).setSerialNumber("改签前");
        for (int i = 1; i < rList.size(); i++) {
            final OrderChangeInfo t = rList.get(i);
            t.setSerialNumber("第"+i+"次改签");

            final LambdaQueryWrapper<FmbOrderRelevantLog> eq = new LambdaQueryWrapper<FmbOrderRelevantLog>()
                    .eq(FmbOrderRelevantLog::getOrderSn, t.getOrderSn())
                    .eq(FmbOrderRelevantLog::getOrderRelevantType, "change_order")
                    .eq(FmbOrderRelevantLog::getOrderRelevantIdentifier, "order_change_status")
                    .select(FmbOrderRelevantLog::getOrderRelevantNote,FmbOrderRelevantLog::getRelevantId)
                    ;
            final List<FmbOrderRelevantLog> list = orderRelevantLogService.list(eq);
            if (!list.isEmpty()) {
                t.setChangeReason(list.get(0).getOrderRelevantNote());
            }
        }

        //计算退款金额
        for (OrderChangeInfo o : rList) {

            final List<FmbOrderReturns> fmbOrderReturns =
                    returnService.queryOrderReturns(o.getOrderSn()).stream().filter(r -> r.getStatus() !=3).collect(Collectors.toList());
            if (!fmbOrderReturns.isEmpty()) {
                final BigDecimal money =
                        fmbOrderReturns.stream().map(FmbOrderReturns::getMoney).reduce(BigDecimal.ZERO
                                , BigDecimal::add);
                final BigDecimal cash =
                        fmbOrderReturns.stream().map(FmbOrderReturns::getUserCashMoney).reduce(BigDecimal.ZERO
                                , BigDecimal::add);
                o.setReturnCash(cash);
                o.setReturnMoney(money);
            }
        }

//        resultMap.put("orderChangeList",rList) ;

        return rList ;
    }

}
