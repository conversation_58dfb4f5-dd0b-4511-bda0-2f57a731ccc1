package com.fmb.server2022.service.order;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.FrontApiRequestDomain;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderSysInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqConfirmOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.domain.AdminOrderActionDomain;
import com.fmb.server2022.domain.ConfirmOrderMoneyGroup;
import com.fmb.server2022.domain.ConfirmOrderMoneyInfo;
import com.fmb.server2022.domain.ConfirmOrderMoneyItem;
import com.fmb.server2022.domain.FrontCouponDomain;
import com.fmb.server2022.domain.FrontRoomDomain;
import com.fmb.server2022.domain.HotelSkuDatePriceDomain;
import com.fmb.server2022.domain.stock.SkuSellNumUpdate;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.server2022.fmbx.entity.FmbCouponLog;
import com.fmb.server2022.fmbx.entity.FmbCouponUnableActivity;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.entity.FmbHotelReserveOrderRecord;
import com.fmb.server2022.fmbx.entity.FmbOrderActions;
import com.fmb.server2022.fmbx.entity.FmbOrderCancel;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.entity.FmbOrderTradeSnapshot;
import com.fmb.server2022.fmbx.entity.FmbPayOrders;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbShieldUsers;
import com.fmb.server2022.fmbx.entity.FmbTemplate;
import com.fmb.server2022.fmbx.entity.FmbUserCashLog;
import com.fmb.server2022.fmbx.entity.FmbUserCoupon;
import com.fmb.server2022.fmbx.entity.FmbUserExtinfo;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBpParterHotelInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxBpsContent;
import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.entity.FmbxHotelOrderConfirm;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.service.IFmbCouponLogService;
import com.fmb.server2022.fmbx.service.IFmbCouponTypeService;
import com.fmb.server2022.fmbx.service.IFmbCouponUnableActivityService;
import com.fmb.server2022.fmbx.service.IFmbGoodsCategoryService;
import com.fmb.server2022.fmbx.service.IFmbHotelReserveOrderRecordService;
import com.fmb.server2022.fmbx.service.IFmbOrderActionsService;
import com.fmb.server2022.fmbx.service.IFmbOrderCancelService;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbOrderRelatedDataService;
import com.fmb.server2022.fmbx.service.IFmbOrderTradeSnapshotService;
import com.fmb.server2022.fmbx.service.IFmbPayOrdersService;
import com.fmb.server2022.fmbx.service.IFmbReserveHotelCodesService;
import com.fmb.server2022.fmbx.service.IFmbShieldUsersService;
import com.fmb.server2022.fmbx.service.IFmbTemplateService;
import com.fmb.server2022.fmbx.service.IFmbUserCashLogService;
import com.fmb.server2022.fmbx.service.IFmbUserCouponService;
import com.fmb.server2022.fmbx.service.IFmbUserExtinfoService;
import com.fmb.server2022.fmbx.service.IFmbUsersService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpsContentService;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxHotelOrderConfirmService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxScheduleJobService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.RoomNumStatus;
import com.fmb.server2022.service.CouponService;
import com.fmb.server2022.service.FrontHotelService;
import com.fmb.server2022.service.FrontSkuService;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.server2022.service.schedule.impel.OrderSuccessPayDomain;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbPhpUtil;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import de.ailis.pherialize.Mixed;
import de.ailis.pherialize.MixedArray;
import de.ailis.pherialize.Pherialize;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.server2022.domain.ConfirmOrderMoneyGroup.GROUPTYPE_1;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.HOTEL_SKU_DELETE;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.ORDER_SUCCESS_PAY;
import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_CANCELORDERJOB;

@Service
public class OrderService {

    public static final String FULL_FORMAT = "yyyyMMddHHmmss";
    public static final String FRONT = "front";
    public static final String ADMIN001 = "admin001";

    //删除订单按钮
    private static final int BUTTON_TYPE_DEL = 1;
    //去支付
    private static final int BUTTON_TYPE_PAY = 2;
    //再次购买
    private static final int BUTTON_TYPE_RE_BUY = 3;
    //去预约
    private static final int BUTTON_TYPE_2_HOTEL_RESERVE = 4;
    //去退货
    private static final int BUTTON_TYPE_2_RETURN_SERVICE = 5;
    //取消订单
    private static final int BUTTON_TYPE_CANCEL = 6;

    //日历房
    public static final int GOODS_TYPE_6 = 6;
    //房券
    public static final int GOODS_TYPE_7 = 7;


    private static Logger logger = LoggerFactory.getLogger(OrderService.class);

    @Autowired
    IFmbOrderTradeSnapshotService snapshotService ;

    @Autowired
    IFmbxBpsRoomService bpsRoomService ;

    @Autowired
    IFmbOrderCancelService orderCancelService ;

    @Autowired
    IFmbOrderActionsService orderActionsService ;

    @Autowired
    FrontHotelService frontHotelService;

    @Autowired
    IFmbGoodsCategoryService goodsCategoryService;
    
    @Autowired
    OrderService self;

    @Autowired
    IFmbxBpsContentService bpsContentService ;

    @Autowired
    IFmbUsersService usersService ;

    @Autowired
    IFmbCouponUnableActivityService couponUnableActivityService;

    @Autowired
    IFmbShieldUsersService shieldUsersService;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbxSkuService skuService ;

    @Autowired
    IFmbOrderRelatedDataService relatedDataService ;

    @Autowired
    IFmbReserveHotelCodesService hotelCodesService;

    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService ;

    @Autowired
    IFmbxSuiteService suiteService ;

    @Autowired
    FrontSkuService frontSkuService ;

    @Autowired
    IFmbPayOrdersService payOrdersService ;

    @Autowired
    IFmbxBpParterInfoService bpParterInfoService ;

    @Autowired
    IFmbxBpMainService bpMainService ;

    @Autowired
    IFmbxBpParterHotelInfoService parterHotelInfoService ;

    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    IFmbOrderGoodsService orderGoodsService ;

    @Autowired
    IFmbHotelReserveOrderRecordService hotelReserveOrderRecordService ;

    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    IFmbUserCouponService couponService ;

    @Autowired
    IFmbCouponTypeService couponTypeService ;

    @Autowired
    IFmbUserExtinfoService userExtinfoService ;
    @Autowired
    IFmbUserCashLogService userCashLogService ;


    @Autowired
    IFmbxSuiteStockService suiteStockService ;

    @Autowired
    IFmbxSuiteRoomdatePriceService roomdatePriceService ;


    @Autowired
    SuiteService mySuiteService ;


    @Autowired
    JedisPool jedisPool ;

    @Autowired
    IFmbCouponLogService couponLogService ;


    @Autowired
    OrderOfHotelReserveService orderOfHotelReserveService ;

    @Autowired
    OrderOfHotelDateService orderOfHotelDateService ;

    @Autowired
    FrontSkuDao frontSkuDao ;

    @Autowired
    IFmbTemplateService templateService ;

    @Autowired
    IFmbxScheduleJobService scheduleJobService;

    @Autowired
    CouponService checkCouponService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IFmbxHotelOrderConfirmService orderConfirmService ;




//    IFmbxHotelNeedConfirmService needConfirmService


    public AbstractOrderservice getOrderService(int type){
        AbstractOrderservice orderservice =null;
        switch (type){
            case SKU_HOTEL_DATE:
                orderservice = orderOfHotelDateService ;
                break;
            case SKU_HOTEL_RESERVE:
                orderservice= orderOfHotelReserveService ;
                break;
        }
        return orderservice ;
    }


    public AbstractOrderservice getOrderServiceFromGoodsType(int type){
        AbstractOrderservice orderservice =null;
        switch (type){
            case SKU_HOTEL_DATE_GOODS_TYPE:
                orderservice = orderOfHotelDateService ;
                break;
            case SKU_HOTEL_RESERVE_GOODS_TYPE:
                orderservice= orderOfHotelReserveService ;
                break;
        }
        return orderservice ;
    }


    public final static Pattern MOBILE = Pattern.compile("1[3-9]\\d{9}");

    /**
     * 创建订单方法的 整体 入口
     *
     * @param resultMap
     * @param input
     * @param nowUser
     */
    public void createOrderMain(HashMap<String, Object> resultMap, ReqGenOrder input, NowUser nowUser) {

        //各种业务逻辑判断 ,并组织 价格数据到 input.getOrderSysInfo().getSkuDetails() 的价格信息里
//        input.getOrderSysInfo().getSkuDetails()
        self.checkCanGenOrder(input, nowUser);

        //该方法结束后就知道了用户购买的所有货物的价格信息了
        //存在 input.OrderSysInfo.goodsPriceAll 里
        self.sumPrice2OrderSysInfo(input,nowUser) ;


        //用户资源加锁
        RedissonLockService.doLockJob(FmbConstants.USER_RESOURCE_LOCK, nowUser.getUserId()+"", 3000, 2000,
                () -> self.lockUserResourceAndCreateOrder(input, nowUser),"系统繁忙");

        //运行到这里核心的订单已经生成可以做其他


        //下单流程顺利完成后 做收尾的操作
        // 一般限定做各种异步通知
        // 1. 如果库存减到 0 进行异步通知
        self.doFinalJobWhenCreateOrderOver(input) ;


        //如果直接订单支付成功了
        if (input.getOrderSysInfo().isOrderDirectSuccess()) {
            self.orderSuccessPay(input.getOrderSysInfo().getFmbOrderInfo().getOrderSn(),null) ;
        }
        //输出结果前 执行可以异步执行的任务
        self.doAsyncJob(input);

        self.buildOutInfo(resultMap,input) ;

//        logger.info("createRandomCode {}",createRandomCode());
    }


    /**
     * 注意这个方法是异步执行
     * @param input
     */
    public void doAsyncJob(ReqGenOrder input) {

        FmbScheduleUtil.doAsyncJob( ()->{
            doAsyncJobCore(input) ;
        } );

    }

    public void doAsyncJobCore(ReqGenOrder input) {

        logger.info("doAsyncJobCore_begin");


        if (!input.getOrderSysInfo().isOrderDirectSuccess()) {
            //增加定时任务检查 订单是否支付了 ,没有就取消订单
            HashMap<String, Object> map = new HashMap<>();
            FmbScheduleUtil.addScheduleJob(JOB_NAME_CANCELORDERJOB,input.getOrderSysInfo().getFmbOrderInfo().getOrderId(),map,
                    input.getOrderSysInfo().getCancelSecond()) ;
        }


        //存储 orderAction
        final FmbOrderInfo orderInfo = input.getOrderSysInfo().getFmbOrderInfo();
        FmbOrderActions action = new FmbOrderActions();
        action.setOrderSn(orderInfo.getOrderSn());
        //代表前台用户
        action.setAdminUid(-1);
        action.setOrderStatus(orderInfo.getOrderStatus());
        action.setShippingStatus(orderInfo.getShippingStatus());
        action.setPayStatus(orderInfo.getPayStatus());
        action.setActionType("");
        action.setActionNote("用户下单");
        action.setLogTime(LocalDateTime.now());
        orderActionsService.save(action) ;

        final ArrayList<SkuSellNumUpdate> skuSellNumUpdates = new ArrayList<>();
        //存储 交易快照
        for (SkuDetail detail : input.getOrderSysInfo().getSkuDetails()) {


            FmbOrderTradeSnapshot snapDB = new FmbOrderTradeSnapshot();
            snapDB.setRecId(detail.getRecId());
            snapDB.setOrderSn(orderInfo.getOrderSn());
            snapDB.setUid(orderInfo.getUid());
            snapDB.setBasicInfo("");

            if (GOODS_TYPE_6==orderInfo.getGoodsType()) {

                HashMap<String, Object> mapSn = new HashMap<>();
                ReqHotel reqHotel = new ReqHotel();
                reqHotel.setSkuId(detail.getReqGenSku().getSkuId());
                frontHotelService.getRoomSkuDetail(mapSn,reqHotel,null);
                snapDB.setContent(JSONUtil.toJsonStr(mapSn));

                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());

                skuSellNumUpdates.add(numUpdate) ;

            }
            if (GOODS_TYPE_7==orderInfo.getGoodsType()) {
                HashMap<String, Object> mapSn = new HashMap<>();
                ReqHotel reqHotel = new ReqHotel();
                reqHotel.setSkuId(detail.getReqGenSku().getSkuId());
                frontHotelService.getResSkuDetail(mapSn,reqHotel,null);
                snapDB.setContent(JSONUtil.toJsonStr(mapSn));

                final SkuSellNumUpdate numUpdate = new SkuSellNumUpdate();
                numUpdate.setSkuId(detail.getReqGenSku().getSkuId());
                numUpdate.setSellNum(detail.getReqGenSku().getSkuNumber());
                numUpdate.setGoodSType(orderInfo.getGoodsType());

                skuSellNumUpdates.add(numUpdate) ;
            }

            snapDB.setAppContent("");
            snapDB.setAppContentNew("");
            //把整个下单计算数据都存储上
            snapDB.setSummary(JSONUtil.toJsonStr(input));
            snapDB.setNearScenerys("");
            //从订单里直接 读取 goodstype
            snapDB.setGoodsType(orderInfo.getGoodsType());
            snapDB.setCtime(LocalDateTime.now());
            //新的下单系统 标记
            snapDB.setIsOldData(2);
            snapshotService.save(snapDB) ;

        }
        self.updateSkuSellNum(skuSellNumUpdates) ;

    }

    public void updateSkuSellNum(ArrayList<SkuSellNumUpdate> skuSellNumUpdates) {

        for (SkuSellNumUpdate s : skuSellNumUpdates) {
            getOrderServiceFromGoodsType(s.getGoodSType()).updateSkuSellNum(s) ;
        }

    }

    /**
     * 构造返回结果
     * @param resultMap
     * @param input
     */
    public void buildOutInfo(HashMap<String, Object> resultMap, ReqGenOrder input) {
        resultMap.put("ordersn",input.getOrderSysInfo().getFmbOrderInfo().getOrderSn() ) ;
        resultMap.put("paySuccess",input.getOrderSysInfo().isOrderDirectSuccess()?1:0) ;

    }

    /**
     * 从这里开始因为要锁定用户的权益 并 生成订单.
     * 所以要整体放在一个事务里
     * @param input
     * @param nowUser
     */
    @DS(DB_master)
    @DSTransactional
    public void lockUserResourceAndCreateOrder(ReqGenOrder input, NowUser nowUser) {
        //使用优惠券 并计算优惠券 最终可以抵扣多少金额 并放入input.OrderSysInfo.couponMoney
        self.processDiscountsInfo(input, nowUser.getUserId()) ;
        //使用余额
        self.processUserBalance(input, nowUser) ;
        //生成订单前 做最后的金额计算
        self.processFinalMoneyInfo(input) ;
        //创建订单和 goods
        self.createOrderAndGoods(input, nowUser) ;
        //扣减库存
        self.subStock(input,nowUser) ;
        //把用户 下单过程中使用的各种权益做各种扣减
        self.changeUserRightToUsedStatus(input,nowUser) ;

    }

    /**
     * 当创建订单和扣减权益完成
     * 做收尾工作
     * @param input
     */
    public void doFinalJobWhenCreateOrderOver(ReqGenOrder input) {

        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).doFinalJobWhenCreateOrderOver(input,skuDetail);
        }

    }

    /**
     * 把用户 下单过程中使用的各种权益做各种扣减
     *
     * @param input
     * @param nowUser
     */
    public void changeUserRightToUsedStatus(ReqGenOrder input, NowUser nowUser) {

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();


        //扣减优惠券
        if (orderSysInfo.isUseCoupon()) {

            self.useCoupon(nowUser, orderSysInfo);

        }

        //扣减用户  余额
        if(orderSysInfo.isUseBalance()){

            self.useBalance(nowUser, orderSysInfo);

        }


        if (orderSysInfo.isUseHotelCode()){

            self.useHotelCode(input, nowUser, orderSysInfo);

        }

    }

    /**
     * 修改房券状态
     * 扣减次数
     * @param input
     * @param nowUser
     * @param orderSysInfo
     */
    public void useHotelCode(ReqGenOrder input, NowUser nowUser, OrderSysInfo orderSysInfo) {

        //增加酒店预约单
        FmbHotelReserveOrderRecord orderDB = new FmbHotelReserveOrderRecord();
        orderDB.setGeneralOrderSn(orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn());
        orderDB.setHotelOrderSn(orderSysInfo.getFmbOrderInfo().getOrderSn());
        orderDB.setUid(nowUser.getUserId());
        orderDB.setMobile(input.getMobile());
        orderDB.setReceiver("");
        FmbReserveHotelCodes hotelCode = orderSysInfo.getHotelCode();
        orderDB.setCode(hotelCode.getCode());
        orderDB.setSettlePrice(orderSysInfo.getReserveCodeFromOrderGoods().getSettlePrice());
        orderDB.setGeneralAid(orderSysInfo.getReserveCodeFromOrderInfo().getAid());
        orderDB.setHotelAid(orderSysInfo.getFmbOrderInfo().getAid());
        orderDB.setGeneralTicketId(orderSysInfo.getReserveCodeFromOrderGoods().getGoodsId());
        orderDB.setHotelTicketId(orderSysInfo.getSkuMains().get(0).getSkuId());
        orderDB.setPackageId(0);
        final Integer skuNumber = input.getReqGenSkus().get(0).getSkuNumber();
        orderDB.setGoodsNumber(skuNumber);
        orderDB.setReserveNum(orderSysInfo.getReserveTotalNightRoom());
        orderDB.setUseNum(orderSysInfo.getReserveTotalNightRoom());
        orderDB.setShopUserId(orderSysInfo.getSkuMains().get(0).getBpId());
        final List<HotelSkuDatePriceDomain> priceList = orderSysInfo.getSkuDetails().get(0).getPriceList();
        orderDB.setPlayTime(FmbDateUtil.dateToLocalDate( input.getReqGenSkus().get(0).getCheckInDate()));
        orderDB.setLeaveTime( FmbDateUtil.dateToLocalDate( input.getReqGenSkus().get(0).getCheckOutDate()));
        orderDB.setHouseType("");
        orderDB.setBedType("");
        orderDB.setAdminUid(0);
        orderDB.setConfirmSn("");
        orderDB.setOperName("");
        orderDB.setStatus(1);
        final LocalDateTime now = LocalDateTime.now();

        orderDB.setCtime(now);
        orderDB.setCancelTime(DEFAULT_DATETIME);
        orderDB.setOperType(0);
        orderDB.setSmsType(0);

        hotelReserveOrderRecordService.save(orderDB) ;


        //扣减次数

        boolean updateCodeResult = false ;
        for (int i = 0; i < 3; i++) {
            int aimNum = hotelCode.getNumber() - orderSysInfo.getReserveTotalNightRoom();

            if(aimNum<0){
                throw new BadLogicException("房券使用异常[2154]");
            }

            Wrapper<FmbReserveHotelCodes> upWr2 = new LambdaUpdateWrapper<FmbReserveHotelCodes>()

                    .eq(FmbReserveHotelCodes::getCodeId, hotelCode.getCodeId())
                    .eq(FmbReserveHotelCodes::getNumber, hotelCode.getNumber())
                    .set(FmbReserveHotelCodes::getNumber, aimNum)
                    .set(aimNum == 0, FmbReserveHotelCodes::getStatus, 3)
                    .set(FmbReserveHotelCodes::getUtime, now)
                    .set(FmbReserveHotelCodes::getUseTime, now);
            updateCodeResult = hotelCodesService.update(upWr2);

            if (updateCodeResult) {
                break;
            } else {
                hotelCode = self.queryHotelCode(hotelCode.getCode());
            }
        }

        if (!updateCodeResult) {
            throw new BadLogicException("房券使用异常[2156]");
        }

        logger.info("---before queryHotelCodeIsFinish {}" ,orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn());
        //如果房券用完了 就更新 related 表的 结束标记
        if (queryHotelCodeIsFinish(orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn())) {
            Wrapper<FmbOrderRelatedData> wrRelatedUp = new LambdaUpdateWrapper<FmbOrderRelatedData>()
                    .eq(FmbOrderRelatedData::getOrderSn,orderSysInfo.getReserveCodeFromOrderInfo().getOrderSn())
                    .set(FmbOrderRelatedData::getReserveEndStatus,1)
                    ;
            relatedDataService.update(wrRelatedUp) ;
        }


    }

    /**
     * 修改余额
     * @param nowUser
     * @param orderSysInfo
     */
    public void useBalance(NowUser nowUser, OrderSysInfo orderSysInfo) {
        Wrapper<FmbUserExtinfo> wr = new LambdaUpdateWrapper<FmbUserExtinfo>()

                .eq(FmbUserExtinfo::getId, orderSysInfo.getUserBalance().getId())
                .eq(FmbUserExtinfo::getUtime, orderSysInfo.getUserBalance().getUtime())
                .set(FmbUserExtinfo::getMoney, orderSysInfo.getUserBalance().getMoney().subtract(orderSysInfo.getActUserBalance()))
                .set(FmbUserExtinfo::getUtime,LocalDateTime.now())
                ;
        if (!userExtinfoService.update(wr)) {
            throw new BadLogicException("余额使用失败[1614]");
        }

        FmbUserCashLog dbCashLog = new FmbUserCashLog();
        dbCashLog.setUid(nowUser.getUserId());
        dbCashLog.setOrderSn(orderSysInfo.getFmbOrderInfo().getOrderSn());
        dbCashLog.setMoney(orderSysInfo.getActUserBalance().multiply(new BigDecimal("-1")));
        dbCashLog.setOpenid("");
        dbCashLog.setSourceType(51);
        dbCashLog.setDirectionType(2);
        dbCashLog.setReturnIds("");
        dbCashLog.setNumber(0);
        dbCashLog.setStatus(2);
        dbCashLog.setValidTime(DEFAULT_DATETIME);
        dbCashLog.setCtime(LocalDateTime.now());
        dbCashLog.setUtime(DEFAULT_DATETIME);
        dbCashLog.setAdminUid(0);
        dbCashLog.setNote("订单号"+ orderSysInfo.getFmbOrderInfo().getOrderSn()+"下单抵扣"+ orderSysInfo.getActUserBalance().doubleValue()+"元,为冻结中");

        userCashLogService.save(dbCashLog);
    }

    /**
     * 使用优惠券
     * @param nowUser
     * @param orderSysInfo
     */
    public void useCoupon(NowUser nowUser, OrderSysInfo orderSysInfo) {
        Wrapper<FmbUserCoupon> wrap = new LambdaUpdateWrapper<FmbUserCoupon>()
                .eq(FmbUserCoupon::getCouponSn, orderSysInfo.getFrontCouponDomain().getCouponSn())
                .eq(FmbUserCoupon::getUseStatus,0)
                .set(FmbUserCoupon::getUseStatus,1)
                .set(FmbUserCoupon::getUseTime,LocalDateTime.now())
                .set(FmbUserCoupon::getOrderSn, orderSysInfo.getFmbOrderInfo().getOrderSn())
                ;
        final boolean update = couponService.update(wrap);

        if (!update) {
            throw new BadLogicException("优惠券使用失败[1612]");
        }

        FmbCouponLog couponLog = new FmbCouponLog();
        couponLog.setCouponSn(orderSysInfo.getFrontCouponDomain().getCouponSn());
        couponLog.setLogType("order_used");
        couponLog.setAdminUid(nowUser.getUserId());
        couponLog.setLogNote("下单绑定");
        couponLog.setLogTime(LocalDateTime.now());
        couponLogService.save(couponLog) ;
    }

    /**
     * //生成订单前 做最后的金额计算
     * @param input
     */
    public void processFinalMoneyInfo(ReqGenOrder input) {
        input.getOrderSysInfo().buildMoneyInfoBeforeCreateOrder() ;
    }

    /**
     * 处理用户使用余额
     * @param input
     * @param nowUser
     */
    public void processUserBalance(ReqGenOrder input, NowUser nowUser) {
        //使用余额
        if ( input.shouldUseBalance()) {

            final int userId = nowUser.getUserId();
            final FmbUserExtinfo userBalance = self.queryUserBalance(userId);
            input.getOrderSysInfo().setUserBalance(userBalance);

            //要使用余额 但账户里却没有
            if (userBalance.getMoney().compareTo(BigDecimal.ZERO)!=1) {
                throw new BadLogicException("余额异常[1540]");
            }

        }
    }

    public FmbUserExtinfo queryUserBalance(int userId) {
        Wrapper<FmbUserExtinfo> wr = new LambdaQueryWrapper<FmbUserExtinfo>()
                .eq(FmbUserExtinfo::getUid, userId)
                ;

        final FmbUserExtinfo userBalance = userExtinfoService.getOne(wr);
        return userBalance;
    }

    /**
     * 处理各种优惠
     * @param input
     * @param userId
     */
    public void processDiscountsInfo(ReqGenOrder input, int userId) {
        if ( input.shouldUseCoupon() ) {

            if (!self.couponAidCheck(input.getXaid())) {
                throw new BadLogicException(input.getXaid()+"不能使用优惠券[1578]");
            }

//            final String couponSn = input.getCouponInfo().get(0);
            HashMap<String, Object> par = new HashMap<>();
            par.put("uid", userId);
            par.put("coupon_sn", input.getCouponSn());
            List<FrontCouponDomain> lists = frontSkuDao.selectFrontCoupon(par);
            if (lists.isEmpty()) {
                throw new BadLogicException("优惠券状态异常[1501]");
            }
            final FrontCouponDomain frontCouponDomain = lists.get(0);

            final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
            final FmbxActivity activity = orderSysInfo.getActivity();

            self.couponCoreCheck(input, activity, frontCouponDomain, input.getXaid(), orderSysInfo.getGoodsPriceAll(),orderSysInfo);

            //把优惠券信息放入 orderSysInfo
            orderSysInfo.setFrontCouponDomain(frontCouponDomain);
            orderSysInfo.setUseCoupon(true);

        }
    }

    @DS(DB_slave)
    public boolean couponCategoryCheck(String limitCate, Integer categoryOfActivity) {

        if (StringUtils.isNotBlank(limitCate)) {

            //反序列化出分类信息
            final Mixed unserialize = Pherialize.unserialize(limitCate);
            final MixedArray array = (MixedArray) unserialize.getValue();
            final List<Integer> collect = array.values().stream().map(o -> Integer.parseInt( o.toString())).collect(Collectors.toList());

            List<FmbGoodsCategory> fmbGoodsCategories = new ArrayList<>() ;
            if (!collect.isEmpty()) {
                fmbGoodsCategories = suiteDao.queryCategoryInfo(collect);
            }

            if (!fmbGoodsCategories.isEmpty()) {
                if (!fmbGoodsCategories.stream().map(c->c.getCateId().toString()).collect(Collectors.toSet()).contains(categoryOfActivity.toString())) {
                    return false ;
                }
            }
        }
        return true ;
    }


    /**
     * 返回6位 随机数字字符串
     * @return
     */
    public String createRandomCode(){

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            Long aLong = jedis.incrBy(FmbConstants.REDIS_RANDOM, RandomUtils.nextInt(1001, 9999));
            aLong = 100000 +  aLong%900000L ;
            return  aLong.toString() ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return StringUtils.leftPad( new StringBuffer().append( System.currentTimeMillis()%900000 ).reverse().toString() ,6,"0");
    }

    /**
     * 创建订单并扣减库存
     * @param input
     * @param nowUser
     */
    public void createOrderAndGoods(ReqGenOrder input, NowUser nowUser) {

        final String paySn = SnowflakeIdWorker.getInstance().nextId() + "";

        self.savePayOrderTable(input, paySn);

        FmbOrderInfo fmbOrderInfo = self.saveOrderInfo(nowUser, paySn,input);
        if (fmbOrderInfo == null) {
            logger.error("genOrderError 查询fmb_pay_orders paySn={} ,可查看用户的请求参数",paySn);
            throw new BadLogicException("生成订单异常[1301]");
        }
        self.saveOrderGoods(input,nowUser,fmbOrderInfo) ;
        input.getOrderSysInfo().setFmbOrderInfo(fmbOrderInfo);

    }

    /**
     * 扣库存
     * @param input
     * @param nowUser
     */
    public void subStock(ReqGenOrder input, NowUser nowUser) {

        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).subStock(input,nowUser,skuDetail) ;
        }
    }

    /**
     * 保存goods
     * @param input
     * @param nowUser
     * @param orderInfo
     */
    public void saveOrderGoods(ReqGenOrder input, NowUser nowUser, FmbOrderInfo orderInfo) {
        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            getOrderService(skuDetail.getSkuType()).saveOrderGoods(nowUser, orderInfo, skuDetail,input) ;
        }
    }

    public void savePayOrderTable(ReqGenOrder input, String paySn) {



        FmbPayOrders pay = new FmbPayOrders();

        pay.setPayId(0);
        pay.setPaySn(paySn);
        pay.setMoneyOrderSn("");
        pay.setTradeNo("");
        pay.setRequestIp(MDC.get(FmbConstants.USER_IP));
        pay.setReturnIp("");
        pay.setNotifyIp("");
        pay.setRequestText(JSONUtil.toJsonStr(input));
        pay.setResponseText("");
        pay.setPayType("");
        pay.setReferer("");
        pay.setIsSuccess(0);
        pay.setCtime(LocalDateTime.now());
        payOrdersService.save(pay);
    }


    public FmbOrderInfo saveOrderInfo(NowUser nowUser, String paySn, ReqGenOrder input) {
        String orderSn = createOrderSn();

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();

        final FmbxActivity activity = orderSysInfo.getActivity();

        FmbOrderInfo order = new FmbOrderInfo();
        order.setOrderSn(orderSn);
        order.setChangeToOrder("");
        order.setBeforeOrderSn("");
        order.setOrderName( activity.getTitle());
        order.setPaySn("");
        order.setMyPaySn(paySn);
        order.setThirdSn("");
        order.setBuyerEmail("");
        order.setReceiverEmail("");
        order.setUid(nowUser.getUserInfo().getUid());
        order.setMycpsId(0);

        order.setShippingStatus(0);
        order.setTraceStatus(0);
        order.setPayStatus(0);
        order.setConfirmStatus(0);
        order.setConfirmRecheckStatus(0);
        order.setOccupyStatus(0);
        order.setPayClear(0);
        order.setMycpsSettleStatus(0);

        order.setAid(activity.getXaid());
        order.setOrderCityId(activity.getCityId());
        order.setUserCityId(input.getSiteId()==null?1:input.getSiteId());
        order.setShopUserId(activity.getBpId());
        order.setShopWarehouseId(0);
        order.setGoodsGroupId(0);

        order.setActivityType(1);
        order.setShapeType(1);

        //使用房券的单子 OrderType 标记为2
        if (orderSysInfo.isUseHotelCode()) {
            order.setOrderType(2);
        }else {
            order.setOrderType(1);
        }


        order.setChangeType(0);


        order.setCountry(1);
        order.setsDistrictId(0);
        order.setProvince(0);
        order.setCity(0);
        order.setArea(0);
        order.setStreet(0);
        order.setAddress("");
        order.setZipcode("");
        order.setTel("");
        order.setMobile(input.getMobile());
        order.setEmail("");
        order.setWeixin("");
        order.setPostscript(input.getNote()==null?"":StringUtils.left(input.getNote(),512));

        HashMap<String, Object> extInfo = new HashMap<>();

        order.setReceiver("");
        //酒店订单
        if (orderSysInfo.isHaveHotelDateSku()) {
            final Optional<SkuDetail> first = orderSysInfo.getSkuDetails().stream().filter(d -> d.getRoomSku() != null).findFirst();
            if (first.isPresent()) {
                extInfo.put("hotelUserName",first.get().getReqGenSku().getHotelUserName()) ;
                order.setReceiver(first.get().getReqGenSku().getHotelUserName().stream().collect(Collectors.joining(",")));
            }
            extInfo.put("hotelDate",1) ;

            order.setGoodsType(GOODS_TYPE_6);


        }
        //购买房券
        if (orderSysInfo.isHaveHotelReserveSku()) {
            extInfo.put("buyHotelCode",1) ;

            final Optional<SkuDetail> first = orderSysInfo.getSkuDetails().stream().filter(d -> d.getReserveSku() != null).findFirst();
            if (first.isPresent()) {
                extInfo.put("hotelUserName",first.get().getReqGenSku().getHotelUserName()) ;
                order.setReceiver(first.get().getReqGenSku().getHotelUserName().stream().collect(Collectors.joining(",")));
            }

            order.setGoodsType(GOODS_TYPE_7);
        }
        //使用房券
        if (orderSysInfo.isUseHotelCode()) {
            extInfo.put("useHotelCode",1) ;
            extInfo.put("hotelCode",orderSysInfo.getHotelCode().getCode()) ;
        }


        order.setExtInfo(JSONUtil.toJsonStr(extInfo));
        order.setCertInfo("");
        order.setInvType(0);
        order.setInvPayee("");
        order.setInvContent("");
        order.setAdultNum(0);
        order.setChildNum(0);
        order.setMoney(orderSysInfo.getGoodsPriceAll());
        order.setShippingFee(orderSysInfo.getShippingFee());
        order.setShippingMethod(0);
        order.setPaySource("");
        order.setPayNote("");
        order.setEditorNote("");
        order.setReferer(input.apiType());
        order.setOrderReferer(1);

        if (input.checkFMB_APP()) {
            order.setAppname("fmb");
        }else {
            order.setAppname("");
        }
        order.setCreateTime(LocalDateTime.now());
        order.setConfirmTime(DEFAULT_DATETIME);
        order.setPayTime(DEFAULT_DATETIME);
        order.setConfirmUpdateTime(DEFAULT_DATETIME);
        order.setShippingTime(DEFAULT_DATETIME);
        order.setPlayTime(DEFAULT_DATETIME);
        order.setUseCoupon(orderSysInfo.isUseCoupon()?1:0);
        order.setCouponMoney(orderSysInfo.getCouponMoney().intValue());
        order.setCulturalCouponMoney(0);
        order.setCashMoney(orderSysInfo.getActUserBalance());
        if (orderSysInfo.isRateCoupon()) {
            order.setDiscountRate(orderSysInfo.getFrontCouponDomain().getDiscountRate());
        }else {
            order.setDiscountRate(0);
        }

        order.setAppSubMoney(0);
        //这里是用户真正要支付的金额
        final BigDecimal goodsPriceAllFinal = orderSysInfo.getGoodsPriceAllFinal();
        order.setRealpayMoney(goodsPriceAllFinal);

        //如果用户需要支付0元 就直接订单交易成功了
        if (goodsPriceAllFinal.compareTo(BigDecimal.ZERO)==0) {
            order.setOrderStatus(5);
            input.getOrderSysInfo().setOrderDirectSuccess(true);
        }else {
            order.setOrderStatus(0);
        }

        order.setCpsName("");
        order.setCpsCookie("");
        order.setVersion(input.getVersion());
        order.setIsDelete(0);

        //防止订单号重复
        for (int i = 0; i < 5; i++) {
            try {
                    boolean save = orderInfoService.save(order);
                    if (save) {
                        return order ;
                    }
            }catch (Exception ex){

                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);

                if (!ex.getMessage().contains("Duplicate entry")) {
                    return null ;
                }
            }
            order.setOrderSn(createOrderSn());
        }

        return null;
    }

    public String createOrderSn() {
        return DateTime.now().toString(FULL_FORMAT) + createRandomCode();
    }

    public void sumPrice2OrderSysInfo(ReqGenOrder input, NowUser nowUser) {

        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
        for (SkuDetail skuDetail : input.getOrderSysInfo().getSkuDetails()) {
            orderSysInfo.setGoodsPriceAll(orderSysInfo.getGoodsPriceAll().add(skuDetail.getGoodsPriceAll()));
            orderSysInfo.setSettlePriceAll(orderSysInfo.getSettlePriceAll().add(skuDetail.getSettlePriceAll()));
        }

    }

    /**
     * 判断是否可以下单
     *
     * @param input
     * @param nowUser
     */
    @DS(FmbConstants.DB_slave)
    public void checkCanGenOrder(ReqGenOrder input, NowUser nowUser) {

        if (nowUser.getUserInfo() == null) {
            throw new BadLogicException("需要登录后操作[100]");
        }
        if (input.getReqGenSkus()==null || input.getReqGenSkus().isEmpty()) {
            throw new BadLogicException("sku信息非法[80]");
        }
        if (  !isMobile(input.getMobile())) {
            throw new BadLogicException(input.getMobile()+",手机号码错误[82]");
        }

        final Map<String, List<ReqGenSku>> reqSkuMaps =
                input.getReqGenSkus().stream().collect(Collectors.groupingBy(x->x.getSkuId().toString()));

        //请求的sku信息 按理说是 按skuid 分组是 唯一的
        //如果不唯一说明有问题
        if (reqSkuMaps.size()!=input.getReqGenSkus().size()) {
            throw new BadLogicException("sku信息非法[84]");
        }

        Wrapper<FmbShieldUsers> wr = new LambdaQueryWrapper<FmbShieldUsers>()
                .eq(FmbShieldUsers::getUid, nowUser.getUserInfo().getUid());
        //用户屏蔽查询
        if (shieldUsersService.count(wr) > 0) {
            throw new BadLogicException("用户状态异常[110]");
        }

        final FmbxActivity activity = self.queryFmbxActivity(input.getXaid());
        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();
        orderSysInfo.setActivity(activity);

        if (activity == null) {
            throw new BadLogicException("产品信息异常[130]");
        }

        if (input.shouldCheckActivity()) {
            //活动层面校验
            self.checkActivity(activity,input) ;
        }

        //房券基础校验
        if(input.useHotelReserveCode()){
            self.checkHotelReserveCode(input.getHotelReserveCode(),nowUser.getUserId(),orderSysInfo) ;
        }


        self.buildSkuDetails(input, reqSkuMaps);

        for (SkuDetail skuDetail : orderSysInfo.getSkuDetails()) {

            if (skuDetail.getReqGenSku().getSkuNumber() == null || skuDetail.getReqGenSku().getSkuNumber() < 1) {
                throw new BadLogicException("skuid=" + skuDetail.getReqGenSku().getSkuId() + "购买数量异常.[163]");
            }
            skuDetail.setSkuNumber(skuDetail.getReqGenSku().getSkuNumber());

            self.baseCheckOfEachSkuType(skuDetail,nowUser,input) ;
            self.buildSkuSinglePriceOfEachType(skuDetail,nowUser,input) ;
            //构造 订单自动取消秒数
            orderSysInfo.setCancelSecond(getOrderService(skuDetail.getSkuType()).getCancelSecond(skuDetail)) ;

        }


        //房券数量校验  这里要使用到  skuDetail.getPriceList()
        // 所以需要放在   buildSkuSinglePriceOfEachType 后面
        if (input.useHotelReserveCode()) {
            final SkuDetail skuDetail = input.getOrderSysInfo().getSkuDetails().get(0);

            final int nightNum = skuDetail.getPriceList().size();
            //想要预定的总总间数    间数*晚数
            final int totalNightRoom = skuDetail.getSkuNumber() * nightNum;
            //该房券拥有的间夜数
            final Integer cardHaveNumNow = input.getOrderSysInfo().getHotelCode().getNumber();

            if (totalNightRoom>cardHaveNumNow) {
                throw new BadLogicException("房券数量不足[2109]");
            }

            input.getOrderSysInfo().setReserveTotalNightRoom(totalNightRoom);


            final FmbxHotelReserveSku reserveCodeSourceSku = orderOfHotelReserveService.querySkuReserve(input.getOrderSysInfo().getHotelCode().getTicketId());
            final String invalidReserveWeekDays = reserveCodeSourceSku.getInvalidReserveWeekDays();
            final String invalidReserveDate = reserveCodeSourceSku.getInvalidReserveDate();

            final Set<Integer> collectWeek = Stream.of(invalidReserveWeekDays.split(",")).map(x -> Integer.parseInt(x)).collect(Collectors.toSet());
            final Set<String> collectDate =
                    Stream.of(invalidReserveDate.split(",")).map(x -> x.toString().trim()).collect(Collectors.toSet());

            for (HotelSkuDatePriceDomain moneyItem : input.getOrderSysInfo().getSkuDetails().get(0).getPriceList()) {

                String d  =FmbDateUtil.toDate10Str(moneyItem.getDatecol()) ;

                if (reserveDateIsLimit(d, collectDate)) {
                    throw new BadLogicException(d+"不能预约[3490]");
                }
                if (reserveWeekIsLimit( DateTime.parse(d).getDayOfWeek(),collectWeek)) {
                    final int dayOfWeek = DateTime.parse(d).getDayOfWeek();
                    throw new BadLogicException(d+"是星期"+(( dayOfWeek !=7 )?dayOfWeek+"":"日")+"不能预约[3491]");
                }

            }


        }



    }


    /**
     * 校验房券
     * @param code
     * @param uid
     * @param orderSysInfo
     */
    public void checkHotelReserveCode(String code, int uid, OrderSysInfo orderSysInfo) {

        logger.info("code=[{}]",code);

        if (StringUtils.isNotBlank(code)) {
            if( !code.startsWith("H") || !code.substring(1).matches("\\d{8}")){
                throw new BadLogicException("房券码异常[2302]");
            }
        }


        final FmbReserveHotelCodes codeDB = self.queryHotelCode(code);
        if (codeDB == null) {
            throw new BadLogicException("房券状态异常[2312]");
        }

        if (  codeDB.getStatus()!=1 || codeDB.getNumber()<1) {
            throw new BadLogicException("房券状态异常[2315]");
        }

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,codeDB.getOrderSn())
                .select(FmbOrderInfo::getOrderId,FmbOrderInfo::getOrderSn,FmbOrderInfo::getUid,FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getMoney,
                        FmbOrderInfo::getAid
                        )
                ;
        FmbOrderInfo reserveCodeFromOrderInfo = orderInfoService.getOne(wrOrder);
        if (reserveCodeFromOrderInfo == null  ) {
            throw new BadLogicException("房券状态异常[2317]");
        }
        if ( reserveCodeFromOrderInfo.getUid().intValue()!=uid) {
            throw new BadLogicException("房券用户状态异常[2339]");
        }

        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn,codeDB.getOrderSn())
                .eq(FmbOrderGoods::getGoodsType, SKU_HOTEL_RESERVE_GOODS_TYPE)
                .eq(FmbOrderGoods::getGoodsId, codeDB.getTicketId())
                .select(
                        FmbOrderGoods::getRecId
                        , FmbOrderGoods::getGoodExt
                        , FmbOrderGoods::getGoodsId
                        , FmbOrderGoods::getGoodsNumber
                        , FmbOrderGoods::getGoodsType
                        , FmbOrderGoods::getGoodsPrice
                        , FmbOrderGoods::getSettlePrice
                        , FmbOrderGoods::getMarketPrice
                )
                ;
        FmbOrderGoods reserveCodeFromOrderGoods = orderGoodsService.getOne(goodsWr);
        if (reserveCodeFromOrderGoods == null) {
            throw new BadLogicException("房券状态异常[2319]");
        }

        orderSysInfo.setReserveCodeFromOrderInfo(reserveCodeFromOrderInfo);
        orderSysInfo.setReserveCodeFromOrderGoods(reserveCodeFromOrderGoods);

        final String goodExt = reserveCodeFromOrderGoods.getGoodExt();
        FmbxSuite suite = JSONUtil.parse(goodExt).getByPath("skuDetail.suite", FmbxSuite.class);
        orderSysInfo.setReserveCodeSourceSuite(suite);
        orderSysInfo.setHotelCode(codeDB);

        final FmbxHotelReserveSku reserveCodeSourceSku = orderOfHotelReserveService.querySkuReserve(codeDB.getTicketId());
        orderSysInfo.setReserveCodeSourceSku(reserveCodeSourceSku);


        orderSysInfo.setUseHotelCode(true);

        final Integer totalNeight = suite.getTotalNeight();
        final BigDecimal goodsPrice = reserveCodeFromOrderGoods.getGoodsPrice();

        final BigDecimal hotelCodeEachNightPay = goodsPrice.divide(new BigDecimal(totalNeight));
        orderSysInfo.setHotelCodeEachNightPay(hotelCodeEachNightPay);



    }

    public FmbReserveHotelCodes queryHotelCode(String code) {
        return queryHotelCode(code,1) ;
    }

    /**
     * 查询房券 根据 condition
     * @param condition
     * @param type 1 -根据code 查询
     *             2 -根据订单号查
     * @return
     */
    public FmbReserveHotelCodes queryHotelCode(String condition,int type) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq(1==type, FmbReserveHotelCodes::getCode, condition)
                .eq(2==type, FmbReserveHotelCodes::getOrderSn, condition)
                .select(
                        FmbReserveHotelCodes::getCodeId
                        , FmbReserveHotelCodes::getCode
                        , FmbReserveHotelCodes::getOrderSn
                        , FmbReserveHotelCodes::getNumber
                        , FmbReserveHotelCodes::getStatus
                        ,FmbReserveHotelCodes::getTicketId

                );

        final FmbReserveHotelCodes codeDB = hotelCodesService.getOne(wr);
        return codeDB;
    }


    public List<FmbReserveHotelCodes> queryHotelCodeList(String condition, int type) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq(1==type, FmbReserveHotelCodes::getCode, condition)
                .eq(2==type, FmbReserveHotelCodes::getOrderSn, condition)
                .select(
                        FmbReserveHotelCodes::getCodeId
                        , FmbReserveHotelCodes::getCode
                        , FmbReserveHotelCodes::getOrderSn
                        , FmbReserveHotelCodes::getNumber
                        , FmbReserveHotelCodes::getStatus
                        ,FmbReserveHotelCodes::getTicketId

                );

        final List<FmbReserveHotelCodes> list = hotelCodesService.list(wr);
        return list;
    }


    /**
     * 房券订单是否有 可用房券
     * @param condition
     * @return
     */
    public boolean queryHotelCodeIsFinish(String condition) {
        Wrapper<FmbReserveHotelCodes> wr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
                .eq( FmbReserveHotelCodes::getOrderSn, condition)
                .gt(FmbReserveHotelCodes::getNumber,0)
                .eq(FmbReserveHotelCodes::getStatus,1)
                ;

        return hotelCodesService.count(wr) == 0  ;
    }




    /**
     * 把用户的输入的sku 信息 构造为 Skudetail 对象
     * 方便各种sku 分不同service 处理
     * @param input
     * @param reqSkuMaps
     */
    public void buildSkuDetails(ReqGenOrder input, Map<String, List<ReqGenSku>> reqSkuMaps) {
        //查询  基础sku信息
        final List<FmbxSku> skuMains = self.queryFmbxSkus(input.getReqGenSkus());
        input.getOrderSysInfo().getSkuMains().addAll(skuMains) ;

        //如果系统查到的 sku数量 为0  或者 用户输入sku数量对不上 就说明sku信息不正确
        if (skuMains.size()==0 || skuMains.size()!= input.getReqGenSkus().size()){
            throw new BadLogicException("sku数据异常[140]");
        }

        final Map<String, List<FmbxSku>> skuMainOfDBMap = skuMains.stream().collect(Collectors.groupingBy(x -> x.getSkuId().toString()));

        //通过这个循环 把具体的sku 信息都放入 OrderSysInfo 的 skuDetails里
        for (FmbxSku skuMain : input.getOrderSysInfo().getSkuMains()) {

           getOrderService(skuMain.getSkuType().intValue()).buildSkuDetail(input, reqSkuMaps, skuMainOfDBMap, skuMain);
        }


        //通过 房券预约走这个检查
        if (input.useHotelReserveCode() ) {
            //酒店预约下单只能 传入酒店日历房的skuid
            for (FmbxSku skuMain : input.getOrderSysInfo().getSkuMains()) {
                if (FmbConstants.SKU_HOTEL_DATE!=  skuMain.getSkuType().intValue()) {
                    throw new BadLogicException("酒店预约下单sku数据异常[2150]");
                }
            }

            //只能传一个 酒店日历房的 sku id
            if (input.getOrderSysInfo().getSkuMains().size()!=1) {
                throw new BadLogicException("酒店预约下单sku数据异常[2152]");
            }

        }


    }

    public List<FmbxSku> queryFmbxSkus(List<ReqGenSku> input) {
        Wrapper<FmbxSku> wrSku = new LambdaQueryWrapper<FmbxSku>()
                .in(FmbxSku::getSkuId, input.stream().map(x->x.getSkuId()).collect(Collectors.toList()))
                .select(FmbxSku::getSkuId,FmbxSku::getSkuName,FmbxSku::getXaid,FmbxSku::getBpsId,FmbxSku::getBpId,FmbxSku::getSkuType)
                ;
        final List<FmbxSku> skuMains = skuService.list(wrSku) ;
        return skuMains;
    }

    /**
     * 判断是否和合法的手机号
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        return StringUtils.isNotBlank(mobile) &&  Validator.isMatchRegex(MOBILE,mobile);
    }

    /**
     * 活动层面校验
     * @param activity
     * @param input
     */
    @DS(FmbConstants.DB_slave)
    public void checkActivity(FmbxActivity activity, ReqGenOrder input) {

        final String[] platIds = activity.getPlatId().split(",");
        final HashSet<String> setPlatIds = new HashSet<>();
        for (String platId : platIds) {
            setPlatIds.add(platId) ;
        }

        //如果不是全平台  售卖平台校验
        if (!setPlatIds.contains("0")) {
//            显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
            if (input.checkH5() && !setPlatIds.contains("3")) {
                throw new BadLogicException("当前平台购买受限[901]");
            }
            else if (input.checkWxApp() && !setPlatIds.contains("10")) {
                throw new BadLogicException("当前平台购买受限[904]");
            }
            else if (input.checkH5() && ( !setPlatIds.contains("1")  && !setPlatIds.contains("2") ) ) {
                throw new BadLogicException("当前平台购买受限[907]");
            }
        }


        //活动删除状态校验
//          `flag_delete` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '删除:0-正常,1-被删除',
        if (1== activity.getFlagDelete().intValue()) {
            throw new BadLogicException("aid="+activity.getXaid()+",当前活动不能购买[910]");
        }


        //活动上下架状态校验
//          `flag_publish` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '上下架状态:0-待发布,1-已上架,2-已下架',
        if (1!= activity.getFlagPublish().intValue()) {
            throw new BadLogicException("aid="+activity.getXaid()+",当前活动发布状态异常[914]");
        }

    }



    @DS(FmbConstants.DB_slave)
    public FmbxActivity queryFmbxActivity(Integer aid) {
        Wrapper<FmbxActivity> aWr = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid, aid)
                .select(FmbxActivity::getXaid,FmbxActivity::getBpsId,FmbxActivity::getBpId,FmbxActivity::getBpcId,
                        FmbxActivity::getCategoryId,FmbxActivity::getTicketType,FmbxActivity::getTitle,FmbxActivity::getPlatId,

                        FmbxActivity::getCityId,FmbxActivity::getSalesCityIds,FmbxActivity::getBusinessType,FmbxActivity::getIsConfirm,
                        FmbxActivity::getFlagDelete,FmbxActivity::getFlagHaveValidSku,FmbxActivity::getFlagPublish)
                ;
        //查出活动信息
        final FmbxActivity activity = activityService.getOne(aWr);
        return activity;
    }

    /**
     * 做基础的sku 校验
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    public void baseCheckOfEachSkuType(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {
        getOrderService(skuDetail.getSkuType()).baseCheck(skuDetail,nowUser,input) ;
    }

    /**
     * 通用的价格构造方法
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    public void buildSkuSinglePriceOfEachType(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {
        getOrderService(skuDetail.getSkuType()).buildPrice(skuDetail,input);
    }


    /**
     * 用户成功支付时 给用户分发相关权益
     * @param orderSn
     * @param orderSuccessPayDomain  支付的信息
     *                               如果为空 目前是下单直接就成功的
     */
    @DSTransactional
    @DS(DB_master)
    public void orderSuccessPay(String orderSn,OrderSuccessPayDomain orderSuccessPayDomain){

        logger.info("orderSuccessPay orderSn={} orderSuccessPayDomain={}",orderSn,JSON.toJSONString(orderSuccessPayDomain));
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,orderSn)
                .select(FmbOrderInfo::getOrderId,  FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getUid,
                        FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getRealpayMoney,
                        FmbOrderInfo::getAid)
                ;
        final FmbOrderInfo orderInfoDB = orderInfoService.getOne(wrOrderInfo);

        final FmbxActivity activity = queryFmbxActivity(orderInfoDB.getAid());

        if (orderInfoDB == null) {
            throw new BadLogicException("订单不存在,order_sn"+orderSn) ;
        }

        //把订单改为交易成功
        FmbOrderInfo orderUpdateDB = new FmbOrderInfo();
        orderUpdateDB.setOrderId(orderInfoDB.getOrderId());
        orderUpdateDB.setPayTime(LocalDateTime.now());

        //当订单状态为未支付时
        if (0==orderInfoDB.getOrderStatus().intValue()) {

            //通过各种三方交易平台返回
            if (orderSuccessPayDomain != null) {
                orderUpdateDB.setPaySource(orderSuccessPayDomain.getPayType());
                if (new BigDecimal(orderSuccessPayDomain.getMoney()).compareTo(orderInfoDB.getRealpayMoney())!=0) {
                    throw new BadLogicException("订单支付金额和预计支付金额不匹配,实际支付:"+orderSuccessPayDomain.getMoney()+"预计需要支付:"+orderInfoDB.getRealpayMoney());
                }

                orderInfoDB.setPayStatus(2);

                orderUpdateDB.setPaySn(orderSuccessPayDomain.getPay_sn());
            }
        }
        if (orderSuccessPayDomain == null) {
            orderUpdateDB.setPaySource("directPayOK");
            orderUpdateDB.setOrderStatus(5);
            orderInfoDB.setPayStatus(2);
        }
        buildOrderStatus(orderUpdateDB,orderInfoDB.getGoodsType(),orderInfoDB.getOrderType(),activity.getIsConfirm()) ;


        final boolean updateResult = orderInfoService.updateById(orderUpdateDB);

        if (updateResult) {


            HashMap<String, Object> par = new HashMap<>();
            par.put("OrderId", orderInfoDB.getOrderId());
            FmbKafkaProducerService.send(ORDER_SUCCESS_PAY,orderInfoDB.getOrderId()+"",par);



            Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                    .eq(FmbOrderGoods::getOrderSn,orderSn)
                    .select(FmbOrderGoods::getGoodsId,FmbOrderGoods::getGoodsType, FmbOrderGoods::getGoodsNumber, FmbOrderGoods::getGoodExt)
                    ;
            final List<FmbOrderGoods> orderGoods = orderGoodsService.list(wrGoods);

            for (FmbOrderGoods orderGood : orderGoods) {
                getOrderServiceFromGoodsType(orderGood.getGoodsType()).createUserAsset(orderGood,orderInfoDB) ;
            }
        }

    }

    public void buildOrderStatus(FmbOrderInfo orderUpdateDB, Integer goodsType, Integer orderType, Integer isConfirm) {

        if (goodsType==GOODS_TYPE_6) {
            //需要二次确认
            if (isConfirm==0) {
                orderUpdateDB.setOrderStatus(5);
            }else {
                orderUpdateDB.setOrderStatus(8);
            }
        }else {
            orderUpdateDB.setOrderStatus(5);
        }


    }

    /**
     *
     * @param orderId
     * @param cancelType 取消来源
     */
    @DSTransactional
    @DS(DB_master)
    public void cancelOrder(  Integer orderId, String cancelType){

        self.cancelOrder(orderId,cancelType,1,null,null) ;
    }


    public boolean cancelOrder(Integer orderId, String cancelType, int conditionType, String orderSn, ReqorderDetail input){

        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(conditionType==1, FmbOrderInfo::getOrderId,orderId)
                .eq(conditionType==2, FmbOrderInfo::getOrderSn,orderSn)
                .select( FmbOrderInfo::getOrderId , FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getUseCoupon,
                        FmbOrderInfo::getCashMoney,
                        FmbOrderInfo::getOrderStatus,
                        FmbOrderInfo::getShippingStatus,
                        FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getUid)
                ;
        final FmbOrderInfo fmbOrderInfo = orderInfoService.getOne(wrOrderInfo);

        if (fmbOrderInfo == null) {
            throw new BadLogicException("订单不存在,orderId"+orderId) ;
        }

        //目前只对 未支付的操作 230222
        if (fmbOrderInfo.getOrderStatus()==0) {
            self.cancelOrderInfo(fmbOrderInfo,cancelType) ;

            Wrapper<FmbOrderGoods> wrGoods = new LambdaQueryWrapper<FmbOrderGoods>()
                    .eq(FmbOrderGoods::getOrderSn,fmbOrderInfo.getOrderSn())
                    .select(FmbOrderGoods::getGoodsId,FmbOrderGoods::getGoodsType, FmbOrderGoods::getGoodsNumber, FmbOrderGoods::getGoodExt)
                    ;
            final List<FmbOrderGoods> orderGoods = orderGoodsService.list(wrGoods);


            for (FmbOrderGoods orderGood : orderGoods) {
                getOrderServiceFromGoodsType(orderGood.getGoodsType()).cancelUserGoods(orderGood,fmbOrderInfo) ;
            }


            FmbOrderCancel cancelDb = new FmbOrderCancel();

            cancelDb.setOrderSn(fmbOrderInfo.getOrderSn());

            if (input != null) {
                cancelDb.setCancelReason(input.getCancelId());
                cancelDb.setCancelDetail(input.getMess());
                cancelDb.setAdminUid(-1);
            }else {
                cancelDb.setCancelReason(0);
                cancelDb.setCancelDetail("系统自动取消");
                cancelDb.setAdminUid(0);
            }
            cancelDb.setCtime(LocalDateTime.now());
            orderCancelService.save(cancelDb);

            FmbOrderActions action  = new FmbOrderActions();

            action.setOrderSn(fmbOrderInfo.getOrderSn());
            if (input != null) {
                //代表前台用户
                action.setAdminUid(-1);
                action.setActionNote( input.apiType()+ "用户取消订单");
            }else {
                action.setAdminUid(0);
                action.setActionNote("系统取消订单");
            }
            action.setOrderStatus(6);
            action.setShippingStatus(fmbOrderInfo.getShippingStatus());
            action.setPayStatus(fmbOrderInfo.getPayStatus());
            action.setActionType("");

            action.setLogTime(LocalDateTime.now());

            orderActionsService.save(action) ;

        }


        return true ;

    }



    public String smsContentGen(FmbOrderInfo orderInfo){



        return "" ;
    }





    /**
     * 取消订单 , 订单表的操作
     * @param fmbOrderInfo
     * @param auto_cancel
     */
    public void cancelOrderInfo(FmbOrderInfo fmbOrderInfo, String auto_cancel) {
        //如果还是未支付状态
        if (fmbOrderInfo.getOrderStatus()==0) {
            // 更新订单状态
            FmbOrderInfo orderUpdateDB = new FmbOrderInfo();
            orderUpdateDB.setOrderId(fmbOrderInfo.getOrderId());
            orderUpdateDB.setOrderStatus(6);
            orderInfoService.updateById(orderUpdateDB);


            RedissonLockService.doLockJob(FmbConstants.USER_RESOURCE_LOCK, fmbOrderInfo.getUid()+"", 5000, 4000,
                    () -> self.returnUserAssert(fmbOrderInfo, auto_cancel),"系统繁忙");

        }
    }

    /**
     * 返还 用户的优惠券和余额
     * @param fmbOrderInfo
     * @param auto_cancel
     */
    public void returnUserAssert(FmbOrderInfo fmbOrderInfo, String auto_cancel) {

        final Integer uid = fmbOrderInfo.getUid();
        //退还优惠券
        if (1== fmbOrderInfo.getUseCoupon().intValue()) {

            self.returnCoupon(fmbOrderInfo, auto_cancel, uid);

        }

        //需要给用户退余额
        if (fmbOrderInfo.getCashMoney().compareTo(BigDecimal.ZERO)==1) {

          self.returnUserBalance(fmbOrderInfo, uid);

        }

        //酒店预约
        if(2==fmbOrderInfo.getOrderType()){

          self.returnHotelCode(fmbOrderInfo);

        }

    }

    public void returnHotelCode(FmbOrderInfo fmbOrderInfo) {
        Wrapper<FmbHotelReserveOrderRecord> wr = new LambdaQueryWrapper<FmbHotelReserveOrderRecord>()
                .eq(FmbHotelReserveOrderRecord::getHotelOrderSn, fmbOrderInfo.getOrderSn())
                .select(FmbHotelReserveOrderRecord::getStatus,FmbHotelReserveOrderRecord::getOrId,FmbHotelReserveOrderRecord::getReserveNum
                ,FmbHotelReserveOrderRecord::getCode,FmbHotelReserveOrderRecord::getGeneralOrderSn
                )
                ;
        final FmbHotelReserveOrderRecord orderRecordServiceOne = hotelReserveOrderRecordService.getOne(wr);
        final FmbOrderRelatedData fmbOrderRelatedData = queryFmbOrderRelatedData(orderRecordServiceOne.getGeneralOrderSn());

        if (1==orderRecordServiceOne.getStatus().intValue()) {
            FmbHotelReserveOrderRecord rDBUpdate = new FmbHotelReserveOrderRecord();
            rDBUpdate.setOrId(orderRecordServiceOne.getOrId());
            rDBUpdate.setStatus(2);
            rDBUpdate.setCancelTime(LocalDateTime.now());
            hotelReserveOrderRecordService.updateById(rDBUpdate) ;

//            Wrapper<FmbReserveHotelCodes> codeWr = new LambdaQueryWrapper<FmbReserveHotelCodes>()
//                    .eq(FmbReserveHotelCodes::getCode,orderRecordServiceOne.getCode())
//                    .eq(FmbReserveHotelCodes::getOrderSn,orderRecordServiceOne.getGeneralOrderSn())
//                    .select(FmbReserveHotelCodes::getCodeId,FmbReserveHotelCodes::getStatus,FmbReserveHotelCodes::getNumber)
//                    ;
//            final FmbReserveHotelCodes aimCodeInfo = hotelCodesService.getOne(codeWr);

            boolean returnCodeFlag =false ;

            for (int i = 0; i < 3; i++) {
                FmbReserveHotelCodes aimCodeInfo = queryHotelCode(orderRecordServiceOne.getCode()) ;
                //返还次数

                int aimNum = aimCodeInfo.getNumber() + orderRecordServiceOne.getReserveNum();

                //返回次数超过应包含 就返回
                if (aimNum > fmbOrderRelatedData.getContainTimes()) {
                    logger.error("returnHotelCode_more_than_should_have");
                    return ;
                }


                Wrapper<FmbReserveHotelCodes> upWr2 = new LambdaUpdateWrapper<FmbReserveHotelCodes>()

                        .eq(FmbReserveHotelCodes::getCodeId,aimCodeInfo.getCodeId())
                        .eq(FmbReserveHotelCodes::getNumber,aimCodeInfo.getNumber())
                        .set(FmbReserveHotelCodes::getNumber,aimNum)
                        .set(aimNum>0,FmbReserveHotelCodes::getStatus,1)
                        .set(FmbReserveHotelCodes::getUseTime,LocalDateTime.now())
                        ;
                returnCodeFlag =  hotelCodesService.update(upWr2) ;

                if (returnCodeFlag) {
                    break;
                }
            }
            if (!returnCodeFlag) {
                throw new BadLogicException("返还房券失败[2158]");
            }

            //更新related 预约状态
            final boolean hotelCodeIsFinish = queryHotelCodeIsFinish(orderRecordServiceOne.getGeneralOrderSn());

            Wrapper<FmbOrderRelatedData> wrRelatedUp = new LambdaUpdateWrapper<FmbOrderRelatedData>()
                    .eq(FmbOrderRelatedData::getOrderSn,orderRecordServiceOne.getGeneralOrderSn())
                    .set(FmbOrderRelatedData::getReserveEndStatus,hotelCodeIsFinish?1:0)
                    ;
            relatedDataService.update(wrRelatedUp) ;



        }
    }

    public void returnUserBalance(FmbOrderInfo fmbOrderInfo, Integer uid) {
        boolean updateBalance = false ;
        for (int i = 0; i < 3; i++) {

            Wrapper<FmbUserExtinfo> wrE = new LambdaQueryWrapper<FmbUserExtinfo>()
                    .eq(FmbUserExtinfo::getUid, uid)
                    .select(FmbUserExtinfo::getId,FmbUserExtinfo::getMoney)
                    ;

            final FmbUserExtinfo userBalance = userExtinfoService.getOne(wrE);

            if (userBalance == null) {
                throw new BadLogicException("uid="+ uid +"用户余额信息异常[1988]");
            }

            Wrapper<FmbUserExtinfo> wr = new LambdaUpdateWrapper<FmbUserExtinfo>()
                    .eq(FmbUserExtinfo::getId,userBalance.getId())
                    .eq(FmbUserExtinfo::getMoney,userBalance.getMoney())
                    .set(FmbUserExtinfo::getMoney, fmbOrderInfo.getCashMoney().add(userBalance.getMoney()))
                    .set(FmbUserExtinfo::getUtime,LocalDateTime.now())
                    ;
            updateBalance = userExtinfoService.update(wr);
            if (updateBalance) {
                break;
            }
        }
        if (!updateBalance) {
            throw new BadLogicException("uid="+ uid +",返还余额更新失败[1644]");
        }

        Wrapper<FmbUserCashLog> cashLogWr = new LambdaQueryWrapper<FmbUserCashLog>()
                .eq(FmbUserCashLog::getUid, uid)
                .eq(FmbUserCashLog::getOrderSn, fmbOrderInfo.getOrderSn())
                .select(FmbUserCashLog::getCashlogId)
                ;
        final FmbUserCashLog userCashLog = userCashLogService.getOne(cashLogWr);

        if (userCashLog==null) {
            throw new BadLogicException("用户返现流水信息异常");
        }

        userCashLog.setStatus(3);
        userCashLog.setUtime(LocalDateTime.now());

        //把使用余额 log 的状态改为失效
        userCashLogService.updateById(userCashLog) ;
    }

    public void returnCoupon(FmbOrderInfo fmbOrderInfo, String auto_cancel, Integer uid) {
        Wrapper<FmbUserCoupon> wrCoupon = new LambdaQueryWrapper<FmbUserCoupon>()
                .eq(FmbUserCoupon::getOrderSn, fmbOrderInfo.getOrderSn())
                .eq(FmbUserCoupon::getUid,uid)
                .select(FmbUserCoupon::getCouponSn)
                ;
        final FmbUserCoupon updateAim = couponService.getOne(wrCoupon);
        updateAim.setUseStatus(0);
        updateAim.setOrderSn("");
        updateAim.setUseTime(DEFAULT_DATETIME);
        couponService.updateById(updateAim) ;

        FmbCouponLog couponLog = new FmbCouponLog();
        couponLog.setCouponSn(updateAim.getCouponSn());
        couponLog.setLogType(auto_cancel);
        couponLog.setAdminUid(uid);
        HashMap<String, Object> map = new HashMap<>();
        map.put("reason", "订单超时未支付,优惠券返回");
        couponLog.setLogNote(JSON.toJSONString(map));
        couponLog.setLogTime(LocalDateTime.now());
        couponLogService.save(couponLog) ;
    }


    /**
     *
     *  确认订单 优惠券和房券 价格计算
     * @param uid 用户uid
     * @param input  选择优惠券时的 sku信息
     * @param resultMap  map 对象 , 根据aimOperate 不同 在这个map里 会返回对应的 数据 
     * @param aimOperate
         1.从确认订单 自动选择最优 优惠券调用该方法  "confirmOrder"
         2.从确认订单,用户要自己选择能用优惠券  "userChooseCoupon"

     */
    public void chooseBestCoupon(Integer uid, @Valid ReqConfirmOrder input, HashMap<String, Object> resultMap,String aimOperate){



        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.countGoodsPrice(input.getReqGenSkus(),input.getHotelReserveCode());



        confirmOrderMoneyInfo.setMainTitle("费用明细");
//        confirmOrderMoneyInfo.setSubTitle("费用明细xxx");

        HashMap<String, Object> par = new HashMap<>();
        par.put("uid", uid);
        List<FrontCouponDomain> lists = frontSkuDao.selectFrontCoupon(par);


        final Integer xaid = input.getXaid();
        //如果是用户自己 要选择优惠券 就默认返回空数组
        if ("userChooseCoupon".equals(aimOperate)) {
            resultMap.put("couponList",new ArrayList<>()) ;
        }

        final OrderSysInfo orderSysInfo = new OrderSysInfo();

        //只要用户不是 确认不使用优惠券 都要走优惠券筛选逻辑
        if (input.getUserChooseNotUseCoupon()!=1) {
            final FmbxActivity activity = queryFmbxActivity(xaid);
            boolean activityIsNotInCouponUnableAndNotYearCard = self.couponAidCheck(xaid);
            if (activityIsNotInCouponUnableAndNotYearCard) {

                for (FrontCouponDomain couponDomain : lists) {
                    try {
//                    self.processEachCoupon(input, activity, couponDomain,confirmOrderMoneyInfo, xaid) ;

                        self.couponCoreCheck(input, activity, couponDomain, xaid, confirmOrderMoneyInfo.countAllGoodsMoney(), orderSysInfo);
                    } catch (BadLogicException e) {
                        //如果 有的优惠券不满足使用条件 就标记为 不能使用
                        logger.info("getCouponSn={} ,errorMessage={}",couponDomain.getCouponSn(),e.getMessage());
                        couponDomain.setCanUse(false);
                    }
                }

                if ("userChooseCoupon".equals(aimOperate)) {
                    resultMap.put("couponList",lists) ;
                }

                if ("confirmOrder".equals(aimOperate) &&  StringUtils.isNotBlank( input.getCouponSn())) {
                    if (lists.stream().filter(co->input.getCouponSn().equals( co.getCouponSn()) && co.isCanUse() ).count()!=1) {
                        throw new BadLogicException("优惠券不合法[2412]");
                    }
                    //只保留用户指定的
                    lists = lists.stream().filter(co->input.getCouponSn().equals( co.getCouponSn()) && co.isCanUse() ).collect(Collectors.toList()) ;
                }

//            final List<FrontCouponDomain> canUseCoupons = lists.stream().filter(FrontCouponDomain::isCanUse).sorted((a, b) -> {
//                return b.getDiscountMoney().compareTo(a.getDiscountMoney());
//            }).collect(Collectors.toList());
//            logger.info("canUseCoupons={}",JSON.toJSONString(canUseCoupons,true));

                //找到 抵扣金额最高的 优惠券
                final Optional<FrontCouponDomain> first = lists.stream().filter(FrontCouponDomain::isCanUse).sorted((a, b) -> {
                    return b.getDiscountMoney().compareTo(a.getDiscountMoney());
                }).findFirst();

                if (first.isPresent()) {
                    final FrontCouponDomain reCoupon = first.get();

                    //只有不使用房券的时候才挑选最优优惠券
                    if (StringUtils.isBlank (input.getHotelReserveCode())){
                        confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" , reCoupon.getDiscountMoney() ) ;
                        confirmOrderMoneyInfo.setCouponSn(reCoupon.getCouponSn());
                        confirmOrderMoneyInfo.setCouponDiscountMoney(reCoupon.getDiscountMoney());
                    }
                }
            }
        }



        //如果在使用 酒店预约码
        if (StringUtils.isNotBlank (input.getHotelReserveCode())) {
            self.checkHotelReserveCode(input.getHotelReserveCode(),uid, orderSysInfo) ;



            logger.info("orderSysInfo={}",JSON.toJSONString(orderSysInfo,true));
            logger.info("confirmOrderMoneyInfo={}",JSON.toJSONString(confirmOrderMoneyInfo,true));

            //日历房确认信息
            final List<ConfirmOrderMoneyItem> collect = confirmOrderMoneyInfo.getGroups().stream().filter(x -> x.getGroupType() == GROUPTYPE_1).map(g -> g.getItemList()).flatMap(x -> x.stream()).collect(Collectors.toList());


            final FmbxHotelReserveSku reserveCodeSourceSku = orderSysInfo.getReserveCodeSourceSku();
            final String invalidReserveWeekDays = reserveCodeSourceSku.getInvalidReserveWeekDays();
            final String invalidReserveDate = reserveCodeSourceSku.getInvalidReserveDate();

            Set<Integer> collectWeek = new HashSet<>();
            if (StringUtils.isNotBlank(invalidReserveWeekDays)) {
                collectWeek = Stream.of(invalidReserveWeekDays.split(",")).map(x -> Integer.parseInt(x)).collect(Collectors.toSet());
            }
            Set<String> collectDate = new HashSet<>() ;
            if (StringUtils.isNotBlank(invalidReserveDate)) {
                collectDate =
                        Stream.of(invalidReserveDate.split(",")).map(x -> x.toString().trim()).collect(Collectors.toSet());
            }

            for (ConfirmOrderMoneyItem moneyItem : collect) {

                final String leftTitle = moneyItem.getLeftTitle();
                if (reserveDateIsLimit(leftTitle, collectDate)) {
                    throw new BadLogicException(leftTitle +"不能预约[3490]");
                }
                if (reserveWeekIsLimit( DateTime.parse(leftTitle).getDayOfWeek(),collectWeek)) {

                    final int dayOfWeek = DateTime.parse(leftTitle).getDayOfWeek();
                    throw new BadLogicException(leftTitle+"是星期"+(( dayOfWeek !=7 )?dayOfWeek+"":"日")+"不能预约[3491]");

                }

            }

            final Integer totalCodeNight = orderSysInfo.getReserveCodeSourceSuite().getTotalNeight();
            final BigDecimal goodsPrice = orderSysInfo.getReserveCodeFromOrderGoods().getGoodsPrice();

            final BigDecimal hotelCodeEachNightPay = goodsPrice.divide(new BigDecimal(totalCodeNight));
            logger.info("totalCodeNight={} ,goodsPrice={} ,hotelCodeEachNightPay = {}",totalCodeNight,goodsPrice,hotelCodeEachNightPay);

            BigDecimal totalHotelCodeSubMoney = BigDecimal.ZERO;
            logger.info(JSON.toJSONString(collect));

            final int totalWantNightOfHotel = collect.size() * collect.get(0).getSkuNumberN();
            if (totalWantNightOfHotel>totalCodeNight) {
                throw new BadLogicException("入住间夜数大于房券间夜数[5638]");
            }

            for (ConfirmOrderMoneyItem confirmOrderMoneyItem : collect) {
                logger.info("confirmOrderMoneyItem.getPriceBig()={}",confirmOrderMoneyItem.getPriceBig());
                // 如果日历房单间夜价格 大于等于 房券单间夜价格  房券抵扣金额就是就是房券本身
                if (confirmOrderMoneyItem.getPriceBig().compareTo(hotelCodeEachNightPay)!=-1) {
                    totalHotelCodeSubMoney =
                            totalHotelCodeSubMoney.add(hotelCodeEachNightPay.multiply(new BigDecimal(  confirmOrderMoneyItem.getSkuNumberN()))) ;


                }else {
                    //如果房券金额比 日历房大 ,抵扣金额 就是日历房金额
                    totalHotelCodeSubMoney =
                            totalHotelCodeSubMoney.add(confirmOrderMoneyItem.getPriceBig().multiply(new BigDecimal(  confirmOrderMoneyItem.getSkuNumberN()))) ;
                }

                logger.info(" confirmOrderMoneyItem.getPriceBig() = {} ,totalHotelCodeSubMoney={} hotelCodeEachNightPay={}",
                        confirmOrderMoneyItem.getPriceBig(),
                        totalHotelCodeSubMoney,hotelCodeEachNightPay);
            }
            confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠","房券抵扣",totalHotelCodeSubMoney);

//            System.out.println("00000000000");
//            System.out.println(JSONUtil.toJsonStr(confirmOrderMoneyInfo));

            //使用房券时需要知道可选的 入离时间和 间数
            HashMap<String, Object> ruleInfo = new HashMap<>();
            self.buildHotelCodeUseRoleInfo(orderSysInfo, ruleInfo);

            final int dayDiff = FmbDateUtil.dayDiff(input.getReqGenSkus().get(0).getCheckOutDate(), input.getReqGenSkus().get(0).getCheckInDate());

//            logger.info("dayDiff={}",dayDiff);
//            logger.info("ruleInfo={}",JSONUtil.toJsonStr(ruleInfo));

            List dr  = (List) ((Map) ruleInfo.get("hotelCodeInfo")).get("dateAndRoomNumInfo");

            ArrayList<Integer> canUse = null ;
            for (Object o : dr) {
                HashMap<String, Object> map = (HashMap<String, Object>) o;
                if (map.get("nightNum").toString().equals(dayDiff+"")) {
                    canUse = (ArrayList<Integer>) map.get("roomNumberCanUse");
                }
            }
            if (canUse == null) {
                throw new BadLogicException("入离日期异常[4589]");
            }

            final Set<String> roomSet = canUse.stream().map(x -> x.toString()).collect(Collectors.toSet());

            //在合理范围内的 间数才能被标记为可用的.
            for (RoomNumStatus roomNumStatus : confirmOrderMoneyInfo.getRoomNumStatus()) {

                if(roomSet.contains(roomNumStatus.getRoomNum()+"")){
                    roomNumStatus.setStatus(1);
                }else {
                    roomNumStatus.setStatus(0);
                }
                roomNumStatus.setSelected(0);
                if(roomNumStatus.getStatus()==1  && roomNumStatus.getRoomNum()==input.getReqGenSkus().get(0).getSkuNumberSource()){
                    roomNumStatus.setSelected(1);
                }

            }

            final List<RoomNumStatus> statusList = confirmOrderMoneyInfo.getRoomNumStatus().stream().filter(x -> x.getStatus() == 1).collect(Collectors.toList());

            if (!statusList.isEmpty()) {
                confirmOrderMoneyInfo.setMinRoomNum(statusList.get(0).getRoomNum());
                confirmOrderMoneyInfo.setMaxRoomNum(statusList.get(statusList.size()-1).getRoomNum());
            }

        }


        logger.info("--->confirmOrderMoneyInfo={}",JSON.toJSONString(confirmOrderMoneyInfo,true));

        confirmOrderMoneyInfo.setTotalMoney( confirmOrderMoneyInfo.countAllGoodsMoney());
        resultMap.put("consumeList",confirmOrderMoneyInfo) ;

    }

    /**
     * 使用房券时因为 考虑到房券的是否可拆分, 以及剩余次数问题,需要把可选晚数和对应的可选间数返回
     * @param orderSysInfo
     * @param resultMap
     */
    public void buildHotelCodeUseRoleInfo(OrderSysInfo orderSysInfo, HashMap<String, Object> resultMap) {
//        orderSysInfo.setReserveCodeFromOrderInfo(reserveCodeFromOrderInfo);
//        orderSysInfo.setReserveCodeFromOrderGoods(reserveCodeFromOrderGoods);
//
//        FmbxSuite suite = JSONUtil.parse(reserveCodeFromOrderGoods.getGoodExt()).getByPath("suite", FmbxSuite.class);
//        orderSysInfo.setReserveCodeSourceSuite(suite);
//        orderSysInfo.setHotelCode(codeDB);
//        orderSysInfo.setUseHotelCode(true);

        final String goodExt = orderSysInfo.getReserveCodeFromOrderGoods().getGoodExt();
        FmbxHotelReserveSku reserveSku = JSONUtil.parse(goodExt).getByPath("skuDetail.reserveSku", FmbxHotelReserveSku.class);

        final Integer totalRemain = orderSysInfo.getHotelCode().getNumber();
        final Integer status = orderSysInfo.getHotelCode().getStatus() ;
        final Integer canDivide = reserveSku.getCanDivide();
        final Integer nightMin = orderSysInfo.getReserveCodeSourceSuite().getNightMin();

        logger.info("totalRemain={},status={},canDivide={},nightMin={}",totalRemain,status,canDivide,nightMin);

        HashMap<String, Object> hotelCodeInfo = new HashMap<>();
        hotelCodeInfo.put("totalRemain", totalRemain);
        hotelCodeInfo.put("canDivide", canDivide);
        hotelCodeInfo.put("nightMin", nightMin);

        resultMap.put("hotelCodeInfo",hotelCodeInfo) ;

        final ArrayList<HashMap<String, Object>> dr = new ArrayList<>();
        hotelCodeInfo.put("dateAndRoomNumInfo",dr) ;
        //可拆分
        if (1==canDivide) {

            for (Integer i = 1; i <= totalRemain; i++) {
                if (totalRemain%i==0) {

                    final int nightNum = totalRemain / i;

                    if (nightNum>=nightMin) {
                        HashMap<String, Object> data = new HashMap<>();
                        data.put("nightNum", nightNum);
                        final ArrayList<Integer> roomNumberCanUse = new ArrayList<>();
                        data.put("roomNumberCanUse", roomNumberCanUse);

                        for (int roomNumCheck = 1; roomNumCheck <= 10; roomNumCheck++) {
                            if (nightNum * roomNumCheck <=totalRemain) {
                                roomNumberCanUse.add(roomNumCheck);
                            }
                        }
                        dr.add(data) ;
                    }
                }
            }
        }else {

            for (Integer i = 1; i <= totalRemain; i++) {
                if (totalRemain%i==0) {

                    final int nightNum = totalRemain / i;

                    if (nightNum>=nightMin) {
                        HashMap<String, Object> data = new HashMap<>();
                        data.put("nightNum", nightNum);
                        final ArrayList<Integer> roomNumberCanUse = new ArrayList<>();
                        data.put("roomNumberCanUse", roomNumberCanUse);

                        for (int roomNumCheck = 1; roomNumCheck <= 10; roomNumCheck++) {
                            if (nightNum * roomNumCheck ==totalRemain) {
                                roomNumberCanUse.add(roomNumCheck);
                            }
                        }
                        dr.add(data) ;
                    }
                }
            }


        }

    }

    /**
     * 判断一个活动是否在 fmb_coupon_unable_activity 里 和 年卡里
     * 因为 这2种实际上 都不能使用优惠券
     * @param xaid
     * @return
     */
    @Cacheable(value = "couponAidCheck#30", keyGenerator = "customKeyGenerator")
    public boolean couponAidCheck(Integer xaid) {
        HashSet<String> aidsOfYearCard = self.queryYearCardAids();

        if (aidsOfYearCard.contains(xaid.toString())) {
            return false ;
        }

        Wrapper<FmbCouponUnableActivity> wrAc = new LambdaQueryWrapper<FmbCouponUnableActivity>()
                .eq(FmbCouponUnableActivity::getAid, xaid)
                ;
        if (couponUnableActivityService.count(wrAc)>0) {
            return false ;
        }

        return true;
    }

    public void processEachCoupon(FrontApiRequestDomain input, FmbxActivity activity, FrontCouponDomain couponDomain, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, Integer xaid) {

        BigDecimal priceAll = confirmOrderMoneyInfo.countAllGoodsMoney();

        self.couponCoreCheck(input, activity, couponDomain, xaid, priceAll, null);
    }

    /**
     * 优惠券 能否合法使用的核心逻辑, 以下情况会调用
     * 1. 系统计算用户最优优惠券
     * 2. 下单校验优惠券
     * 3. 用户自主选择可用优惠券列表
     * @param input  主要区分平台信息
     * @param activity 活动信息
     * @param couponDomain 优惠券和券组信息
     * @param xaid   活动id
     * @param priceAll
     * @param orderSysInfo   下单校验优惠券传入非空对象 ,其他情况为空
     */
    public void couponCoreCheck(FrontApiRequestDomain input, FmbxActivity activity, FrontCouponDomain couponDomain, Integer xaid,
             BigDecimal priceAll, OrderSysInfo orderSysInfo) {
        int platValue = couponDomain.getPlatId() ;
        if (checkCouponService.isNotValidCoupon(xaid, couponDomain.getCouponId().intValue())) {
            throw new BadLogicException("优惠券黑白名单校验失败[1502]");
        }

        if (0!= platValue && 1!= platValue) {

            if (platValue ==2 && !input.checkFMB_APP()) {
                throw new BadLogicException("优惠券平台限制异常[1506]");
            }else
            if (platValue ==3 && !input.checkH5()) {
                throw new BadLogicException("优惠券平台限制异常[1507]");
            }else
            if (platValue ==4 && !(input.checkFMB_APP() || input.checkWxApp())) {
                throw new BadLogicException("优惠券平台限制异常[1509]");
            }else
            if (platValue ==5 && !input.checkWxApp()) {
                throw new BadLogicException("优惠券平台限制异常[1511]");
            }
        }

        if (1== couponDomain.getLimitType().intValue() && !xaid.toString().equals(couponDomain.getActivityId().toString())) {
            throw new BadLogicException("优惠券不满足活动限制[1513]");
        }

        //限制品类
        if (2== couponDomain.getLimitType().intValue() &&!self.couponCategoryCheck(couponDomain.getLimitCate(), activity.getCategoryId())) {
            throw new BadLogicException("优惠券分类限制异常[1504]");
        }

        if (1== couponDomain.getCouponType().intValue() ) {

            //商品金额 小于 代金券金额
            if ( priceAll.compareTo( BigDecimal.valueOf ( couponDomain.getCouponMoney()))==-1){
                throw new BadLogicException("优惠券金额异常[1510]");
            }

            if (orderSysInfo != null) {
                orderSysInfo.setCouponMoney( BigDecimal.valueOf ( couponDomain.getCouponMoney()));
            }
        }
        else if (2== couponDomain.getCouponType().intValue() ) { //满减券

            //商品金额 需要大于折扣券金额
            if ( priceAll.compareTo( BigDecimal.valueOf ( couponDomain.getMinAmount()))==-1){
                throw new BadLogicException("优惠券金额异常[1514]");
            }
            if (orderSysInfo != null) {
                orderSysInfo.setCouponMoney(  BigDecimal.valueOf( couponDomain.getCouponMoney()));
            }


        }else if (4== couponDomain.getCouponType().intValue() ) { //折扣券

            //商品金额 需要大于折扣券金额
            if ( priceAll.compareTo(BigDecimal.valueOf(  couponDomain.getMinAmount()))==-1){
                throw new BadLogicException("优惠券金额异常[1516]");
            }
            //折扣券
            couponDomain.setDiscountMoney(priceAll.multiply(BigDecimal.valueOf(100- couponDomain.getDiscountRate()).divide(new BigDecimal(
                    "100"))));

            if (orderSysInfo != null) {

                orderSysInfo.setCouponMoney( orderSysInfo.getGoodsPriceAll().multiply(BigDecimal.valueOf(100-couponDomain.getDiscountRate()).divide(new BigDecimal("100")))  );
                orderSysInfo.setRateCoupon(true);
            }


        }
    }

    /**
     * 根据 用输入 的sku信息 查询原始价格
     * @param reqGenSkus
     * @param hotelReserveCode
     * @return
     */
    public ConfirmOrderMoneyInfo countGoodsPrice(List<ReqGenSku> reqGenSkus, String hotelReserveCode) {

        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = new ConfirmOrderMoneyInfo();


        final List<FmbxSku> fmbxSkus = self.queryFmbxSkus(reqGenSkus);

        final Map<String, List<FmbxSku>> collect = fmbxSkus.stream().collect(Collectors.groupingBy(x -> x.getSkuId().toString()));

        BigDecimal bigDecimalAll = BigDecimal.ZERO;
        for (ReqGenSku genSkus : reqGenSkus) {
            final FmbxSku fmbxSku = collect.get(genSkus.getSkuId().toString()).get(0);
            //构造价格明细
            final ConfirmOrderMoneyGroup group = getOrderService(fmbxSku.getSkuType()).buildPriceWhenUseCoupon(fmbxSku, genSkus,
                    "confirmOrder",confirmOrderMoneyInfo,hotelReserveCode);

//            for (ConfirmOrderMoneyItem i : group.getItemList()) {
//                bigDecimalAll.add(i.getPriceBig()) ;
//            }
            //取第一组的 价格数据的描述 作为 整体明细的副标题
            if (StringUtils.isNotBlank(group.getSkuDesc())) {
                confirmOrderMoneyInfo.setSubTitle(group.getSkuDesc());
            }
            //从下一层提取数据到 confirmOrderMoneyInfo
            if (fmbxSku.getSkuType()==1 && confirmOrderMoneyInfo.getRoomNumStatus().isEmpty() ) {
                confirmOrderMoneyInfo.getRoomNumStatus().addAll(  group.getRoomNumStatus());

                final List<RoomNumStatus> allOk = group.getRoomNumStatus().stream().filter(s -> s.getStatus() == 1).collect(Collectors.toList());
                if (!allOk.isEmpty()) {
                    confirmOrderMoneyInfo.setMinRoomNum(allOk.get(0).getRoomNum());
                    confirmOrderMoneyInfo.setMaxRoomNum(allOk.get(allOk.size()-1).getRoomNum());
                }


                group.getRoomNumStatus().clear();
            }

            confirmOrderMoneyInfo.getGroups().add(group) ;
            confirmOrderMoneyInfo.setFmbxSku(fmbxSku) ;
        }

        return confirmOrderMoneyInfo;
    }


    @Cacheable(value = "queryYearCardAids#120", keyGenerator = "customKeyGenerator")
    public HashSet<String> queryYearCardAids(){
        //cityIds = 1,2,3 //查全国、北京、上海的所有年卡活动
        Wrapper<FmbTemplate> wr = new LambdaQueryWrapper<FmbTemplate>()
                .eq(FmbTemplate::getSectionName,"year_card")
                .eq(FmbTemplate::getModuleName, "new_activity_config")
                .in(FmbTemplate::getCityId, new Integer[]{1, 2, 3})
                .select(
                        FmbTemplate::getContent,
                        FmbTemplate::getSectionName,
                        FmbTemplate::getModuleName,
                        FmbTemplate::getCityId
                );
        List<FmbTemplate> data = templateService.list(wr) ;

        HashSet<String> aidList=new HashSet<String>();
        //循环处理数据
        for (FmbTemplate item : data ){

            final ArrayList<HashMap<String, Object>> lists = FmbPhpUtil.unserialize2ListOfMap(item.getContent());

            for (HashMap<String, Object> map : lists) {
                System.out.println(JSON.toJSONString(map));
                if (map.containsKey("aid")){
                    if (map.containsKey("prepare") &&  StringUtils.isNotBlank(  map.get("prepare").toString())) {
                        Date prepare = FmbDateUtil.str2Date(map.get("prepare").toString());
                        if (prepare != null) {
                            if (prepare.getTime() <System.currentTimeMillis() ) {
                                aidList.add(map.get("aid").toString()) ;
                            }
                        }
                    }else  {
                        aidList.add(map.get("aid").toString()) ;
                    }
                }
            }
        }
        return aidList;
    }

    /**
     * 订单详情接口
     * @param resultMap
     * @param input
     * @param nowUser
     * @param from
     *   1. "front" 前台
     */
    @DS(DB_master)
    public void orderDetail(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser,String from) {

        final String orderSn = input.getOrderSn();

        if (FRONT.equals(from) &&   nowUser == null) {
            throw new BadLogicException("用户需要登录[3104]");
        }

        FmbOrderInfo orderInfo = null ;
        if (FRONT.equals(from)){
            orderInfo = self.queryFmbOrderInfo(orderSn);
        }else
        if (ADMIN001.equals(from)){
            orderInfo = self.queryFmbOrderInfoFromAdmin001(orderSn);
        }

        if (orderInfo == null) {
            throw new BadLogicException("订单数据异常[3101]");
        }
        if (FRONT.equals(from) &&   nowUser.getUserId() != orderInfo.getUid().intValue()) {
            throw new BadLogicException("用户信息异常[3106]");
        }


        //查询goods 信息
        List<FmbOrderGoods> goodsList =null ;
        //前台尽量少返回
        if (FRONT.equals(from) ) {
            goodsList = self.queryFmbOrderGoods(orderSn);
        }
        if (ADMIN001.equals(from) ) {
            goodsList = self.queryFmbOrderGoodsAdmin(orderSn);
            resultMap.put("goodsList",goodsList) ;
        }

        final ArrayList<OrderDetailGoodsInfo> goodsInfos = new ArrayList<>();
        //构造价格信息列表 和 确认订单时的 结构一样
        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = self.createConfirmOrderMoneyInfoInOrderDetail(orderInfo, goodsList, goodsInfos);

        final Integer orderStatus = orderInfo.getOrderStatus();
        OrderDetailInfo orderDetailInfo = self.buildOrderStatusAndButtonInfo(orderInfo, orderStatus);

        //构造前台 展示的 购买人信息 和 购买物品信息
        // buyInfo 在后续代码里添加
        HashMap<String, Object> orderExtInfo = new HashMap<>();
        HashMap<String, Object> buyInfo = new HashMap<>();
        if ( ( GOODS_TYPE_6== orderInfo.getGoodsType())){
            orderExtInfo.put("title", "入住信息");
            final ArrayList<HashMap<String, Object>> listExts = new ArrayList<>();
            orderExtInfo.put("lists", listExts);
            putItemInfo(listExts, "入住人",orderInfo.getReceiver() );
            putItemInfo(listExts, "联系电话",orderInfo.getMobile() );

        }
        if ( ( GOODS_TYPE_7== orderInfo.getGoodsType())){
            orderExtInfo.put("title", "收货人信息");
            final ArrayList<HashMap<String, Object>> listExts = new ArrayList<>();
            orderExtInfo.put("lists", listExts);

            putItemInfo(listExts, "收货人",orderInfo.getReceiver() );
            putItemInfo(listExts, "联系电话",orderInfo.getMobile() );
        }

        resultMap.put("orderExtInfo",orderExtInfo) ;
        resultMap.put("buyInfo",buyInfo) ;


        //房券是否可拆分 字段
        int  canDivide = 0 ;
        //把套餐信息从 交易快照里读出来
        if ( ( GOODS_TYPE_6== orderInfo.getGoodsType() || GOODS_TYPE_7== orderInfo.getGoodsType())){

            Wrapper<FmbOrderTradeSnapshot> wrSn = new LambdaQueryWrapper<FmbOrderTradeSnapshot>()
                    .eq(FmbOrderTradeSnapshot::getOrderSn,orderSn)
                    .select(FmbOrderTradeSnapshot::getContent);
            final FmbOrderTradeSnapshot one = snapshotService.getOne(wrSn);

            if (one != null) {
                final JSONObject jsonObject = JSON.parseObject(one.getContent());
                resultMap.put("suiteInfo",jsonObject) ;
            }

            final FmbOrderGoods goods = goodsList.get(0);

            final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());
            final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
            final Integer bpsId = skuDetail.getFmbxSku().getBpsId();
            FmbxBps bps = self.queryBps(bpsId);

            resultMap.put("hotelBasicInfo",bps) ;
            resultMap.put("skuDetail",skuDetail) ;

            if (GOODS_TYPE_7== orderInfo.getGoodsType()) {
                canDivide = skuDetail.getReserveSku().getCanDivide() ;
            }

            if (  GOODS_TYPE_6== orderInfo.getGoodsType()){
                final FmbxBpsRoom room = bpsRoomService.getById(skuDetail.getRoomSku().getRoomId());
                resultMap.put("roomInfo" , room) ;


                final OrderDetailGoodsInfo goodsInfo = goodsInfos.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_6).findFirst().get();


                // buyInfo 添加
                buyInfo.put("title", orderInfo.getOrderName());
                final ArrayList<HashMap<String, Object>> buyInfoExts = new ArrayList<>();
                buyInfo.put("lists", buyInfoExts);

                putItemInfo(buyInfoExts, "套餐名称",goodsInfo.getSuiteName()+"-"+room.getRoomName() );
                putItemInfo(buyInfoExts, "房间数量",goodsInfo.getRoomNum()+"" );
                putItemInfo(buyInfoExts, "入离日期",goodsInfo.getCheckInOutDateStr()+"(共"+goodsInfo.getTotalNight()+"晚)" );

            }

            if (  GOODS_TYPE_7== orderInfo.getGoodsType()){

                final OrderDetailGoodsInfo goodsInfo = goodsInfos.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_7).findFirst().get();

                // buyInfo 数据添加
                buyInfo.put("title", orderInfo.getOrderName());
                final ArrayList<HashMap<String, Object>> buyInfoExts = new ArrayList<>();
                buyInfo.put("lists", buyInfoExts);

                putItemInfo(buyInfoExts, "套餐名称",goodsInfo.getSuiteName() );
                putItemInfo(buyInfoExts, "数量",goodsInfo.getSkuNumber()+"" );
                putItemInfo(buyInfoExts, "有效期",goodsInfo.getValidStartEndDate() );

            }

        }

        if (isUseHotelCodeGenHotelOrder(orderInfo)) {
            resultMap.put("reserveOKTip",
                    "预约提交后，是否预约成功以父母邦的二次确认短信为准。若预约有问题，我们会在24小时内(周末及节假日除外)与您电话联系，如您长时间未收到短信通知，或有其他需求请联系客服"
                            +FmbServerConfig.getServiceTel()+"(8:00-18:30/天)") ;
        }

        //买房券 房券信息列表
        if (GOODS_TYPE_7== orderInfo.getGoodsType()) {

            final List<FmbReserveHotelCodes> codes = queryHotelCodeList(orderSn, 2);

            //房券是否用完了
            boolean codeCanUse = false ;
            for (FmbReserveHotelCodes code : codes) {
                if(code.getNumber()>0){
                    code.setCodeDesc("余"+code.getNumber()+"间夜,"+(canDivide==1?"可拆分":"不可拆分"));
                    codeCanUse = true ;
                }
            }

            //如果发现没次数了
            if (!codeCanUse) {
                logger.info("房券用完了~~~");
                final Iterator<HashMap<String, Object>> iterator = orderDetailInfo.getButtonsList().iterator();
                while (iterator.hasNext()) {
                    final HashMap<String, Object> next = iterator.next();
                    if ((BUTTON_TYPE_2_HOTEL_RESERVE+"").equals(  next.get("type").toString())) {

                        iterator.remove();
                    }
                }
            }

            resultMap.put("hotelCode", codes) ;
            final FmbOrderRelatedData relatedData = self.queryFmbOrderRelatedData(orderSn);
            resultMap.put("hotelCodeRelatedDate",relatedData) ;
        }

        FmbOrderInfoOfAdmin fmbOrderInfoOfAdmin = new FmbOrderInfoOfAdmin();
        BeanUtils.copyProperties(orderInfo,fmbOrderInfoOfAdmin);

        resultMap.put("orderInfo",fmbOrderInfoOfAdmin) ;
        resultMap.put("status",orderDetailInfo) ;
        resultMap.put("goodsInfos",goodsInfos) ;
        resultMap.put("confirmOrderMoneyInfo",confirmOrderMoneyInfo) ;

        //后台订单查询
        if ( ADMIN001.equals(from) ) {


            DynamicDataSourceContextHolder.push(DB_slave);

            final Integer uid = orderInfo.getUid();
            final FmbUsers fmbUser = self.queryFmbUser(uid);
            final FmbxActivity activity = self.queryFmbxActivity(orderInfo.getAid());

            resultMap.put("fmbUser",fmbUser) ;
            resultMap.put("activity",activity) ;

            final List<AdminOrderActionDomain> orderActions = self.selectAdminOrderAction(orderSn);

            final FmbxBpParterInfo bpParterInfo = bpParterInfoService.getById(orderInfo.getShopUserId());

            resultMap.put("bpParterInfo",bpParterInfo) ;
            resultMap.put("bpMain",bpMainService.getById(orderInfo.getShopUserId())) ;

            if (GOODS_TYPE_6== orderInfo.getGoodsType()) {
                final FmbxBpParterHotelInfo bpParterHotelInfo = parterHotelInfoService.getById(orderInfo.getShopUserId());
                resultMap.put("bpParterHotelInfo",bpParterHotelInfo) ;

            }

            resultMap.put("orderActions",orderActions) ;
            resultMap.put("netOrCom", FmbServerConfig.netOrCom()) ;

            HashMap<String, Object> extStr = new HashMap<>();

            extStr.put("activityCityStr", "") ;
            if (FMB_SITE.containsKey(""+activity.getCityId())) {
                extStr.put("activityCityStr", FMB_SITE.get(""+activity.getCityId())) ;
            }
            extStr.put("userCityStr", "") ;
            if (FMB_SITE.containsKey(""+orderInfo.getUserCityId())) {
                extStr.put("userCityStr", FMB_SITE.get(""+orderInfo.getUserCityId())) ;
            }

            resultMap.put("extStr",extStr) ;

        }
    }

    private void putItemInfo(ArrayList<HashMap<String, Object>> listExts, String key, String hotelUserName) {
        HashMap<String, Object> tempMap = new HashMap<>();
        tempMap.put("key", key) ;
        tempMap.put("val", hotelUserName) ;
        listExts.add(tempMap) ;
    }

    /**
     * 订单详情页面里 构造 价格信息 数据结构
     * @param orderInfo
     * @param goodsList
     * @param goodsInfos
     * @return
     */
    public ConfirmOrderMoneyInfo createConfirmOrderMoneyInfoInOrderDetail(FmbOrderInfo orderInfo, List<FmbOrderGoods> goodsList,
                                                                     ArrayList<OrderDetailGoodsInfo> goodsInfos) {
        //费用明细信息 构造 开始
        ConfirmOrderMoneyInfo confirmOrderMoneyInfo = new ConfirmOrderMoneyInfo() ;
        confirmOrderMoneyInfo.setTotalMoney(orderInfo.getRealpayMoney());
        confirmOrderMoneyInfo.setCouponSn("");

        confirmOrderMoneyInfo.setMainTitle("费用明细");

        /**
         * 这个地方 后期可能有问题
         * 下单逻辑里 为了后期扩展 可以一个订单下 多个不同skuid
         * 对应的 就是 ordergoods里的 多条记录
         * 这个地方就是循环 ordergoods 然后构造订单详情里的 费用明细 .
         * 费用明细标题头部的副标题 只有一个
         * 目前按照 取第一个 ordergoods里的 数据 进行构造该费用明细副标题
         */
        for (FmbOrderGoods goods : goodsList) {
            OrderDetailGoodsInfo info =   getOrderServiceFromGoodsType(goods.getGoodsType()).readGoodsInfoFromGoodsExt(goods,
                    confirmOrderMoneyInfo, orderInfo) ;
            goodsInfos.add(info) ;
        }

        if(orderInfo.getCouponMoney()>0){
            if(orderInfo.getDiscountRate()>0){
                //如果使用折扣券 ,因为 数据库表里的优惠券金额是整数,所以金额可能不正确,得自己计算优惠券到底用了多少钱
                final BigDecimal discountMoney = orderInfo.getMoney().multiply(new BigDecimal("100").subtract(new BigDecimal(orderInfo.getDiscountRate())).divide(new BigDecimal("100")));
                confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" ,discountMoney) ;
            }else if(orderInfo.getCouponMoney()>0) {
                confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "优惠券" ,BigDecimal.valueOf(orderInfo.getCouponMoney())) ;
            }
        }

        // 余额使用了多少
        if (orderInfo.getCashMoney().compareTo(BigDecimal.ZERO)==1) {
            confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠", "余额" , orderInfo.getCashMoney()) ;
        }
        return confirmOrderMoneyInfo;
    }

    private void addButtonInfo(ArrayList<HashMap<String, Object>> buttonsList, int type) {
        HashMap<String, Object> buttonInfo = new HashMap<>();
        buttonInfo.put("type", type);

        switch (type){

            case BUTTON_TYPE_DEL :
                buttonInfo.put("buttonTxt", "删除订单");
                break;
            case BUTTON_TYPE_PAY:
                buttonInfo.put("buttonTxt", "去支付");
                break;
            case BUTTON_TYPE_RE_BUY:
                buttonInfo.put("buttonTxt", "再次购买");
                break;
            case BUTTON_TYPE_2_HOTEL_RESERVE:
                buttonInfo.put("buttonTxt", "去预约");
                break;
            case BUTTON_TYPE_2_RETURN_SERVICE:
                buttonInfo.put("buttonTxt", "申请退款");
                break;
            case BUTTON_TYPE_CANCEL:
                buttonInfo.put("buttonTxt", "取消订单");
                break;

            default:
        }


        buttonsList.add(buttonInfo) ;
    }

    public List<FmbOrderGoods> queryFmbOrderGoods(String orderSn) {
        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn)
                .select(FmbOrderGoods::getRecId,FmbOrderGoods::getPlayTime,FmbOrderGoods::getLeaveTime,
                FmbOrderGoods::getGoodsType
                ,FmbOrderGoods::getGoodsPrice,FmbOrderGoods::getGoodsNumber,FmbOrderGoods::getGoodExt
                )
                ;
        //找到goods 信息
        final List<FmbOrderGoods> goodsList = orderGoodsService.list(goodsWr);
        return goodsList;
    }
    public List<FmbOrderGoods> queryFmbOrderGoodsAdmin(String orderSn) {
        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn)
                ;
        //找到goods 信息
        final List<FmbOrderGoods> goodsList = orderGoodsService.list(goodsWr);
        return goodsList;
    }

    public List<FmbOrderActions> queryFmbOrderActions(String orderSn) {
        Wrapper<FmbOrderActions> awr = new LambdaQueryWrapper<FmbOrderActions>()
                .eq(FmbOrderActions::getOrderSn, orderSn)
                .select(FmbOrderActions::getLogTime,FmbOrderActions::getActionNote,FmbOrderActions::getPayStatus,
                        FmbOrderActions::getShippingStatus,FmbOrderActions::getOrderStatus,FmbOrderActions::getAdminUid)
                ;
        final List<FmbOrderActions> orderActions = orderActionsService.list(awr);
        return orderActions;
    }


    /**
     * 前台 普通查询
     * @param orderSn
     * @return
     */
    public FmbOrderInfo queryFmbOrderInfo(String orderSn) {

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn)
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn
                        ,FmbOrderInfo::getOrderName
                        , FmbOrderInfo::getOrderStatus, FmbOrderInfo::getConfirmStatus
                        , FmbOrderInfo::getMoney, FmbOrderInfo::getCouponMoney, FmbOrderInfo::getShippingFee,
                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getCashMoney,FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getDiscountRate,FmbOrderInfo::getExtInfo,
                        FmbOrderInfo::getOrderCityId ,FmbOrderInfo::getPaySource,FmbOrderInfo::getOrderType
                        , FmbOrderInfo::getAid,FmbOrderInfo::getReferer,FmbOrderInfo::getOrderReferer
                        , FmbOrderInfo::getReceiver, FmbOrderInfo::getMobile,FmbOrderInfo::getUid
                        ,FmbOrderInfo::getCreateTime,FmbOrderInfo::getIsDelete,FmbOrderInfo::getPostscript
                )
                ;
        final FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrder);
        return orderInfo;
    }

    /**
     * 后台查询 订单详情
     * @param orderSn
     * @return
     */
    @DS(DB_slave)
    public FmbOrderInfo queryFmbOrderInfoFromAdmin001(String orderSn) {

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn)
//                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn
//                        , FmbOrderInfo::getOrderStatus, FmbOrderInfo::getConfirmStatus
//                        , FmbOrderInfo::getMoney, FmbOrderInfo::getCouponMoney, FmbOrderInfo::getShippingFee,
//                        FmbOrderInfo::getRealpayMoney,FmbOrderInfo::getCashMoney,
//                        FmbOrderInfo::getDiscountRate,FmbOrderInfo::getExtInfo,
//                        FmbOrderInfo::getOrderCityId ,FmbOrderInfo::getPaySource,FmbOrderInfo::getOrderType,FmbOrderInfo::getPaySource
//                        , FmbOrderInfo::getAid,FmbOrderInfo::getOrderName,FmbOrderInfo::getAddress,FmbOrderInfo::getReceiver
//                        , FmbOrderInfo::getReceiver, FmbOrderInfo::getMobile,FmbOrderInfo::getUid,FmbOrderInfo::getGoodsType
//                        ,FmbOrderInfo::getCreateTime,FmbOrderInfo::getIsDelete
//
//                        //                            后台多的字段
//                        ,FmbOrderInfo::getEditorNote
//                )
                ;
        final FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrder);
        return orderInfo;
    }



    public FmbOrderRelatedData queryFmbOrderRelatedData(String orderSn) {
        Wrapper<FmbOrderRelatedData> wrRel = new LambdaQueryWrapper<FmbOrderRelatedData>()
                .eq(FmbOrderRelatedData::getOrderSn, orderSn)
                .select(FmbOrderRelatedData::getContainTimes,FmbOrderRelatedData::getNumber,FmbOrderRelatedData::getReserveEndStatus);
        final FmbOrderRelatedData relatedData = relatedDataService.getOne(wrRel);
        return relatedData;
    }

    public FmbUsers queryFmbUser(Integer uid) {
        Wrapper<FmbUsers> userWr = new LambdaQueryWrapper<FmbUsers>()
                .eq(FmbUsers::getUid, uid)
                .select(FmbUsers::getUid,FmbUsers::getPhoneNumber,FmbUsers::getUsername,FmbUsers::getRegdate)
                ;
        final FmbUsers fmbUser = usersService.getOne(userWr);
        return fmbUser;
    }

    /**
     * 返回订单信息和 按钮信息
     * @param orderInfo
     * @param orderStatus
     * @return
     */
    public OrderDetailInfo buildOrderStatusAndButtonInfo(FmbOrderInfo orderInfo, Integer orderStatus) {

        final ArrayList<HashMap<String, Object>> buttonsList = new ArrayList<>();

        OrderDetailInfo orderDetailInfo = new OrderDetailInfo();

        // `order_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态。0.未付款 1.等待发货 2.已发货 3.退货中 4.已退货 5.交易成功 6.交易关闭 7.支付尾款 8.支付成功后续处理中',
        switch (orderStatus.intValue())
        {
            case 0:
                orderDetailInfo.setStatusStr( "待付款");
                final FmbxScheduleJob s = self.queryPayEndTime(orderInfo.getOrderId());

                if (s!=null) {
                    final long lm = FmbDateUtil.localDateTime2Date(s.getScheduleRunTime()).getTime() - System.currentTimeMillis();
                    orderDetailInfo.setPayRemainSeconds((int) (lm/1000));
                    orderDetailInfo.setStatusDetail("");
                }

                //待支付
                    if (orderInfo.getIsDelete()==0) {
                        addButtonInfo(buttonsList, BUTTON_TYPE_CANCEL);
                    }
                    addButtonInfo(buttonsList, BUTTON_TYPE_PAY);

                break;
            case 1:
                orderDetailInfo.setStatusStr( "等待发货");
                break;
            case 2:
                orderDetailInfo.setStatusStr( "已发货");
                break;
            case 3:
                orderDetailInfo.setStatusStr( "退货中");
                break;
            case 4:
                orderDetailInfo.setStatusStr( "已退货");
                break;
            case 5:
                orderDetailInfo.setStatusStr( "交易成功");
                orderDetailInfo.setStatusDetail("您的订单已交易成功。");

                //用房券生成的 酒店日历房
                if ( GOODS_TYPE_7== orderInfo.getGoodsType() ) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_2_HOTEL_RESERVE);
                }
                if (orderInfo.getIsDelete()==0) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_DEL);
                }
                addButtonInfo(buttonsList, BUTTON_TYPE_2_RETURN_SERVICE);


                break;
            case 6:
                orderDetailInfo.setStatusStr( "交易关闭");
                orderDetailInfo.setStatusDetail("您的订单已交易关闭。");

                if (orderInfo.getIsDelete()==0) {
                    addButtonInfo(buttonsList, BUTTON_TYPE_DEL);
                }
                addButtonInfo(buttonsList, BUTTON_TYPE_RE_BUY);

                break;
            case 7:
                orderDetailInfo.setStatusStr( "支付尾款");
                orderDetailInfo.setStatusDetail("订单需补尾款，请尽快支付。");
                break;
            case 8:

                orderDetailInfo.setStatusStr( "处理中");
                orderDetailInfo.setStatusDetail( "订单仍在确认中，确认成功后会短信通知您。");

                break;
        }

        orderDetailInfo.getButtonsList().addAll(buttonsList) ;
        return orderDetailInfo;
    }

    private boolean isUseHotelCodeGenHotelOrder(FmbOrderInfo orderInfo) {
        return  GOODS_TYPE_6==orderInfo.getGoodsType() && 2==orderInfo.getOrderType() ;
    }

    /**
     * 查询 订单自动关闭任务表 找到自动关闭任务的结束时间和现在对比
     * @param orderId
     * @return
     */
    public FmbxScheduleJob queryPayEndTime(Integer orderId) {

        Wrapper<FmbxScheduleJob> wr = new LambdaQueryWrapper<FmbxScheduleJob>()
                .eq(FmbxScheduleJob::getJobType,JOB_NAME_CANCELORDERJOB)
                .eq(FmbxScheduleJob::getJobFmbxId,orderId)
                .select(FmbxScheduleJob::getScheduleRunTime)
                .orderByDesc(FmbxScheduleJob::getJobId)
                .last(" limit 1 ")
                ;
        final FmbxScheduleJob one = scheduleJobService.getOne(wr);
//        if (one==null) {
//            throw new BadLogicException("订单自动关闭时间异常[3112]");
//        }

        return one ;


    }


    /**
     *
     * @param userId
     * @param phoneNumber
     * @return
     */
    public Map queryPreOrderReceiver(int userId, String phoneNumber) {
        Map user = new HashMap();
        //查询最后一个酒店订单
        Wrapper<FmbOrderInfo> wr = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getUid,userId)
//                .in(FmbOrderInfo::getGoodsType, Arrays.asList( new Integer[]{4, 6, 15}))
                .in(FmbOrderInfo::getOrderStatus, Arrays.asList( new Integer[]{1,2,5,8}))
                .select(
                        FmbOrderInfo::getMobile,
                        FmbOrderInfo::getOrderSn,
                        FmbOrderInfo::getReceiver
                )
                .orderByDesc(FmbOrderInfo::getOrderId)
                .last(" limit 1");
        FmbOrderInfo orderInfo = orderInfoService.getOne(wr);
//        System.out.println(JSON.toJSONString(orderInfo));
        //查询最后一个订单

        if(null != orderInfo){
            user.put("mobile",orderInfo.getMobile());

            if (orderInfo.getReceiver().indexOf(",")!=-1) {
                user.put("receiver",orderInfo.getReceiver().split(",")[0]);
            }else {
                user.put("receiver",orderInfo.getReceiver());
            }
//            user.put("orderSn",orderInfo.getOrderSn());
        } else {
            user.put("mobile",phoneNumber);
            user.put("receiver","");
//            user.put("orderSn","");
        }
        return user  ;
    }

    public void confirmOrder(HashMap<String, Object> resultMap, NowUser nowUser,ReqConfirmOrder input) {


        self.chooseBestCoupon(nowUser.getUserId(),input,resultMap,"confirmOrder");

        resultMap.put("preOrderInfo",queryPreOrderReceiver(nowUser.getUserId(),nowUser.getUserInfo().getPhoneNumber())) ;


        //处理余额
        final FmbUserExtinfo fmbUserExtinfo = self.queryUserBalance(nowUser.getUserId());
        resultMap.put("balance",BigDecimal.ZERO) ;
        if (fmbUserExtinfo != null) {
            resultMap.put("balance",fmbUserExtinfo.getMoney()) ;
        }

        final FmbxActivity activity = queryFmbxActivity(input.getXaid());
        resultMap.put("activityTitle",activity.getTitle()) ;

        ReqHotel inputBasic = new ReqHotel();
        final ReqGenSku reqGenSku = input.getReqGenSkus().get(0);
        inputBasic.setSkuId(reqGenSku.getSkuId());
        inputBasic.setPlayDate(reqGenSku.getCheckInDate());
        inputBasic.setLeaveDate(reqGenSku.getCheckOutDate());

        BeanUtils.copyProperties(input,inputBasic);

        HashMap<String, Object> basicInfo = new HashMap<>();
        resultMap.put("basicInfo", basicInfo);

        ConfirmOrderMoneyInfo orderMoneyInfo = (ConfirmOrderMoneyInfo) resultMap.get("consumeList");


        resultMap.put("skutype",orderMoneyInfo.getFmbxSku().getSkuType()) ;

        if (orderMoneyInfo.getFmbxSku().getSkuType()==SKU_HOTEL_DATE) {
            frontHotelService.selectOrderRoomInfo(basicInfo, inputBasic, nowUser);
        }else if(orderMoneyInfo.getFmbxSku().getSkuType()==SKU_HOTEL_RESERVE){

            final FmbxHotelReserveSku reserveSku = orderOfHotelReserveService.querySkuReserve(orderMoneyInfo.getFmbxSku().getSkuId());
            final FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());

            final String yxq = FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseStartTime()) + "至" + FmbDateUtil.localDateTime2String10(reserveSku.getReserveChooseEndTime());

            String buyNumberStr = "" ;

            final ArrayList<String> buyLimitStr = buildReserveSkuBuyLimit( reserveSku.getFlagBuyLimit(), reserveSku.getEachOrderMinNum(), reserveSku.getEachOrderMaxNum(), reserveSku.getMaxBuyAllNum());

            basicInfo.put("buyLimitStr", buyLimitStr.stream().collect(Collectors.joining(",")));
            //入住有效期
            basicInfo.put("validReserveDateInfo", yxq);
            basicInfo.put("sku", reserveSku);
            basicInfo.put("suite", suite);
            final List<SuiteContent> suiteContentList = FrontRoomDomain.getSuiteContentList(suite.getSuiteContent());
            if (suiteContentList != null) {
                basicInfo.put("suiteContent", suiteContentList);
                suite.setSuiteContent("");
            }
        }
    }


    /**
     * 构造房券 购买数量限制信息
     * @param flagBuyLimit
     * @param eachOrderMinNum
     * @param eachOrderMaxNum
     * @param maxBuyAllNum
     * @return
     */
    public static ArrayList<String> buildReserveSkuBuyLimit( Integer flagBuyLimit, Integer eachOrderMinNum, Integer eachOrderMaxNum, Integer maxBuyAllNum) {
        final ArrayList<String> buyLimitStr = new ArrayList<>();
        if (flagBuyLimit ==1) {
            if (eachOrderMinNum.intValue() != 1) {
                buyLimitStr.add("每单最少购买" + eachOrderMinNum.intValue());
            }
            if (eachOrderMaxNum.intValue() != 9999) {
                buyLimitStr.add("每单最多购买" + eachOrderMaxNum.intValue());
            }
            if (maxBuyAllNum.intValue() != 9999) {
                buyLimitStr.add("每个用户最多购买" + maxBuyAllNum.intValue());
            }
        }
        return buyLimitStr;
    }


    public void orderSuccessPayFromRedis(OrderSuccessPayDomain orderSuccessPayDomain) {


        self.orderSuccessPay(orderSuccessPayDomain.getOrderSn(),orderSuccessPayDomain);
    }

    /**
     * 拿房券进入 酒店日历房 确认订单
     * @param resultMap
     * @param nowUser
     * @param input
     */
    @DS(DB_slave)
    public void hotelCodeQueryInConfirmPage(HashMap<String, Object> resultMap, NowUser nowUser, ReqConfirmOrder input) {

        final OrderSysInfo orderSysInfo = new OrderSysInfo();
        //查询房券信息
        self.checkHotelReserveCode(input.getHotelReserveCode(),nowUser.getUserId(), orderSysInfo) ;




        //查询上一个订单 收货人和电话
        resultMap.put("preOrderInfo",queryPreOrderReceiver(nowUser.getUserId(),nowUser.getUserInfo().getPhoneNumber())) ;

        HashMap<String, Object> basicInfo = new HashMap<>();
        resultMap.put("basicInfo", basicInfo);

        //查询房券sku 和套餐
        final FmbxHotelReserveSku reserveSku =
                orderOfHotelReserveService.querySkuReserve(orderSysInfo.getHotelCode().getTicketId());
        final FmbxSuite suite = orderOfHotelReserveService.queryFmbxSuite(reserveSku.getSuiteId());


        //查询 当前套餐对应的 日历房sku 的房型列表
        List<FrontRoomDomain> frontRoomDomainList =  frontHotelService.queryFrontRoomDomains(reserveSku.getSuiteId());


        //并返回
        resultMap.put("roomList", frontRoomDomainList);
        resultMap.put("hotelCodeEachNightPayed", orderSysInfo.getHotelCodeEachNightPay());
        resultMap.put("hotelCodeSkuId", reserveSku.getSkuId());


        //查询活动 信息
        final FmbxActivity activity = queryFmbxActivity(orderSysInfo.getReserveCodeFromOrderInfo().getAid());

        Wrapper<FmbxBpsContent> wrContent = new LambdaQueryWrapper<FmbxBpsContent>()
                .eq(FmbxBpsContent::getBpsId,activity.getBpsId())
                .select(FmbxBpsContent::getPolicy);
        //查询政策信息
        final FmbxBpsContent bpsContent = bpsContentService.getOne(wrContent);

        HashMap<String, Object> policy = new HashMap<>();
        policy.put("policy", bpsContent.getPolicy());
        policy.put("riskWarningTip", suite.getRiskWarningTip());

        basicInfo.put("orderRoomInfo", policy);
        basicInfo.put("suite", suite);
        final List<SuiteContent> suiteContentList = FrontRoomDomain.getSuiteContentList(suite.getSuiteContent());
        if (suiteContentList != null) {
            basicInfo.put("suiteContent", suiteContentList);
            suite.setSuiteContent("");
        }

        self.buildHotelCodeUseRoleInfo(orderSysInfo,resultMap) ;

    }




    /**
     * 查询商户信息
     * @param bpsId
     * @return
     */
    public FmbxBps queryBps(Integer bpsId) {
        LambdaQueryWrapper<FmbxBps> wrp = new LambdaQueryWrapper<FmbxBps>();
        wrp
                .eq(FmbxBps::getBpsId, bpsId)
                .select(FmbxBps::getName, FmbxBps::getPhone, FmbxBps::getAddress, FmbxBps::getProvinces, FmbxBps::getArea, FmbxBps::getLatitude, FmbxBps::getLongitude
                ,FmbxBps::getPlayTime,FmbxBps::getLeaveTime
                )
        ;

        FmbxBps bps = bpsService.getOne(wrp);
        return bps;
    }



    /**
     * 订单操作记录
     * @return
     */

    public List<AdminOrderActionDomain> selectAdminOrderAction(String ordersn) {

        HashMap<String, Object> p = new HashMap<>();
        p.put("ordersn", ordersn);

        List<AdminOrderActionDomain> lists = frontSkuDao.selectAdminOrderAction(p);

        return lists ;

    }


    public boolean saveOrderNote(String orderSn, String note){



        Wrapper<FmbOrderInfo> upW = new LambdaUpdateWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,orderSn)
                .setSql(" editor_note = concat(editor_note,'\r\n','"+FmbDateUtil.nowDateTimeStr()+":"+note.replace("'","\"")+"')   ")
                ;
        return  orderInfoService.update(upW);

    }




    public String getOrderHotelEmailTemp(String orderSn) {

        //设置参数
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        ReqorderDetail inputx = new ReqorderDetail();
        inputx.setAppid("");
        inputx.setOpVersion("");
        inputx.setVersion("");
        inputx.setOrderSn(orderSn);
        self.orderDetail(resultMap, inputx,null,ADMIN001);

        FmbOrderInfo orderInfo = (FmbOrderInfo) resultMap.get("orderInfo");

        //生成二维码
        UploadFileOutInfo qrCodePath = UploadFileUtil.prepareFilePath(FmbServerConfig.getFmbImgDir(), FmbServerConfig.getFmbImgUrl(), "qrCode", "png");
        QrCodeUtil.generate(String.valueOf(orderInfo.getOrderId()), 300, 300, FileUtil.file(qrCodePath.getFilePath()));
        String qrCodeUrl = qrCodePath.getOutUrl();
        System.out.println(qrCodeUrl);
        resultMap.put("qrCodeUrl",qrCodeUrl);
        resultMap.put("dfdStatusStr","新订"); // 1新订 2改签 3变更 4取消 5其他
        resultMap.put("dfdStatus",1);
        resultMap.put("serviceTel",FmbServerConfig.getServiceTel());//客服电话
        resultMap.put("serviceFax",FmbServerConfig.getServiceFax());//客服电话

        FreeMarkerConfigurer freeMarkerConfigurer = new FreeMarkerConfigurer();
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_22);
        cfg.setClassLoaderForTemplateLoading(ClassLoader.getSystemClassLoader(),"ftl");
        freeMarkerConfigurer.setConfiguration(cfg);
        String mailText = "";
        try {
            Template template = freeMarkerConfigurer.getConfiguration().getTemplate("hotelFrom.ftl");
            mailText = FreeMarkerTemplateUtils.processTemplateIntoString(template,resultMap);
            System.out.println(mailText);
        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
        return mailText;
    }


    /**
     * 检查    week是否在 房券的限制预约星期里
     * @param week 星期   1-星期1   7-星期日
     * @param limitWeek  房券限制的星期
     * @return
     */
    public  boolean reserveWeekIsLimit( Integer week,  Set<Integer> limitWeek){

        return  limitWeek.contains(week) ;


    }

    /**
     * 检查  dateStr  是否在 房券的限制预约日期里
     * @param dateStr 核查日期
     * @param limitsDate  房券限制的日期
     * @return
     */
    public  boolean reserveDateIsLimit(String dateStr,   Set<String> limitsDate){

        return  (limitsDate.contains(dateStr)) ;


    }


    @DSTransactional
    @DS(DB_master)
    public void cancelOrderByOrderSn(HashMap<String, Object> resultMap, ReqorderDetail input, NowUser nowUser, String front) {

        if (FRONT.equals(front) && nowUser==null ) {
            throw new BadLogicException("需要登录[3489]");
        }

        resultMap.put("result",0) ;

        final boolean cancelOrder = self.cancelOrder(null, front, 2, input.getOrderSn(),input);
        if (cancelOrder) {
            resultMap.put("result",1) ;
        }
    }
}
