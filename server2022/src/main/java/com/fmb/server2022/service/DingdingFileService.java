package com.fmb.server2022.service;


import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.server2022.domain.DingdingFileInfo;
import com.fmb.server2022.fmbx.entity.FmbxOutfileInfo;
import com.fmb.server2022.fmbx.service.IFmbxOutfileInfoService;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 文件上传服务
 */
@Service
public class DingdingFileService {


    private static Logger logger = LoggerFactory.getLogger(DingdingFileService.class);

    @Autowired
    IFmbxOutfileInfoService outfileInfoService;

    @Autowired
    SysTokenService tokenService ;

    @Value("${fmb.fmbImgDir}")
    private String fmbImgDir;
    @Value("${fmb.fmbImgUrl}")
    private String fmbImgUrl;


    /**
     *
     * 下载钉钉审批里的附件并存储到表里
     *
     * 根据 fileid ,modeltype ,和modelid判断是否重复
     * @param url
     * @param info
     * @param id 合同id
     */
    public void downLoadDingdingFile(String url, DingdingFileInfo info,Integer id){

        FmbxOutfileInfo dbInsert = new FmbxOutfileInfo();
        dbInsert.setThirdFileId(info.getFileId());
        dbInsert.setModelType("fmbx_bp_contract");
        dbInsert.setModelId(id);

        LambdaQueryWrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<FmbxOutfileInfo>()
                .eq(FmbxOutfileInfo::getThirdFileId,info.getFileId())
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract")
                .eq(FmbxOutfileInfo::getModelId,id)
        ;

        //检查文件是否存在
        FmbxOutfileInfo one = outfileInfoService.getOne(wr);

        //文件已经存在就不用下载了
        if (one != null) {
            return;
        }

        UploadFileOutInfo uploadFileOutInfo = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, "dingding", info.getFileType());

        byte[] data = HttpUtil.downloadBytes(url);
        try {
            FileUtils.writeByteArrayToFile(new File( uploadFileOutInfo.getFilePath()), data);
        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            //文件存储错了就没必要再往下走了
            return ;
        }

        dbInsert.setAdminUid(0);
        //存储钉钉 附件 form 的名字
        dbInsert.setExtString1(info.getFormName());
        dbInsert.setExtString2("");
        dbInsert.setExtInt1(0);
        dbInsert.setExtInt2(0);


        //设置默认值

        dbInsert.setSavePath(uploadFileOutInfo.getRelativePath());
        dbInsert.setSourceFileName(info.getFileName());
        dbInsert.setCtime(LocalDateTime.now());
        dbInsert.setType(info.getFileType());

        dbInsert.setLength(data.length);

        dbInsert.setUrl(uploadFileOutInfo.getOutUrl());


        outfileInfoService.save(dbInsert) ;

    }
}
