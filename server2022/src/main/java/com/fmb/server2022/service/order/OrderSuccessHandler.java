package com.fmb.server2022.service.order;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.server2022.controller.demo.FmbKafkaWorkerAutoAck;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxHotelOrderConfirm;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelOrderConfirmService;
import com.fmb.server2022.service.CouponService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.fmb.server2022.service.kafka.FmbKafkaProducerService.BINFO;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.*;
import static com.fmb.server2022.service.order.OrderService.GOODS_TYPE_6;

/**
 * <AUTHOR>
 * @Date: 2023/2/23 10:00 上午
 */
@Service
public class OrderSuccessHandler {
    private static Logger logger = LoggerFactory.getLogger(OrderSuccessHandler.class);



    @Autowired
    IFmbxHotelOrderConfirmService orderConfirmService ;

    @Autowired
    OrderSuccessHandler self ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    CouponService checkCouponService ;


    @Autowired
    OrderService coreOrderService ;


    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;

    @KafkaListener(topics = {ORDER_SUCCESS_PAY}, groupId = "hotelSaveConfirmInfo", containerFactory = "autoBatchAck")
    public void orderPay(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    hotelSaveConfirmInfo(record);
                }
            });
        }
    }

    public void hotelSaveConfirmInfo(ConsumerRecord<String, String> record) {

        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
        if (!kafkaMessage.isPresent()) {
            return;
        }
        logger.info("recive message [{}]",kafkaMessage.get());
        JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;

        int orderId = data.getInteger("OrderId");

        final FmbOrderInfo orderInfoDB = self.queryOrderInfo(orderId);
        final FmbxActivity activity = self.queryFmbxActivity(orderInfoDB.getAid());

        if (orderInfoDB.getGoodsType()== GOODS_TYPE_6  && activity.getIsConfirm()!=0  ) {
            self.saveConfirmInfo(orderInfoDB.getOrderSn());
        }

    }

    @Cacheable(value = "queryOrderInfoSuccess#4", keyGenerator = "customKeyGenerator")
    public FmbOrderInfo queryOrderInfo(int orderId) {
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderId, orderId)
                .select(FmbOrderInfo::getOrderId,  FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getUid,
                        FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getRealpayMoney,
                        FmbOrderInfo::getAid)
                ;
        final FmbOrderInfo orderInfoDB = orderInfoService.getOne(wrOrderInfo);
        return orderInfoDB ;
    }


    @Cacheable(value = "queryActivitySuccess#4", keyGenerator = "customKeyGenerator")
    public FmbxActivity queryFmbxActivity(Integer aid) {
        Wrapper<FmbxActivity> aWr = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid, aid)
                .select(FmbxActivity::getXaid,FmbxActivity::getBpsId,FmbxActivity::getBpId,FmbxActivity::getBpcId,
                        FmbxActivity::getCategoryId,FmbxActivity::getTicketType,FmbxActivity::getTitle,FmbxActivity::getPlatId,

                        FmbxActivity::getCityId,FmbxActivity::getSalesCityIds,FmbxActivity::getBusinessType,FmbxActivity::getIsConfirm,
                        FmbxActivity::getFlagDelete,FmbxActivity::getFlagHaveValidSku,FmbxActivity::getFlagPublish)
                ;
        //查出活动信息
        final FmbxActivity activity = activityService.getOne(aWr);
        return activity;
    }


    /**
     * 酒店二次确认 信息 存储
     */
    public void saveConfirmInfo(String orderSn){

        FmbxHotelOrderConfirm confirmDB = new FmbxHotelOrderConfirm();

        FmbOrderInfo orderInfo = coreOrderService.queryFmbOrderInfoFromAdmin001(orderSn);

        if (orderInfo == null) {
            logger.error("订单不存在"+orderSn);
            return ;
        }

        if (orderInfo.getGoodsType()!=GOODS_TYPE_6) {
            logger.error("不是酒店订单"+orderSn);
            return ;
        }

        final List<FmbOrderGoods> goodsList = coreOrderService.queryFmbOrderGoods(orderSn);


        BeanUtils.copyProperties(orderInfo,confirmDB);

        final JSONObject jsonObject = JSON.parseObject(orderInfo.getExtInfo());

        if (jsonObject.containsKey("hotelUserName")) {

            final String hotelUserName = jsonObject.getJSONArray("hotelUserName").stream().map(x -> x.toString()).collect(Collectors.joining(","));
            confirmDB.setReceiver(hotelUserName);
//            confirmDB.setPaySource(orderInfo.getPaySource());
        }


        confirmDB.setCtime(LocalDateTime.now());

        if(orderInfo.getDiscountRate()>0){
            confirmDB.setCouponMoney(orderInfo.getMoney().multiply(new BigDecimal(100-orderInfo.getDiscountRate()).divide(new BigDecimal(
                    "100"))));
        }else {
            confirmDB.setCouponMoney(new BigDecimal( orderInfo.getCouponMoney())) ;
        }

//        confirmDB.setCouponMoney();
        confirmDB.setOrderCreateTime(orderInfo.getCreateTime());

        final FmbOrderGoods goods = goodsList.get(0);
        final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
        final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);
        final FmbxSuite suite = json.getByPath("skuDetail.suite", FmbxSuite.class);

        confirmDB.setBuyNum(goods.getGoodsNumber());

        confirmDB.setCheckinDate(goods.getPlayTime().toLocalDate());
        confirmDB.setCheckoutDate(goods.getLeaveTime().toLocalDate());

        final FmbGoodsCategory goodsCate = checkCouponService.queryGoodsCate(activity.getCategoryId());
        confirmDB.setCateName(goodsCate.getCateName());


        HashMap<String, Object> goodsInfo = new HashMap<>();
        goodsInfo.put("checkInDate", skuDetail.getReqGenSku().getCheckInDate());
        goodsInfo.put("checkOutDate", skuDetail.getReqGenSku().getCheckOutDate());
        goodsInfo.put("roomNum", goods.getGoodsNumber());
        goodsInfo.put("goodsPrice", goods.getGoodsPrice());
        goodsInfo.put("settlePrice", goods.getSettlePrice());
        goodsInfo.put("suite", skuDetail.getSuite().getName());

        confirmDB.setGoodsList(JSONUtil.toJsonStr(goodsInfo));


        final Integer bpsId = skuDetail.getFmbxSku().getBpsId();
        FmbxBps bps = coreOrderService.queryBps(bpsId);

        confirmDB.setHotelInfo(JSONUtil.toJsonStr(bps));

        orderConfirmService.save(confirmDB) ;

    }

}
