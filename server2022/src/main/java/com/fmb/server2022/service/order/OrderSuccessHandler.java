package com.fmb.server2022.service.order;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.demo.FmbKafkaWorkerAutoAck;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.fmbx.entity.FmbGoodsCategory;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxHotelOrderConfirm;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSkuExt;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteExt;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelOrderConfirmService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuExtService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteExtService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.service.CouponService;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.FmbOrderInfoUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.fmb.server2022.service.kafka.FmbKafkaProducerService.BINFO;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.ORDER_SUCCESS_PAY;
import static com.fmb.server2022.service.order.KefuOrderService.FMBX_SECOND_CONFIRM;
import static com.fmb.server2022.service.order.OrderService.GOODS_TYPE_6;
import static com.fmb.server2022.service.order.OrderService.GOODS_TYPE_7;

/**
 * <AUTHOR>
 * @Date: 2023/2/23 10:00 上午
 */
@Service
public class OrderSuccessHandler {
    public static final String FMBX_HOTEL_RISK_TIP = "fmbx_hotel_risk_tip";
    public static final String FMBX_PAY_SUCCESS = "fmbx_pay_success";
    private static Logger logger = LoggerFactory.getLogger(OrderSuccessHandler.class);


    @Autowired
    FrontSkuDao frontSkuDao ;

    @Autowired
    IFmbxHotelOrderConfirmService orderConfirmService ;

    @Autowired
    OrderSuccessHandler self ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    CouponService checkCouponService ;

    @Autowired
    IFmbxSuiteExtService suiteExtService ;

    @Autowired
    IFmbxHotelReserveSkuExtService reserveSkuExtService ;


    @Autowired
    IFmbOrderGoodsService orderGoodsService ;
    @Autowired
    IFmbxSuiteService suiteService ;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService ;




    @Autowired
    OrderService coreOrderService ;

    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;

    @KafkaListener(topics = {ORDER_SUCCESS_PAY}, groupId = "hotelSaveConfirmInfo", containerFactory = "autoBatchAck")
    public void orderPay(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    Optional<String> kafkaMessage = Optional.ofNullable(record.value());
                    if (!kafkaMessage.isPresent()) {
                        return;
                    }
                    JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;
//                    int orderId = data.getInteger("OrderId");


                    hotelSaveConfirmInfo(data);
                }
            });
        }
    }


    @KafkaListener(topics = {ORDER_SUCCESS_PAY}, groupId = "sendSmsWhenSuccess", containerFactory = "autoBatchAck")
    public void sendSmsWhenSuccess(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    Optional<String> kafkaMessage = Optional.ofNullable(record.value());
                    if (!kafkaMessage.isPresent()) {
                        return;
                    }
                    JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;
                    int orderId = data.getInteger("OrderId");
                    sendSmsWhenSuccess(orderId);
                }
            });
        }
    }

    public void sendSmsWhenSuccess(int orderId) {
        sendSmsWhenSuccess(orderId, FMBX_PAY_SUCCESS);
    }

    /**
     * 发送成功短信
     * @param orderId
     * @param type "fmbx_pay_success":支付成功;"fmbx_second_confirm":二次确认
     */
    public void sendSmsWhenSuccess(int orderId, String type) {

        logger.info("sendSmsWhenSuccess_begin {}",orderId);
        final FmbOrderInfo orderInfoDB = self.queryOrderInfo(orderId);
        final FmbxActivity activity = self.queryFmbxActivity(orderInfoDB.getAid());
        final List<FmbOrderGoods> fmbOrderGoods = self.queryFmbOrderGoods(orderInfoDB.getOrderSn());


//        String smsContent = "通用字段：{活动标题}、{活动副标题}、" +
//                "订单相关字段：{订单号}、{应付金额}、{商品说明}、{收货人姓名}、{手机号}、{验证码}、{预约码}" +
//                "商户相关字段：{商户地址}、{商户电话}" +
//                "客服电话字段：{kefu_phone}" ;

        String smsContent = getSmsContent(orderInfoDB, activity, fmbOrderGoods,type);
//        System.out.println(smsContent);
        logger.info("send_sms [{}] [{}]" ,orderInfoDB.getMobile(), smsContent);
        FmbSmsUtil.sendSms(orderInfoDB.getMobile(),smsContent,type);

        //支付成功 增加 酒店风险短信发送
        if (FMBX_PAY_SUCCESS.equals(type)) {
            if (GOODS_TYPE_6 ==   orderInfoDB.getGoodsType()) {
                final FmbOrderGoods goodsDetail = fmbOrderGoods.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_6).findFirst().get();

                final FmbxSuiteRoomSku roomSku = roomSkuService.getById(goodsDetail.getGoodsId());

                final FmbxSuite fmbxSuite = suiteService.getById(roomSku.getSuiteId());

                // 风险提示:0-不需要,1-需要
                // 风险提示短信开关:0-不发短信,1-发短信
                final String riskSmsContent = fmbxSuite.getRiskSmsContent();
                if (fmbxSuite.getFlagRiskWarning()==1 && fmbxSuite.getRiskSmsFlag()==1 && StringUtils.isNotBlank(riskSmsContent)) {

                    String smsContentRisk = getSmsContent(orderInfoDB, activity, fmbOrderGoods, FMBX_HOTEL_RISK_TIP,riskSmsContent);

                    FmbSmsUtil.sendSms(orderInfoDB.getMobile(),smsContentRisk,FMBX_HOTEL_RISK_TIP);
                }
            }
        }

    }

    /**
     * 发送成功短信
     * @param orderSn
     * @param type "fmbx_pay_success":支付成功;"fmbx_second_confirm":二次确认
     */
    public String getSmsContentByOrderSn(String orderSn, String type) {
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderSn)
                .select(FmbOrderInfo::getOrderId,  FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getUid,
                        FmbOrderInfo::getAid,
                        FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getMobile ,
                        FmbOrderInfo::getReceiver,
                        FmbOrderInfo::getRealpayMoney,
                        FmbOrderInfo::getAid)
                ;
        final FmbOrderInfo orderInfoDB = orderInfoService.getOne(wrOrderInfo);
        final FmbxActivity activity = self.queryFmbxActivity(orderInfoDB.getAid());
        final List<FmbOrderGoods> fmbOrderGoods = self.queryFmbOrderGoods(orderSn);
        String smsContent = getSmsContent(orderInfoDB, activity, fmbOrderGoods,type);
        return smsContent;
    }

    public String getSmsContent(FmbOrderInfo orderInfoDB, FmbxActivity activity, List<FmbOrderGoods> fmbOrderGoods, String type) {
        return getSmsContent(orderInfoDB,activity,fmbOrderGoods,type,"") ;
    }

    public String getSmsContent(FmbOrderInfo orderInfoDB, FmbxActivity activity, List<FmbOrderGoods> fmbOrderGoods, String type,
                                String inputSmsContent) {
        String smsContent = "" ;

        HashMap<String, String> info = new HashMap<>();
        info.put("{活动标题}", activity.getTitle());
        info.put("{活动副标题}", activity.getSubTitle());
        info.put("{订单号}",  orderInfoDB.getOrderSn());
        info.put("{应付金额}",  FmbNumberUtil.bigDecimal2Str( orderInfoDB.getRealpayMoney()));
        info.put("{手机号}",  orderInfoDB.getMobile());

        info.put("{kefu_phone}", FmbServerConfig.getServiceTel());
        info.put("{收货人姓名}",  orderInfoDB.getReceiver());

        //先给默认值
        info.put("{商品说明}",  "");
        info.put("{商户地址}",  "");
        info.put("{商户电话}",  "");

        /**
         * 是否是改签订单
         */
        boolean isChangeOrder = StringUtils.isNotBlank(orderInfoDB.getBeforeOrderSn()) && orderInfoDB.getBeforeOrderSn().length()==20 ;


        if (orderInfoDB.getGoodsType()==GOODS_TYPE_6) {
            final FmbOrderGoods goodsDetail = fmbOrderGoods.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_6).findFirst().get();

            final cn.hutool.json.JSON json = JSONUtil.parse(goodsDetail.getGoodExt());

            final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
//            final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);
            final FmbxSuite suite = json.getByPath("skuDetail.suite", FmbxSuite.class);

            final FmbxSuiteExt suiteExt = suiteExtService.getById(suite.getSuiteId());

            if (FMBX_PAY_SUCCESS.equals(type)){

                //改签 短信模板优先级更高
                if (isChangeOrder) {
                    smsContent = suiteExt.getSmsReorder() ;
                }else {
                    if (orderInfoDB.getOrderType()==1) {
                        smsContent = suiteExt.getSmsPaySuccess() ;
                    }else if (orderInfoDB.getOrderType()==2) {
//                    {收货人姓名}，您的预约单{订单号}提交成功。预约单正在处理中，预定成功后将有确认短信给您。若预定有问题，我们会在24小时内(周末及节假日除外)与您电话联系。订单信息：{活动标题}，{商品说明}。
                        smsContent = suiteExt.getSmsResSuccess();
                    }
                }

            }else if(FMBX_SECOND_CONFIRM.equals(type)){
                smsContent = suiteExt.getSmsSecondConfirm() ;
            }else if(FMBX_HOTEL_RISK_TIP.equals(type)){
                smsContent = inputSmsContent ;
            }

            final String roomName = skuDetail.getRoomSku().getRoomName();

            info.put("{商品说明}",
                    suite.getName() +","+ roomName+goodsDetail.getGoodsNumber()+"间"+ FmbDateUtil.localDateTime2String10(goodsDetail.getPlayTime())
                            +"入住,"+ FmbDateUtil.localDateTime2String10(goodsDetail.getLeaveTime())+"离店" );

//            logger.info( "goodsDetail={}", JSON.toJSONString( goodsDetail,true));
            FmbxBps bps =  frontSkuDao.queryBpsInfoBySkuId(goodsDetail.getGoodsId()) ;

            info.put("{商户地址}",  bps.getPhone());
            info.put("{商户电话}",  bps.getAddress());


        }
        if (orderInfoDB.getGoodsType()==GOODS_TYPE_7) {
            final FmbOrderGoods goodsDetail = fmbOrderGoods.stream().filter(x -> x.getGoodsType() == GOODS_TYPE_7).findFirst().get();

            final cn.hutool.json.JSON json = JSONUtil.parse(goodsDetail.getGoodExt());
            final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

            info.put("{商品说明}", skuDetail.getReserveSku().getSkuName());

            final FmbxHotelReserveSkuExt reserveSkuExt = reserveSkuExtService.getById(skuDetail.getReserveSku().getSkuId());
            logger.info("reserveSkuExt {}",reserveSkuExt);

            
            smsContent = reserveSkuExt.getSmsPaySuccess() ;




            final String code = coreOrderService.queryHotelCodeList(orderInfoDB.getOrderSn(), 2)
                    .stream().map(FmbReserveHotelCodes::getCode).collect(Collectors.joining(","));
            info.put("{预约码}", code);

            FmbxBps bps =  frontSkuDao.queryBpsInfoBySkuId(goodsDetail.getGoodsId()) ;

            info.put("{商户地址}",  bps.getPhone());
            info.put("{商户电话}",  bps.getAddress());

        }

        final String[] aim = new String[info.size()];
        final String[] result = new String[info.size()];

        int i = 0 ;
        for (Map.Entry<String, String> stringEntry : info.entrySet()) {
            aim[i] = stringEntry.getKey() ;
            result[i] = stringEntry.getValue() ;
            i++ ;
        }
        smsContent  = StringUtils.replaceEach(smsContent,aim,result) ;
        return smsContent;
    }

    /**
     * 二次确认
     * @param info
     */
    public void hotelSaveConfirmInfo(JSONObject info) {


        if (logger.isInfoEnabled()) {
            logger.info("hotelSaveConfirmInfo_begin");
        }


        Integer orderId = info.getInteger("OrderId") ;
        final FmbOrderInfo orderInfoDB = self.queryOrderInfo(orderId);
        final FmbxActivity activity = self.queryFmbxActivity(orderInfoDB.getAid());

        if (orderInfoDB.getGoodsType()== GOODS_TYPE_6  && activity.getIsConfirm()!=0  ) {
//            if (logger.isInfoEnabled()) {
//                logger.info("hotelSaveConfirmInfo_begin2_{}",orderId);
//            }
            self.saveConfirmInfo(orderInfoDB.getOrderSn(),info);
        }

    }

    @Cacheable(value = "queryOrderInfoSuccess#4", keyGenerator = "customKeyGenerator")
    public FmbOrderInfo queryOrderInfo(int orderId) {
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderId, orderId)
                .select(FmbOrderInfo::getOrderId,  FmbOrderInfo::getOrderSn,FmbOrderInfo::getOrderStatus,FmbOrderInfo::getShopUserId,
                        FmbOrderInfo::getUid,
                        FmbOrderInfo::getAid,
                        FmbOrderInfo::getGoodsType,
                        FmbOrderInfo::getOrderType,
                        FmbOrderInfo::getMobile ,
                        FmbOrderInfo::getReceiver,
                        FmbOrderInfo::getRealpayMoney,
                        FmbOrderInfo::getBeforeOrderSn,
                        FmbOrderInfo::getAid)
                ;
        final FmbOrderInfo orderInfoDB = orderInfoService.getOne(wrOrderInfo);
        return orderInfoDB ;
    }


    @Cacheable(value = "queryFmbOrderGoods#4", keyGenerator = "customKeyGenerator")
    public List<FmbOrderGoods> queryFmbOrderGoods(String orderSn) {
        Wrapper<FmbOrderGoods> goodsWr = new LambdaQueryWrapper<FmbOrderGoods>()
                .eq(FmbOrderGoods::getOrderSn, orderSn)
                .select(FmbOrderGoods::getRecId,FmbOrderGoods::getPlayTime,FmbOrderGoods::getLeaveTime
                        , FmbOrderGoods::getGoodsType
                        ,FmbOrderGoods::getGoodsId
                        ,FmbOrderGoods::getGoodsPrice,FmbOrderGoods::getGoodsNumber,FmbOrderGoods::getGoodExt
                )
                ;
        //找到goods 信息
        final List<FmbOrderGoods> goodsList = orderGoodsService.list(goodsWr);
        return goodsList;
    }


    @Cacheable(value = "queryActivitySuccess#4", keyGenerator = "customKeyGenerator")
    public FmbxActivity queryFmbxActivity(Integer aid) {
        Wrapper<FmbxActivity> aWr = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid, aid)
                .select(FmbxActivity::getXaid,FmbxActivity::getBpsId,FmbxActivity::getBpId,FmbxActivity::getBpcId,
                        FmbxActivity::getCategoryId,FmbxActivity::getTicketType,FmbxActivity::getTitle,FmbxActivity::getPlatId,

                        FmbxActivity::getSubTitle,
                        FmbxActivity::getCityId,FmbxActivity::getSalesCityIds,FmbxActivity::getBusinessType,FmbxActivity::getIsConfirm,
                        FmbxActivity::getFlagDelete,FmbxActivity::getFlagHaveValidSku,FmbxActivity::getFlagPublish)
                ;
        //查出活动信息
        final FmbxActivity activity = activityService.getOne(aWr);
        return activity;
    }


    /**
     * 酒店二次确认 信息 存储
     */
    public void saveConfirmInfo(String orderSn, JSONObject info){

        FmbxHotelOrderConfirm confirmDB = new FmbxHotelOrderConfirm();

        FmbOrderInfo orderInfo = coreOrderService.queryFmbOrderInfoFromAdmin001(orderSn);

        if (orderInfo == null) {
            logger.error("订单不存在"+orderSn);
            return ;
        }

        if (orderInfo.getGoodsType()!=GOODS_TYPE_6) {
            logger.error("不是酒店订单"+orderSn);
            return ;
        }



        final List<FmbOrderGoods> goodsList = coreOrderService.queryFmbOrderGoods(orderSn);


        BeanUtils.copyProperties(orderInfo,confirmDB);

        final JSONObject jsonObject = JSON.parseObject(orderInfo.getExtInfo());

        if (jsonObject.containsKey("hotelUserName")) {

            final String hotelUserName = jsonObject.getJSONArray("hotelUserName").stream().map(x -> x.toString()).collect(Collectors.joining(","));
            confirmDB.setReceiver(hotelUserName);
//            confirmDB.setPaySource(orderInfo.getPaySource());
        }


        confirmDB.setCtime(LocalDateTime.now());

        if(orderInfo.getDiscountRate()>0){
            confirmDB.setCouponMoney(orderInfo.getMoney().multiply(new BigDecimal(100-orderInfo.getDiscountRate()).divide(new BigDecimal(
                    "100"))));
        }else {
            confirmDB.setCouponMoney(new BigDecimal( orderInfo.getCouponMoney())) ;
        }

//        confirmDB.setCouponMoney();
        confirmDB.setOrderCreateTime(orderInfo.getCreateTime());

        final FmbOrderGoods goods = goodsList.get(0);
        final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
        final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);
        final FmbxSuite suite = json.getByPath("skuDetail.suite", FmbxSuite.class);

        confirmDB.setBuyNum(goods.getGoodsNumber());

        confirmDB.setCheckinDate(goods.getPlayTime().toLocalDate());
        confirmDB.setCheckoutDate(goods.getLeaveTime().toLocalDate());

        final FmbGoodsCategory goodsCate = checkCouponService.queryGoodsCate(activity.getCategoryId());
        confirmDB.setCateName(goodsCate.getCateName());


        HashMap<String, Object> goodsInfo = new HashMap<>();
        goodsInfo.put("checkInDate", skuDetail.getReqGenSku().getCheckInDate());
        goodsInfo.put("checkOutDate", skuDetail.getReqGenSku().getCheckOutDate());
        goodsInfo.put("roomName", skuDetail.getRoomSku().getRoomName());
        goodsInfo.put("roomNum", goods.getGoodsNumber());
        goodsInfo.put("goodsPrice", goods.getGoodsPrice());
        goodsInfo.put("settlePrice", goods.getSettlePrice());
        goodsInfo.put("suite", skuDetail.getSuite().getName());

        confirmDB.setGoodsList(JSONUtil.toJsonStr(goodsInfo));


        final Integer bpsId = skuDetail.getFmbxSku().getBpsId();
        FmbxBps bps = coreOrderService.queryBps(bpsId);

        confirmDB.setHotelInfo(JSONUtil.toJsonStr(bps));

//
//        par.put("PayStatus", orderUpdateDB.getPayStatus());
//        par.put("OrderStatus", orderUpdateDB.getOrderStatus());
//        par.put("ConfirmStatus", orderUpdateDB.getConfirmStatus());
//        par.put("GoodsType", orderUpdateDB.getGoodsType());

        confirmDB.setOrderStatus(info.getInteger("OrderStatus"));

        if (info.containsKey("ConfirmStatus")){
            confirmDB.setConfirmStatus(info.getInteger("ConfirmStatus"));
        }


        orderConfirmService.save(confirmDB) ;

    }


    /**
     * 改签 生成 orderconfim
     * @param orderSn
     */
    public void saveConfirmInfoFromOrderChange(FmbOrderInfo orderInfo , FmbOrderGoods goods){
        FmbxHotelOrderConfirm confirmDB = new FmbxHotelOrderConfirm();



        BeanUtils.copyProperties(orderInfo,confirmDB);

        final JSONObject jsonObject = JSON.parseObject(orderInfo.getExtInfo());

        if (jsonObject.containsKey("hotelUserName")) {

            final String hotelUserName = jsonObject.getJSONArray("hotelUserName").stream().map(x -> x.toString()).collect(Collectors.joining(","));
            confirmDB.setReceiver(hotelUserName);
//            confirmDB.setPaySource(orderInfo.getPaySource());
        }


        confirmDB.setCtime(LocalDateTime.now());

        if(orderInfo.getDiscountRate()>0){
            confirmDB.setCouponMoney(orderInfo.getMoney().multiply(new BigDecimal(100-orderInfo.getDiscountRate()).divide(new BigDecimal(
                    "100"))));
        }else {
            confirmDB.setCouponMoney(new BigDecimal( orderInfo.getCouponMoney())) ;
        }

//        confirmDB.setCouponMoney();
        confirmDB.setOrderCreateTime(orderInfo.getCreateTime());


        final cn.hutool.json.JSON json = JSONUtil.parse(goods.getGoodExt());

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
        final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);
        final FmbxSuite suite = json.getByPath("skuDetail.suite", FmbxSuite.class);

        confirmDB.setBuyNum(goods.getGoodsNumber());

        confirmDB.setCheckinDate(goods.getPlayTime().toLocalDate());
        confirmDB.setCheckoutDate(goods.getLeaveTime().toLocalDate());

        final FmbGoodsCategory goodsCate = checkCouponService.queryGoodsCate(activity.getCategoryId());
        confirmDB.setCateName(goodsCate.getCateName());


        HashMap<String, Object> goodsInfo = new HashMap<>();
        goodsInfo.put("checkInDate", skuDetail.getReqGenSku().getCheckInDate());
        goodsInfo.put("checkOutDate", skuDetail.getReqGenSku().getCheckOutDate());
        goodsInfo.put("roomName", skuDetail.getRoomSku().getRoomName());
        goodsInfo.put("roomNum", goods.getGoodsNumber());
        goodsInfo.put("goodsPrice", goods.getGoodsPrice());
        goodsInfo.put("settlePrice", goods.getSettlePrice());
        goodsInfo.put("suite", skuDetail.getSuite().getName());

        confirmDB.setGoodsList(JSONUtil.toJsonStr(goodsInfo));


        final Integer bpsId = skuDetail.getFmbxSku().getBpsId();
        FmbxBps bps = coreOrderService.queryBps(bpsId);

        confirmDB.setHotelInfo(JSONUtil.toJsonStr(bps));

        orderConfirmService.save(confirmDB) ;
    }



    /**
     * 检查订单是否 是首单
     * @param records
     */
    @KafkaListener(topics = {ORDER_SUCCESS_PAY}, groupId = "checkIsFirstOrder", containerFactory = "autoBatchAck")
    public void checkIsFirstOrder(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    Optional<String> kafkaMessage = Optional.ofNullable(record.value());
                    if (!kafkaMessage.isPresent()) {
                        return;
                    }
                    JSONObject data =  JSON.parseObject( kafkaMessage.get()).getJSONObject(BINFO) ;
                    int orderId = data.getInteger("OrderId");
                    checkIsFirstOrderCore(orderId);
                }
            });
        }
    }

    public void checkIsFirstOrderCore(int orderId) {

        if (logger.isInfoEnabled()) {
            logger.info("checkIsFirstOrderCore {}",orderId);
        }
        final FmbOrderInfo orderInfoDB = self.queryOrderInfo(orderId);

        Wrapper<FmbOrderInfo> wr1 = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getUid,orderInfoDB.getUid())
                .eq(FmbOrderInfo::getIsFirstOrder,1)
                ;

        //之前已经存在 首单数据 就不需要过滤之前的所有订单了
        final long count = orderInfoService.count(wr1);
        if (count>0) {
            return;
        }

        Wrapper<FmbOrderInfo> wrOrder = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getUid,orderInfoDB.getUid())
                .eq(FmbOrderInfo::getPayStatus,2)
                .select(FmbOrderInfo::getMoney,FmbOrderInfo::getCouponMoney,FmbOrderInfo::getShippingFee,FmbOrderInfo::getCashMoney,
                        FmbOrderInfo::getDiscountRate,FmbOrderInfo::getGoodsType,FmbOrderInfo::getRealpayMoney,
                        FmbOrderInfo::getAppSubMoney,FmbOrderInfo::getCulturalCouponMoney,

                        FmbOrderInfo::getOrderId
                        )
                ;

        final List<FmbOrderInfo> orderList = orderInfoService.list(wrOrder);



        boolean isFirstOrder = true ;
        //过滤之前的所有 支付订单, 计算订单实际支付 金额 看是否大于0
        for (FmbOrderInfo orderInfo : orderList) {

            //排除当前订单 
            if (orderInfo.getOrderId().intValue()==orderId) {
                continue;
            }

            if( FmbOrderInfoUtil.isNewSysOrder(orderInfo)){
                //新订单 直接看实际支付金额
                if (orderInfo.getRealpayMoney().compareTo(BigDecimal.ZERO)==1) {
                    isFirstOrder = false ;
                    break;
                }


            }else {
                // 老订单  计算实际支付金额
                if (FmbOrderInfoUtil.oldOrderRealPayMoney(orderInfo).compareTo(BigDecimal.ZERO)==1){
                    isFirstOrder = false ;
                    break;
                }
            }
        }

        if (isFirstOrder) {

            Wrapper<FmbOrderInfo> upOrderWr = new LambdaUpdateWrapper<FmbOrderInfo>()
                    .eq(FmbOrderInfo::getOrderId,orderId)
                    .set(FmbOrderInfo::getIsFirstOrder,1)
                    ;
            final boolean update = orderInfoService.update(upOrderWr);

            if (logger.isInfoEnabled()) {
                logger.info("checkIsFirstOrderCore_update {}  {}",orderId, update);
            }

        }




    }

}
