package com.fmb.server2022.service.schedule.impel;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.server2022.service.schedule.ConsumeResult;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.fmb.basic.FmbConstants.IMPORTANT_INFO_LOGGER;
import static com.fmb.server2022.service.schedule.FmbScheduleUtil.JOB_NAME_ORDER_SUCCESS_PAY;

@Service(value = JOB_NAME_ORDER_SUCCESS_PAY)
public class OrderSuccessPayJob implements ConsumeJob {

    private static Logger logger = LoggerFactory.getLogger(OrderSuccessPayJob.class);

    private static Logger loggerImportant = LoggerFactory.getLogger(IMPORTANT_INFO_LOGGER);


    @Autowired
    OrderService orderService ;

    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

        try {

            final OrderSuccessPayDomain orderSuccessPayDomain = JSON.parseObject(job.getJobExtPars(), OrderSuccessPayDomain.class);

            loggerImportant.info("OrderSuccessPayJob {}",job.getJobExtPars());


            orderService.orderSuccessPayFromRedis(orderSuccessPayDomain) ;

//            orderService.cancelOrder(job.getJobFmbxId(),"auto_cancel");
        }catch (BadLogicException ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            return ConsumeResult.fail("fail");
        }

        return ConsumeResult.success("ok");
    }
}
