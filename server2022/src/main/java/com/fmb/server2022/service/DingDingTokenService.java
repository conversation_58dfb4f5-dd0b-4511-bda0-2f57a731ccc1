package com.fmb.server2022.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import com.taobao.api.ApiException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class DingDingTokenService {

    public static final String DINGDING_APPKEY = "dingxvmqiwc9jb1duf4r";
    private static final String DINGDING_APPSECRET = "Q5Uw7C5bwGpz3S05ZoRZhhHx2VcerK5XmfZcv8GzC3Ht9XD62StPITXnqheJD6HA";

    public static final String DINGDING_AES_KEY = "hCvqlltJIA6moPSK8c5xq5HwgiK9CxQNByNp9ixV6tN";
    //订阅管理
    // https://open-dev.dingtalk.com/fe/app?spm=a2q3p.21071111.0.0.44031cfaVD0QDI#/appMgr/inner/eapp/1179314192/9
    public static final String DINGDING_AES_TOKEN = "5sQcCkWubMtyA2XKHi6M0n0lqzcYSe2wxjBog";

//
    // OA审批测试的
    public static final String DINGDING_OA_FORMTEST_PROCID = "PROC-A057AAB1-A778-4DE2-9C1E-D8DEF8A82E5E";

    //测试合同审批
    public static final String DINGDING_CONTRACT_TEST_PROCID = "PROC-AD88F1B4-6A5D-40E5-8144-0F363DD62221";

    //正式的合同审批
    public static final String DINGDING_OA_CONTRACT_ONLINE_PROC_ID = "PROC-C3BEE436-0E6A-4002-B91D-DC5760594BF3";


    public static final  Long DINGDING_AGENTID = 1179314192L ;

    private static Logger logger = LoggerFactory.getLogger(DingDingTokenService.class);


    /**
     *  获取 access_token 并缓存2小时
     * @return
     */
    @Cacheable( cacheNames = "dingdingtoken#5400",   unless="#result==null or #result==''")
    public String getDingDingToken() {

        //只能在生成环境获取token
        if (FmbServerConfig.isPro()) {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(DINGDING_APPKEY);
            request.setAppsecret(DINGDING_APPSECRET);
            request.setHttpMethod("GET");
            OapiGettokenResponse response = null;
            try {
                response = client.execute(request);

                String access_token = response.getAccessToken();
                logger.info("getDingDingToken={}", access_token);
                return access_token;

            } catch (ApiException ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            }
        }else{

            HashMap<String, Object> par = new HashMap<>();
            final long value = System.currentTimeMillis();
            par.put("mill", value);
            par.put("sign", MD5Util.md5(""+value+ FmbConstants.PHP_MD5_SALT_KEY));
            final String json = JSONUtil.toJsonStr(par);

            final HttpRequest post1 = HttpUtil.createPost("http://api.fumubang.com/fmbx/dingding/dingToken.do");
//            final HttpRequest post1 = HttpUtil.createPost("http://127.0.0.1:8070/fmbx/dingding/dingToken.do");
            post1.header("Content-Type","application/json") ;
            post1.header("content-type","application/json") ;
            post1.body(json) ;
            final String body = post1.execute().body();

            final String token = JSON.parseObject(body).getString("token");
            if (StringUtils.isNotBlank(token)) {
                return  token ;
            }

        }



        return "";
    }






}
