package com.fmb.server2022.service;
import java.time.LocalDate;
import java.math.BigDecimal;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.basic.user.SessionUserInfoOfBp;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.domain.BpMediaInfoDomain;
import com.fmb.server2022.domain.BpsInfoDomain;
import com.fmb.server2022.domain.FmbAdminMenu;
import com.fmb.server2022.domain.FmbxBpListDomain;
import com.fmb.server2022.domain.FmbxBpReviewListDomain;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmbx.entity.FmbShopInterfacePerson;
import com.fmb.server2022.fmbx.entity.FmbShopUserExt;
import com.fmb.server2022.fmbx.entity.FmbShopUsers;
import com.fmb.server2022.fmbx.entity.FmbxBpBpsBind;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBpParterHotelInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpReviewHis;
import com.fmb.server2022.fmbx.entity.FmbxBpStat;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.service.IFmbShopInterfacePersonService;
import com.fmb.server2022.fmbx.service.IFmbShopUserExtService;
import com.fmb.server2022.fmbx.service.IFmbShopUsersService;
import com.fmb.server2022.fmbx.service.IFmbxBpBpsBindService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpMediaService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpReviewHisService;
import com.fmb.server2022.fmbx.service.IFmbxBpStatService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.mapper.BpsDao;
import com.fmb.server2022.mapper.FmbxBpDao;
import com.fmb.server2022.reqdomain.FrameLogin;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqCheckBpReview;
import com.fmb.server2022.reqdomain.ReqCommon;
import com.fmb.server2022.reqdomain.ReqFmbxBpList;
import com.fmb.server2022.reqdomain.ReqFmbxBpReviewList;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.server2022.reqdomain.ReqNewFmbxBp;
import com.fmb.server2022.service.order.FmbSmsUtil;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.github.benmanes.caffeine.cache.Cache;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fmb.util.FmbCommonUtil ;

import static com.fmb.basic.FmbConstants.TOKEN_KEEP_SECONDS_BPS;
import static com.fmb.basic.FmbConstants.TOKEN_PRE_OF_BPS;

@Service
public class FmbxBpService {

    private static Logger logger = LoggerFactory.getLogger(FmbxBpService.class);

    @Autowired
    IFmbShopUserExtService shopUserExtService ;

    @Autowired
    IFmbShopInterfacePersonService interfacePersonService ;

    @Autowired
    IFmbxBpMainService fmbxBpMainService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxBpParterHotelInfoService hotelInfoService ;

    @Autowired
    IFmbxBpMediaService bpMediaService ;


    @Autowired
    ExcelService excelService ;

    @Autowired
    BpsDao bpsDao ;

    @Autowired
    IFmbxBpStatService bpStatService ;


    @Autowired
    IFmbxBpReviewHisService fmbxBpReviewHisService;
    @Autowired
    FmbAdminTokenService tokenService ;
    @Autowired
    FmbxBpDao fmbxBpDao;

    @Autowired
    IFmbAdminService adminService ;

    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    IFmbxBpBpsBindService bindService;

    @Autowired
    IFmbShopUsersService shopUsersService ;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    Cache caffeineCache ;


    @Autowired
    BpsService xBpsService ;


    private static final String FIX_PASSWD = "-5n60:Y#" ;



    @DSTransactional
    public void saveBpFirst(@RequestBody ReqNewFmbxBp input, HashMap<String, Object> resultMap) {

        final FmbShopUsers shopUsers = saveShopUserFirst(input);


        resultMap.put("result", 0);
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        LambdaQueryWrapper<FmbxBpMain> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpMain::getAccountName,input.getAccountName()) ;
        FmbxBpMain one = fmbxBpMainService.getOne(test);
        if (one != null) {
            throw new BadLogicException("用户名重复:["+input.getAccountName()+"]");
        }

        LocalDateTime now = LocalDateTime.now();
        FmbxBpMain fmbxBpMain = new FmbxBpMain();
        BeanUtils.copyProperties(input,fmbxBpMain);

        fmbxBpMain.setBpId(shopUsers.getShopUserId());

        fmbxBpMain.setParterType(1);
        fmbxBpMain.setAccountType(1);
        fmbxBpMain.setAccountStatus(1);
        fmbxBpMain.setCheckStatus(1);
//        String salt = RandomUtil.randomString(8);
//        fmbxBpMain.setPasswd(MD5Util.md5(input.getInputPass()+salt));
//        fmbxBpMain.setSalt(salt);

        // 如果新增时使用这个这个密码 说明就是要用老系统商商家的密码
        //否则就是要用 老商户信息而且同时修改密码
        boolean updatePhpShopUser = false ;
        if (FIX_PASSWD.equals(input.getInputPass()) && input.getShopUserId()!=null) {
            fmbxBpMain.setPasswd(  shopUsers.getPassword());
        }else {

            if (StringUtils.isBlank(input.getInputPass())|| StringUtils.isBlank(input.getPasswdConfirm())) {
                throw  new BadLogicException("密码或确认密码为空") ;
            }
            if (!(StringUtils.isNotBlank(input.getPasswdConfirm())&& input.getPasswdConfirm().equals(input.getInputPass()))) {
                throw  new BadLogicException("密码校验不一致") ;
            }

            fmbxBpMain.setPasswd(  MD5Util.md5(input.getInputPass()));

            updatePhpShopUser = true ;
        }


        fmbxBpMain.setSalt("");

//        LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid,input.getAdminUid()) ;

        Wrapper<FmbAdmin> wr1 = new LambdaQueryWrapper<FmbAdmin>()
                .eq(FmbAdmin::getUid,input.getAdminUid()) ;
        FmbAdmin admin = adminService.getOne(wr1) ;

        //我司
        fmbxBpMain.setAdminUid(input.getAdminUid());
        fmbxBpMain.setAdminName(admin.getRealname());
        fmbxBpMain.setAdminMobile(admin.getPhone());
        fmbxBpMain.setAdminCity(admin.getArea().toString());
        fmbxBpMain.setCreateUid(userInfo.getUserId());
        fmbxBpMain.setCtime(now);
        fmbxBpMain.setUtime(now);

        boolean save = fmbxBpMainService.save(fmbxBpMain);

        if (save) {
            FmbxBpParterInfo info = new FmbxBpParterInfo();


            info.setBpId(fmbxBpMain.getBpId());
            info.setCtime(now);
            info.setUtime(now);

            if (StringUtils.isNotBlank(shopUsers.getShopAccount())) {
                info.setSettleAccountName(shopUsers.getShopAccount());
            }

            if (StringUtils.isNotBlank(shopUsers.getShopAccountSn())) {
                info.setSettleAccountNo(shopUsers.getShopAccountSn());
            }
            if (StringUtils.isNotBlank(shopUsers.getFinanceContact())) {
                info.setSettleAccountUsername(shopUsers.getFinanceContact());
            }
            if (StringUtils.isNotBlank(shopUsers.getFinanceTel())) {
                info.setSettleAccountUserMobile(shopUsers.getFinanceTel());
            }
            if (StringUtils.isNotBlank(shopUsers.getFinanceEmail())) {
                info.setSettleAccountUserEmail(shopUsers.getFinanceEmail());
            }



            // shopUsers.getShopUserId()
            final FmbShopUserExt shopUserExt = shopUserExtService.getOne(new LambdaQueryWrapper<FmbShopUserExt>().eq(FmbShopUserExt::getShopUserId,
                    shopUsers.getShopUserId()));
            if (shopUserExt != null && StringUtils.isNotBlank(shopUserExt.getShopAccount())) {
                info.setPartnerName(shopUserExt.getShopAccount());
            }else{
                info.setPartnerName(shopUsers.getUsername());
            }

            final LambdaQueryWrapper<FmbShopInterfacePerson> interfacePersonLambdaQueryWrapper = new LambdaQueryWrapper<FmbShopInterfacePerson>()
                    .eq(FmbShopInterfacePerson::getShopUserId, shopUsers.getShopUserId())
                    .eq(FmbShopInterfacePerson::getType,2)
                    .last(" limit 1 ")
                    ;
            FmbShopInterfacePerson p1 = interfacePersonService.getOne(interfacePersonLambdaQueryWrapper);
            if (p1 != null) {

                if (StringUtils.isNotBlank(p1.getRealName())) {
                    info.setPartnerContactsName(p1.getRealName());
                }
                if (StringUtils.isNotBlank(p1.getMobile())) {
                    info.setPartnerContactsMobile(p1.getMobile().trim().replaceAll("\\s",""));
                }
                if (StringUtils.isNotBlank(p1.getEmail())) {
                    info.setPartnerContactsEmail(p1.getEmail());
                }
                if (StringUtils.isNotBlank(p1.getTel())) {
                    info.setPartnerContactsPhone(p1.getTel());
                }

            }


            parterInfoService.save(info) ;

            if (fmbxBpMain.getParterType()==1) {
                FmbxBpParterHotelInfo hotelInfo = new FmbxBpParterHotelInfo();


                hotelInfo.setBpId(fmbxBpMain.getBpId());
                hotelInfo.setCtime(now);
                hotelInfo.setUtime(now);


                final LambdaQueryWrapper<FmbShopInterfacePerson> interfacePerson = new LambdaQueryWrapper<FmbShopInterfacePerson>()
                        .eq(FmbShopInterfacePerson::getShopUserId, shopUsers.getShopUserId())
                        .eq(FmbShopInterfacePerson::getType,1)
                        .last(" limit 1 ")
                        ;
                FmbShopInterfacePerson p2 = interfacePersonService.getOne(interfacePerson);
                if (p2 != null) {

                    if (StringUtils.isNotBlank(p2.getRealName())) {
                        hotelInfo.setHotelConfirmContractName(p2.getRealName());
                    }
                }

                if (StringUtils.isNotBlank(shopUsers.getEmail())) {
                    hotelInfo.setHotelConfirmContractEmail(shopUsers.getEmail());
                } else if (p2 != null && StringUtils.isNotBlank(p2.getEmail())) {
                    hotelInfo.setHotelConfirmContractEmail(p2.getEmail());
                }

                if (StringUtils.isNotBlank(shopUsers.getBookMailCc())) {
                    hotelInfo.setHotelConfirmContractEmailCc(shopUsers.getBookMailCc());
                }

                if (StringUtils.isNotBlank(shopUsers.getTel())) {
                    hotelInfo.setHotelConfirmContractPhone(shopUsers.getTel());
                }

                if (StringUtils.isNotBlank(shopUsers.getBookTime())) {
                    hotelInfo.setHotelConfirmContractNote(shopUsers.getBookTime());
                }

                if (StringUtils.isNotBlank(shopUsers.getFax())) {
                    hotelInfo.setHotelConfirmContractFax(shopUsers.getFax());
                }

                hotelInfoService.save(hotelInfo);
            }

            if (input.getBpsIds() != null) {
                saveBpBind(input, fmbxBpMain);
            }

            resultMap.put("result",1) ;
            resultMap.put("bpId", fmbxBpMain.getBpId());

            String passTip = "" ;
            if (!FIX_PASSWD.equals(input.getInputPass())) {
                passTip = "，密码：" + input.getInputPass();
            }

            resultMap.put("tipsInfo", "您已经开通父母邦供应商后台账号，登录地址：http://www.fumubang.com/shoplogin ,\n" +
                    "账号：" + input.getAccountName() + passTip + "，请尽快登录PC版供应商后台完善信息！");
        }

        //同步数据 到 老的商家表
        if (save && updatePhpShopUser){
            syncBpMain2ShopUsers(fmbxBpMain.getBpId());
        }

        resultMap.put("result", 1);
    }

    public void saveBpBind(@RequestBody ReqNewFmbxBp input, FmbxBpMain fmbxBpMain) {
        for(Map<String, Object> bpsid : input.getBpsIds()) {

            Integer bpsid1 = (Integer) bpsid.get("bpsid");

            Wrapper<FmbxBpBpsBind> wrBind = new LambdaQueryWrapper<FmbxBpBpsBind>()
                    .eq(FmbxBpBpsBind::getBpId,fmbxBpMain.getBpId())
                    .eq(FmbxBpBpsBind::getBpsId,bpsid1)
                    ;
            if (bindService.count(wrBind)==0) {

                FmbxBpBpsBind fmbxBpBpsBind = new FmbxBpBpsBind();
                fmbxBpBpsBind.setBpId(fmbxBpMain.getBpId());

                fmbxBpBpsBind.setBpsId(bpsid1);
                fmbxBpBpsBind.setAdminUid(input.getAdminUid());
                fmbxBpBpsBind.setCtime(LocalDateTime.now());
                bindService.save(fmbxBpBpsBind);

            }

            //如果是 这个商户需要可编辑
            if("1".equals( bpsid.get("edit").toString())){
                FmbxBps upInfo = new FmbxBps();
                upInfo.setBpsId(bpsid1);
                upInfo.setBpidOfEdit(fmbxBpMain.getBpId());
                bpsService.updateById(upInfo) ;
            }


        }
    }

    public void fmbxBpUpdate(ReqNewFmbxBp input, HashMap<String, Object> resultMap) {
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        FmbxBpMain dbBp = getFmbxBpMain(input.getBpId());
        LambdaQueryWrapper<FmbxBpMain> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpMain::getAccountName,input.getAccountName()) ;
        FmbxBpMain one = fmbxBpMainService.getOne(test);
        if (one != null && !one.getBpId().equals(input.getBpId())) {
            throw new BadLogicException("用户名重复:["+input.getAccountName()+"]");
        }

        BeanUtils.copyProperties(input,dbBp);

        //如果设置了密码
        if(StringUtils.isNotBlank(input.getInputPass())){
//            String salt = RandomUtil.randomString(8);
            dbBp.setPasswd(MD5Util.md5(input.getInputPass()));
//            dbBp.setSalt(salt) ;
            resultMap.put("tipsInfo", "您已经开通父母邦供应商后台账号，登录地址：http://www.fumubang.com/shoplogin ,\n"+
                    "账号："+input.getAccountName()+"，密码："+input.getInputPass()+"，请尽快登录PC版供应商后台完善信息！");
        }

        dbBp.setUtime(LocalDateTime.now());

        boolean save = fmbxBpMainService.updateById(dbBp) ;
        if (save) {
            //可编辑逻辑？
            LambdaQueryWrapper<FmbxBpBpsBind> wr1 = Wrappers.lambdaQuery(FmbxBpBpsBind.class).eq(FmbxBpBpsBind::getBpId,input.getBpId()) ;
            bindService.remove(wr1);

            saveBpBind(input, dbBp);

            syncBpMain2ShopUsers(dbBp.getBpId());
        }
        resultMap.put("result",1) ;
    }

    public void sendBpParterPwd(ReqNewFmbxBp input, HashMap<String, Object> resultMap){
        if (StringUtils.isNotBlank(input.getBpParterEmail())) {
            MailService.sendMimeMail(input.getBpParterEmail().split(","), "父母邦供应商账号", input.getTipsInfo(), null, false);
            resultMap.put("result",1) ;
            return;
        }else if (StringUtils.isNotBlank(input.getBpParterMobile())) {
            FmbSmsUtil.sendSms(input.getBpParterMobile(), input.getTipsInfo(),"fmbx_bp");
            resultMap.put("result",1) ;
            return;
        }
    }

    /**
     * 查询供应商详情
     * @param bpId
     * @param resultMap
     */
    public void bpDetailNoPass(Integer bpId, HashMap<String, Object> resultMap) {
        FmbxBpMain dbBp = getFmbxBpMain(bpId);
        dbBp.setPasswd(null);
        dbBp.setSalt(null);
        ReqNewFmbxBp bp = new ReqNewFmbxBp();
        BeanUtils.copyProperties(dbBp,bp);
        LambdaQueryWrapper<FmbAdmin> wrad = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid, dbBp.getAdminUid());
        FmbAdmin dbadmin = adminService.getOne(wrad);
        if (dbadmin != null) {
            bp.setAdminName(dbadmin.getRealname());
        }
        resultMap.put("bp",bp) ;

//        HashMap<String, Object> roleInfo = new HashMap<>();
//        roleInfo.put("isShowMobile", dbBp.getIsShowMobile());
//        roleInfo.put("isShowCustomerName", dbBp.getIsShowCustomerName());
//        roleInfo.put("showOrderStat", dbBp.getShowOrderStat());
//
//        resultMap.put("roleInfo",roleInfo) ;

        //查询基础信息
        LambdaQueryWrapper<FmbxBpParterInfo> wr1 = Wrappers.lambdaQuery(FmbxBpParterInfo.class).eq(FmbxBpParterInfo::getBpId, bp.getBpId());
        FmbxBpParterInfo parterInfo = parterInfoService.getOne(wr1);
        resultMap.put("parterInfo",parterInfo) ;

        //查询酒店信息
        if (bp.getParterType()==1) {

            resultMap.put("hotelInfo", hotelInfoService.getOne(Wrappers.lambdaQuery(FmbxBpParterHotelInfo.class).eq
                    (FmbxBpParterHotelInfo::getBpId,bp.getBpId())   )  ) ;
        }



        List<BpMediaInfoDomain> medias = bpsDao.selectBpMediaInfo(bpId);
        for(BpMediaInfoDomain media : medias){
            media.setExpireDate(parterInfo.getExpireDate());
        }
        resultMap.put("medias",medias) ;

        List<FmbxBps> shopList = fmbxBpDao.getShopList(bpId);

        ReqBpsInfo reqx = new ReqBpsInfo();

        reqx.setBpId(bpId);


        //供应商 关联的 商户信息
        List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(reqx);
        resultMap.put("list", lists);

        for (BpsInfoDomain infoDomain : lists) {
            if ( bpId.intValue()==infoDomain.getBpidOfEdit().intValue()) {
                infoDomain.setEdit(1);
            }else {
                infoDomain.setEdit(0);
            }
        }
        resultMap.put("shopList",lists) ;

//        List<> fmbxBpDao.selectBpMedia(bpId) ;


    }

    private FmbxBpMain getFmbxBpMain(Integer bpId) {
        FmbxBpMain bp = fmbxBpMainService.getById(bpId);
        if(bp==null ){
            throw new BadLogicException("供应商不存在:"+bpId);
        }
        return bp;
    }

    public void bpList(JSONObject par, HashMap<String, Object> resultMap) {

        String key = par.getString("key");
        boolean haveKey = StringUtils.isNotBlank(key) ;

        //构造查询
        LambdaQueryWrapper<FmbxBpMain> wraper = new LambdaQueryWrapper<>(FmbxBpMain.class) ;

        if (haveKey) {
            wraper .and(wr ->
                    {
                        wr.eq(
                                FmbxBpMain::getBpId, key)
                                .or()
                                // 账号名字
                                .like( FmbxBpMain::getAccountName, key);
                                // 联系电话
                                //.eq( FmbxBusinessPartner::getPartnerContactsPhone, key)
                                //.or()
                                // 联系人
                                //.like(  FmbxBusinessPartner::getPartnerContactsName, key)
                                //.or()
                                // 联系人
                                //.like( FmbxBusinessPartner::getPartnerName, key);
                    }

            ) ;
        }

        // 账号类型:1-主账号,2-子账号
        wraper.eq(null!=par.getInteger("accountType"),  FmbxBpMain::getAccountType,par.getInteger("accountType"))

                // 账号类型:1-正常,0-禁用
                .eq(null!=par.getInteger("accountStatus"),  FmbxBpMain::getAccountStatus,par.getInteger("accountStatus"))

                // 审核状态:1-待供应商完善,2-待商务审核,3-审核通过
                .eq(null!=par.getInteger("checkStatus"),  FmbxBpMain::getCheckStatus,par.getInteger("checkStatus"))

                // 我司对接人,后台用户uid
                .eq(null!=par.getInteger("adminUid"),  FmbxBpMain::getAdminUid,par.getInteger("adminUid"))

                // 商家类型:1-酒店,2-其他
                .eq(null!=par.getInteger("parterType"),  FmbxBpMain::getParterType,par.getInteger("parterType"))

                // 省份
                //.eq(StringUtils.isNotBlank(par.getString("partnerProvince")),  FmbxBusinessPartner::getPartnerProvince,par.getString("partnerProvince"))

                // 市
                //.eq(StringUtils.isNotBlank(par.getString("partnerCity")),  FmbxBusinessPartner::getPartnerCity,par.getString("partnerCity"))


                .orderBy("1".equals(par.getString("sort")), false, FmbxBpMain::getCtime)
                //.orderBy("2".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderCount)
                //.orderBy("3".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleGoodsCount)
                //.orderBy("4".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderMoney)

        ;

        Integer reqPageNo = par.getInteger("pageno");
        Integer pagesize = par.getInteger("pagesize");
        //构造分页参数
        IPage<FmbxBpMain> page = new Page<>(reqPageNo ==null?1: reqPageNo, pagesize ==null?10: pagesize);
        //查询得到结果
        IPage<FmbxBpMain> pageInfo = fmbxBpMainService.page(page,wraper);

        resultMap.put("listsData",pageInfo.getRecords()) ;
        resultMap.put("total",pageInfo.getTotal()) ;
        resultMap.put("pagesize",pageInfo.getSize()) ;
        resultMap.put("pageno",pageInfo.getCurrent()) ;
    }

    /**
     * 供应商后台登录
     * @param user
     * @return
     */
    public String authBps(ReqLoginUser user) {

        String bpsToken = "" ;
        Wrapper wr = new LambdaQueryWrapper<FmbxBpMain>()
                .eq(FmbxBpMain::getAccountName,user.getUsername())
                .eq(FmbxBpMain::getAccountStatus,1)
        ;

        FmbxBpMain dbBp = fmbxBpMainService.getOne(wr);

        if (dbBp==null) {
            throw new BadLogicException("供应商不存在");
        }

//        if(!dbBp.getPasswd().equals(  MD5Util.md5(user.getPasswd()+dbBp.getSalt()))){
        if(!dbBp.getPasswd().equals(  MD5Util.md5(user.getPasswd()))){
            throw new BadLogicException("密码错误");
        }

        bpsToken  = genBpsToken(dbBp) ;

        Wrapper<FmbxBpStat> wrEx = new LambdaQueryWrapper<FmbxBpStat>()
                .eq(FmbxBpStat::getBpId,dbBp.getBpId());
        if (bpStatService.count(wrEx)==0) {

            FmbxBpStat nBpStat = new FmbxBpStat();
            nBpStat.setBpId(dbBp.getBpId());
            nBpStat.setSaleOrderCount(0);
            nBpStat.setSaleGoodsCount(0);
            nBpStat.setSaleOrderMoney(new BigDecimal("0"));
            nBpStat.setLoginStatCount(1);
            nBpStat.setLoginStatLasttime(LocalDateTime.now());
            nBpStat.setCtime(LocalDateTime.now());
            nBpStat.setUtime(LocalDateTime.now());

            bpStatService.save(nBpStat);

        }else {

            Wrapper<FmbxBpStat> wrU  = new LambdaUpdateWrapper<FmbxBpStat>()
                    .eq(FmbxBpStat::getBpId,dbBp.getBpId())
                    .set(FmbxBpStat::getLoginStatLasttime,LocalDateTime.now())
                    .set(FmbxBpStat::getUtime,LocalDateTime.now())
                    .setSql(" login_stat_count= login_stat_count+1 ")
                    ;
            bpStatService.update(wrU);

        }


        return bpsToken ;


    }

    public void updateBpOrderStat(Integer bpid,BigDecimal money){

        final FmbxBpStat dbCheck = bpStatService.getById(bpid);
        if (dbCheck != null) {

            FmbxBpStat info = new FmbxBpStat();
            info.setBpId(bpid);
            info.setSaleOrderCount(dbCheck.getSaleOrderCount()+1);
            info.setSaleGoodsCount(0);
            info.setSaleOrderMoney(money.add(dbCheck.getSaleOrderMoney()));
            info.setUtime(LocalDateTime.now());
            bpStatService.updateById(info) ;
        }else {
            FmbxBpStat info = new FmbxBpStat();
            info.setBpId(bpid);
            info.setSaleOrderCount(1);
            info.setSaleGoodsCount(0);
            info.setSaleOrderMoney(money);
            info.setLoginStatCount(0);
            info.setCtime(LocalDateTime.now());
            bpStatService.save(info);
        }

    }


    /**
     * 从老的商家后台 打开新的商家后台需要 根据约定参数 返回bpsToken 实现登录
     * @param userInfo
     * @return
     */
    public String frameLogin(String userInfo) {

        String bpsToken = "" ;

        if (StringUtils.isBlank(userInfo)) {
            throw new BadLogicException("请求登录参数为空");
        }

        final FrameLogin frameLoginDomain = JSON.parseObject(Base64.decodeStr( userInfo), FrameLogin.class);
        if (frameLoginDomain == null) {
            throw new BadLogicException("请求登录参数异常");
        }

        if (!MD5Util.md5(frameLoginDomain.getShopUserId() + "_" + frameLoginDomain.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY).equals(frameLoginDomain.getMd5())) {
            throw new BadLogicException("请求登录参数校验失败");
        }

        Wrapper wr = new LambdaQueryWrapper<FmbxBpMain>()
                .eq(FmbxBpMain::getBpId,frameLoginDomain.getShopUserId())
                .eq(FmbxBpMain::getAccountStatus,1)
                ;
        FmbxBpMain dbBp = fmbxBpMainService.getOne(wr);

        if (dbBp==null) {
            throw new BadLogicException("供应商不存在");
        }
        bpsToken  = genBpsToken(dbBp) ;


        return bpsToken ;
    }


    /**
     * 登录成功 创建token 并写入redis 和 caffeineCache
     * @param dbBp
     * @return
     */
    private String genBpsToken(FmbxBpMain dbBp) {


        //生成token
        String token = SnowflakeIdWorker.getInstance().nextId()+"" ;

        SessionUserInfoOfBp userInfo = new SessionUserInfoOfBp();

        userInfo.setUserId(dbBp.getBpId() );
        userInfo.setUsername(dbBp.getAccountName());

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            caffeineCache.put(TOKEN_PRE_OF_BPS +token,userInfo);
            jedis.setex(TOKEN_PRE_OF_BPS +token, TOKEN_KEEP_SECONDS_BPS ,JSON.toJSONString(userInfo)) ;


        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return token;
    }

    /**
     * 供应商列表
     */

    public void selectFmbxBpList(HashMap<String, Object> resultMap, ReqFmbxBpList req) {

        if (req.getDoExport().intValue() == 0) {
            IPage<FmbxBpListDomain> page = new Page<>(req.getPageno(), req.getPagesize());
            IPage<FmbxBpListDomain> rlist = fmbxBpDao.selectFmbxBpList(page, req);

            resultMap.put("list", rlist.getRecords());
            resultMap.put("total", rlist.getTotal());
            resultMap.put("pages", rlist.getPages());
            resultMap.put("size", rlist.getSize());
            resultMap.put("current", rlist.getCurrent());
        } else {

            List<FmbxBpListDomain> lists = fmbxBpDao.selectFmbxBpList(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, FmbxBpListDomain.class, "供应商列表", "selectFmbxBpList");

            resultMap.put("excelInfo", uploadFileOutInfo);
        }

    }

    public void checkFmbxBpReview(ReqCheckBpReview input, HashMap<String, Object> resultMap) {
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        LambdaQueryWrapper<FmbxBpReviewHis> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpReviewHis::getStatus,1);
        test.eq(FmbxBpReviewHis::getBpId, input.getBpId());
        FmbxBpReviewHis dbReview = fmbxBpReviewHisService.getOne(test);
        if(dbReview==null ){
            throw new BadLogicException("供应商没有待审信息:"+input.getBpId());
        }

        BeanUtils.copyProperties(input,dbReview);
        dbReview.setAdminUid(userInfo.getUserId());
        dbReview.setChecktime(LocalDateTime.now());

        fmbxBpReviewHisService.updateById(dbReview) ;

        LambdaQueryWrapper<FmbxBpMain> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBpMain::getBpId, input.getBpId());

        FmbxBpMain dbBp = fmbxBpMainService.getOne(wr);
        if (input.getStatus()==2) {
            dbBp.setCheckStatus(3);
        }else if (input.getStatus()==3){
            dbBp.setCheckStatus(4);
        }
        fmbxBpMainService.updateById(dbBp) ;
        resultMap.put("result",1) ;
    }


    /**
     * 供应商审核列表
     */

    public void selectFmbxBpReviewList(HashMap<String, Object> resultMap, ReqFmbxBpReviewList req) {

        if (req.getDoExport().intValue() == 0) {
            IPage<FmbxBpReviewListDomain> page = new Page<>(req.getPageno(), req.getPagesize());
            IPage<FmbxBpReviewListDomain> rlist = fmbxBpDao.selectFmbxBpReviewList(page, req);

            resultMap.put("list", rlist.getRecords());
            resultMap.put("total", rlist.getTotal());
            resultMap.put("pages", rlist.getPages());
            resultMap.put("size", rlist.getSize());
            resultMap.put("current", rlist.getCurrent());
        } else {

            List<FmbxBpReviewListDomain> lists = fmbxBpDao.selectFmbxBpReviewList(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, FmbxBpReviewListDomain.class, "供应商审核列表", "selectFmbxBpReviewList");

            resultMap.put("excelInfo", uploadFileOutInfo);
        }

    }


    /**
     * 供应商退出登录
     * @return
     */
    public boolean logout() {


        String bpstoken = MDC.get(FmbConstants.BPSTOKEN);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            Long del = jedis.del(TOKEN_PRE_OF_BPS+bpstoken);
            return del>0 ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }


        return false ;
    }


    @DS(FmbConstants.DB_slave)
    public void nowBpInfo(HashMap<String,Object> resultMap) {

        SessionUser userInfo = userUnionService.nowUserInfo();
        FmbxBpMain fmbxBpMain = getFmbxBpMain(userInfo.getUserId());
        FmbxBpParterInfo parterInfo = parterInfoService.getById(userInfo.getUserId());


        resultMap.put("bpid",fmbxBpMain.getBpId()) ;
        resultMap.put("checkStatus",fmbxBpMain.getCheckStatus()) ;
        resultMap.put("accountName",fmbxBpMain.getAccountName()) ;
        resultMap.put("partnerName",parterInfo.getPartnerName()) ;

    }

    /**
     * 供应商开关
     * @param resultMap
     * @param bpid
     */
    public void toggleBp(HashMap<String,Object> resultMap, Integer bpid) {

        resultMap.put("result", 0);
        FmbxBpMain fmbxBpMain = getFmbxBpMain(bpid);

        FmbxBpMain update = new FmbxBpMain();

        update.setBpId(bpid);

        if (fmbxBpMain.getAccountStatus()==1){
            update.setAccountStatus(0);
        }else if (fmbxBpMain.getAccountStatus()==0){
            update.setAccountStatus(1);
        }

        update.setUtime(LocalDateTime.now());

        fmbxBpMainService.updateById(update) ;

        resultMap.put("result",1 );


    }

    public void bpMenuTree(HashMap<String,Object> resultMap) {


        String jsonFile = "" ;
        try {

            jsonFile =  IOUtils.toString(new ClassPathResource("bpTree.json").getInputStream(),"UTF-8") ;

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        List<FmbAdminMenu> fmbAdminMenus = JSONArray.parseArray(jsonFile, FmbAdminMenu.class);


        resultMap.put("trees",fmbAdminMenus) ;

    }






    /**
     * 通知BD 有供应商要审核
     * @param bpId
     */
    public void notifyBD(Integer bpId) {


        FmbxBpMain fmbxBpMain = getFmbxBpMain(bpId);

        FmbxBpParterInfo parterInfo = parterInfoService.getById(bpId);

        String url =
                "http://admin001.fumubang."+ FmbServerConfig.netOrCom() +"/xfmbadmin/src/entries/fmb/#/supplierManagement/supplierInfo/"+bpId ;

        if (Validator.isEmail(fmbxBpMain.getAdminEmail())) {

            String info = StrUtil.format("供应商：{} <br> 联系人：{}，手机号：{} <br> 供应商已填写入驻信息，请尽快<a href='"+url+"'>点击</a>审核！"
                    ,parterInfo.getPartnerName()
                    ,parterInfo.getPartnerContactsName()
                    ,parterInfo.getPartnerContactsMobile()

                    );
            MailService.sendMimeMail(new String[]{fmbxBpMain.getAdminEmail()}
                    , "【提醒】供应商入驻信息审核", info, null, true
            );
        }

        if (Validator.isMobile(fmbxBpMain.getAdminMobile())) {

            String shortUrl =  FmbCommonUtil.createShortUrl(url) ;

            String info = StrUtil.format("供应商：{}，联系人：{}，手机号：{}，供应商已填写入驻信息，请尽快审核！ {}"
                    ,parterInfo.getPartnerName()
                    ,parterInfo.getPartnerContactsName()
                    ,parterInfo.getPartnerContactsMobile()
                    ,shortUrl
            );
            FmbSmsUtil.sendSms(fmbxBpMain.getAdminMobile(),info,"bpNeedCheck");
        }


    }

    /**
     * 根据商户关键字搜索 商户信息
     * @param resultMap
     * @param input
     */
    public void listFmbShopUsers(HashMap<String, Object> resultMap, ReqCommon input) {

        if (StringUtils.isBlank(input.getShopUserKeyword())) {
            throw new BadLogicException("商家关键字信息不能为空");
        }

        Wrapper<FmbShopUsers> wrQ = new LambdaQueryWrapper<FmbShopUsers>()
                .like(StringUtils.isNotBlank(input.getShopUserKeyword()),FmbShopUsers::getUsername,input.getShopUserKeyword())
                .eq(FmbShopUsers::getStatus,1)
                .eq(FmbShopUsers::getShopType,0)
                //目前只做完酒店 ,这里限制是 酒店商家
                //后续可能放开
                .eq(FmbShopUsers::getType,3)
                .select(FmbShopUsers::getShopUserId,FmbShopUsers::getUsername,FmbShopUsers::getShowOrderStat,
                        FmbShopUsers::getIsShowCustomerName,FmbShopUsers::getIsShowMobile)
                .orderByDesc(FmbShopUsers::getShopUserId)
                ;
        final List<FmbShopUsers> list = shopUsersService.list(wrQ);
        resultMap.put("shopUserList",list) ;

    }

    public FmbShopUsers saveShopUserFirst(ReqNewFmbxBp input){


        if (input.getShopUserId()!=null  && 0!=input.getShopUserId()) {
            FmbShopUsers  shopUser = shopUsersService.getById(input.getShopUserId());
            if (shopUser == null) {
                throw new BadLogicException("商家不存在");
            }

            return shopUser ;
        }else{

            //防止重复 登录 用户名
            final String accountName = input.getAccountName();

            Wrapper<FmbShopUsers> wrU = new LambdaQueryWrapper<FmbShopUsers>()
                    .eq(FmbShopUsers::getUsername,accountName)
                    .last(" limit 1 ")
                    ;
            final FmbShopUsers one = shopUsersService.getOne(wrU);
            if (one != null) {
                return one ;
            }

            FmbShopUsers shopNew = new FmbShopUsers();

            if (StringUtils.isBlank(input.getInputPass())|| StringUtils.isBlank(input.getPasswdConfirm())) {
                throw  new BadLogicException("密码或确认密码为空") ;
            }
            if (!(StringUtils.isNotBlank(input.getPasswdConfirm())&& input.getPasswdConfirm().equals(input.getInputPass()))) {
                throw  new BadLogicException("密码校验不一致") ;
            }


            final LocalDateTime now = LocalDateTime.now();
            shopNew.setPid(0);
            shopNew.setShopType(0);
            shopNew.setUsername(input.getAccountName());
            shopNew.setPassword(MD5Util.md5(input.getInputPass()));
            shopNew.setShopId(0);
            shopNew.setCityId(0);
            shopNew.setLogo("");
            shopNew.setHead("");
            shopNew.setHeadMobile("");
            shopNew.setHeadEmail("");
            shopNew.setRealName("");
            shopNew.setPosition("");
            shopNew.setPartnerEmail("");
            shopNew.setMobile("");
            shopNew.setTel("");
            shopNew.setFax("");
            shopNew.setEmail("");
            shopNew.setAddress("");
            shopNew.setPolicy("");
            shopNew.setFinanceContact("");
            shopNew.setFinanceTel("");
            shopNew.setFinanceEmail("");
            shopNew.setFinanceFax("");
            shopNew.setInvoiceTitle("");
            shopNew.setShopBank("");
            shopNew.setShopAccount("");
            shopNew.setShopAccountSn("");
            shopNew.setIsCycle(0);
            shopNew.setCycleType(0);
            shopNew.setSettleCycle(0);
            shopNew.setSettleAlert(0);
            shopNew.setLastLoginIp("");

            shopNew.setLastLoginTime(now);
            shopNew.setStatus(1);
            shopNew.setIsStockShow(0);
            //指定类型 为酒店
            shopNew.setType(3);
            shopNew.setBookContact("");
            shopNew.setBookTime("");
            shopNew.setBookMailCc("");
            shopNew.setBookContactMode("other");
            shopNew.setBookContactOther("");
            shopNew.setIsShowMobile(input.getIsShowMobile());
            shopNew.setInformSales("");
            shopNew.setCtime(now);
            shopNew.setSignCompany("");
            shopNew.setIsShowSeal(0);
            shopNew.setBusinessLicenseList("");
            shopNew.setTaxList("");
            shopNew.setOrganizationList("");
            shopNew.setContractFileList("");
            shopNew.setShopPriceConfirmationList("");
            shopNew.setOtherList("");
            shopNew.setIsEntityTicket(0);
            shopNew.setIsShowRejectNoGoods(0);
            shopNew.setIsShowCustomerName(input.getIsShowCustomerName());
            shopNew.setCacelReserve(0);
            shopNew.setShowOrderStat(input.getShowOrderStat());
            shopNew.setIsUpTicketsShow(0);

            shopUsersService.save(shopNew) ;

            return shopNew ;
        }
    }


    /**
     * 批量一次性导入
     * 2年里 有成功订单的供应商 批量同步
     */
    public  void shopUser2BpMain(){
        List<Integer> shopUserIds = fmbxBpDao.listShopUsers() ;

        for (Integer shopUserId : shopUserIds) {
                syncShopUserInfo2BpMain(shopUserId);
        }

    }


    public void syncShopUserInfoFromPhp(Integer shopUserId) {
        final FmbShopUsers shopUser = shopUsersService.getById(shopUserId);
        //只同步酒店商家的
        if (shopUser != null  && shopUser.getType()==3  ) {
            syncShopUserInfo2BpMain(shopUserId) ;
        }
    }

    /**
     * 单个商户 同步
     * @param shopUserId
     */
    public void syncShopUserInfo2BpMain(Integer shopUserId) {

        logger.info("syncShopUserInfo2BpMain shopUserId = {}",shopUserId);

        final FmbShopUsers shopUser = shopUsersService.getById(shopUserId);


        Wrapper<FmbxBpMain> wrMain = new LambdaQueryWrapper<FmbxBpMain>()
                .eq(FmbxBpMain::getBpId,shopUserId);
        //如果供应商不存在
        if (fmbxBpMainService.count(wrMain)==0) {

            FmbxBpMain dbMain = new FmbxBpMain();
            dbMain.setBpId(shopUser.getShopUserId());
            dbMain.setParterType(1);
            dbMain.setAccountName(shopUser.getUsername());
            dbMain.setPasswd(shopUser.getPassword());
            dbMain.setSalt("");
            dbMain.setAdminUid(0);
            dbMain.setAdminName(shopUser.getHead());
            dbMain.setAdminMobile(shopUser.getHeadMobile());
            dbMain.setAdminEmail(shopUser.getHeadEmail());
            dbMain.setAdminCity("");
            dbMain.setSettleEachMonthday(0);
            dbMain.setSettleGenType(0);
            dbMain.setSettleType(0);
            dbMain.setInvoiceType(0);
            dbMain.setPid(0);
            dbMain.setAccountType(0);
            dbMain.setAccountStatus(0);
            dbMain.setCheckStatus(0);
            dbMain.setCreateUid(0);
            dbMain.setCtime(LocalDateTime.now());
            dbMain.setUtime(LocalDateTime.now());
            dbMain.setIsShowMobile(shopUser.getIsShowMobile());
            dbMain.setIsShowCustomerName(shopUser.getIsShowCustomerName());
            dbMain.setShowOrderStat(shopUser.getShowOrderStat());

            fmbxBpMainService.save(dbMain) ;
        }else {

            FmbxBpMain dbMain = new FmbxBpMain();
            dbMain.setBpId(shopUser.getShopUserId());
            dbMain.setPasswd(shopUser.getPassword());
            dbMain.setAdminName(shopUser.getHead());
            dbMain.setAdminMobile(shopUser.getHeadMobile());
            dbMain.setAdminEmail(shopUser.getHeadEmail());
            dbMain.setIsShowMobile(shopUser.getIsShowMobile());
            dbMain.setIsShowCustomerName(shopUser.getIsShowCustomerName());
            dbMain.setShowOrderStat(shopUser.getShowOrderStat());

            dbMain.setUtime(LocalDateTime.now());

            fmbxBpMainService.updateById(dbMain) ;

        }

    }


    public void syncBpMain2ShopUsers(Integer bpId) {

        final FmbxBpMain bpMain = fmbxBpMainService.getById(bpId);

        FmbShopUsers shopUser = new FmbShopUsers();

        shopUser.setShopUserId(bpMain.getBpId());
        shopUser.setPassword(bpMain.getPasswd());

        shopUser.setHead(bpMain.getAdminName());
        shopUser.setHeadMobile(bpMain.getAdminMobile());
        shopUser.setHeadEmail(bpMain.getAdminEmail());

        shopUser.setIsShowCustomerName(bpMain.getIsShowCustomerName());
        shopUser.setIsShowMobile(bpMain.getIsShowMobile());
        shopUser.setShowOrderStat(bpMain.getShowOrderStat());

        shopUsersService.updateById(shopUser) ;


    }

    public void selectFmbxBpQuery(HashMap<String, Object> resultMap, ReqFmbxBpList req) {

        List<Map> results = new ArrayList<>();

        if (StringUtils.isNotBlank(req.getQueryKeyword())) {
            results =  bpsDao.selectFmbxBpQuery(req.getQueryKeyword()) ;
        }


        resultMap.put("data", results);

    }
}
