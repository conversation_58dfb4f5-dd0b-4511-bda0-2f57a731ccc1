package com.fmb.server2022.service;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.basic.user.SessionUserInfoOfBp;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.domain.BpMediaInfoDomain;
import com.fmb.server2022.domain.BpsInfoDomain;
import com.fmb.server2022.domain.FmbAdminMenu;
import com.fmb.server2022.domain.FmbxBpListDomain;
import com.fmb.server2022.domain.FmbxBpReviewListDomain;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmbx.entity.FmbxBpBpsBind;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBpParterHotelInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpReviewHis;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.service.IFmbxBpBpsBindService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpMediaService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpReviewHisService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.mapper.BpsDao;
import com.fmb.server2022.mapper.FmbxBpDao;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqCheckBpReview;
import com.fmb.server2022.reqdomain.ReqFmbxBpList;
import com.fmb.server2022.reqdomain.ReqFmbxBpReviewList;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.server2022.reqdomain.ReqNewFmbxBp;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.github.benmanes.caffeine.cache.Cache;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fmb.basic.FmbConstants.TOKEN_KEEP_SECONDS_BPS;
import static com.fmb.basic.FmbConstants.TOKEN_PRE_OF_BPS;

@Service
public class FmbxBpService {

    private static Logger logger = LoggerFactory.getLogger(FmbxBpService.class);

    @Autowired
    IFmbxBpMainService fmbxBpMainService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxBpParterHotelInfoService hotelInfoService ;

    @Autowired
    IFmbxBpMediaService bpMediaService ;


    @Autowired
    ExcelService excelService ;

    @Autowired
    BpsDao bpsDao ;


    @Autowired
    IFmbxBpReviewHisService fmbxBpReviewHisService;
    @Autowired
    FmbAdminTokenService tokenService ;
    @Autowired
    FmbxBpDao fmbxBpDao;

    @Autowired
    IFmbAdminService adminService ;

    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    IFmbxBpBpsBindService bindService;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    Cache caffeineCache ;


    @Autowired
    BpsService xBpsService ;

    public void saveBpFirst(
            @RequestBody ReqNewFmbxBp input,
            HashMap<String, Object> resultMap) {

        resultMap.put("result", 0);
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        LambdaQueryWrapper<FmbxBpMain> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpMain::getAccountName,input.getAccountName()) ;
        FmbxBpMain one = fmbxBpMainService.getOne(test);
        if (one != null) {
            throw new BadLogicException("用户名重复:["+input.getAccountName()+"]");
        }

        LocalDateTime now = LocalDateTime.now();
        FmbxBpMain fmbxBpMain = new FmbxBpMain();
        BeanUtils.copyProperties(input,fmbxBpMain);

        fmbxBpMain.setParterType(1);
        fmbxBpMain.setAccountType(1);
        fmbxBpMain.setAccountStatus(1);
        fmbxBpMain.setCheckStatus(1);
        String salt = RandomUtil.randomString(8);
        fmbxBpMain.setPasswd(MD5Util.md5(input.getInputPass()+salt));
        fmbxBpMain.setSalt(salt);

        LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid,input.getAdminUid()) ;

        FmbAdmin admin = adminService.getOne(wr1) ;

        //我司
        fmbxBpMain.setAdminUid(input.getAdminUid());
        fmbxBpMain.setAdminName(admin.getRealname());
        fmbxBpMain.setAdminMobile(admin.getPhone());
        fmbxBpMain.setAdminCity(admin.getArea().toString());
        fmbxBpMain.setCreateUid(userInfo.getUserId());
        fmbxBpMain.setCtime(now);
        fmbxBpMain.setUtime(now);

        boolean save = fmbxBpMainService.save(fmbxBpMain);

        if (save) {
            FmbxBpParterInfo info = new FmbxBpParterInfo();
            info.setBpId(fmbxBpMain.getBpId());
            info.setCtime(now);
            info.setUtime(now);
            parterInfoService.save(info) ;

            if (fmbxBpMain.getParterType()==1) {
                FmbxBpParterHotelInfo hotelInfo = new FmbxBpParterHotelInfo();
                hotelInfo.setBpId(fmbxBpMain.getBpId());
                hotelInfo.setCtime(now);
                hotelInfo.setUtime(now);

                hotelInfoService.save(hotelInfo);
            }

            if (input.getBpsIds() != null) {
                saveBpBind(input, fmbxBpMain);
            }

            resultMap.put("result",1) ;
            resultMap.put("bpId", fmbxBpMain.getBpId());
        }

        resultMap.put("result", 1);
    }

    public void saveBpBind(@RequestBody ReqNewFmbxBp input, FmbxBpMain fmbxBpMain) {
        for(Map<String, Object> bpsid : input.getBpsIds()) {
            FmbxBpBpsBind fmbxBpBpsBind = new FmbxBpBpsBind();
            fmbxBpBpsBind.setBpId(fmbxBpMain.getBpId());
            Integer bpsid1 = (Integer) bpsid.get("bpsid");
            fmbxBpBpsBind.setBpsId(bpsid1);
            fmbxBpBpsBind.setAdminUid(input.getAdminUid());
            fmbxBpBpsBind.setCtime(LocalDateTime.now());
            bindService.save(fmbxBpBpsBind);


            //如果是 这个商户需要可编辑
            if("1".equals( bpsid.get("edit").toString())){

                FmbxBps upInfo = new FmbxBps();
                upInfo.setBpsId(bpsid1);
                upInfo.setBpidOfEdit(fmbxBpMain.getBpId());
                bpsService.updateById(upInfo) ;

            }

        }
    }

    public void fmbxBpUpdate(ReqNewFmbxBp input, HashMap<String, Object> resultMap) {
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        FmbxBpMain dbBp = getFmbxBpMain(input.getBpId());
        LambdaQueryWrapper<FmbxBpMain> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpMain::getAccountName,input.getAccountName()) ;
        FmbxBpMain one = fmbxBpMainService.getOne(test);
        if (one != null && !one.getBpId().equals(input.getBpId())) {
            throw new BadLogicException("用户名重复:["+input.getAccountName()+"]");
        }

        BeanUtils.copyProperties(input,dbBp);

        //如果设置了密码
        if(StringUtils.isNotBlank(input.getInputPass())){
            String salt = RandomUtil.randomString(8);
            dbBp.setPasswd(MD5Util.md5(input.getInputPass()+salt));
            dbBp.setSalt(salt) ;
        }

        dbBp.setUtime(LocalDateTime.now());

        boolean save = fmbxBpMainService.updateById(dbBp) ;
        if (save) {
            //可编辑逻辑？
            LambdaQueryWrapper<FmbxBpBpsBind> wr1 = Wrappers.lambdaQuery(FmbxBpBpsBind.class).eq(FmbxBpBpsBind::getBpId,input.getBpId()) ;
            bindService.remove(wr1);

            saveBpBind(input, dbBp);
        }
        resultMap.put("result",1) ;
    }

    /**
     * 查询供应商详情
     * @param bpId
     * @param resultMap
     */
    public void bpDetailNoPass(Integer bpId, HashMap<String, Object> resultMap) {
        FmbxBpMain dbBp = getFmbxBpMain(bpId);
        dbBp.setPasswd(null);
        dbBp.setSalt(null);
        ReqNewFmbxBp bp = new ReqNewFmbxBp();
        BeanUtils.copyProperties(dbBp,bp);
        LambdaQueryWrapper<FmbAdmin> wrad = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid, dbBp.getAdminUid());
        FmbAdmin dbadmin = adminService.getOne(wrad);
        bp.setAdminName(dbadmin.getRealname());
        resultMap.put("bp",bp) ;

        //查询基础信息
        LambdaQueryWrapper<FmbxBpParterInfo> wr1 = Wrappers.lambdaQuery(FmbxBpParterInfo.class).eq(FmbxBpParterInfo::getBpId, bp.getBpId());
        FmbxBpParterInfo parterInfo = parterInfoService.getOne(wr1);
        resultMap.put("parterInfo",parterInfo) ;

        //查询酒店信息
        if (bp.getParterType()==1) {

            resultMap.put("hotelInfo", hotelInfoService.getOne(Wrappers.lambdaQuery(FmbxBpParterHotelInfo.class).eq
                    (FmbxBpParterHotelInfo::getBpId,bp.getBpId())   )  ) ;
        }



        List<BpMediaInfoDomain> medias = bpsDao.selectBpMediaInfo(bpId);
        for(BpMediaInfoDomain media : medias){
            media.setExpireDate(parterInfo.getExpireDate());
        }
        resultMap.put("medias",medias) ;

        List<FmbxBps> shopList = fmbxBpDao.getShopList(bpId);

        ReqBpsInfo reqx = new ReqBpsInfo();

        reqx.setBpId(bpId);


        //供应商 关联的 商户信息
        List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(reqx);
        resultMap.put("list", lists);

        for (BpsInfoDomain infoDomain : lists) {
            if ( bpId.intValue()==infoDomain.getBpidOfEdit().intValue()) {
                infoDomain.setEdit(1);
            }else {
                infoDomain.setEdit(0);
            }
        }
        resultMap.put("shopList",lists) ;

//        List<> fmbxBpDao.selectBpMedia(bpId) ;


    }

    private FmbxBpMain getFmbxBpMain(Integer bpId) {
        FmbxBpMain bp = fmbxBpMainService.getById(bpId);
        if(bp==null ){
            throw new BadLogicException("供应商不存在:"+bpId);
        }
        return bp;
    }

    public void bpList(JSONObject par, HashMap<String, Object> resultMap) {

        String key = par.getString("key");
        boolean haveKey = StringUtils.isNotBlank(key) ;

        //构造查询
        LambdaQueryWrapper<FmbxBpMain> wraper = new LambdaQueryWrapper<>(FmbxBpMain.class) ;

        if (haveKey) {
            wraper .and(wr ->
                    {
                        wr.eq(
                                FmbxBpMain::getBpId, key)
                                .or()
                                // 账号名字
                                .like( FmbxBpMain::getAccountName, key);
                                // 联系电话
                                //.eq( FmbxBusinessPartner::getPartnerContactsPhone, key)
                                //.or()
                                // 联系人
                                //.like(  FmbxBusinessPartner::getPartnerContactsName, key)
                                //.or()
                                // 联系人
                                //.like( FmbxBusinessPartner::getPartnerName, key);
                    }

            ) ;
        }

        // 账号类型:1-主账号,2-子账号
        wraper.eq(null!=par.getInteger("accountType"),  FmbxBpMain::getAccountType,par.getInteger("accountType"))

                // 账号类型:1-正常,0-禁用
                .eq(null!=par.getInteger("accountStatus"),  FmbxBpMain::getAccountStatus,par.getInteger("accountStatus"))

                // 审核状态:1-待供应商完善,2-待商务审核,3-审核通过
                .eq(null!=par.getInteger("checkStatus"),  FmbxBpMain::getCheckStatus,par.getInteger("checkStatus"))

                // 我司对接人,后台用户uid
                .eq(null!=par.getInteger("adminUid"),  FmbxBpMain::getAdminUid,par.getInteger("adminUid"))

                // 商家类型:1-酒店,2-其他
                .eq(null!=par.getInteger("parterType"),  FmbxBpMain::getParterType,par.getInteger("parterType"))

                // 省份
                //.eq(StringUtils.isNotBlank(par.getString("partnerProvince")),  FmbxBusinessPartner::getPartnerProvince,par.getString("partnerProvince"))

                // 市
                //.eq(StringUtils.isNotBlank(par.getString("partnerCity")),  FmbxBusinessPartner::getPartnerCity,par.getString("partnerCity"))


                .orderBy("1".equals(par.getString("sort")), false, FmbxBpMain::getCtime)
                //.orderBy("2".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderCount)
                //.orderBy("3".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleGoodsCount)
                //.orderBy("4".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderMoney)

        ;

        Integer reqPageNo = par.getInteger("pageno");
        Integer pagesize = par.getInteger("pagesize");
        //构造分页参数
        IPage<FmbxBpMain> page = new Page<>(reqPageNo ==null?1: reqPageNo, pagesize ==null?10: pagesize);
        //查询得到结果
        IPage<FmbxBpMain> pageInfo = fmbxBpMainService.page(page,wraper);

        resultMap.put("listsData",pageInfo.getRecords()) ;
        resultMap.put("total",pageInfo.getTotal()) ;
        resultMap.put("pagesize",pageInfo.getSize()) ;
        resultMap.put("pageno",pageInfo.getCurrent()) ;
    }

    /**
     * 供应商后台登录
     * @param user
     * @return
     */
    public String authBps(ReqLoginUser user) {

        String bpsToken = "" ;
        LambdaQueryWrapper<FmbxBpMain> wr = new LambdaQueryWrapper<>();
        wr
                .eq(FmbxBpMain::getAccountName,user.getUsername())
                .eq(FmbxBpMain::getAccountStatus,1)

        ;

        FmbxBpMain dbBp = fmbxBpMainService.getOne(wr);

        if (dbBp==null) {
            throw new BadLogicException("供应商不存在");
        }

        if(!dbBp.getPasswd().equals(  MD5Util.md5(user.getPasswd()+dbBp.getSalt()))){
            throw new BadLogicException("密码错误");
        }

        bpsToken  = genBpsToken(dbBp) ;


        return bpsToken ;


    }

    /**
     * 登录成功 创建token 并写入redis 和 caffeineCache
     * @param dbBp
     * @return
     */
    private String genBpsToken(FmbxBpMain dbBp) {


        //生成token
        String token = SnowflakeIdWorker.getInstance().nextId()+"" ;

        SessionUserInfoOfBp userInfo = new SessionUserInfoOfBp();

        userInfo.setUserId(dbBp.getBpId() );
        userInfo.setUsername(dbBp.getAccountName());

        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            caffeineCache.put(TOKEN_PRE_OF_BPS +token,userInfo);
            jedis.setex(TOKEN_PRE_OF_BPS +token, TOKEN_KEEP_SECONDS_BPS ,JSON.toJSONString(userInfo)) ;


        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return token;
    }

    /**
     * 供应商列表
     */

    public void selectFmbxBpList(HashMap<String, Object> resultMap, ReqFmbxBpList req) {

        if (req.getDoExport().intValue() == 0) {
            IPage<FmbxBpListDomain> page = new Page<>(req.getPageno(), req.getPagesize());
            IPage<FmbxBpListDomain> rlist = fmbxBpDao.selectFmbxBpList(page, req);

            resultMap.put("list", rlist.getRecords());
            resultMap.put("total", rlist.getTotal());
            resultMap.put("pages", rlist.getPages());
            resultMap.put("size", rlist.getSize());
            resultMap.put("current", rlist.getCurrent());
        } else {

            List<FmbxBpListDomain> lists = fmbxBpDao.selectFmbxBpList(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, FmbxBpListDomain.class, "供应商列表", "selectFmbxBpList");

            resultMap.put("excelInfo", uploadFileOutInfo);
        }

    }

    public void checkFmbxBpReview(ReqCheckBpReview input, HashMap<String, Object> resultMap) {
        SessionUser userInfo = userUnionService.nowUserInfo();
        resultMap.put("result",0) ;

        LambdaQueryWrapper<FmbxBpReviewHis> test = new LambdaQueryWrapper<>();
        test.eq(FmbxBpReviewHis::getStatus,1);
        test.eq(FmbxBpReviewHis::getBpId, input.getBpId());
        FmbxBpReviewHis dbReview = fmbxBpReviewHisService.getOne(test);
        if(dbReview==null ){
            throw new BadLogicException("供应商没有待审信息:"+input.getBpId());
        }

        BeanUtils.copyProperties(input,dbReview);
        dbReview.setAdminUid(userInfo.getUserId());
        dbReview.setChecktime(LocalDateTime.now());

        fmbxBpReviewHisService.updateById(dbReview) ;

        LambdaQueryWrapper<FmbxBpMain> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBpMain::getBpId, input.getBpId());

        FmbxBpMain dbBp = fmbxBpMainService.getOne(wr);
        if (input.getStatus()==2) {
            dbBp.setCheckStatus(3);
        }else if (input.getStatus()==3){
            dbBp.setCheckStatus(1);
        }
        fmbxBpMainService.updateById(dbBp) ;
        resultMap.put("result",1) ;
    }


    /**
     * 供应商审核列表
     */

    public void selectFmbxBpReviewList(HashMap<String, Object> resultMap, ReqFmbxBpReviewList req) {

        if (req.getDoExport().intValue() == 0) {
            IPage<FmbxBpReviewListDomain> page = new Page<>(req.getPageno(), req.getPagesize());
            IPage<FmbxBpReviewListDomain> rlist = fmbxBpDao.selectFmbxBpReviewList(page, req);

            resultMap.put("list", rlist.getRecords());
            resultMap.put("total", rlist.getTotal());
            resultMap.put("pages", rlist.getPages());
            resultMap.put("size", rlist.getSize());
            resultMap.put("current", rlist.getCurrent());
        } else {

            List<FmbxBpReviewListDomain> lists = fmbxBpDao.selectFmbxBpReviewList(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, FmbxBpReviewListDomain.class, "供应商审核列表", "selectFmbxBpReviewList");

            resultMap.put("excelInfo", uploadFileOutInfo);
        }

    }


    /**
     * 供应商退出登录
     * @return
     */
    public boolean logout() {


        String bpstoken = MDC.get(FmbConstants.BPSTOKEN);

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
            Long del = jedis.del(TOKEN_PRE_OF_BPS+bpstoken);
            return del>0 ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }


        return false ;
    }


    @DS(FmbConstants.DB_slave)
    public void nowBpInfo(HashMap<String,Object> resultMap) {

        SessionUser userInfo = userUnionService.nowUserInfo();
        FmbxBpMain fmbxBpMain = getFmbxBpMain(userInfo.getUserId());
        FmbxBpParterInfo parterInfo = parterInfoService.getById(userInfo.getUserId());


        resultMap.put("bpid",fmbxBpMain.getBpId()) ;
        resultMap.put("checkStatus",fmbxBpMain.getCheckStatus()) ;
        resultMap.put("accountName",fmbxBpMain.getAccountName()) ;
        resultMap.put("partnerName",parterInfo.getPartnerName()) ;

    }

    /**
     * 供应商开关
     * @param resultMap
     * @param bpid
     */
    public void toggleBp(HashMap<String,Object> resultMap, Integer bpid) {

        resultMap.put("result", 0);
        FmbxBpMain fmbxBpMain = getFmbxBpMain(bpid);

        FmbxBpMain update = new FmbxBpMain();

        update.setBpId(bpid);

        if (fmbxBpMain.getAccountStatus()==1){
            update.setAccountStatus(0);
        }else if (fmbxBpMain.getAccountStatus()==0){
            update.setAccountStatus(1);
        }

        update.setUtime(LocalDateTime.now());

        fmbxBpMainService.updateById(update) ;

        resultMap.put("result",1 );


    }

    public void bpMenuTree(HashMap<String,Object> resultMap) {


        String jsonFile = "" ;
        try {

            jsonFile =  IOUtils.toString(new ClassPathResource("bpTree.json").getInputStream(),"UTF-8") ;

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        List<FmbAdminMenu> fmbAdminMenus = JSONArray.parseArray(jsonFile, FmbAdminMenu.class);


        resultMap.put("trees",fmbAdminMenus) ;

    }






    /**
     * 通知BD 有供应商要审核
     * @param bpId
     */
    public void notifyBD(Integer bpId) {


        FmbxBpMain fmbxBpMain = getFmbxBpMain(bpId);

        FmbxBpParterInfo parterInfo = parterInfoService.getById(bpId);

        if (Validator.isEmail(fmbxBpMain.getAdminEmail())) {

            String info = StrUtil.format("供应商：{} <br> 联系人：{}，手机号：{} <br> 供应商已填写入驻信息，请尽快点击审核！"
                    ,parterInfo.getPartnerName()
                    ,parterInfo.getPartnerContactsName()
                    ,parterInfo.getPartnerContactsPhone()

                    );
            MailService.sendMimeMail(new String[]{fmbxBpMain.getAdminEmail()}
                    , "【提醒】供应商入驻信息审", info, null, true
            );
        }

    }
}
