package com.fmb.server2022.service.order;

import cn.hutool.extra.pinyin.engine.pinyin4j.Pinyin4jEngine;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.MailSenderConfig;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.entity.FmbOrderConfirm;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmb_new.service.IFmbOrderConfirmService;
import com.fmb.server2022.fmbx.entity.*;
import com.fmb.server2022.fmbx.service.*;
import com.fmb.server2022.reqdomain.ReqFmbOrderConfirm;
import com.fmb.server2022.reqdomain.ReqFmbOrderHotel;
import com.fmb.server2022.reqdomain.ReqHotelMail;
import com.fmb.server2022.service.UserUnionService;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.SnowflakeIdWorker;
import lombok.AllArgsConstructor;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class KefuOrderService {
    public static final String FMBX_SECOND_CONFIRM = "fmbx_second_confirm";
    @Autowired
    JedisPool jedisPool ;
    @Autowired
    UserUnionService userUnionService;
    @Autowired
    private IFmbAdminService adminService;
    @Autowired
    OrderService orderService;
    @Autowired
    OrderSuccessHandler orderSuccessHandler;
    @Autowired
    IFmbOrderHotelService orderHotelService;
    @Autowired
    IFmbxHotelGoodsDetailService hotelGoodsDetailService;
    @Autowired
    IFmbOrderInfoService orderInfoService;
    @Autowired
    IFmbOrderGoodsService orderGoodsService;
    @Autowired
    IFmbxHotelOrderConfirmService orderConfirmService;
    @Autowired
    IFmbOrderConfirmService fmbOrderConfirmService;
    @Autowired
    IFmbOrderActionsService orderActionsService;
    @Autowired
    IFmbOrderRelevantLogService orderRelevantLogService;
    @Autowired
    IFmbxSuiteExtService suiteExtService;

    MailSenderConfig senderConfig;

    private static Logger logger = LoggerFactory.getLogger(KefuOrderService.class);

    public void saveFmbOrderHotel(HashMap<String, Object> resultMap, ReqFmbOrderHotel input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        resultMap.put("result", 0);
        FmbOrderHotel fmbOrderHotel = new FmbOrderHotel();
        BeanUtils.copyProperties(input1, fmbOrderHotel);
        fmbOrderHotel.setFormType(input1.getType()!=null && ReqFmbOrderHotel.typeMap.containsKey(input1.getType())? ReqFmbOrderHotel.typeMap.get(input1.getType()):1);
        fmbOrderHotel.setAdminUid(nowUserInfo.getUserId());
        fmbOrderHotel.setCreateTime(LocalDateTime.now());
        Wrapper<FmbOrderHotel> orderHotelWrapper = new LambdaQueryWrapper<FmbOrderHotel>()
                .eq(FmbOrderHotel::getOrderSn, input1.getOrderSn());
        FmbOrderHotel orderHoteldb = orderHotelService.getOne(orderHotelWrapper);
        if (input1.getId() == null || input1.getId() == 0) {
            if(orderHoteldb == null) {
                orderHotelService.save(fmbOrderHotel);
            }else{
                fmbOrderHotel.setId(orderHoteldb.getId());
                orderHotelService.updateById(fmbOrderHotel);
            }
        } else {
            orderHotelService.updateById(fmbOrderHotel);
        }
        resultMap.put("id", fmbOrderHotel.getId());
        resultMap.put("result", 1);
    }

    public void getFmbOrderHotel(HashMap<String, Object> resultMap, ReqFmbOrderHotel input) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderHotel> orderHotelWrapper = new LambdaQueryWrapper<FmbOrderHotel>()
                .eq(FmbOrderHotel::getOrderSn, input.getOrderSn());
        FmbOrderHotel orderHoteldb = orderHotelService.getOne(orderHotelWrapper);
        ReqFmbOrderHotel orderHotel = new ReqFmbOrderHotel();
        if (orderHoteldb != null) {
            orderHotel.setTitle("修改" + orderHoteldb.getGoodsId() + "订房单");
            orderHotel.setAdminUid(nowUserInfo.getUserId());
            orderHotel.setAdminName(nowUserInfo.getUsername());
            BeanUtils.copyProperties(orderHoteldb, orderHotel);
            resultMap.put("orderHotel", orderHotel);
        } else {
            Wrapper<FmbxHotelGoodsDetail> detailWrapper = new LambdaQueryWrapper<FmbxHotelGoodsDetail>()
                    .eq(FmbxHotelGoodsDetail::getOrderSn, input.getOrderSn());
            List<FmbxHotelGoodsDetail> detailList = hotelGoodsDetailService.list(detailWrapper);
            orderHotel.setId(0);
            orderHotel.setShopName(((FmbxBps) resultMap.get("hotelBasicInfo")).getName() + "(ID:" + ((FmbxActivity) resultMap.get("activity")).getXaid() + ")");
            orderHotel.setOrderSn(((FmbOrderInfo) resultMap.get("orderInfo")).getOrderSn());
            orderHotel.setReceiver(((FmbOrderInfo) resultMap.get("orderInfo")).getReceiver());
            orderHotel.setMobile(((FmbOrderInfo) resultMap.get("orderInfo")).getMobile());
            final SkuDetail skuDetail = (SkuDetail) resultMap.get("skuDetail");
            orderHotel.setTicketName(skuDetail.getSuite().getName());
            if (skuDetail.getRoomSku() != null) {
                orderHotel.setHotelType(skuDetail.getRoomSku().getRoomName());
            }
            orderHotel.setPostscript(((FmbOrderInfo) resultMap.get("orderInfo")).getPostscript());
            List<FmbOrderGoods> goodslist = (List<FmbOrderGoods>) resultMap.get("goodsList");
            if (goodslist.size() > 0) {
                orderHotel.setRecId(goodslist.get(0).getRecId());
                orderHotel.setPlayTime(goodslist.get(0).getPlayTime());
                orderHotel.setLeaveTime(goodslist.get(0).getLeaveTime());
                orderHotel.setGoodsNumber(goodslist.get(0).getGoodsNumber());
                orderHotel.setGoodsId(goodslist.get(0).getGoodsId());
                orderHotel.setRoomNum(goodslist.get(0).getGoodsNumber());
            }
            orderHotel.setTitle("生成" + orderHotel.getGoodsId() + "订房单");
            //String name = "";
            String goodsPrice = "";
            String settlePrice = "";
            for (FmbxHotelGoodsDetail detail : detailList) {
                //orderHotel.setTicketName(name+detail.getRoomDate()+",");
                orderHotel.setGoodsPrice(goodsPrice + detail.getGoodsPrice() + "X" + detail.getRoomNumber() + ",");
                orderHotel.setSettlePrice(settlePrice + detail.getSettlePrice() + "X" + detail.getRoomNumber() + ",");
                //name = orderHotel.getTicketName();
                goodsPrice = orderHotel.getGoodsPrice();
                settlePrice = orderHotel.getSettlePrice();
            }
            final FmbxBpParterHotelInfo bpParterHotelInfo = (FmbxBpParterHotelInfo) resultMap.get("bpParterHotelInfo");

            if (bpParterHotelInfo != null) {
                orderHotel.setShopTel(bpParterHotelInfo.getHotelConfirmContractPhone());
                orderHotel.setShopFax(bpParterHotelInfo.getHotelConfirmContractFax());
            }

            orderHotel.setCardType("");
            orderHotel.setCardSn("");
            orderHotel.setFmbComment("");
            Integer suiteId = skuDetail.getSuite().getSuiteId();
            FmbxSuiteExt ext = suiteExtService.getById(suiteId);
            if (ext != null && StringUtils.isNotBlank(ext.getRoomOrderNote())) {
                orderHotel.setTicketComment(ext.getRoomOrderNote());
            }
            orderHotel.setConfirmPeople("");
            orderHotel.setConfirmSn("");
            orderHotel.setAdminUid(nowUserInfo.getUserId());
            orderHotel.setAdminName(nowUserInfo.getUsername());
            orderHotel.setCreateTime(LocalDateTime.now());
            resultMap.put("orderHotel", orderHotel);
        }
        Wrapper<FmbOrderRelevantLog> recheckLogWrapper = new LambdaQueryWrapper<FmbOrderRelevantLog>()
                .eq(FmbOrderRelevantLog::getOrderSn, input.getOrderSn())
                .eq(FmbOrderRelevantLog::getOrderRelevantType,"order_confirm_recheck")
                .orderByDesc(FmbOrderRelevantLog::getOrderRelevantLogTime);
        List<FmbOrderRelevantLog> logList = orderRelevantLogService.list(recheckLogWrapper);
        if (logList.size()>0) {
            LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid, logList.get(0).getAdminUid())
                    .select(FmbAdmin::getName);
            FmbAdmin dbadmin = adminService.getOne(wr1);
            orderHotel.setConfirmRecheckStatus(logList.get(0).getOrderRelevantTypeStatus());
            if (logList.get(0).getOrderRelevantTypeStatus()==1) {
                orderHotel.setConfirmRecheckStatusStr(FmbDateUtil.FORMATTER_FULL.format(logList.get(0).getOrderRelevantLogTime()) + "由 " + (dbadmin != null ? dbadmin.getName() : "") + " 标为“已审”");
            }else{
                orderHotel.setConfirmRecheckStatusStr("");
            }
        }else{
            ReqFmbOrderConfirm re = new ReqFmbOrderConfirm();
            orderHotel.setConfirmRecheckStatus(0);
            orderHotel.setConfirmRecheckStatusStr("");
        }
    }

    public void addKefuConfirm(HashMap<String, Object> resultMap, ReqFmbOrderConfirm input) {
        LocalDateTime now = LocalDateTime.now();
        input.setCreateTime(now);
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, input.getOrderSn())
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShopUserId, FmbOrderInfo::getAid,FmbOrderInfo::getMobile,
                        FmbOrderInfo::getOrderType, FmbOrderInfo::getGoodsType, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShippingStatus, FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getConfirmStatus, FmbOrderInfo::getConfirmRecheckStatus, FmbOrderInfo::getEditorNote);
        FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrderInfo);

        if (orderInfo != null && input.getConfirmStatus() != null && input.getConfirmStatus() > -1) {
            Wrapper<FmbxHotelOrderConfirm> wrFmbxConfirm = new LambdaQueryWrapper<FmbxHotelOrderConfirm>()
                    .eq(FmbxHotelOrderConfirm::getOrderSn, input.getOrderSn());
            FmbxHotelOrderConfirm fmbxHotelOrderConfirm = orderConfirmService.getOne(wrFmbxConfirm);
            if (fmbxHotelOrderConfirm != null) {
                fmbxHotelOrderConfirm.setConfirmStatus(input.getConfirmStatus());
                fmbxHotelOrderConfirm.setLastConfirmTime(now);
                fmbxHotelOrderConfirm.setLastConfirmUid(nowUserInfo.getUserId());
                fmbxHotelOrderConfirm.setLastConfirmName(nowUserInfo.getUsername());
                if (input.getConfirmStatus() == 2) {
                    fmbxHotelOrderConfirm.setOrderStatus(5);
                }
                orderConfirmService.updateById(fmbxHotelOrderConfirm);
            }
            //订单
            orderInfo.setConfirmStatus(input.getConfirmStatus());
            orderInfo.setConfirmUpdateTime(now);
            Boolean hasNote = StringUtils.isNotBlank(input.getPostscript());
            if (hasNote) {
                orderInfo.setEditorNote((StringUtils.isBlank(orderInfo.getEditorNote()) ? orderInfo.getEditorNote() : orderInfo.getEditorNote() + "\r\n") + input.getPostscript());
            }
            //有货保留/无货退款 短信处理
            String smsNote = "";
            if (input.getConfirmStatus() == 2) {
                orderInfo.setConfirmTime(now);
                orderInfo.setOrderStatus(5);//有货保留
                if (input.getHasSms() != null && input.getHasSms()) {
                    smsNote = "，已短信通知用户";
                    //orderSuccessHandler.sendSmsWhenSuccess(orderInfo.getOrderId(), FMBX_SECOND_CONFIRM);
                    logger.info("send_sms [{}] [{}]" ,orderInfo.getMobile(), input.getSmsContent());
                    FmbSmsUtil.sendSms(orderInfo.getMobile(),input.getSmsContent(),FMBX_SECOND_CONFIRM);
                }
                Wrapper<FmbOrderHotel> orderHotelWrapper = new LambdaQueryWrapper<FmbOrderHotel>()
                        .eq(FmbOrderHotel::getOrderSn, input.getOrderSn());
                FmbOrderHotel orderHoteldb = orderHotelService.getOne(orderHotelWrapper);
                if (orderHoteldb != null) {
                    orderHoteldb.setConfirmSn(input.getPostscript());
                    orderHotelService.updateById(orderHoteldb);
                }
            }
            orderInfoService.updateById(orderInfo);
            String note = "后台用户" + nowUserInfo.getUsername() + "修改了二次确认：" + ReqHotelMail.confirmStatusMap.get(input.getConfirmStatus());
            if (hasNote) {
                note += "，并修改备注：" + input.getPostscript();
            }
            if (StringUtils.isNotBlank(smsNote)){
                note += smsNote;
            }
            this.saveOrderInfoAction(orderInfo, nowUserInfo, note);
            FmbOrderConfirm confirm = new FmbOrderConfirm();
            BeanUtils.copyProperties(input, confirm);
            confirm.setAdminUid(nowUserInfo.getUserId());
            fmbOrderConfirmService.save(confirm);
        }
        resultMap.put("result", 1);
    }

    public void addKefuEditorNote(HashMap<String, Object> resultMap, ReqFmbOrderConfirm input) {
        LocalDateTime now = LocalDateTime.now();
        input.setCreateTime(now);
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, input.getOrderSn())
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShopUserId, FmbOrderInfo::getAid,
                        FmbOrderInfo::getOrderType, FmbOrderInfo::getGoodsType, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShippingStatus, FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getConfirmStatus, FmbOrderInfo::getConfirmRecheckStatus, FmbOrderInfo::getEditorNote);
        FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrderInfo);
        if (orderInfo != null) {
            Boolean hasNote = StringUtils.isNotBlank(input.getPostscript());
            if (hasNote) {
                orderInfo.setEditorNote((StringUtils.isBlank(orderInfo.getEditorNote()) ? orderInfo.getEditorNote() : orderInfo.getEditorNote() + "\r\n") + input.getPostscript());
                orderInfoService.updateById(orderInfo);
                String note = "后台用户" + nowUserInfo.getUsername() + "修改备注：" + input.getPostscript();
                this.saveOrderInfoAction(orderInfo, nowUserInfo, note);
            }
        }
        resultMap.put("result", 1);
    }

    public void recheckOrderConfirm(HashMap<String, Object> resultMap, ReqFmbOrderHotel input) {
        LocalDateTime now = LocalDateTime.now();
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, input.getOrderSn())
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShopUserId, FmbOrderInfo::getAid,
                        FmbOrderInfo::getOrderType, FmbOrderInfo::getGoodsType, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShippingStatus, FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getConfirmStatus, FmbOrderInfo::getConfirmRecheckStatus, FmbOrderInfo::getEditorNote);
        FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrderInfo);
        if (orderInfo != null && input.getConfirmRecheckStatus() != null) {
            orderInfo.setConfirmRecheckStatus(input.getConfirmRecheckStatus());
            orderInfo.setConfirmUpdateTime(now);
            orderInfoService.updateById(orderInfo);
            String note = "后台用户" + nowUserInfo.getUsername() + "修改复审状态为“已审”";
            this.saveOrderInfoAction(orderInfo, nowUserInfo, note);
            FmbOrderRelevantLog recheckLog = new FmbOrderRelevantLog();
            recheckLog.setOrderSn(input.getOrderSn());
            recheckLog.setOrderRelevantType("order_confirm_recheck");
            recheckLog.setOrderRelevantIdentifier("confirm_recheck_status");
            recheckLog.setOrderRelevantTypeStatus(1);
            recheckLog.setAdminUid(nowUserInfo.getUserId());
            recheckLog.setOrderRelevantLogTime(now);
            recheckLog.setOrderRelevantNote("修改订单（"+input.getOrderSn()+"）复审状态为“已审”");
            orderRelevantLogService.save(recheckLog);
            //fmbOrderConfirmService.save(input);
            //有货保留/无货退款 短信处理 orderInfo.setConfirmTime(LocalDateTime.now());
            Wrapper<FmbxHotelOrderConfirm> wrFmbxConfirm = new LambdaQueryWrapper<FmbxHotelOrderConfirm>()
                    .eq(FmbxHotelOrderConfirm::getOrderSn, input.getOrderSn());
            FmbxHotelOrderConfirm fmbxHotelOrderConfirm = orderConfirmService.getOne(wrFmbxConfirm);
            if (fmbxHotelOrderConfirm != null) {
                fmbxHotelOrderConfirm.setConfirmRecheckStatus(input.getConfirmRecheckStatus());
                fmbxHotelOrderConfirm.setLastConfirmTime(now);
                fmbxHotelOrderConfirm.setLastConfirmUid(nowUserInfo.getUserId());
                fmbxHotelOrderConfirm.setLastConfirmName(nowUserInfo.getUsername());
                orderConfirmService.updateById(fmbxHotelOrderConfirm);
            }
        }
        resultMap.put("result", 1);
    }

    public void sendOrderHotelMail(HashMap<String, Object> resultMap, ReqHotelMail input1) {
        LocalDateTime now = LocalDateTime.now();
        //二次确认状态，客服备注处理
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderHotel> orderHotelWrapper = new LambdaQueryWrapper<FmbOrderHotel>()
                .eq(FmbOrderHotel::getId, input1.getId());
        FmbOrderHotel orderHoteldb = orderHotelService.getOne(orderHotelWrapper);
        if (orderHoteldb == null) {
            throw new BadLogicException("订房单不存在");
        }
        Wrapper<FmbOrderInfo> wrOrderInfo = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn, orderHoteldb.getOrderSn())
                .select(FmbOrderInfo::getOrderId, FmbOrderInfo::getOrderSn, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShopUserId, FmbOrderInfo::getAid,
                        FmbOrderInfo::getOrderType, FmbOrderInfo::getGoodsType, FmbOrderInfo::getOrderStatus, FmbOrderInfo::getShippingStatus, FmbOrderInfo::getPayStatus,
                        FmbOrderInfo::getConfirmStatus, FmbOrderInfo::getConfirmRecheckStatus, FmbOrderInfo::getEditorNote);
        FmbOrderInfo orderInfo = orderInfoService.getOne(wrOrderInfo);
        if (input1.getConfirmStatus() > -1) {
            orderInfo.setConfirmStatus(input1.getConfirmStatus());
            orderInfo.setConfirmUpdateTime(now);
            Boolean hasNote = StringUtils.isNotBlank(input1.getConfirmComment());
            if (hasNote) {
                orderInfo.setEditorNote((StringUtils.isBlank(orderInfo.getEditorNote()) ? orderInfo.getEditorNote() : orderInfo.getEditorNote() + "\r\n") + input1.getConfirmComment());
            }
            //有货保留/无货退款 短信处理
            String smsNote = "";
            if (input1.getConfirmStatus() == 2) {
                orderInfo.setConfirmTime(now);
                orderInfo.setOrderStatus(5);//有货保留
                smsNote = "，已短信通知用户";
                orderSuccessHandler.sendSmsWhenSuccess(orderInfo.getOrderId(), FMBX_SECOND_CONFIRM);
            }
            orderInfoService.updateById(orderInfo);
            String note = "后台用户" + nowUserInfo.getUsername() + "修改了二次确认：" + ReqHotelMail.confirmStatusMap.get(input1.getConfirmStatus());
            if (hasNote) {
                note += "，并修改备注：" + input1.getConfirmComment();
            }
            note += smsNote;
            this.saveOrderInfoAction(orderInfo, nowUserInfo, note);
            FmbOrderConfirm confirm = new FmbOrderConfirm();
            confirm.setOrderSn(orderInfo.getOrderSn());
            confirm.setAdminUid(nowUserInfo.getUserId());
            confirm.setConfirmStatus(input1.getConfirmStatus());
            confirm.setPostscript(input1.getConfirmComment());
            confirm.setCreateTime(now);
            fmbOrderConfirmService.save(confirm);
            Wrapper<FmbxHotelOrderConfirm> wrFmbxConfirm = new LambdaQueryWrapper<FmbxHotelOrderConfirm>()
                    .eq(FmbxHotelOrderConfirm::getOrderSn, orderHoteldb.getOrderSn());
            FmbxHotelOrderConfirm fmbxHotelOrderConfirm = orderConfirmService.getOne(wrFmbxConfirm);
            if (fmbxHotelOrderConfirm != null) {
                fmbxHotelOrderConfirm.setConfirmStatus(input1.getConfirmStatus());
                fmbxHotelOrderConfirm.setLastConfirmTime(now);
                fmbxHotelOrderConfirm.setLastConfirmUid(nowUserInfo.getUserId());
                fmbxHotelOrderConfirm.setLastConfirmName(nowUserInfo.getUsername());
                orderConfirmService.updateById(fmbxHotelOrderConfirm);
            }
        }

        try {
            JavaMailSenderImpl mailSender = senderConfig.getSender("df");
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, MimeMessageHelper.MULTIPART_MODE_RELATED, "UTF-8");
            helper.setFrom("<EMAIL>");
            if (StringUtils.isBlank(input1.getSetTo())) {
                throw new BadLogicException("请填写收件人");
            }
            helper.setTo(input1.getSetTo());
            if (StringUtils.isNotBlank(input1.getSetCc())) {
                helper.setCc(input1.getSetCc());
            }
            helper.setSubject(input1.getSubject());
            if (StringUtils.isNotBlank(input1.getTextContent())) {
                String text = "备注（用户特别要求）:" + input1.getTextContent() + "<br/>-----<br/>";
                helper.setText(text + input1.getHtmlContent(), true);
            }else{
                helper.setText(input1.getHtmlContent(), true);
            }
            mailSender.send(mimeMessage);
            setOrderHotelNumByAid(orderInfo.getAid());
            resultMap.put("result", 1);
            //邮件是否要日志
        } catch (MessagingException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
    }

    public void getOrderHotelMail(HashMap<String, Object> resultMap, ReqFmbOrderHotel input1) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Wrapper<FmbOrderHotel> orderHotelWrapper = new LambdaQueryWrapper<FmbOrderHotel>()
                .eq(FmbOrderHotel::getId, input1.getId());
        FmbOrderHotel orderHoteldb = orderHotelService.getOne(orderHotelWrapper);
        if (orderHoteldb == null) {
            throw new BadLogicException("订房单不存在");
        }
        ReqFmbOrderHotel re = new ReqFmbOrderHotel();
        re.setAdminUid(nowUserInfo.getUserId());
        re.setAdminName(nowUserInfo.getUsername());
        BeanUtils.copyProperties(orderHoteldb, re);
        re.setFormType(input1.getFormType());
        HashMap<String, Object> dataMap = orderService.getOrderHotelEmailTemp(re);
        ReqHotelMail mail = new ReqHotelMail();
        mail.setSetTo(((FmbxBpParterHotelInfo) dataMap.get("bpParterHotelInfo")).getHotelConfirmContractEmail());
        mail.setSetCc(((FmbxBpParterHotelInfo) dataMap.get("bpParterHotelInfo")).getHotelConfirmContractEmailCc());
        List<OrderDetailGoodsInfo> goodsInfos = (List<OrderDetailGoodsInfo>) dataMap.get("goodsInfos");
        LocalDateTime now = LocalDateTime.now();
        Integer num = getOrderHotelNumByAid(((FmbOrderInfo) dataMap.get("orderInfo")).getAid(),now.format(DateTimeFormatter.ofPattern("MMdd")));
        String username = ((FmbOrderInfo) dataMap.get("orderInfo")).getReceiver();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        Pinyin4jEngine engine = new Pinyin4jEngine(format);
        String usernamePy = engine.getPinyin(username, " ");
        String subject = ((FmbxBps) dataMap.get("hotelBasicInfo")).getName()
                + "(" + now.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "——第 " + num + " 单)" + username + "(" + usernamePy + ")"
                + orderHoteldb.getPlayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "入住" + ((SkuDetail) dataMap.get("skuDetail")).getSuite().getName()
                + "(" + ((SkuDetail) dataMap.get("skuDetail")).getRoomSku().getRoomName() + ")"
                + goodsInfos.get(0).getRoomNum() + "间" + goodsInfos.get(0).getTotalNight() + "晚——父母邦";
        mail.setSubject(subject);
        mail.setTextContent(orderHoteldb.getPostscript());
        mail.setHtmlContent((String) dataMap.get("mailText"));
        mail.setConfirmComment(now.format(DateTimeFormatter.ofPattern("M")) + "/" + now.format(DateTimeFormatter.ofPattern("d")) + "--邮件已发");
        mail.setConfirmStatusSelectedId(5);
        resultMap.put("hotelMail", mail);
    }


    private void saveOrderInfoAction(FmbOrderInfo orderInfo, SessionUser nowUserInfo, String note) {
        FmbOrderActions action = new FmbOrderActions();
        action.setOrderSn(orderInfo.getOrderSn());
        action.setAdminUid(nowUserInfo.getUserId());
        action.setOrderStatus(orderInfo.getOrderStatus());
        action.setShippingStatus(orderInfo.getShippingStatus());
        action.setPayStatus(orderInfo.getPayStatus());
        action.setActionType("");
        action.setActionNote(note);
        action.setLogTime(LocalDateTime.now());
        orderActionsService.save(action);
    }

    public void changeGoodsSettlePrice(HashMap<String, Object> resultMap, ReqFmbOrderHotel input) {
        SessionUser nowUserInfo = userUnionService.nowUserInfo();
        Set<Integer> details = new HashSet<>();
        for (FmbxHotelGoodsDetail detail : input.getHotelGoodsDetail()) {
            if (detail.getDetailId() == null || detail.getSettlePrice() == null){
                continue;
            }
            FmbxHotelGoodsDetail db = new FmbxHotelGoodsDetail();
            details.add(detail.getDetailId());
            db.setDetailId(detail.getDetailId());
            db.setSettlePrice(detail.getSettlePrice());
            hotelGoodsDetailService.updateById(db);
        }
        Wrapper<FmbxHotelGoodsDetail> detailWrapper = new LambdaQueryWrapper<FmbxHotelGoodsDetail>()
                .in(details.size()>0, FmbxHotelGoodsDetail::getDetailId, details);
        List<FmbxHotelGoodsDetail> detailList = hotelGoodsDetailService.list(detailWrapper);
        //Map<Integer,BigDecimal> map = detailList.stream()
        //        .collect(Collectors.groupingBy(FmbxHotelGoodsDetail::getRecId),Collectors.reducing(BigDecimal.ZERO,FmbxHotelGoodsDetail::getSettlePrice,BigDecimal::add));
        Map<Integer,BigDecimal> map = detailList.stream()
                .collect(Collectors.groupingBy(FmbxHotelGoodsDetail::getRecId)).entrySet()
                .stream().collect(Collectors.toMap(Map.Entry::getKey,m->m.getValue().stream().filter(d->Objects.nonNull(d.getSettlePrice()))
                .map(FmbxHotelGoodsDetail::getSettlePrice).reduce(BigDecimal.ZERO,BigDecimal::add)));
        for (Map.Entry<Integer,BigDecimal> item : map.entrySet()){
            FmbOrderGoods goods  = new FmbOrderGoods();
            goods.setRecId(item.getKey());
            goods.setSettlePrice(item.getValue());
            orderGoodsService.updateById(goods);
        }
        //权限和日志
        resultMap.put("result", 1);
    }

    public Integer getOrderHotelNumByAid(Integer aid,String date){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "hotel_info_"+aid+"_"+date;
            String num = jedis.get(key);
            if (StringUtils.isNotBlank(num) && StringUtils.isNumeric(num)){
                return Integer.valueOf(num);
            }else{
                return 1;
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return null ;
    }

    public void setOrderHotelNumByAid(Integer aid){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String key = "hotel_info_"+aid+"_"+LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd"));
            String num = jedis.get(key);
            Integer value = 1;
            if (StringUtils.isNotBlank(num) && StringUtils.isNumeric(num)){
                value = Integer.valueOf(num);
            }
            value++ ;
            jedis.setex(key,86400,value.toString());
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

}
