package com.fmb.server2022.service.order;

import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2023/2/9 4:58 下午
 */
@Data
public class FmbOrderInfoOfAdmin extends FmbOrderInfo {




    /**
     * 订单所属城市
     */
    String orderCity ;

    /**
    订单API 渠道
     */
    String apiType ;

    public String getOrderReferStr() {

//        System.out.println("getOrderReferer()="+getOrderReferer());
        if (getOrderReferer() != null) {
            switch (getOrderReferer()){
                case 1 :
                    return "客户下单" ;
                case 2 :
                    return "客服代下单" ;
                case 3 :
                    return "假占房" ;

            }
        }

        return "";
    }

    String orderReferStr ;

    public String getPaySourceStr() {
//        System.out.println("pay--->"+getPaySource());
        if(FmbConstants.FMB_PAY_SOURCE.containsKey(getPaySource())){
            return FmbConstants.FMB_PAY_SOURCE.get(getPaySource());
        }
        return getPaySource();
    }

    String paySourceStr ;


    public String getOrderCity() {
        switch (getOrderCityId()){

            case 1 :
                return "北京" ;
            case 2 :
                return "上海" ;

        }

        return "";
    }

}
