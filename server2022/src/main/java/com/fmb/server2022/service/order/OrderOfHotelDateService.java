package com.fmb.server2022.service.order;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.OrderDetailGoodsInfo;
import com.fmb.server2022.controller.front.reqdomain.OrderSysInfo;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.domain.ConfirmOrderMoneyGroup;
import com.fmb.server2022.domain.ConfirmOrderMoneyInfo;
import com.fmb.server2022.domain.ConfirmOrderMoneyItem;
import com.fmb.server2022.domain.HotelSkuDatePriceDomain;
import com.fmb.server2022.domain.stock.SkuSellNumUpdate;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.entity.FmbxSuiteStock;
import com.fmb.server2022.fmbx.service.IFmbCouponTypeService;
import com.fmb.server2022.fmbx.service.IFmbOrderGoodsService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbPayOrdersService;
import com.fmb.server2022.fmbx.service.IFmbShieldUsersService;
import com.fmb.server2022.fmbx.service.IFmbUserCashLogService;
import com.fmb.server2022.fmbx.service.IFmbUserCouponService;
import com.fmb.server2022.fmbx.service.IFmbUserExtinfoService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomdatePriceService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteStockService;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.reqdomain.ReqHotelDate;
import com.fmb.server2022.reqdomain.RoomNumStatus;
import com.fmb.server2022.service.FrontSkuService;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.util.FmbDateUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.*;
import static com.fmb.util.FmbNumberUtil.double2String;
import static java.time.temporal.ChronoUnit.DAYS;

@Service
public class OrderOfHotelDateService implements AbstractOrderservice {
    private static Logger logger = LoggerFactory.getLogger(OrderOfHotelDateService.class);


    @Autowired
    OrderOfHotelDateService self ;

    @Autowired
    IFmbShieldUsersService shieldUsersService;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    IFmbxSkuService skuService ;

    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;

    @Autowired
    IFmbxSuiteRoomSkuService roomSkuService ;

    @Autowired
    IFmbxSuiteService suiteService ;

    @Autowired
    FrontSkuService frontSkuService ;

    @Autowired
    IFmbPayOrdersService payOrdersService ;


    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    IFmbOrderGoodsService orderGoodsService ;

    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    IFmbUserCouponService couponService ;

    @Autowired
    IFmbCouponTypeService couponTypeService ;

    @Autowired
    IFmbUserExtinfoService userExtinfoService ;
    @Autowired
    IFmbUserCashLogService userCashLogService ;


    @Autowired
    IFmbxSuiteStockService suiteStockService ;

    @Autowired
    IFmbxSuiteRoomdatePriceService roomdatePriceService ;


    @Autowired
    SuiteService mySuiteService ;


    @Autowired
    JedisPool jedisPool ;


    @Override
    public int getSkuType() {
        return FmbConstants.SKU_HOTEL_DATE;
    }

    @Override
    public int skuType2OldGoodsType() {
        return  SKU_HOTEL_DATE_GOODS_TYPE;
    }

    @Override
    public void doFinalJobWhenCreateOrderOver(ReqGenOrder input, SkuDetail skuDetail) {

        List<HotelSkuDatePriceDomain> priceList = skuDetail.getPriceList();
        for (HotelSkuDatePriceDomain priceDomain : priceList) {
            //更新成功记录一下 设置的目标库存 可后面发通知
            // 这里正好是没有库存了
            if (priceDomain.getAimStock()==0) {
                mySuiteService.sendHotelStockChangeNotify(priceDomain.getRoomId().intValue());
            }
        }

    }

    @Override
    public void subStock(ReqGenOrder input, NowUser nowUser, SkuDetail skuDetail) {
        List<HotelSkuDatePriceDomain> priceList = skuDetail.getPriceList();


        RedissonLockService.doLockJob(HOTEL_ROOM_SKU_NUMBER,skuDetail.getRoomSku().getRoomId().toString(),2000,1500,
                ()->self.subHotelSkuStockNumberUnderLock(skuDetail, priceList),"库存扣减失败");




    }

    void subHotelSkuStockNumberUnderLock(SkuDetail skuDetail, List<HotelSkuDatePriceDomain> priceList) {
        for (HotelSkuDatePriceDomain priceDomain : priceList) {

            boolean update = false ;

            long aimStock = priceDomain.getStockNum() - skuDetail.getSkuNumber();
            if (aimStock>=0) {
                update = self.updateHotelStockNumber( priceDomain.getStockId(),priceDomain.getVersionNum() ,aimStock);
            }


            // 日历房 库存扣减重试 自旋 最多3次
            if (!update) {
                //
                for (int i = 0; i < 3; i++) {
                    Wrapper<FmbxSuiteStock> wrStock = new LambdaQueryWrapper<FmbxSuiteStock>()
                            .eq(FmbxSuiteStock::getStockId,priceDomain.getStockId())
                            .eq(FmbxSuiteStock::getStatus,1)
                            .select(FmbxSuiteStock::getStockNum,FmbxSuiteStock::getVersionNum,FmbxSuiteStock::getStockId)
                            .last(" limit 1 ")
                            ;
                    //重新查询库存
                    final FmbxSuiteStock retryDB = suiteStockService.getOne(wrStock);
                    if (retryDB == null) {
                        throw new BadLogicException("库存扣减异常[1568]");
                    }

                    aimStock = retryDB.getStockNum() - skuDetail.getSkuNumber();
                    if(aimStock<0){
                        throw new BadLogicException("库存不足[1570]");
                    }

                    logger.info("retryDB=null {} {} {}",retryDB==null ,retryDB.getStockId(),retryDB.getVersionNum());
                    update = self.updateHotelStockNumber( retryDB.getStockId(),retryDB.getVersionNum() ,aimStock);
                    if (update) {
                        //扣减成功就跳出
                        break ;
                    }
                }
            }
            if (update) {
                //更新成功记录一下 设置的目标库存 可后面发通知
                priceDomain.setAimStock((int) aimStock);
            }else {
                throw new  BadLogicException("库存不足[1572]");
            }

        }
    }

    @Override
    public void saveOrderGoods(NowUser nowUser, FmbOrderInfo orderInfo, SkuDetail skuDetail, ReqGenOrder input) {
        FmbOrderGoods goods = new FmbOrderGoods();
        goods.setOrderSn(orderInfo.getOrderSn());
        goods.setUid(nowUser.getUserId());
        goods.setAid(input.getXaid());

        self.buildHotelDateGoods(nowUser, orderInfo, goods,skuDetail,input);

        orderGoodsService.save(goods);
        skuDetail.setRecId(goods.getRecId());
    }

    @Override
    public void buildSkuDetail(ReqGenOrder input, Map<String, List<ReqGenSku>> reqSkuMaps, Map<String, List<FmbxSku>> skuMainOfDBMap, FmbxSku skuMain) {
        FmbxSuiteRoomSku roomSku = self.querySkuHotelDate(skuMain.getSkuId());
        if (roomSku == null) {
            throw new BadLogicException("sku数据异常[150]");
        }

        FmbxSuite suite = self.queryFmbxSuite(roomSku.getSuiteId());
        if (suite == null) {
            throw new BadLogicException("套餐数据异常[152]");
        }

//        input.getOrderSysInfo().addSkuDetail(SKU_HOTEL_DATE,roomSku, reqSkuMaps.get(skuMain.getSkuId()+"").get(0),
//                skuMainOfDBMap.get(skuMain.getSkuId()+"").get(0),suite);

        SkuDetail e = new SkuDetail();

        e.setReqGenSku(reqSkuMaps.get(skuMain.getSkuId()+"").get(0));
        e.setFmbxSku(skuMain);
        e.setSuite(suite);
            e.setRoomSku((FmbxSuiteRoomSku) roomSku);
            e.setSkuType(SKU_HOTEL_DATE);
        input.getOrderSysInfo().getSkuDetails().add(e) ;
    }



    /**
     * 查询套餐信息
     * @param suiteId 套餐id
     * @return
     */
    public FmbxSuite queryFmbxSuite(Integer suiteId) {
        Wrapper<FmbxSuite> wrSuite = new LambdaQueryWrapper<FmbxSuite>()
                .eq(FmbxSuite::getSuiteId, suiteId)
                .select(FmbxSuite::getSuiteId,FmbxSuite::getXaid,FmbxSuite::getSuiteContent,FmbxSuite::getTotalNeight
                        ,FmbxSuite::getName
                        ,FmbxSuite::getNightMin,FmbxSuite::getFlagStandardHotel,FmbxSuite::getFlagHotelReserve
                        ,FmbxSuite::getBuyLimit,FmbxSuite::getAutoCloseOrderSecond,FmbxSuite::getCertFlag,FmbxSuite::getCertTip
                        ,FmbxSuite::getFlagRiskWarning,FmbxSuite::getRiskWarningTip,FmbxSuite::getIsReturn,FmbxSuite::getReturnPolicy
                        ,FmbxSuite::getPreReserveDay,FmbxSuite::getPreReserveTime
                        ,FmbxSuite::getReturnPolicy,FmbxSuite::getReturnValidDay,FmbxSuite::getReturnValidSecond,FmbxSuite::getReturnValidTime
                        ,FmbxSuite::getIsInvoice,FmbxSuite::getVersionNum
                )
                ;
        final FmbxSuite suite = suiteService.getOne(wrSuite);
        return suite;
    }

    @DS(FmbConstants.DB_master)
    public FmbxSuiteRoomSku querySkuHotelDate(Integer skuid) {
        Wrapper<FmbxSuiteRoomSku> wrDate = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                .eq(FmbxSuiteRoomSku::getSkuId, skuid)
                .select(FmbxSuiteRoomSku::getSkuId, FmbxSuiteRoomSku::getSkuName, FmbxSuiteRoomSku::getRoomName,
                        FmbxSuiteRoomSku::getSuiteId,FmbxSuiteRoomSku::getSellNum
                        , FmbxSuiteRoomSku::getRoomId, FmbxSuiteRoomSku::getFlagSell, FmbxSuiteRoomSku::getStatus, FmbxSuiteRoomSku::getVersionNum
                )
                ;
        FmbxSuiteRoomSku roomSku = roomSkuService.getOne(wrDate);
        return roomSku;
    }

    void buildHotelDateGoods(NowUser nowUser, FmbOrderInfo orderInfo, FmbOrderGoods goods, SkuDetail skuDetail, ReqGenOrder input) {

        final FmbxSuiteRoomSku roomSku = skuDetail.getRoomSku();
        goods.setThirdSn("");

        goods.setPackProductId(0);
        goods.setProductId(0);
        goods.setSupplierId(0);
        goods.setSellId(0);
        goods.setTypeName("");
        goods.setPlaneTripStatus(0);

        goods.setActivityType(0);
        goods.setShopUserId(skuDetail.getFmbxSku().getBpId());
        goods.setShopWarehouseId(0);
        goods.setGoodsGroupId(0);
        goods.setGoodsType(skuType2OldGoodsType());
        goods.setGoodsId(roomSku.getSkuId());
        goods.setGoodsNumber(skuDetail.getSkuNumber());
        goods.setMarketPrice(skuDetail.getMarketPrice());
        goods.setGoodsPrice(skuDetail.getGoodsPrice());
        goods.setMycpsPrice(BigDecimal.ZERO);
        goods.setSettlePrice(skuDetail.getSettlePrice());
        goods.setPlayTime( FmbDateUtil.dateToLocalDate( skuDetail.getReqGenSku().getCheckInDate()).atTime(0,0,0));
        goods.setLeaveTime(FmbDateUtil.dateToLocalDate( skuDetail.getReqGenSku().getCheckOutDate()).atTime(0,0,0));
        goods.setCtime(LocalDateTime.now());
        goods.setBedRequire("");
        goods.setExtInfo("");
        goods.setRowIndex(0);
        goods.setTaxMoney(BigDecimal.ZERO);
        goods.setSettleTaxMoney(BigDecimal.ZERO);
        goods.setTuanStatus(0);
        goods.setExtendInfo("");


        HashMap<String, Object> extInfo = new HashMap<>();
//        extInfo.put("suite", skuDetail.getSuite());
//        extInfo.put("skuInfo", skuDetail.getRoomSku());
//        extInfo.put("priceList", skuDetail.getPriceList());
        extInfo.put("skuDetail", skuDetail);
        extInfo.put("activity", input.getOrderSysInfo().getActivity());

        goods.setGoodExt(JSONUtil.toJsonStr(extInfo));
    }

    /**
     *
     * @param goods
     * @param confirmOrderMoneyInfo  订单详情页里需要显示费用明细  要构造类似确认订单页的 费用明细数据
     * @param orderInfo
     * @return
     */
    @Override
    public OrderDetailGoodsInfo readGoodsInfoFromGoodsExt(FmbOrderGoods goods, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, FmbOrderInfo orderInfo){

//        logger.info("goods={}",goods);

        OrderDetailGoodsInfo orderDetailGoodsInfo = new OrderDetailGoodsInfo();
        orderDetailGoodsInfo.setGoodsType(goods.getGoodsType());

        final JSON json = JSONUtil.parse(goods.getGoodExt());

        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);
        final FmbxActivity activity = json.getByPath("activity", FmbxActivity.class);
        final FmbxSuite suite = json.getByPath("skuDetail.suite", FmbxSuite.class);
//        final List<HotelSkuDatePriceDomain> priceDomainList = json.getByPath("priceList", new ArrayList<HotelSkuDatePriceDomain>().getClass() );
        JSONArray array = (JSONArray) json.getByPath("skuDetail.priceList");
        final List<HotelSkuDatePriceDomain> priceDomainList = array.toList(HotelSkuDatePriceDomain.class);



        logger.info("活动标题={}, 套餐名称={} ,房间数量={}  ",activity.getTitle(),suite.getName(),goods.getGoodsNumber());
        logger.info("入住日期={} ,离店日期={} ,总晚数= {} ", FmbDateUtil.FORMATTER10.format( goods.getPlayTime()),
                FmbDateUtil.FORMATTER10.format( goods.getLeaveTime()),priceDomainList.size());


        orderDetailGoodsInfo.setTitle(activity.getTitle());
        orderDetailGoodsInfo.setSuiteName(suite.getName());
        orderDetailGoodsInfo.setRoomNum(goods.getGoodsNumber());
        orderDetailGoodsInfo.setCheckInOutDateStr(FmbDateUtil.FORMATTER10.format( goods.getPlayTime())+"至"+FmbDateUtil.FORMATTER10.format( goods.getLeaveTime()));
        orderDetailGoodsInfo.setTotalNight(priceDomainList.size());

        if (StringUtils.isBlank(confirmOrderMoneyInfo.getSubTitle())) {
            confirmOrderMoneyInfo.setSubTitle(goods.getGoodsNumber()+"间"+priceDomainList.size()+"晚");
        }

        ConfirmOrderMoneyGroup group = new ConfirmOrderMoneyGroup();
        group.setTitle("套餐房费");
        group.setGroupType(ConfirmOrderMoneyGroup.GROUPTYPE_1);

        for (HotelSkuDatePriceDomain priceDomain : priceDomainList) {
            ConfirmOrderMoneyItem item = new ConfirmOrderMoneyItem();
            item.setLeftTitle( FmbDateUtil.toDate10Str( priceDomain.getDatecol()));
            item.setSkuNumber(goods.getGoodsNumber()+ MUTI_CHAR);
            item.setSkuNumberN(goods.getGoodsNumber());
            item.setPrice(RMB_CHAR+ double2String(priceDomain.getGoodsPrice()));
            item.setPriceBig( BigDecimal.valueOf(priceDomain.getGoodsPrice()));
//            item.setPriceBigAll(new BigDecimal(priceDomain.getGoodsPrice()).multiply(new BigDecimal(goods.getGoodsNumber())));

            group.getItemList().add(item) ;
        }


        confirmOrderMoneyInfo.getGroups().add( group) ;

        logger.info("skuDetail.getTotalHotelCodeSubMoney()={}",skuDetail.getTotalHotelCodeSubMoney());
        if (skuDetail.getTotalHotelCodeSubMoney().compareTo(BigDecimal.ZERO)==1) {
            confirmOrderMoneyInfo.addNewGroupOfDiscount("优惠","房券抵扣",skuDetail.getTotalHotelCodeSubMoney());
        }


        return orderDetailGoodsInfo;
    }

    @Override
    public void updateSkuSellNum(SkuSellNumUpdate s) {

        RedissonLockService.doLockJobNoWait("updateSkuSellNum"+s.getGoodSType(),s.getSkuId().toString(),200,()->{
            final FmbxSuiteRoomSku roomSku = querySkuHotelDate(s.getSkuId());
            Wrapper<FmbxSuiteRoomSku> upw = new LambdaUpdateWrapper<FmbxSuiteRoomSku>()
                    .eq(FmbxSuiteRoomSku::getSkuId,s.getSkuId())
                    .eq(FmbxSuiteRoomSku::getVersionNum,roomSku.getVersionNum())
                    .set(FmbxSuiteRoomSku::getSellNum,  roomSku.getSellNum()+s.getSellNum())
                    .set(FmbxSuiteRoomSku::getVersionNum,roomSku.getVersionNum()+1)
                    ;
            roomSkuService.update(upw);
        });

    }


    /**
     *
     * @param stockId
     * @param versionNum
     * @param aimStock
     * @return
     */
    public boolean updateHotelStockNumber( int stockId , int versionNum, long aimStock) {
        boolean update;
        Wrapper<FmbxSuiteStock> wr = new LambdaUpdateWrapper<FmbxSuiteStock>()
                .eq(FmbxSuiteStock::getStockId,stockId)
                .eq(FmbxSuiteStock::getStatus,1)
                .eq(FmbxSuiteStock::getVersionNum,versionNum)
                .set(FmbxSuiteStock::getStockNum, aimStock)
                .set(FmbxSuiteStock::getVersionNum,versionNum + 1)
                ;
        //第一次扣减
        update = suiteStockService.update(wr);
        return update;
    }


    /**
     * 日历房规则校验
     * @param skuDetail
     * @param nowUser
     * @param input
     */
    @Override
    @DS(FmbConstants.DB_master)
    public void baseCheck(SkuDetail skuDetail, NowUser nowUser, ReqGenOrder input) {


        final OrderSysInfo orderSysInfo = input.getOrderSysInfo();

        final FmbxSuiteRoomSku roomSku = skuDetail.getRoomSku();
//  `flag_sell` smallint(2) unsigned NOT NULL DEFAULT '0' COMMENT '售买状态:0-无效（无可售日期）,1-有效',
        if (0 == roomSku.getFlagSell().intValue()) {
            throw new BadLogicException("skuid=" + roomSku.getSkuId() + "无有效库存[1001]");
        }
//          `status` smallint(2) unsigned NOT NULL DEFAULT '1' COMMENT '上下架状态:0-已删除,1-已上架,2-已下架',
        if (1 != roomSku.getStatus().intValue()) {
            throw new BadLogicException("skuid=" + roomSku.getSkuId() + "上架状态异常[1004]");
        }

        orderSysInfo.setHaveHotelDateSku(true);

        //输入
        if(StringUtils.isBlank( skuDetail.getReqGenSku().getCheckInDate())   ||  StringUtils.isBlank( skuDetail.getReqGenSku().getCheckOutDate()) ){
            throw new BadLogicException("skuid=" + roomSku.getSkuId() + "入住日期信息异常[1006]");
        }

//  `flag_standard_hotel` smallint(2) unsigned NOT NULL DEFAULT '0' COMMENT '日历房:0-否,1-是',

        //套餐信息校验
        if (1 != skuDetail.getSuite().getFlagStandardHotel().intValue()) {
            throw new BadLogicException("skuid=" + roomSku.getSkuId() + "套餐信息异常,suiteid=" + skuDetail.getSuite().getSuiteId() + " [1007]");
        }

        final List<String> hotelUserName = skuDetail.getReqGenSku().getHotelUserName();
        logger.info("hotelUserName={}",hotelUserName);
        logger.info("skuDetail.getReqGenSku().getSkuNumber()={}",skuDetail.getReqGenSku().getSkuNumber());
        if (hotelUserName == null || hotelUserName.isEmpty() || hotelUserName.stream().collect(Collectors.toSet()).size() !=skuDetail.getReqGenSku().getSkuNumber()) {
            throw new BadLogicException("入住人信息异常[1098]");
        }

        //  假设今天为6月1日，设置提前1天，17:00前可订，则表示用户在今天17:00之前可以预订到6月2号入住的产品，在今天17:00之后能预订到6月3号入住的产品
        self.isValidCheckinDate(skuDetail.getReqGenSku().getCheckInDate(), skuDetail.getSuite().getPreReserveDay(),
                skuDetail.getSuite().getPreReserveTime(), skuDetail.getSuite());

        self.certCheck(skuDetail.getSuite(),input) ;



    }

    /**
     * 查询日历房 入离 区间内的 信息
     * @param skuDetail
     * @param input
     */
    @Override
    public void buildPrice(SkuDetail skuDetail, ReqGenOrder input) {


        final String checkInDate = skuDetail.getReqGenSku().getCheckInDate();
        final String checkOutDate = skuDetail.getReqGenSku().getCheckOutDate();

        final int dayDiff =FmbDateUtil.dayDiff(checkOutDate,checkInDate) ;

        final FmbxSuite suite = skuDetail.getSuite();
        //连住数
        final Integer nightMin = suite.getNightMin();
        //总间夜
        final Integer totalNight = suite.getTotalNeight();
        //sku 购买的房间数量
        final Integer skuNumber = skuDetail.getReqGenSku().getSkuNumber();
        final Integer skuId = skuDetail.getRoomSku().getSkuId();
        logger.info(" skuid={}, checkInDate={} ,checkOutDate ={} ,dayDiff={} ,totalNight={},skuNumber={},suiteid={}" ,skuId ,checkInDate,
                checkOutDate,
                dayDiff,
                totalNight,skuNumber,suite.getSuiteId());

        if (dayDiff<1){
            throw new BadLogicException( "skuid="+ skuId + ",入住时间不合法[1030]");
        }

        if (dayDiff%nightMin!=0){
            throw new BadLogicException( "skuid="+ skuId + ",入住晚数不合法[1032]");
        }

        // 晚数和间数 相乘得到总间夜数
//        if( (dayDiff *skuNumber) %totalNight!=0){
//            throw new BadLogicException("skuid="+ skuId + ",间夜数不合法[1033]");
//        }

        //计算日历房 的价格信息
        self.buildHotelPrice(skuDetail, checkInDate, checkOutDate, skuNumber, skuId,input);



    }

    @Override
    public CreateUserAssetResult createUserAsset(FmbOrderGoods orderGood, FmbOrderInfo fmbOrderInfo) {

        logger.info("createUserAsset[日历房]");
        final CreateUserAssetResult createUserAssetResult = new CreateUserAssetResult();
        createUserAssetResult.setHotelDate(true);
        return createUserAssetResult;
    }

    /**
     * 构造酒店每天的价格信息
     * 用房券去生成酒店订单时 要在这里减掉差价
     * @param skuDetailInput
     * @param checkInDate
     * @param checkOutDate
     * @param skuNumber
     * @param skuId
     * @param input
     */
    public void buildHotelPrice(SkuDetail skuDetailInput, String checkInDate, String checkOutDate, Integer skuNumber, Integer skuId, ReqGenOrder input) {

        logger.info("buildHotelPrice## {} {} {} {}",checkInDate,checkOutDate,skuNumber,skuId);
        logger.info("input## {} {} {} {}", com.alibaba.fastjson.JSON.toJSONString(input));
        List<HotelSkuDatePriceDomain> priceList = buildPriceList(checkInDate, checkOutDate, skuNumber, skuId);
        final int nightNum = priceList.size();

        final FmbOrderGoods sourceGoods = input.getOrderSysInfo().getReserveCodeFromOrderGoods();
        final FmbxSuite reserveCodeSourceSuite = input.getOrderSysInfo().getReserveCodeSourceSuite();

        // 起订晚数 不能被 房券套餐要求最低晚数整除
        final boolean useHotelReserveCode = input.useHotelReserveCode();
        if(useHotelReserveCode){

            if (nightNum% reserveCodeSourceSuite.getNightMin()!=0) {
                throw new BadLogicException("房券起订晚数错误[1690]");
            }
            if (nightNum*skuNumber> input.getOrderSysInfo().getHotelCode().getNumber()) {
                throw new BadLogicException("房券剩余次数不足[1693]");
            }

            if (skuDetailInput.getSuite().getSuiteId().intValue()!=reserveCodeSourceSuite.getSuiteId().intValue()) {
                throw new BadLogicException("房券套餐异常[1693]");
            }

            final int totalWantNightOfHotel = nightNum * skuDetailInput.getSkuNumber();
            if (totalWantNightOfHotel>input.getOrderSysInfo().getHotelCode().getNumber()) {
                throw new BadLogicException("入住间夜数大于房券间夜数[5639]");
            }

        }

        BigDecimal totalHotelCodeSubMoney = new BigDecimal("0");
        logger.info("priceList={}",com.alibaba.fastjson.JSON.toJSONString(priceList));
        for (HotelSkuDatePriceDomain price : priceList) {
            BigDecimal goodsPrice =  BigDecimal.valueOf(price.getGoodsPrice());


            //增加房券价格计算逻辑
            if (useHotelReserveCode) {

                final Integer totalNight = reserveCodeSourceSuite.getTotalNeight();
                //房券已经支付的单间夜价格
                final BigDecimal hotelCodeEachNightPay = sourceGoods.getGoodsPrice().divide(new BigDecimal(totalNight));

                logger.info("priceInfo ={}", com.alibaba.fastjson.JSON.toJSONString(price));
                logger.info("goodsPrice ={} hotelCodeEachNightPay={}", goodsPrice, hotelCodeEachNightPay);
                //需要减去房券 的单间夜价格
                if (goodsPrice.compareTo(hotelCodeEachNightPay)!=-1) {
                    totalHotelCodeSubMoney = totalHotelCodeSubMoney.add(hotelCodeEachNightPay.multiply(new BigDecimal(skuNumber))) ;
                    goodsPrice = goodsPrice.subtract(hotelCodeEachNightPay);
                }else{
                    //如果 房券金额大于 日历房金额 减免数就是日历房的价格 .扣减完的价格用0 处理
                    totalHotelCodeSubMoney = totalHotelCodeSubMoney.add(goodsPrice.multiply(new BigDecimal(skuNumber))) ;
                    goodsPrice = BigDecimal.ZERO ;
                }
            }

            skuDetailInput.setGoodsPrice(   skuDetailInput.getGoodsPrice().add(goodsPrice)) ;
            skuDetailInput.setSettlePrice(   skuDetailInput.getSettlePrice().add(new BigDecimal(price.getSettlePrice().toString())));
            skuDetailInput.setMarketPrice(   skuDetailInput.getMarketPrice().add(new BigDecimal(price.getMarketPrice().toString())));
        }

        skuDetailInput.setTotalHotelCodeSubMoney(totalHotelCodeSubMoney);

//        logger.info("123123\n"+JSON.toJSONString(skuDetail,true));
        skuDetailInput.setGoodsPriceAll(skuDetailInput.getGoodsPrice().multiply( new BigDecimal(skuNumber)));
        skuDetailInput.setSettlePriceAll(skuDetailInput.getSettlePrice().multiply( new BigDecimal(skuNumber)));
        skuDetailInput.setMarketPriceAll(skuDetailInput.getMarketPrice().multiply( new BigDecimal(skuNumber)));
        //把每天价格信息存储 下单ok后 扣减库存
        skuDetailInput.setPriceList(priceList);

        logger.info("skuDetailInput={}",JSONUtil.toJsonStr(skuDetailInput));
//        logger.info(JSON.toJSONString(skuDetailInput,true));
    }

    private List<HotelSkuDatePriceDomain> buildPriceList(String checkInDate, String checkOutDate, Integer skuNumber, Integer skuId) {
        ReqHotelDate req = new ReqHotelDate();
        req.setDatecolBegin(checkInDate);
        req.setDatecolEnd(checkOutDate);
        req.setSku_id(skuId);

        //该 查询里 一定有日期信息 但如果没有对应的库存和价格  对应的 价格id 和 库存id 为 0
        //该查询包含包含 结束那天的日期 .但计算价格不能包含最后一天
        List<HotelSkuDatePriceDomain> priceList = suiteDao.selectHotelSkuDatePrice(req);

        if (priceList.size()<2) {
            throw new BadLogicException("skuid="+ skuId + ",价格和库存信息异常[1046]");
        }

        //删除离店那天的数据
        priceList.remove(priceList.size()-1);

        for (HotelSkuDatePriceDomain price : priceList) {
            if (0==price.getPriceId().intValue() ) {
                throw new BadLogicException("skuid="+ skuId +","+ FmbDateUtil.toDate10Str(  price.getDatecol())+ ",价格信息异常[1034]");
            }
            if ( 0==price.getStockId().intValue()) {
                throw new BadLogicException("skuid="+ skuId +"," + FmbDateUtil.toDate10Str(  price.getDatecol())+ ",库存信息异常[1036]");
            }
            logger.info("price={}",JSONUtil.toJsonStr(price));
            if ( skuNumber >price.getStockNum()) {
                throw new BadLogicException("skuid="+ skuId +","+ FmbDateUtil.toDate10Str(  price.getDatecol())+ ",库存不满足[1038]");
            }
        }
        return priceList;
    }


    /**
     * todo 根据套餐信息 校验 预订证件资料
     * @param suite
     * @param input
     */
    public void certCheck(FmbxSuite suite, ReqGenOrder input) {

    }

    /**
     * 入住时间校验 因为有最少提前时间限制
     * @param checkInDate
     * @param preReserveDay
     * @param preReserveTime
     * @param suite
     * @return
     */
    public boolean isValidCheckinDate(String checkInDate, Integer preReserveDay, String preReserveTime, FmbxSuite suite) {

//        System.currentTimeMillis()
        logger.info("suiid={} , checkInDate={}, time={},date={}",suite.getSuiteId(),checkInDate,suite.getPreReserveTime(),suite.getPreReserveDay());
        //当前秒数 从凌晨0点算起的 秒数
        int seconds = DateTime.now().get(DateTimeFieldType.secondOfDay()) ;
        final String[] split = preReserveTime.split(":");

        if (split.length!=2) {
            logger.error("入住时间数据异常1:preReserveTime={} ,suitid={}",preReserveTime,suite.getSuiteId());
            throw new BadLogicException("入住时间数据异常[1012]");
        }

        final int hour = Integer.parseInt(split[0]);
        final int min = Integer.parseInt(split[1]);
        if (hour<0 || hour>23) {
            logger.error("入住时间数据异常2:preReserveTime={} ,suitid={}",preReserveTime,suite.getSuiteId());
            throw new BadLogicException("小时数异常[1014]");
        }
        if (min<0 || min>59) {
            logger.error("入住时间数据异常3:preReserveTime={} ,suitid={}",preReserveTime,suite.getSuiteId());
            throw new BadLogicException("分钟数异常[1016]");
        }

        int dayAdd =preReserveDay ;
        final int p = hour * 3600 + min * 60;
        logger.info("seconds={},{}",seconds,p);
        if(seconds > p){
            dayAdd ++ ;
        }

        final LocalDate localDateAim = FmbDateUtil.dateToLocalDate(checkInDate);
        if (localDateAim == null) {
            throw new BadLogicException("入住时间格式异常"+checkInDate+"[1018]");
        }
        //  假设今天为6月1日，设置提前1天，17:00前可订，则表示用户在今天17:00之前可以预订到6月2号入住的产品，在今天17:00之后能预订到6月3号入住的产品
        final LocalDate minDate = LocalDate.now().plus(dayAdd, DAYS);
        logger.info("localDateAim={},minDate={}",localDateAim,minDate);
        if (minDate.isAfter(localDateAim)) {
            throw new BadLogicException("入住日期不符合要求[1020]");
        }

        return true ;
    }


    @Override
    public int getCancelSecond(SkuDetail skuDetail) {
        return skuDetail.getSuite().getAutoCloseOrderSecond();
    }

    /**
     * todo 酒店日历房 取消操作
     * @param orderGood
     * @param fmbOrderInfo
     */
    @Override
    public void cancelUserGoods(FmbOrderGoods orderGood, FmbOrderInfo fmbOrderInfo) {

        //更新售卖数量
        SkuSellNumUpdate sell = new SkuSellNumUpdate();
        sell.setSkuId(orderGood.getGoodsId());
        sell.setSellNum(-1*orderGood.getGoodsNumber());
        sell.setSellNum(-1*orderGood.getGoodsNumber());
        sell.setGoodSType(orderGood.getGoodsType());

        updateSkuSellNum(sell);

        //回库存
        final JSON json = JSONUtil.parse(orderGood.getGoodExt());
        final SkuDetail skuDetail = json.getByPath("skuDetail", SkuDetail.class);

        RedissonLockService.doLockJob(HOTEL_ROOM_SKU_NUMBER,skuDetail.getRoomSku().getRoomId().toString(),2000,1500,
                ()->self.backStock(json,orderGood),"回库存失败");

    }

    /**
     * 返还库存
     * @param json
     * @param goods
     */
    public void backStock(JSON json, FmbOrderGoods goods) {

        JSONArray array = (JSONArray) json.getByPath("skuDetail.priceList");
        final List<HotelSkuDatePriceDomain> priceDomainList = array.toList(HotelSkuDatePriceDomain.class);
        for (HotelSkuDatePriceDomain hotelSkuDatePriceDomain : priceDomainList) {
            Wrapper<FmbxSuiteStock> wrStock = new LambdaQueryWrapper<FmbxSuiteStock>()
                    .eq(FmbxSuiteStock::getStockId,hotelSkuDatePriceDomain.getStockId())
                    .select(FmbxSuiteStock::getStockId,FmbxSuiteStock::getVersionNum,FmbxSuiteStock::getStockNum,FmbxSuiteStock::getStatus)
                    ;
            final FmbxSuiteStock stock = suiteStockService.getOne(wrStock);

            Wrapper<FmbxSuiteStock> wrBack = new LambdaUpdateWrapper<FmbxSuiteStock>()
                    .eq(FmbxSuiteStock::getStockId,stock.getStockId())
                    .eq(FmbxSuiteStock::getVersionNum,stock.getVersionNum())
                    .set(FmbxSuiteStock::getStockNum,stock.getStockNum()+goods.getGoodsNumber())
                    .set(FmbxSuiteStock::getVersionNum,stock.getVersionNum()+1)
                    ;
            suiteStockService.update(wrBack) ;


        }
    }

    @Override
    public ConfirmOrderMoneyGroup buildPriceWhenUseCoupon(FmbxSku fmbxSku, ReqGenSku genSkus, String confirmOrder, ConfirmOrderMoneyInfo confirmOrderMoneyInfo, String hotelReserveCode) {

        final ConfirmOrderMoneyGroup confirmOrderMoneyGroup = new ConfirmOrderMoneyGroup();
        //如果是确认订单过来 用户其实只有 入离日期
        //需要系统自动计算 间数 数据
        if("confirmOrder".equals(confirmOrder)){
            Wrapper<FmbxSuiteRoomSku> wr = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                    .eq(FmbxSuiteRoomSku::getSkuId,fmbxSku.getSkuId())
                    .select(FmbxSuiteRoomSku::getSuiteId)
                    ;
            final FmbxSuiteRoomSku one = roomSkuService.getOne(wr);
            final FmbxSuite suite = queryFmbxSuite(one.getSuiteId());

            final int night = FmbDateUtil.dayDiff(genSkus.getCheckOutDate(), genSkus.getCheckInDate());
//            logger.info("suite.getTotalNeight()={} neight={}",suite.getTotalNeight(),night);


            final ArrayList<RoomNumStatus> roomNumStatuses = roomNumGen(suite.getTotalNeight(), night);
//            logger.info("json={}", JSONUtil.toJsonStr (roomNumStatuses));

            //所有可选的
            final List<Integer> collect =
                    roomNumStatuses.stream().filter(s -> s.getStatus() == 1).map(RoomNumStatus::getRoomNum).collect(Collectors.toList());
            if (collect.isEmpty()) {
                throw new BadLogicException("房间数量异常[3423]");
            }

                //用户刚进入 确认订单不知道是几间 或者用户 选择本来就是错的 纠正为最小的间数
                if (!collect.contains(genSkus.getSkuNumber())) {
                    final RoomNumStatus roomNumStatus = roomNumStatuses.stream().filter(s -> s.getStatus() == 1).findFirst().get();
                    confirmOrderMoneyGroup.setRoomNumStatus(roomNumStatuses) ;
                    genSkus.setSkuRecommend(collect.get(0));
                    roomNumStatus.setSelected(1);
                }else if (collect.contains(genSkus.getSkuNumber())){
                    final RoomNumStatus roomNumStatus = roomNumStatuses.stream().filter(s -> s.getRoomNum() == genSkus.getSkuNumber()).findFirst().get();
                    confirmOrderMoneyGroup.setRoomNumStatus(roomNumStatuses) ;
                    roomNumStatus.setSelected(1);
                }

        }

        final List<HotelSkuDatePriceDomain> hotelSkuDatePriceDomains = buildPriceList(genSkus.getCheckInDate(), genSkus.getCheckOutDate(), genSkus.getSkuNumber(), genSkus.getSkuId());

        logger.info("fmbxSku type={}",fmbxSku.getSkuType());


        confirmOrderMoneyGroup.setTitle("套餐房费");
        confirmOrderMoneyGroup.setGroupType(ConfirmOrderMoneyGroup.GROUPTYPE_1);

        confirmOrderMoneyGroup.setSkuDesc(genSkus.getSkuNumber()+"间"+hotelSkuDatePriceDomains.size()+"晚");

        List<ConfirmOrderMoneyItem> list =  new ArrayList<>() ;

        for (HotelSkuDatePriceDomain hotelSkuDatePriceDomain : hotelSkuDatePriceDomains) {

            ConfirmOrderMoneyItem confirmOrderMoneyItem = new ConfirmOrderMoneyItem();
            confirmOrderMoneyItem.setLeftTitle(FmbDateUtil.toDate10Str( hotelSkuDatePriceDomain.getDatecol()));
            confirmOrderMoneyItem.setSkuNumber(genSkus.getSkuNumber()+ FmbConstants.MUTI_CHAR );
            confirmOrderMoneyItem.setPrice(RMB_CHAR+ double2String( hotelSkuDatePriceDomain.getGoodsPrice()));

            confirmOrderMoneyItem.setSkuNumberN(genSkus.getSkuNumber());
            confirmOrderMoneyItem.setPriceBig( BigDecimal.valueOf (hotelSkuDatePriceDomain.getGoodsPrice()));
            list.add(confirmOrderMoneyItem) ;
        }
        confirmOrderMoneyGroup.setItemList(list);


        return confirmOrderMoneyGroup ;


    }



    /**
     * 根据 总间夜和晚数 生成可候选间数 数据
     *  @param total 总间夜
     * @param night 晚数
     * @return
     */
    public static ArrayList<RoomNumStatus> roomNumGen(int total, int night) {

        final ArrayList<RoomNumStatus> roomNumStatuses = new ArrayList<>();
        final ArrayList<Integer> okRoom = new ArrayList<>();

        logger.info("total={},night={}",total,night);
        if (total<0 || night <0 ||  total >100 || night>100) {
            throw new BadLogicException("间数计算数据异常[3290]");
        }

        final Integer zxgbs = lowestCommonMultiple(total, night);

        if (zxgbs == null) {
            throw new BadLogicException("间数计算错误[3293]");
        }

        final int MAX_ROOM = 10;
        for (int i = 1; i <= MAX_ROOM; i++) {
            final int roomX = (zxgbs * i) / night;
            if (roomX<= MAX_ROOM) {
                okRoom.add(roomX);
            }else {
                break;
            }
        }

        if (!okRoom.isEmpty()) {
            for (int i = 1; i <= 10; i++) {
                final RoomNumStatus e = new RoomNumStatus();
                e.setRoomNum(i);
                e.setStatus(okRoom.contains(i)?1:0);
                roomNumStatuses.add(e) ;
            }
        }else {
            throw new BadLogicException("间数计算错误[3299]");
        }

        return roomNumStatuses ;

    }


    /**
     * 最小公倍数
     * @param num1
     * @param num2
     * @return
     */
    public static Integer lowestCommonMultiple(int num1 ,int num2){

        if(num1 < num2){
            int temp = num1;
            num1 = num2;
            num2 = temp;
        }
        for (int i = num1; i > 0; i++) {
            if(i % num1 == 0 && i %num2 == 0){
                return new Integer(i) ;
            }
        }
        return null ;

    }

}
