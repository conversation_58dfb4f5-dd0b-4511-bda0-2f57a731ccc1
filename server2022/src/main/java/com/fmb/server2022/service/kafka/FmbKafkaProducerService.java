package com.fmb.server2022.service.kafka;

import com.alibaba.fastjson.JSON;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static com.fmb.server2022.service.kafka.FmbKafkaTopic.PUBLIC_GROUP;

@Service
public class FmbKafkaProducerService {


    public static final String MESSAGEID = "messageid";
    public static final String BINFO = "binfo";
    private static Logger logger = LoggerFactory.getLogger(FmbKafkaProducerService.class);
    @Autowired
    private static KafkaTemplate<String, String> kafkaTemplate;


    @Autowired
    public FmbKafkaProducerService(KafkaTemplate<String, String> kafkaTemplate) {

        FmbKafkaProducerService.kafkaTemplate = kafkaTemplate ;
    }

    /**
     * 使用公用组 发消息
     * @param info
     * @param groupName
     * @param key
     */
    public static void sendUsePublicGroup(HashMap<String,Object> info,String groupName,String key) {
        info.put("INNERGROUP",groupName) ;
        send(PUBLIC_GROUP, key, info);
    }

    /**
     *  发送kafka 消息
     * @param topic 组名字
     * @param key  消息key
     * @param info 业务参数
     */
    public static void send(String topic,String key, HashMap<String,Object> info) {

        HashMap<String, Object> data = new HashMap<>();
        data.put(BINFO, info);
        String traceId = "" ;
        traceId =  MDC.get("traceId");
        if (StringUtils.isNoneBlank(traceId)) {
            data.put("traceId", traceId);
        }
        data.put(MESSAGEID, ""+SnowflakeIdWorker.getInstance().nextId());

        String dataSend = JSON.toJSONString(data);
        logger.info("dataSend {}",dataSend);

        kafkaTemplate.send(topic, key, dataSend) ;

    }
}
