package com.fmb.server2022.service;

import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.fmb_new.entity.FmbAdminLog;
import com.fmb.server2022.fmb_new.service.IFmbAdminLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class AdminLogService {


    private static final String PRE_IDENTIFER_FMBX = "fmbx_";
    private static IFmbAdminLogService logService ;
    private static UserUnionService unionService ;

    @Autowired
    public AdminLogService(IFmbAdminLogService logService,UserUnionService unionService ) {
        AdminLogService.logService = logService ;
        AdminLogService.unionService = unionService ;
    }


    public static void saveLog(String text,String identifier){

        SessionUser nowUserInfo = unionService.nowUserInfo();
        if (SessionUser.SESSIONUSER_FMB == nowUserInfo.getUserType().intValue()) {
            FmbAdminLog entity = new FmbAdminLog();

            entity.setAdminId(nowUserInfo.getUserId());
            entity.setAdminName(nowUserInfo.getUsername());
            entity.setLogTime(LocalDateTime.now());
            entity.setLogText(text);
            entity.setLogIdentifier(PRE_IDENTIFER_FMBX +identifier);

            logService.save(entity);
        }

    }

}
