package com.fmb.server2022.service.order;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/1/16 3:23 下午
 */
@Data
public class CreateUserAssetResult {

    boolean hotelReserve ;
    boolean hotelDate ;

    List<String> hotelReserveCode ;
    List<String> exchangeCode ;

    public CreateUserAssetResult() {
        this.hotelReserveCode = new ArrayList<>() ;
        this.exchangeCode = new ArrayList<>() ;
    }
}
