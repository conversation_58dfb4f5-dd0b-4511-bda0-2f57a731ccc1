package com.fmb.server2022.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.ui.FmbCollectionsUtils;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.domain.BpContractDomain;
import com.fmb.server2022.domain.BpsInfoDomain;
import com.fmb.server2022.domain.BpsMediaFilterInfo;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.BpsRoomInfoDomain;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmb_new.entity.FmbProductIndex;
import com.fmb.server2022.fmb_new.service.IFmbProductIndexService;
import com.fmb.server2022.fmbx.entity.FmbActivityContent;
import com.fmb.server2022.fmbx.entity.FmbGroupTickets;
import com.fmb.server2022.fmbx.entity.FmbHotelHouseTypes;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBpBpsBind;
import com.fmb.server2022.fmbx.entity.FmbxBpContract;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxBpsContent;
import com.fmb.server2022.fmbx.entity.FmbxBpsMedia;
import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.entity.FmbxMedia;
import com.fmb.server2022.fmbx.entity.FmbxOutfileInfo;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import com.fmb.server2022.fmbx.entity.FmbxThirdsysUser;
import com.fmb.server2022.fmbx.service.IFmbActivityContentService;
import com.fmb.server2022.fmbx.service.IFmbGroupTicketsService;
import com.fmb.server2022.fmbx.service.IFmbHotelHouseTypesService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpBpsBindService;
import com.fmb.server2022.fmbx.service.IFmbxBpContractService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpsContentService;
import com.fmb.server2022.fmbx.service.IFmbxBpsMediaService;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxMediaService;
import com.fmb.server2022.fmbx.service.IFmbxOutfileInfoService;
import com.fmb.server2022.fmbx.service.IFmbxSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteRoomSkuService;
import com.fmb.server2022.fmbx.service.IFmbxSuiteService;
import com.fmb.server2022.fmbx.service.IFmbxThirdsysUserService;
import com.fmb.server2022.mapper.BpsDao;
import com.fmb.server2022.reqdomain.ReqBpContract;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqBpsMedia;
import com.fmb.server2022.reqdomain.ReqBpsMediaInfo;
import com.fmb.server2022.reqdomain.ReqBpsMediaUpdate;
import com.fmb.server2022.reqdomain.ReqBpsRoomInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomSku;
import com.fmb.server2022.reqdomain.ReqFmbxBpsRoom;
import com.fmb.server2022.reqdomain.ReqQuickBpBindBps;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.util.MD5Util;
import com.fmb.util.RegStringUtil;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.VideoThumbTaker;
import com.fmb.util.upload.FmbImgUtil;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.JedisPool;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fmb.basic.FmbConstants.DB_slave;
import static com.fmb.basic.user.SessionUser.SESSIONUSER_BP;
import static com.fmb.basic.user.SessionUser.SESSIONUSER_FMB;
import static com.fmb.server2022.domain.BpContractDomain.bpCreateTypeMap;
import static com.fmb.server2022.domain.BpsMediaInfoDomain.bpsTypeMap;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.PUBLIC_GROUP_BPS_FLAG_UPDATE;

@Service
public class BpsService {


    private static Logger logger = LoggerFactory.getLogger(BpsService.class);

    private static final List<String> rolesCanReadAllContract = Arrays.asList("admin","tech","product");

    @Autowired
    IFmbAdminService adminService ;
    private static final  String BED_TYPE_JSON = "{\n" +
            "    \"value\": bedTypeNameINT ,\n" +
            "    \"db\": [\n" +
            "        {\n" +
            "            \"value\": 1,\n" +
            "            \"type\": \"大床\",\n" +
            "            \"list\": [\n" +
            "                {\n" +
            "                    \"name\": \"双人床\",\n" +
            "                    \"nameOtions\": [\n" +
            "                        \"双人床\",\n" +
            "                        \"圆床\",\n" +
            "                        \"水床\",\n" +
            "                        \"榻榻米\",\n" +
            "                        \"炕\",\n" +
            "                        \"沙发床\",\n" +
            "                        \"太空舱\",\n" +
            "                        \"小型双人床\",\n" +
            "                        \"大床\",\n" +
            "                        \"特大床\"\n" +
            "                    ],\n" +
            "                    \"num\": 1,\n" +
            "                    \"width\": \"2\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"value\": 2,\n" +
            "            \"type\": \"双床\",\n" +
            "            \"list\": [\n" +
            "                {\n" +
            "                    \"name\": \"\",\n" +
            "                    \"nameOtions\": [\n" +
            "                        \"双人床\",\n" +
            "                        \"单人床\",\n" +
            "                        \"小型双人床\",\n" +
            "                        \"大床\",\n" +
            "                        \"特大床\"\n" +
            "                    ],\n" +
            "                    \"num\": 2,\n" +
            "                    \"width\": \"\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"value\": 3,\n" +
            "            \"type\": \"单人床\",\n" +
            "            \"list\": [\n" +
            "                {\n" +
            "                    \"name\": \"\",\n" +
            "                    \"nameOtions\": [\n" +
            "                        \"单人床\",\n" +
            "                        \"胶囊床\",\n" +
            "                        \"太空舱\"\n" +
            "                    ],\n" +
            "                    \"num\": 1,\n" +
            "                    \"width\": \"\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"value\": 4,\n" +
            "            \"type\": \"多张床\",\n" +
            "            \"list\": [\n" +
            "                {\n" +
            "                    \"name\": \"\",\n" +
            "                    \"nameOtions\": [\n" +
            "                        \"双人床\",\n" +
            "                        \"圆床\",\n" +
            "                        \"水床\",\n" +
            "                        \"榻榻米\",\n" +
            "                        \"炕\",\n" +
            "                        \"单人床\",\n" +
            "                        \"上下铺\",\n" +
            "                        \"太空舱\",\n" +
            "                        \"沙发床\",\n" +
            "                        \"小型双人床\",\n" +
            "                        \"大床\",\n" +
            "                        \"特大床\"\n" +
            "                    ],\n" +
            "                    \"num\": \"\",\n" +
            "                    \"width\": \"\"\n" +
            "                }\n" +
            "            ]\n" +
            "        }\n" +
            "    ]\n" +
            "}" ;


    public static final Map<String, String> OLD_BED_TYPE_MAP;
    public static final Map<String, String> OLD_BED_TYPE_2_NEW_MAP;

    static {
        {
//            big 大床
//            double 双床
//            single 单床
//            three_persons_rooms  三床
//            other 其他


            {
                HashMap<String, String> map = new HashMap<>();
                map.put("big", "大床");
                map.put("double", "双床");
                map.put("single", "单床");
                map.put("three_persons_rooms", "三床");
                map.put("other", "其他");
                OLD_BED_TYPE_MAP = Collections.unmodifiableMap(map);
            }
            {
                HashMap<String, String> map = new HashMap<>();
                map.put("big", "1");
                map.put("double", "2");
                map.put("single", "3");
                map.put("three_persons_rooms", "4");
                map.put("other", "4");
                OLD_BED_TYPE_2_NEW_MAP = Collections.unmodifiableMap(map);
            }
        }


    }

    @Autowired
    IFmbActivityContentService activityContentService ;

    @Autowired
    IFmbProductIndexService productIndexService ;

    @Autowired
    IFmbxBpBpsBindService bindService;

    @Autowired
    IFmbxBpMainService mainService;

    @Autowired
    IFmbxBpsService bpsService;

    @Autowired
    IFmbxBpsContentService bpsContentService;

    @Autowired
    IFmbxBpsRoomService roomService;


    @Autowired
    IFmbxMediaService mediaService ;


    @Autowired
    IFmbxBpsMediaService bpsMediaService ;

    @Autowired
    private FmbAdminTokenService loginService;

    @Autowired
    IFmbxBpContractService contractService ;

    @Autowired
    IFmbxOutfileInfoService outfileInfoService ;

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxThirdsysUserService thirdsysUserService ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    BpsDao bpsDao;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    BpsService  self ;


    @Autowired
    IFmbxBpsRoomService fmbxBpsRoomService;

    @Autowired
    IFmbxBpsMediaService fmbxBpsMediaService;

    @Autowired
    IFmbxBpsService fmbxBpsService;

    @Autowired
    IFmbxSuiteRoomSkuService suiteRoomSkuService;

    @Autowired
    IFmbxSkuService skuService;

    @Autowired
    IFmbxSuiteService suiteService;

    @Autowired
    IFmbGroupTicketsService groupTicketsService ;

    @Autowired
    IFmbHotelHouseTypesService hotelHouseTypesService ;

    public static HashSet<String> videoExt  ;
    static {
        videoExt = new HashSet<>() ;
        videoExt.add("mp4");
        videoExt.add("mov");
    }

//    public static boolean ENV_PRO = "prod".equals( SpringUtils.getProperty("spring.profiles.active=prod")) ;

    @Value("${fmb.fmbImgDir}")
    private String fmbImgDir;
    @Value("${fmb.fmbImgUrl}")
    private String fmbImgUrl;

    /**
     * 通过商户ID 获取该商户关联的供应商信息
     * @param cpsId 商户ID
     * @param resultMap 结果
     */
    public void getBpListByCpsId(Integer cpsId,HashMap<String, Object> resultMap) {
        List<Map<String,Object>> list = bpsDao.selectBpListByCpsId(cpsId);
        resultMap.put("list",list);
    }


//    private static String fmbImgDir = SpringUtils.getProperty("fmb.fmbImgDir");
//    private static String fmbImgUrl = SpringUtils.getProperty("fmb.fmbImgUrl");

    /**
     * 增加 商户
     *
     * @param resultMap
     * @param input1
     */
    @DSTransactional
    public void addBps(HashMap<String, Object> resultMap, FmbxBps input1) {

        resultMap.put("result",0) ;
        SessionUser nowUserInfo = userUnionService.nowUserInfo();


//        FmbxBps input1 = new FmbxBps() ;

        LambdaQueryWrapper<FmbxBps> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBps::getName, input1.getName());
        FmbxBps one = bpsService.getOne(wr);
        if (one != null) {
            throw new BadLogicException("商户名称重复");
        }

        LocalDateTime now = LocalDateTime.now();
        input1.setBpsId(null);

        //现在就是酒店类型的
//        input1.setBpsType(1);
        input1.setStatus(1);
        input1.setCtime(now);
        input1.setUtime(now);
        input1.setFlagBasic(1);
        input1.setCreateUid(nowUserInfo.getUserId());
        input1.setAdminUid(nowUserInfo.getUserId());

        boolean save = bpsService.save(input1);
        if (save) {

            FmbxBpsContent content = new FmbxBpsContent();

            BeanUtils.copyProperties(input1, content);


            if(StringUtils.isBlank(content.getIntroduce())){
                content.setIntroduce("");
            }
            if(StringUtils.isBlank(content.getPolicy())){
                content.setPolicy("");
            }
            if(StringUtils.isBlank(content.getFoodInfo())){
                content.setFoodInfo("");
            }
            if(StringUtils.isBlank(content.getServices())){
                content.setServices("");
            }

            content.setBpsId(input1.getBpsId());
            content.setCtime(now);
            content.setUtime(now);

            bpsContentService.save(content);
            resultMap.put("content_id", content.getId());

        }

        resultMap.put("bps_id", input1.getBpsId());
        resultMap.put("result",1) ;

    }

    /**
     * 通过供应商ID判断 房间信息 图片信息 视频信息 是否都有数据 同时更新表中flg
     * @param bps_id
     * @return
     */
    public void updateBpsInfoStatus(int bps_id){

        logger.info("updateBpsInfoStatus bps_id={}",bps_id);

        int room_flg = 0, pic_flg = 0, video_flg = 0;
        //查询房间
        LambdaQueryWrapper<FmbxBpsRoom> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBpsRoom::getRoomStatus,1);
        wr.eq(FmbxBpsRoom::getBpsId,bps_id);
        long dbBpRoom = fmbxBpsRoomService.count(wr);
        if(dbBpRoom > 0){
            room_flg = 1;
        }

        List<Integer> ids = Arrays.asList(1,2);
        Wrapper<FmbxBpsMedia> wr5 = new QueryWrapper<FmbxBpsMedia>().select(" type, count(id) as num ").in("type", ids).eq("bps_id",bps_id).eq("status",1).groupBy("type");
        List<Map<String,Object>> countMap = bpsMediaService.listMaps(wr5);
        if(countMap != null){
            for(Map item : countMap ){
                if((int)item.get("type") == 1 ){//图片
                    pic_flg = 1;
                }else if((int)item.get("type") == 2 ){//视频
                    video_flg = 1;
                }
            }
        }

        //更新赋值数据
        FmbxBps fmbxBps = new FmbxBps();
        fmbxBps.setBpsId(bps_id);
        fmbxBps.setFlagRoom(room_flg);
        fmbxBps.setFlagPic(pic_flg);
        fmbxBps.setFlagVideo(video_flg);
        boolean update_flg = fmbxBpsService.updateById(fmbxBps);
    }




    /**
     * 修改商家信息
     * @param resultMap
     * @param input1
     */
    public void updateBps(HashMap<String, Object> resultMap, FmbxBps input1) {

        resultMap.put("result",0) ;
        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        //先查查是否存在
        FmbxBps dbAim = bpsService.getById(input1.getBpsId());
        if (dbAim == null) {
            throw new BadLogicException("商户不存在"+input1.getBpsId());
        }

        checkUserCanEditBps(nowUserInfo ,dbAim.getBpsId());

        LocalDateTime now = LocalDateTime.now();

        input1.setUtime(now);
        input1.setAdminUid(nowUserInfo.getUserId());

        if (input1.getDecorateTime() == null) {
            input1.setDecorateTime("");
        }

        boolean b = bpsService.updateById(input1);
        if (b) {
            resultMap.put("bps_update", 1);
        }

        FmbxBpsContent fmbxBpsContent = getFmbxBpsContent(input1.getBpsId());
        if (fmbxBpsContent != null) {
            fmbxBpsContent.setIntroduce(input1.getIntroduce());
            fmbxBpsContent.setPolicy(input1.getPolicy());
            fmbxBpsContent.setFoodInfo(input1.getFoodInfo());
            fmbxBpsContent.setServices(input1.getServices());
            fmbxBpsContent.setUtime(now);

            boolean b1 = bpsContentService.updateById(fmbxBpsContent);
            if (b1) {
                resultMap.put("bps_content_update", 1);
            }
        }else {

            FmbxBpsContent fmbxBpsContent1 = new FmbxBpsContent();


            BeanUtils.copyProperties(input1,fmbxBpsContent1);

            fmbxBpsContent1.setId(null);


            fmbxBpsContent1.setCtime(now);
            fmbxBpsContent1.setUtime(now);

            boolean b1 = bpsContentService.save(fmbxBpsContent1);
            if (b1) {
                resultMap.put("bps_content_id", fmbxBpsContent1.getId());
            }

        }

        updatBpsFlagAfterEdit(input1.getBpsId()) ;

        final List<FmbxActivity> fmbxActivities = listActivityByBps(input1.getBpsId());
        if (fmbxActivities != null) {
            for (FmbxActivity fmbxActivity : fmbxActivities) {
                SuiteService.sendActivityChangeNotify(fmbxActivity.getXaid());
            }
        }

        resultMap.put("result",1) ;

    }


    public List<FmbxActivity> listActivityByBps(Integer bpsId){

        DynamicDataSourceContextHolder.push(DB_slave);

        Wrapper<FmbxActivity> wrAct = new LambdaQueryWrapper<FmbxActivity>()
                .eq(FmbxActivity::getBpsId,bpsId)
                .select(FmbxActivity::getXaid)
                ;
        final List<FmbxActivity> list = activityService.list(wrAct);
        return list ;

    }


    /**
     * 发异步通知  检查更新商户 状态
     * @param bpsId
     */
    private void updatBpsFlagAfterEdit(Integer bpsId) {
        HashMap<String, Object> info = new HashMap<>();
        info.put("bpsid", bpsId);
        FmbKafkaProducerService.sendUsePublicGroup(info,PUBLIC_GROUP_BPS_FLAG_UPDATE,""+bpsId) ;
    }

    /**
     * 对比当前用户 是否可以编辑商户
     * @param nowUserInfo
     * @param bpsid
     */
    public void checkUserCanEditBps(SessionUser nowUserInfo,Integer bpsid) {
        if (nowUserInfo.getUserType()==SessionUser.SESSIONUSER_BP && bpsid!=null ) {

            FmbxBps byId = bpsService.getById(bpsid);

            if ( byId==null ||  nowUserInfo.getUserId().intValue()!=byId.getBpidOfEdit().intValue()) {
                throw new BadLogicException("您无权修改该商户");
            }
        }
    }


    public void bpsInfo(HashMap<String, Object> resultMap,  Integer bpsid,Integer simple) {


        resultMap.put("result",0) ;
        FmbxBps bps = bpsService.getById(bpsid);

        if(simple==1){

            resultMap.put("bpsname", bps.getName());
            resultMap.put("bpsTypeStr", bpsTypeMap.get(bps.getBpsType()));

            return ;
        }

        FmbxBpsContent contentServiceOne = getFmbxBpsContent(bpsid);

        resultMap.put("bps", bps);
        resultMap.put("bpscontent", contentServiceOne);

        resultMap.put("result",1) ;

    }

    private FmbxBpsContent getFmbxBpsContent(Integer bpsid) {
        LambdaQueryWrapper<FmbxBpsContent> wrap = new LambdaQueryWrapper<>();
        wrap.eq(FmbxBpsContent::getBpsId, bpsid);
        return bpsContentService.getOne(wrap);
    }

    /**
     * 增加房型
     * @param resultMap
     * @param input1
     */
    public void saveOrUpdateRoom(HashMap<String, Object> resultMap, ReqFmbxBpsRoom input1) {

        resultMap.put("result",0) ;
        FmbxBps one = bpsService.getById(input1.getBpsId());
        if (one == null) {
            throw new BadLogicException("商户不存在");
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,one.getBpsId()) ;

        FmbxBpsRoom dbRoom = null ;

        if (input1.getRoomId() != null) {
            dbRoom =  roomService.getById(input1.getRoomId());
        }

        LocalDateTime now = LocalDateTime.now();

        //修改房型
        if (dbRoom != null) {

            if (!dbRoom.getRoomName().equals(input1.getRoomName())){
                Wrapper<FmbxSuiteRoomSku> wrapSku = new LambdaQueryWrapper<FmbxSuiteRoomSku>()
                        .eq(FmbxSuiteRoomSku::getRoomId, input1.getRoomId())
                        .ne(FmbxSuiteRoomSku::getStatus, 0);
                List<FmbxSuiteRoomSku> roomSkuList = suiteRoomSkuService.list(wrapSku);
                for (FmbxSuiteRoomSku roomSku : roomSkuList){
                    FmbxSuite suitedb = suiteService.getById(roomSku.getSuiteId());
                    String skuName = suitedb.getName() +"("+ input1.getRoomName()+")";
                    roomSku.setSkuName(skuName);
                    roomSku.setRoomName(input1.getRoomName());
                    suiteRoomSkuService.updateById(roomSku);
                    FmbxSku sku = skuService.getById(roomSku.getSkuId());
                    sku.setSkuName(skuName);
                    skuService.updateById(sku);
                }
            }
            //更新房型文本信息
            BeanUtils.copyProperties(input1,dbRoom);

            dbRoom.setAdminUid(nowUserInfo.getUserId());
            dbRoom.setAdminType(nowUserInfo.getUserType());
            dbRoom.setUtime(now);
            roomService.updateById(dbRoom) ;


            //查询现有房型图片
            ReqBpsMediaInfo req = new ReqBpsMediaInfo();

            req.setType(1);
            req.setBps_id(dbRoom.getBpsId());
            req.setRoomId(input1.getRoomId());
            req.setGroupval(10);
            //数据库里现有的
            List<BpsMediaInfoDomain> mediaInfo = bpsDao.selectBpsMediaInfo(req);

            //最终需要的
            Set<String> resultMedias = input1.getRoomImages().stream().map(r -> r.getMediaId() + "").collect(Collectors.toSet());

            //找到 已经不存在的
            ArrayList<Integer> aimDel = new ArrayList<>();
            for (BpsMediaInfoDomain bpsMediaInfoDomain : mediaInfo) {

                if (!resultMedias.contains(bpsMediaInfoDomain.getMediaId().toString())) {
                    aimDel.add(bpsMediaInfoDomain.getId()) ;
                }
            }
            //删除已经去掉的
            bpsMediaService.removeByIds(aimDel) ;


            //
            ReqBpsMedia reqMedia = new ReqBpsMedia();

            reqMedia.setBpsId(input1.getBpsId());
            reqMedia.setRoomId(input1.getRoomId());
            List<Integer> collect = input1.getRoomImages().stream().map(r -> r.getMediaId()).collect(Collectors.toList());
            reqMedia.setMediaIds(collect);
            reqMedia.setDescription(dbRoom.getRoomName());
            reqMedia.setGroupval(10);
            reqMedia.setType(1);

            self.saveBpsMedia(reqMedia);


            resultMap.put("update", 1);


        }else {


            //新增
            input1.setRoomId(null);

            //现在就是酒店类型的
//        input1.setBpsType(1);
            input1.setRoomStatus(1);
            input1.setCtime(now);
//        input1.setUtime(now);
            input1.setAdminUid(nowUserInfo.getUserId());
            input1.setAdminType(nowUserInfo.getUserType());

            boolean save = roomService.save(input1);
            if (save) {

                //更新商户 房屋完成 度标识
                FmbxBps up = new FmbxBps();
                up.setBpsId(input1.getBpsId());
                up.setFlagRoom(1);
                bpsService.updateById(up);

                ReqBpsMedia reqMedia = new ReqBpsMedia();

                reqMedia.setBpsId(input1.getBpsId());
                reqMedia.setRoomId(input1.getRoomId());
                List<Integer> collect = input1.getRoomImages().stream().map(r -> r.getMediaId()).collect(Collectors.toList());
                reqMedia.setMediaIds(collect);
                reqMedia.setDescription(input1.getRoomName());
                reqMedia.setGroupval(0);
                reqMedia.setType(1);


                self.saveBpsMedia(reqMedia);


                resultMap.put("roomid", input1.getRoomId());
                resultMap.put("add", 1);

            }
        }

        updatBpsFlagAfterEdit(input1.getBpsId()) ;
        resultMap.put("result",1) ;

    }

    /**
     * 房间详情
     * @param resultMap
     * @param roomid
     */
    public void roomDetail(HashMap<String, Object> resultMap, Integer roomid) {

        FmbxBpsRoom room = roomService.getById(roomid);

        if (room == null) {
            throw new BadLogicException("房型错误");
        }
        resultMap.put("rooom", room);

        ReqBpsMediaInfo req = new ReqBpsMediaInfo();

        req.setType(1);
        req.setBps_id(room.getBpsId());
        req.setGroupval(10);
        req.setRoomId(roomid);

        req.setFrom(1);

        List<BpsMediaInfoDomain> mediaInfo = bpsDao.selectBpsMediaInfo(req);

        resultMap.put("mediaInfo",mediaInfo) ;


    }

    /**
     * 快速创建 供应商和 商户和的定关系
     *
     * @param resultMap
     * @param input
     */
    public void quickBpBindBps(HashMap<String, Object> resultMap, ReqQuickBpBindBps input) {

        resultMap.put("result",0) ;
        FmbxBpMain bpMain = mainService.getById(input.getBpid());
        if (bpMain == null) {
            throw new BadLogicException("供应商不存在:" + input.getBpsid());
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        FmbxBps dbBps = bpsService.getById(input.getBpsid());

        //商户已经存在
        LocalDateTime now = LocalDateTime.now();
        if (dbBps != null) {

            //如果 已经存在的商户 可编辑供应商和输入不同 就更新
            if (input.getCanEdit() == 1 && dbBps.getBpidOfEdit().intValue() != input.getBpid().intValue()) {
                FmbxBps up = new FmbxBps();
                up.setBpsId(input.getBpsid());
                up.setBpidOfEdit(input.getBpid());
                up.setUtime(now);

                //更新商户的 可编辑供应商
                bpsService.updateById(up);

            }

            //绑定关系处理
            {
                Integer bpsid = input.getBpsid();
                Integer bpid = input.getBpid();
                int userId = nowUserInfo.getUserId();

                doBindJob(bpsid, bpid, userId);
            }

        } else {
            //商户不存在

            FmbxBps fmbxBps = new FmbxBps();
            if (input.getCanEdit() == 1) {
                fmbxBps.setBpidOfEdit(input.getBpid());
            }

            fmbxBps.setName(input.getBpsName());
            fmbxBps.setBpsType(1);
            fmbxBps.setStatus(1);
            fmbxBps.setCtime(now);
            fmbxBps.setCreateUid(nowUserInfo.getUserId());
            fmbxBps.setUtime(now);
            fmbxBps.setAdminUid(nowUserInfo.getUserId());

            bpsService.save(fmbxBps);
            doBindJob(fmbxBps.getBpsId(), input.getBpid(), nowUserInfo.getUserId());

        }

        resultMap.put("result", 1);

    }

    /**
     * 供应商绑商户
     * @param bpsid
     * @param bpid
     * @param userId
     */
    private void doBindJob(Integer bpsid, Integer bpid, int userId) {
        LambdaQueryWrapper<FmbxBpBpsBind> bindWrap = new LambdaQueryWrapper<>();
        bindWrap.eq(FmbxBpBpsBind::getBpId, bpid)
                .eq(FmbxBpBpsBind::getBpsId, bpsid);

        if (bindService.getOne(bindWrap) == null) {
            FmbxBpBpsBind fmbxBpBpsBind = new FmbxBpBpsBind();

            fmbxBpBpsBind.setBpId(bpid);
            fmbxBpBpsBind.setBpsId(bpsid);
            fmbxBpBpsBind.setAdminUid(userId);
            fmbxBpBpsBind.setCtime(LocalDateTime.now());

            bindService.save(fmbxBpBpsBind);
        }
    }

    @Autowired
    ExcelService excelService ;

    /**
     * 商户查询
     */

    public void selectBpsInfo(HashMap<String, Object> resultMap, ReqBpsInfo req) {



            if (req.getDoExport().intValue()==0) {


                if (req.getReqSource()==1) {
                    IPage<Integer> page = new Page<>(req.getPageno(), req.getPagesize());
                    IPage<Integer> rlist = bpsDao.selectBpsInfo(page, req);

                    List<BpsInfoDomain> lists = new ArrayList<>() ;
                    if (!rlist.getRecords().isEmpty()) {
                        lists = bpsDao.selectBpsInfoStr(rlist.getRecords());
                    }

                    resultMap.put("list", lists);
                    resultMap.put("total", rlist.getTotal());
                    resultMap.put("pages", rlist.getPages());
                    resultMap.put("size", rlist.getSize());
                    resultMap.put("current", rlist.getCurrent());
                }
                if (req.getReqSource()==2) {
                    List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(req);

                    for (BpsInfoDomain infoDomain : lists) {
                        if ( req.getBpId().intValue()==infoDomain.getBpidOfEdit().intValue()) {
                            infoDomain.setEdit(1);
                        }else {
                            infoDomain.setEdit(0);
                        }
                    }

                    resultMap.put("list", lists);
                }


            }else {

                List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(req);

                UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, BpsInfoDomain.class, "商户信息", "selectBpsInfo");

                resultMap.put("excelInfo",uploadFileOutInfo) ;

            }




    }

    public HashMap upload(MultipartFile uploadFile) {


        SessionUser userInfo = userUnionService.nowUserInfo();
        HashMap<String, Object> map = new HashMap<>();

        String originalFilename = uploadFile.getOriginalFilename();
        //取扩展名
        String ext = "" ;
        if(originalFilename.indexOf('.')!=-1){
            ext = StringUtils.substringAfterLast(originalFilename,".") ;
        }

        String type = "fmbupload" ;
        if (userInfo.getUserType()==SESSIONUSER_BP) {
            type = "bpsupload" ;
        }

        //本地文件存储准备名字
        UploadFileOutInfo uploadFileOutInfo = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, type, ext);

        int copyResult = 0 ;
        try {
            final FileOutputStream outputStream = new FileOutputStream(uploadFileOutInfo.getFilePath());
            final InputStream inputStream = uploadFile.getInputStream();
            copyResult = IOUtils.copy(inputStream, outputStream);
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(outputStream);
        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);

            throw new BadLogicException("文件读取异常");
        }

        if (!isVideoExt(ext)) {
            uploadFileOutInfo = checkPicAndConvert(uploadFileOutInfo,uploadFile,type,ext) ;
        }




        FmbxMedia media = new FmbxMedia();

        media.setSavePath(uploadFileOutInfo.getFilePath());
        media.setSourceFileName(uploadFile.getOriginalFilename());
        media.setCtime(LocalDateTime.now());
        media.setFileExtType(ext);

        //如果是视频
        if (isVideoExt(ext)) {

            UploadFileOutInfo snap = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, "videoSnap", "jpg");
            final HashMap<String, String> stringObjectHashMap = VideoThumbTaker.readVideAndCreateKeySnap(uploadFileOutInfo.getFilePath(),
                    snap.getFilePath());
            if(stringObjectHashMap.containsKey("result") && "1".equals( stringObjectHashMap.get("result"))){
                String img = stringObjectHashMap.get("img");
                media.setVideoSnapUrl(snap.getOutUrl());
            }
        }


        media.setLength((int) uploadFile.getSize());

        media.setAdminType(userInfo.getUserType());
        media.setAdminUid(userInfo.getUserId());
        media.setUrl(uploadFileOutInfo.getOutUrl());
        media.setMd5(MD5Util.md5(uploadFileOutInfo.getOutUrl()));



        boolean save = mediaService.save(media);
        if (save) {
            map.put("mediaId", media.getMediaId());
            map.put("url", media.getUrl());
            map.put("name", media.getSourceFileName());
        }


        return map;
    }

    /**
     * 检查图片格式 如果是 webp格式 就转换文件存储为jpg的格式
     * @param uploadFileOutInfo
     * @param uploadFile
     * @param type
     * @param ext
     * @return
     */
    public UploadFileOutInfo checkPicAndConvert(UploadFileOutInfo uploadFileOutInfo, MultipartFile uploadFile, String type, String ext) {
        InputStream inputStream = null ;
        try {
            inputStream = uploadFile.getInputStream();
            final String format = FmbImgUtil.getImageStreamFormat(inputStream);

            if ("webp".equals(format)) {
                UploadFileOutInfo uploadFileOutInfoPic = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, type, ext);
                BufferedImage webpImage = ImageIO.read(new File( uploadFileOutInfo.getFilePath()));
                final File output = new File(uploadFileOutInfoPic.getFilePath());
                ImageIO.write(webpImage, "jpg", output);

                if (output.exists() && output.length()>0) {
                    return  uploadFileOutInfoPic ;
                }
            }

        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }finally {
            if (inputStream != null) {
                IOUtils.closeQuietly(inputStream);
            }
        }

        return uploadFileOutInfo;
    }

    public  static boolean  isVideoExt(String ext){
        return videoExt.contains(ext.toLowerCase()) ;
    }


    /**
     *   同一个 商户 资源存在更新排序值, 不存在就保存
     * @param req
     * @return
     */
    public int saveBpsMedia(ReqBpsMedia req) {

        SessionUser userInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(userInfo,req.getBpsId());


        int addNum  = 0 ;
        LocalDateTime now = LocalDateTime.now();

        int rankVal =  10 ;
        for (Integer mediaId : req.getMediaIds()) {
            FmbxMedia dbMedia = mediaService.getById(mediaId);

            //资源找不到 跳过
            if (dbMedia == null) {
//                throw new BadLogicException("资源不存在"+mediaId);
                continue;
            }


            //检查是否存在
            LambdaQueryWrapper<FmbxBpsMedia> testWra = Wrappers.lambdaQuery(FmbxBpsMedia.class)
                    .eq(FmbxBpsMedia::getMediaId, mediaId)
                    .eq(FmbxBpsMedia::getBpsId, req.getBpsId())
                    .eq(req.getGroupval()!=null,FmbxBpsMedia::getGroupval,req.getGroupval())
                    .eq(req.getRoomId()!=null,FmbxBpsMedia::getRoomId,req.getRoomId())

                    ;

            FmbxBpsMedia testExistMedia = bpsMediaService.getOne(testWra);


            // 如果存在  更新排序值
            if (testExistMedia!=null) {

                FmbxBpsMedia upm = new FmbxBpsMedia();
                upm.setId(testExistMedia.getId());
                upm.setRankval(rankVal);
                upm.setUtime(now);
                bpsMediaService.updateById(upm) ;
                
            }else {

                //不存在 就新增
                FmbxBpsMedia media = new FmbxBpsMedia();
                BeanUtils.copyProperties(req,media);

                //如果有房型 id  写死Groupval 10
                //图片分组:1-外观,2-大厅,3-餐饮,4家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
                if(req.getRoomId()!=null ){
                    media.setGroupval(10);
                }


                media.setMediaId(mediaId);
                media.setAdminUid(userInfo.getUserId());
                media.setAdminType(userInfo.getUserType());
                media.setAdminName(userInfo.myInfo());

                media.setStatus(1);
                media.setCtime(now);
                media.setUtime(now);
                media.setRankval(rankVal);

                if (bpsMediaService.save(media)) {
                    addNum ++ ;
                }
            }

            rankVal +=10 ;

        }

        /**
         * 商户media 信息添加后 ,关联更新 商户的flag
         */
        if (addNum>0) {
            FmbxBps upBps = new FmbxBps();
            upBps.setBpsId(req.getBpsId());

            if (req.getType().intValue()==1) {
                upBps.setFlagPic(1);
            }else
            if (req.getType().intValue()==2) {
                upBps.setFlagVideo(1);
            }
            boolean b = bpsService.updateById(upBps);
        }


        updatBpsFlagAfterEdit(req.getBpsId()) ;


        return addNum ;
        

    }

    public void addBpsMedia(HashMap<String,Object> resultMap, ReqBpsMedia req) {

        resultMap.put("addNum", saveBpsMedia(req) );

    }

/**
 * 商户图片查询
 */
   
public void selectBpsMediaInfo(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {

    req.setFrom(2);
      if (req.getDoExport().intValue()==0) {
        IPage<BpsMediaInfoDomain> page = new Page<>(req.getPageno(),req.getPagesize());
        IPage<BpsMediaInfoDomain> rlist = bpsDao.selectBpsMediaInfo(page, req);

        resultMap.put("list", rlist.getRecords());
        resultMap.put("total", rlist.getTotal());
        resultMap.put("pages", rlist.getPages());
        resultMap.put("size", rlist.getSize());
        resultMap.put("current", rlist.getCurrent());
       }else{

          List<BpsMediaInfoDomain> lists = bpsDao.selectBpsMediaInfo(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,BpsMediaInfoDomain.class, "商户图片查询", "selectBpsMediaInfo");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }


    /**
     * 商户图片   筛选条件查询
     * @param resultMap
     * @param req
     */
    @DS(DB_slave)
    public void bpsMediaSimpleInfo(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {

        LambdaQueryWrapper<FmbxBps> wrp = new LambdaQueryWrapper<>();
        wrp.select(FmbxBps::getName)
                .eq(FmbxBps::getBpsId,req.getBps_id()) ;

        FmbxBps one = bpsService.getOne(wrp);

        resultMap.put("bpsname",one.getName()) ;

        List<BpsMediaFilterInfo> bpsMediaFilterInfos = bpsDao.selectCreateInfo(req.getBps_id());

        resultMap.put("createInfo",bpsMediaFilterInfos) ;

    }

    public void bpsMediaToggleStatus(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {


        resultMap.put("result",0) ;
        SessionUser userInfo = userUnionService.nowUserInfo();

        updateBpsMediaOperate(resultMap, req,  userInfo);

    }




    public void updateBpsMediaOperate(HashMap<String, Object> resultMap, ReqBpsMediaInfo req, SessionUser userInfo) {
        FmbxBpsMedia dbInfo = bpsMediaService.getById(req.getId());

        if (dbInfo == null) {
            throw new BadLogicException("商户资源不存在:"+req.getId());
        }

        checkUserCanEditBps(userInfo,dbInfo.getBpsId());

        if (dbInfo.getStatus()==1) {
            dbInfo.setStatus(0);
        }else {
            dbInfo.setStatus(1);
        }

        dbInfo.setUtime(LocalDateTime.now());
        dbInfo.setLastopAdminInfo( SessionUser.userTypeMap.get( userInfo.getUserType()) + ":" + userInfo.getUsername() +"("+ userInfo
                .getUserId() +")");

        boolean b = bpsMediaService.updateById(dbInfo);


        updatBpsFlagAfterEdit(dbInfo.getBpsId()) ;

        if (b) {
            resultMap.put("result",1) ;
        }
    }

    @DS(DB_slave)
    public void bpsMediaDetail(HashMap<String, Object> resultMap, Integer id) {
        FmbxBpsMedia bpsMedia = bpsMediaService.getById(id);

        if (bpsMedia == null || bpsMedia.getStatus().intValue()==0) {
            throw new BadLogicException("图片不存在或状态异常");
        }
        FmbxMedia media = mediaService.getById(bpsMedia.getMediaId());


        resultMap.put("bpsMedia",bpsMedia) ;
        resultMap.put("media",media) ;
        resultMap.put("groupval",FmbCollectionsUtils.mapToList(BpsMediaInfoDomain.groupvalMap)) ;


    }

    public void bpsMediaUpdate(HashMap<String, Object> resultMap, ReqBpsMediaUpdate req) {
        resultMap.put("result",0) ;
        SessionUser userInfo = userUnionService.nowUserInfo();

        FmbxBpsMedia bpsMedia = bpsMediaService.getById(req.getId());

        if (bpsMedia == null || bpsMedia.getStatus().intValue()==0 ) {
            throw new BadLogicException("图片不存在或状态异常");
        }

        FmbxBps bps = bpsService.getById(bpsMedia.getBpsId());
        //如果是 供应商后台 而且 当前供应商ID和登录的供应商ID 不一样 就不能编辑

        if (SESSIONUSER_BP==userInfo.getUserType() &&  bps.getBpidOfEdit().intValue()!=userInfo.getUserId()) {
            throw new BadLogicException("您不能编辑该商户信息");
        }

        FmbxBpsMedia dbup = new FmbxBpsMedia();

        BeanUtils.copyProperties(req,dbup);


        dbup.setLastopAdminInfo(userInfo.myInfo());

        boolean b = bpsMediaService.updateById(dbup);
        if (b) {
            resultMap.put("result",1) ;
        }

    }

/**
 * 商户管理房型管理
 */

@DS(DB_slave)
public void selectBpsRoomInfo(HashMap<String,Object> resultMap, ReqBpsRoomInfo req) {

      if (req.getDoExport().intValue()==0) {

          List<BpsRoomInfoDomain> lists = bpsDao.selectBpsRoomInfo(req);

          resultMap.put("rooms", lists);

//        IPage<BpsRoomInfoDomain> page = new Page<>(req.getPageno(),req.getPagesize());
//        IPage<BpsRoomInfoDomain> rlist = bpsDao.selectBpsRoomInfo(page, req);
//
//        resultMap.put("list", rlist.getRecords());
//        resultMap.put("total", rlist.getTotal());
//        resultMap.put("pages", rlist.getPages());
//        resultMap.put("size", rlist.getSize());
//        resultMap.put("current", rlist.getCurrent());
       }else{

          List<BpsRoomInfoDomain> lists = bpsDao.selectBpsRoomInfo(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,BpsRoomInfoDomain.class, "商户管理房型管理", "selectBpsRoomInfo");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }

    @DS(DB_slave)
    public void selectBpsRoomSku(HashMap<String,Object> resultMap, ReqBpsRoomSku req) {
        List<BpsRoomInfoDomain> lists = new ArrayList<>();
        if(req.getBpsId()!=null && req.getBpsId()>0) {
            lists = bpsDao.selectBpsRoomSku(req);
        }else{
            if(req.getXaid()!=null && req.getXaid()>0){
                LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
                wr.eq(FmbxActivity::getXaid, req.getXaid());
                FmbxActivity a = activityService.getOne(wr);

                req.setBpsId(a.getBpsId());
                lists = bpsDao.selectBpsRoomSku(req);
            }
        }
        resultMap.put("rooms", lists);

    }


    /**
     * 房型状态 开关
     * @param resultMap
     * @param roomid
     */
    public void bpsRoomStatusToggle(HashMap<String,Object> resultMap, Integer roomid) {

        resultMap.put("result",0) ;
        FmbxBpsRoom dbroom = roomService.getById(roomid);
        if (dbroom==null) {
            throw new BadLogicException("房型不存在" + roomid);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,dbroom.getBpsId());

        FmbxBpsRoom update = new FmbxBpsRoom();
        update.setRoomId(roomid);
        if (dbroom.getRoomStatus()==1) {
            update.setRoomStatus( 0 );
        }else {
            update.setRoomStatus( 1 );
        }
        update.setUtime(LocalDateTime.now());
        update.setAdminUid(nowUserInfo.getUserId());
        boolean b = roomService.updateById(update);

        updatBpsFlagAfterEdit(dbroom.getBpsId()) ;

        if (b) {
            resultMap.put("result",1) ;
            resultMap.put("nowStatus",update.getRoomStatus()) ;
        }

    }

    /**
     * 复制房型
     * @param resultMap
     * @param roomid
     */
    public void bpsRoomCopy(HashMap<String,Object> resultMap, Integer roomid) {

        resultMap.put("result",0) ;
        FmbxBpsRoom dbroom = roomService.getById(roomid);
        if (dbroom==null) {
            throw new BadLogicException("房型不存在" + roomid);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,dbroom.getBpsId());

        FmbxBpsRoom nroom = new FmbxBpsRoom();

        BeanUtils.copyProperties(dbroom,nroom);

        nroom.setRoomId(null);


        nroom.setRoomName(dbroom.getRoomName()+"(复制)");
        nroom.setRoomStatus(0);
        nroom.setAdminUid(nowUserInfo.getUserId());
        LocalDateTime now = LocalDateTime.now();
        nroom.setCtime(now);
        nroom.setUtime(now);

        boolean save = roomService.save(nroom);
        if (save) {
            resultMap.put("newRoomId",nroom.getRoomId());
            resultMap.put("result",1);
        }

    }

    /**
     * 商户 状态 开关
     * @param resultMap
     * @param bpsId
     */
    public void bpsStatusToggle(HashMap<String,Object> resultMap, Integer bpsId) {

        resultMap.put("result",0) ;
        FmbxBps bpsDB = bpsService.getById(bpsId);
        if (bpsDB == null) {
            throw new BadLogicException("商户不存在"+bpsId);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        FmbxBps nbps = new FmbxBps();
        nbps.setBpsId(bpsId);
        if (bpsDB.getStatus().intValue()==1) {
            nbps.setStatus(0);
        }else {
            nbps.setStatus(1);
        }

        nbps.setUtime(LocalDateTime.now());
        nbps.setAdminUid(nowUserInfo.getUserId());
        boolean b = bpsService.updateById(nbps);
        if (b) {
            resultMap.put("result",1) ;
            resultMap.put("nowStatus",nbps.getStatus()) ;
        }

    }

    /**
     * 供应商合同管理
     */
    @DS(DB_slave)
    public void selectBpContract(HashMap<String,Object> resultMap, ReqBpContract req) {

        final SessionUser sessionUser = userUnionService.nowUserInfo();

        if (sessionUser.getUserType()==SESSIONUSER_FMB) {
            final FmbAdmin fmbAdmin = adminService.getById(sessionUser.getUserId());

            final String role = fmbAdmin.getRole();

            //不是  rolesCanReadAllContract 里的用户只能看自己提的
            if (!rolesCanReadAllContract.contains(role)) {
                //增加 只能查看自己合同的筛选条件
                final ArrayList<Integer> userIds = new ArrayList<>();
                userIds.add(sessionUser.getUserId()) ;
                req.setUserIds(userIds);
            }
        }


        if (req.getDoExport().intValue() == 0) {
           IPage<BpContractDomain> page = new Page<>(req.getPageno(), req.getPagesize());
           IPage<BpContractDomain> rlist = bpsDao.selectBpContract(page, req);

           for (BpContractDomain record : rlist.getRecords()) {
               final long count = outfileInfoService.count(createBpContrsctFileCountWrapper(record.getBpcId()));
               record.setHaveContractFile(count > 0 ? 1 : 0);
           }

           resultMap.put("list", rlist.getRecords());
           resultMap.put("total", rlist.getTotal());
           resultMap.put("pages", rlist.getPages());
           resultMap.put("size", rlist.getSize());
           resultMap.put("current", rlist.getCurrent());
       } else {

           List<BpContractDomain> lists = bpsDao.selectBpContract(req);

           UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, BpContractDomain.class, "供应商合同管理", "selectBpContract");

           resultMap.put("excelInfo", uploadFileOutInfo);
       }

   }


    /**
     * 查看合同详情
     * @param resultMap
     * @param bpcId
     */
    @DS(DB_slave)
    public void contractDatail(HashMap<String,Object> resultMap, Integer bpcId) {

        FmbxBpContract bpContract = contractService.getById(bpcId);
        if (bpContract==null) {
            throw new BadLogicException("合同不存在"+bpcId);
        }

        LambdaQueryWrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<>();
        wr.select(FmbxOutfileInfo::getSourceFileName,FmbxOutfileInfo::getLength,FmbxOutfileInfo::getUrl,FmbxOutfileInfo::getType,FmbxOutfileInfo::getExtString1)

                .eq(FmbxOutfileInfo::getModelId,bpcId)
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract") ;

        List<FmbxOutfileInfo> list = outfileInfoService.list(wr);

        JSONArray ops = JSONArray.parseArray(bpContract.getDingdingOpDetail());
        bpContract.setDingdingOpDetail(null);

        resultMap.put("files",list);
        resultMap.put("contract",bpContract) ;
        resultMap.put("operations",ops) ;


    }

    /**
     * 定时检查是否有 即将过期的 合同,并发邮件
     *
     */
    public void checkContractWillEnd() {

        //查找符合条件的邮件
        LambdaQueryWrapper<FmbxBpContract> wrap = new LambdaQueryWrapper<FmbxBpContract>()
                .select(FmbxBpContract::getBpcId,FmbxBpContract::getBpId, FmbxBpContract::getBpcNumber,FmbxBpContract::getCreaterDingUserid,
                FmbxBpContract::getBpCreateType
        ,FmbxBpContract::getBpTemplateType,FmbxBpContract::getBpStartDate,FmbxBpContract::getBpEndDate,FmbxBpContract::getPrePay)

                .in(FmbxBpContract::getBpcRunuingStatus,Arrays.asList(5,6))
                .eq(FmbxBpContract::getBpcAllStatus,1)
                .eq(FmbxBpContract::getWillendCheckStatus,0)
                .eq(FmbxBpContract::getBpEndDate,LocalDateTime.now().plusDays(30).toLocalDate())
                .gt(FmbxBpContract::getBpEndDate,LocalDateTime.now().toLocalDate())

        ;

        List<FmbxBpContract> contractList = contractService.list(wrap);



        for (FmbxBpContract c : contractList) {
//            System.out.println(fmbxBpContract.getBpcId());

            genMailAndSend( c,1);

            //更新 标记值 已经发起过提醒了 
            FmbxBpContract up = new FmbxBpContract();
            up.setBpcId(c.getBpcId());
            up.setWillendCheckStatus(1);
            contractService.updateById(up);

        }

    }

    /**
     *
     * @param c  合同
     * @param type  1-即将终止,2-已经到期
     */
    private void genMailAndSend( FmbxBpContract c,int type) {
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String mailTitle = "【提醒】合同即将过期" ;

        if (type==2) {
            mailTitle = "【提醒】合同已终止" ;
        }


        String parterName = "";
        if (c.getBpId().intValue()!=0) {
            FmbxBpParterInfo parterInfo = parterInfoService.getById(c.getBpId());
            if (parterInfo != null) {
                parterName = parterInfo.getPartnerName() ;
            }
        }

        LambdaQueryWrapper<FmbxThirdsysUser> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxThirdsysUser::getThirdSysUid,c.getCreaterDingUserid());
        FmbxThirdsysUser thirdsysUser = thirdsysUserService.getOne(wr);

        String fqr = thirdsysUser!=null?thirdsysUser.getSysUsername():"" ;

        String mailContent = StrUtil.format("合同编号：{}<br>关联供应商：{}<br>发起人：{}<br>合同签署方式：新签合同<br>合同类型：{}<br>有效期：{}至{}<br>预付款保证金：{}元"
                , c.getBpcNumber()
                , parterName
                , fqr
                , bpCreateTypeMap.get(c.getBpCreateType())
                , c.getBpStartDate().format(formater)
                , c.getBpEndDate().format(formater)
                , c.getPrePay()
        );

        if (type==1) {
            mailContent += "<br><br> <strong>该合同即将过期，请及时续签，如不再继续合作，请追回预付款保证金</strong>" ;
        }
        if (type==2) {
            mailContent += "<br><br> <strong>该合同已终止请知悉，如已续签请忽略~</strong>" ;
        }



        ArrayList<String> strings = new ArrayList<>();
        strings.add(FmbConstants.LEAGAL_EMAIL) ;

        if (StringUtils.isNotBlank(thirdsysUser.getEmail()) && RegStringUtil.isValidEmail(thirdsysUser.getEmail())) {
            strings.add(thirdsysUser.getEmail());
        }

        String[] emails = strings.toArray(new String[]{});

        //非线上 给固定用户发邮件
        if (!FmbServerConfig.isPro()) {
//            emails = new String[]{"<EMAIL>","<EMAIL>"};
//            emails = new String[]{"<EMAIL>"};
            mailTitle = "[来自测试系统]"+ mailTitle ;
        }

        MailService.sendMimeMail(emails,mailTitle,mailContent,null,true);


    }

    /**
     * 找到已经过期的合同 发送邮件通知
     */
    public void checkContractEnd() {


        LambdaQueryWrapper<FmbxBpContract> wrap = new LambdaQueryWrapper<>();

        //查找符合条件的邮件
        wrap.select(FmbxBpContract::getBpcId,FmbxBpContract::getBpId, FmbxBpContract::getBpcNumber,FmbxBpContract::getCreaterDingUserid,
                FmbxBpContract::getBpCreateType
                ,FmbxBpContract::getBpTemplateType,FmbxBpContract::getBpStartDate,FmbxBpContract::getBpEndDate,FmbxBpContract::getPrePay)
                .in(FmbxBpContract::getBpcRunuingStatus,Arrays.asList(5,6))
                .eq(FmbxBpContract::getBpcAllStatus,1)
                .lt(FmbxBpContract::getBpEndDate,LocalDateTime.now().toLocalDate())

        ;

        List<FmbxBpContract> contractList = contractService.list(wrap);


        for (FmbxBpContract c : contractList) {
//            System.out.println(c.getBpcId());

            genMailAndSend( c,2);


            //更新合同状态 为终结
            FmbxBpContract updb = new FmbxBpContract();
            updb.setBpcId(c.getBpcId());
            updb.setBpcAllStatus(2);
            contractService.updateById(updb);

        }


    }


    public void downLoadFinalContract(HashMap<String,Object> resultMap, Integer bpcId) {

        //下载合同是否需要 身份校验 
//        final SessionUser sessionUser = userUnionService.nowUserInfo();
//        final FmbxBpContract contract = contractService.getById(bpcId);



        Wrapper<FmbxOutfileInfo> wr = creteBpContrsctFileWrapper(bpcId);

        FmbxOutfileInfo outFile = outfileInfoService.getOne(wr);

        if (outFile == null) {
            throw new BadLogicException("合同数据不存在"+bpcId);
        }

//        List<FmbxOutfileInfo> list = outfileInfoService.list(wr);
        File fileContract = new File(fmbImgDir + outFile.getSavePath());

//        System.out.println(fileContract.getAbsolutePath());

        if (!fileContract.exists()) {
            throw new BadLogicException("合同文件损坏");
        }


        resultMap.put("url",fmbImgUrl+"/"+outFile.getSavePath()) ;
        resultMap.put("sourceFileName",outFile.getSourceFileName()) ;
        resultMap.put("type",outFile.getType()) ;

//        HttpServletResponse response = ServletUtils.getResponse();
//        response.reset();
//        response.addHeader("Content-Disposition", "attachment;filename="+outFile.getSourceFileName());
//
//        try {
//            IOUtils.copy(new FileInputStream(fileContract),response.getOutputStream()) ;
//        } catch (IOException ex) {
//            long errorid = SnowflakeIdWorker.getInstance().nextId();
//            logger.error("errorid " + errorid, ex);
//            throw new BadLogicException("文件传输异常");
//        }

    }

    private Wrapper<FmbxOutfileInfo> creteBpContrsctFileWrapper(Integer bpcId) {
        Wrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<FmbxOutfileInfo>()
                .select(FmbxOutfileInfo::getSourceFileName, FmbxOutfileInfo::getSavePath, FmbxOutfileInfo::getLength,
                        FmbxOutfileInfo::getUrl,
                FmbxOutfileInfo::getType,
                FmbxOutfileInfo::getExtString1)

                .eq(FmbxOutfileInfo::getModelId, bpcId)
                .eq(FmbxOutfileInfo::getExtString1,"盖章合同上传")
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract") ;
        return wr;
    }

    private Wrapper<FmbxOutfileInfo> createBpContrsctFileCountWrapper(Integer bpcId) {
        Wrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<FmbxOutfileInfo>()
                .select(FmbxOutfileInfo::getOutfileId)

                .eq(FmbxOutfileInfo::getModelId, bpcId)
                .eq(FmbxOutfileInfo::getExtString1,"盖章合同上传")
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract") ;
        return wr;
    }

    /**
     * 合同绑定 商户
     * @param resultMap
     * @param req
     */
    public void contractBindBp(HashMap<String, Object> resultMap, ReqBpContract req) {


        resultMap.put("result",0);

        final Integer reqBpcId = req.getBpcId();
        final Integer reqBpId = req.getBpId();
        if (reqBpcId !=null && reqBpId !=null) {
            if (contractService.count(new LambdaQueryWrapper<FmbxBpContract>().eq(FmbxBpContract::getBpcId, reqBpcId).select(FmbxBpContract::getBpcId))==1
            && mainService.count(new LambdaQueryWrapper<FmbxBpMain>().eq(FmbxBpMain::getBpId, reqBpId).select(FmbxBpMain::getBpId))==1
            ) {
                final boolean update = contractService.update(new LambdaUpdateWrapper<FmbxBpContract>().eq(FmbxBpContract::getBpcId, reqBpcId).set(FmbxBpContract::getBpId, reqBpId));
                if (update) {
                    resultMap.put("result",1);
                }
            }
        }
    }

    /**
     * 根据 活动ID 查询 活动名称
     * 需要时老活动ID
     * @param resultMap
     * @param aid
     * @param loadImg
     * @param bpsId
     */
    public void activityQuery(HashMap<String, Object> resultMap, Integer aid, Integer loadImg, Integer bpsId) {

        if (aid == null) {
            throw new BadLogicException("活动ID不能为空");
        }

        Wrapper<FmbProductIndex> wrIndex = new LambdaQueryWrapper<FmbProductIndex>()
                .eq(FmbProductIndex::getApId,aid)
                .select(FmbProductIndex::getTitle,FmbProductIndex::getSellStat,FmbProductIndex::getFmbxActivityFlag)
                .last(" limit 1 ")
                ;
        final FmbProductIndex productIndex = productIndexService.getOne(wrIndex);


        resultMap.put("result",0) ;
        if (productIndex == null) {
            resultMap.put("message","活动不存在") ;

            return ;
        }


        processOldActivityContent(aid, loadImg,resultMap,bpsId) ;


        HashMap<String, Object> actInfo = new HashMap<>();
        actInfo.put("aid", aid);
        actInfo.put("title", productIndex.getTitle());
        resultMap.put("activity",actInfo) ;

        if (productIndex.getFmbxActivityFlag()==0) {
            resultMap.put("result",1) ;
            resultMap.put("message","OK") ;
        }else {
            resultMap.put("message","活动不是老活动") ;
        }


    }

    /**
     * 处理 老活动图文详情里的 图片信息
     * @param aid
     * @param loadImg
     * @param resultMap
     * @param bpsId
     */
    public void processOldActivityContent(Integer aid, Integer loadImg, HashMap<String, Object> resultMap, Integer bpsId) {

        if (loadImg != null && loadImg==1) {
            final LambdaQueryWrapper<FmbActivityContent> wr = new LambdaQueryWrapper<FmbActivityContent>().eq(FmbActivityContent::getAid,
                    aid);
            final FmbActivityContent contentServiceOne = activityContentService.getOne(wr);

            HashMap<String, Object> par = new HashMap<>();
            par.put("content", contentServiceOne.getAppContentNew() );
            //解析为json 字符串
            final String jsonStr = doPhpEvalStringMapJob(par);

            final JSONObject jsonObject = JSON.parseObject(jsonStr);

            final cn.hutool.json.JSON parse = JSONUtil.parse(jsonObject);
            //读取对应节点的 富文本内容
            final cn.hutool.json.JSONArray byPath = (cn.hutool.json.JSONArray) parse.getByPath("content.mobile_data.activity_content");

            // 把富文本连接起来
            final StringBuffer stringBuffer = new StringBuffer();
            for (Object o : byPath) {
//                System.out.println(o);
                stringBuffer.append(((cn.hutool.json.JSONObject) o).getStr("mo_content") ) ;
            }


            // jsoup 读取图片 src 内容
            Document docImgs = Jsoup.parse(stringBuffer.toString());
            Elements imgs = docImgs.getElementsByTag("img");

            final ArrayList<String> imgUrls = new ArrayList<>();
            for (int i = 0; i < imgs.size(); i++) {
                final Element element = imgs.get(i);

                final String src = element.attr("src");

                if (StringUtils.isNotBlank(src)) {
                    imgUrls.add(src) ;
                }
            }

            final ArrayList<Map<String,Object>> resultsInfo  = new ArrayList<>();

            for (String imgUrl : imgUrls) {

                String md5 = MD5Util.md5(imgUrl) ;
                Wrapper<FmbxMedia> wrMedia = new LambdaQueryWrapper<FmbxMedia>()
                        .eq(FmbxMedia::getMd5, md5)
                        .select(FmbxMedia::getMediaId)
                        .last(" limit 1 ");
                FmbxMedia mediaCheck = mediaService.getOne(wrMedia);

                int isBpsMedis = 0 ;

                if (mediaCheck != null) {
                    Wrapper<FmbxBpsMedia> wrBpsMedia = new LambdaQueryWrapper<FmbxBpsMedia>()
                            .eq(FmbxBpsMedia::getMediaId, mediaCheck.getMediaId())
                            .eq(FmbxBpsMedia::getBpsId,bpsId)
                            ;

                    isBpsMedis = bpsMediaService.count(wrBpsMedia)==0?0:1 ;
                }



                HashMap<String, Object> line = new HashMap<>();
                line.put("imgUrl", imgUrl);
                line.put("isBpsMedis", isBpsMedis);

                resultsInfo.add(line) ;

            }

            resultMap.put("imagesInfo",resultsInfo) ;




        }

    }

    /**
     * 把老的 活动房型数据 变为新的 房型数据
     * @param resultMap
     * @param aid
     * @param bpsId
     */
    public void roomInfoImport(HashMap<String, Object> resultMap, Integer aid, Integer bpsId) {

        final SessionUser sessionUser = userUnionService.nowUserInfo();

        Wrapper<FmbGroupTickets> wrGroup = new LambdaQueryWrapper<FmbGroupTickets>()
                .eq(FmbGroupTickets::getAid,aid)
                .select(FmbGroupTickets::getTicketGroupId)
                ;
        final FmbGroupTickets fmbGroupTickets = groupTicketsService.getOne(wrGroup);

        resultMap.put("result",0) ;
        if (fmbGroupTickets == null) {
            resultMap.put("message","活动不存在["+aid+"]") ;
            return ;
        }

        Wrapper<FmbxBps> bpsCheck = new LambdaQueryWrapper<FmbxBps>()
                .eq(FmbxBps::getBpsId,bpsId)
                .select(FmbxBps::getBpsId);
        if (bpsService.count(bpsCheck)==0) {
            resultMap.put("message","商户不存在["+bpsId+"]") ;
            return ;
        }


        int counterBpsMedia = 0 ;
        int counterImg = 0 ;
        int counterRoom = 0 ;

        //读取老房型数据
        Wrapper<FmbHotelHouseTypes> wrHouse = new LambdaQueryWrapper<FmbHotelHouseTypes>()
                .eq(FmbHotelHouseTypes::getTicketGroupId,fmbGroupTickets.getTicketGroupId())
                .eq(FmbHotelHouseTypes::getStatus,0)
                ;
        final List<FmbHotelHouseTypes> houseTypesList = hotelHouseTypesService.list(wrHouse);

        for (FmbHotelHouseTypes houseTypes : houseTypesList) {

            HashMap<String, Object> parMap = new HashMap<>();
            parMap.put("image", houseTypes.getImage());
            parMap.put("bedTypes", houseTypes.getBedTypeList());
            parMap.put("addBed", houseTypes.getIsAddBed());
            parMap.put("network", houseTypes.getNetworkType());
            parMap.put("smoke", houseTypes.getSmokeType());
            //通过php 接口 解析内容
            final String resultJson = doPhpEvalStringMapJob(parMap);

            final JSONObject jsonObject = JSON.parseObject(resultJson);

            final JSONArray bedTypes = jsonObject.getJSONArray("bedTypes");
            final JSONArray smoke = jsonObject.getJSONArray("smoke");
            final JSONArray addBed = jsonObject.getJSONArray("addBed");
            List<String> imageList = jsonObject.getJSONArray("image").toJavaList(String.class);

//            System.out.println(resultJson);

//            // [{"width":"1.5","type":"three_persons_rooms"}, {"width":"2","type":"other"}]
//            final List<String> bedTypeList = FmbPhpUtil.phpEvalString2List(houseTypes.getBedTypeList());
//

            final ArrayList<Integer> mediaIds = new ArrayList<>();
            for (String imgUrl : imageList) {

                //  /home/<USER>/fmb.photo/house_type/20191016/20191016110700301.jpg
                // http://img3.fumubang.net/house_type/20191016/20191016110648410.jpg
                FmbxMedia mediaNew = new FmbxMedia();

                final String url = "http://img3.fumubang." + FmbServerConfig.netOrCom() + "/" + imgUrl;
                final String savePath = "/home/<USER>/fmb.photo/" + imgUrl;
                final String md5 = MD5Util.md5(url);

                Wrapper<FmbxMedia> wrMedia = new LambdaQueryWrapper<FmbxMedia>()
                        .eq(FmbxMedia::getMd5, md5)
                        .select(FmbxMedia::getMediaId)
                        .last(" limit 1 ");
                FmbxMedia mediaCheck = mediaService.getOne(wrMedia);

                //解析 图片数据
                if (mediaCheck == null) {

                    mediaNew.setUrl(url);
                    File fTest = new File(savePath);
                    mediaNew.setSavePath(savePath);
                    mediaNew.setSourceFileName(StringUtils.substringAfterLast(imgUrl, "/"));
                    mediaNew.setCtime(LocalDateTime.now());
                    final String fileExtType = StringUtils.substringAfterLast(imgUrl, ".");
                    mediaNew.setFileExtType("");

                    if (fileExtType != null && fileExtType.length()<10) {
                        mediaNew.setFileExtType(fileExtType);
                    }

                    mediaNew.setAdminType(sessionUser.getUserType());
                    mediaNew.setLength(0);

                    if (fTest.exists()) {
                        mediaNew.setLength((int) fTest.length());
                    }
                    mediaNew.setAdminUid(sessionUser.getUserId());
                    mediaNew.setVideoSnapUrl("");

                    mediaNew.setMd5(md5);

                    mediaService.save(mediaNew);

                    mediaIds.add(mediaNew.getMediaId());
                    counterImg ++ ;

                } else {
                    mediaIds.add(mediaCheck.getMediaId());
                }
            }

            final ArrayList<Integer> roomIds = new ArrayList<>();


            //根据 床型数据 组织房型数据
            for (int i = 0; i < bedTypes.size(); i++) {

                JSONObject jsonObjectOfBed = bedTypes.getJSONObject(i);

                final String typeOfBidEn = jsonObjectOfBed.getString("type");
                final String bedTypeName = OLD_BED_TYPE_MAP.get(typeOfBidEn);
                final String bedTypeNameINT = OLD_BED_TYPE_2_NEW_MAP.get(typeOfBidEn);

                final String widthStr = jsonObjectOfBed.getString("width");

                final String importKey = "" + houseTypes.getHouseTypeId() + "_" + bedTypeName;
                Wrapper<FmbxBpsRoom> wrCheckRoom = new LambdaQueryWrapper<FmbxBpsRoom>()
                        .eq(FmbxBpsRoom::getRoomImportKey, importKey)
                        .eq(FmbxBpsRoom::getBpsId,bpsId)
                        .select(FmbxBpsRoom::getRoomId)
                        .last(" limit 1 ")

                        ;
                final FmbxBpsRoom bpsRoomCheck = roomService.getOne(wrCheckRoom);

                if (bpsRoomCheck == null) {
                    FmbxBpsRoom room = new FmbxBpsRoom();

                    room.setBpsId(bpsId);
                    room.setRoomName(houseTypes.getName()+"_"+bedTypeName);
                    room.setRoomIntroduce(houseTypes.getInfoDesc());

                    if ("other".equals(typeOfBidEn)) {
                        room.setRoomIntroduce(houseTypes.getInfoDesc()+widthStr);
                    }

                    room.setRoomNum(0);
                    room.setFloorNumber(houseTypes.getFloorNumber());
                    room.setBuildArea(houseTypes.getBuildArea());


                    final String bedTypeNameINT1 = BED_TYPE_JSON.replace("bedTypeNameINT", bedTypeNameINT);

                    final JSONObject jsonObject1 = JSON.parseObject(bedTypeNameINT1);

                    final JSONArray jsonArray = jsonObject1.getJSONArray("db");
                    for (int ji = 0; ji < jsonArray.size(); ji++) {
                        final JSONObject jsonObject2 = jsonArray.getJSONObject(ji);
                        if (jsonObject2.getString("value").equals(bedTypeNameINT)) {
                            jsonObject2.getJSONArray("list").getJSONObject(0).put("width",widthStr) ;
                        }
                    }

                    room.setBedType(jsonObject1.toJSONString());

                    if (addBed != null && addBed.size()>0) {
                        final JSONObject jsonObjectAddBed = addBed.getJSONObject(0);

                        final Integer type = jsonObjectAddBed.getInteger("type");

                        //新数据 对应关系
//                    加床:1-未知,2-不可加床,3-免费加不含早,4-免费加含早,5-收费加床不含早,6-收费加床含早

                        //旧的
                        //        <input type="radio" name="add_bed" value="1" checked="">未知&nbsp;
//        <input type="radio" name="add_bed" value="2">不可加&nbsp;
//        <input type="radio" name="add_bed" value="3">免费加床，不含早&nbsp;

                        if (type==1|| type==2|| type==3|| type==6) {
                            room.setAddBedType(type);
                        }

                        //旧的  <input type="radio" name="add_bed" value="4">收费加床，不含早&nbsp;
                        if (type==4) {
                            room.setAddBedType(5);
                        }
                        // <input type="radio" name="add_bed" value="5">免费加床，含早
                        if (type==5) {
                            room.setAddBedType(4);
                        }

//                      <input type="radio" name="add_bed" value="6" checked="">收费加床，含早&nbsp;
//                        加床价：￥<input type="text" name="add_bed_price_han" size="20" value=" 300 ">/床/间夜<br>


                        if (room.getAddBedType()!=null &&  (room.getAddBedType()==5 || room.getAddBedType()==6) ) {
                            final String bed_price = jsonObjectAddBed.getString("bed_price");
                            if (StringUtils.isNotBlank(bed_price)) {
                                room.setAddBedFeeInfo("加床价："+ bed_price +"/床/间夜");
                            }
                        }

                        if (room.getAddBedType()!=null &&  room.getAddBedType()<5) {
                            room.setAddBedFeeInfo("");
                        }
                    }

                    room.setMaxPeopleNum(0);

                    room.setSmokeType(0);
                    if (smoke != null && smoke.size()>0) {

                        // 旧数据格式
//                    <p> <b>无烟信息：</b>
//			                    <input type="radio" name="smoke_type_code" size="20" value="0">未知
//                            <input type="radio" name="smoke_type_code" size="20" value="1">有无烟房
//                            <input type="radio" name="smoke_type_code" size="20" value="2">可无烟处理
//                            <input type="radio" name="smoke_type_code" size="20" value="3" checked="">禁烟
//                            </p>

                        final JSONObject jsonObjectSmoke = smoke.getJSONObject(0);
                        final Integer smokeType = jsonObjectSmoke.getInteger("type");

                        //新数据格式   吸烟信息:1-可吸烟,2-禁烟,3-部分客房可吸烟
                        if (smokeType==3) {
                            room.setSmokeType(2);
                        }
                        if (smokeType==1) {
                            room.setSmokeType(3);
                        }
                        if (smokeType==2) {
                            room.setSmokeType(1);
                        }

                    }

                    room.setBathtubType(0);
                    room.setRoomStatus(0);
                    room.setAdminUid(sessionUser.getUserId());
                    room.setAdminType(sessionUser.getUserType());
                    room.setCtime(LocalDateTime.now());

                    room.setRoomImportKey(importKey);

                    roomService.save(room);
                    counterRoom ++ ;
                    roomIds.add(room.getRoomId()) ;
                }else {
                    roomIds.add(bpsRoomCheck.getRoomId()) ;
                }

            }


            //最终组合 房型和 图片资源
            for (Integer roomId : roomIds) {
                for (Integer mediaId : mediaIds) {

                    Wrapper<FmbxBpsMedia> wrBpsMedia = new LambdaQueryWrapper<FmbxBpsMedia>()
                            .eq(FmbxBpsMedia::getMediaId,mediaId)
                            .eq(FmbxBpsMedia::getRoomId,roomId)
                            
                            ;
                    if (bpsMediaService.count(wrBpsMedia) == 0) {

                        FmbxBpsMedia bpsMedia = new FmbxBpsMedia();

                        bpsMedia.setBpsId(bpsId);
                        bpsMedia.setRoomId(roomId);
                        bpsMedia.setMediaId(mediaId);
                        bpsMedia.setType(1);
                        bpsMedia.setDescription("");
                        bpsMedia.setGroupval(10);
                        bpsMedia.setStatus(1);
                        bpsMedia.setAdminUid(sessionUser.getUserId());
                        bpsMedia.setAdminType(sessionUser.getUserType());
                        bpsMedia.setAdminName(sessionUser.getUsername());
                        bpsMedia.setCtime(LocalDateTime.now());
                        bpsMedia.setLastopAdminInfo(sessionUser.myInfo());
                        bpsMedia.setRankval(0);

                        bpsMediaService.save(bpsMedia);

                        counterBpsMedia ++ ;
                    }
                }
            }
        }
        resultMap.put("result",1) ;
        resultMap.put("message",  "新增房型"+counterRoom+",新增房型资源"+counterBpsMedia  ) ;

    }

    /**
     * 从图文详情里 把选中的图片 存储到 bps  media 库
     * @param resultMap
     * @param bpsId
     * @param imagesInfo
     */
    public void actRichHtmlImageImport(HashMap<String, Object> resultMap, Integer bpsId, List<String> imagesInfo) {

        final SessionUser sessionUser = userUnionService.nowUserInfo();

        resultMap.put("result",0) ;


        int counterBpsMedia = 0 ;
        int counterMedia = 0 ;

        if (imagesInfo != null) {
            for (String reqUrl : imagesInfo) {

                //检查 图片资源是否已经存在
                String md5 = MD5Util.md5(reqUrl);
                Wrapper<FmbxMedia> wrMedia = new LambdaQueryWrapper<FmbxMedia>()
                        .eq(FmbxMedia::getMd5, md5)
                        .select(FmbxMedia::getMediaId)
                        .last(" limit 1 ");
                FmbxMedia mediaCheck = mediaService.getOne(wrMedia);

                Integer mediaId = null;

                if (mediaCheck == null) {

                    FmbxMedia mediaNew = new FmbxMedia();
                    String imgUrl = reqUrl;

                    mediaNew.setLength(0);

                    final String site = "fumubang.com";
                    final int i1 = imgUrl.indexOf(site);
                    final int i2 = imgUrl.indexOf("fumubang.net");
                    int idx = 0;
                    if (i1 != -1) {
                        idx = i1;
                    } else if (i2 != -1) {
                        idx = i2;
                    }

                    //如果是父母邦的图片
                    if (idx != -1) {
                        final String savePath = "/home/<USER>/fmb.photo" + imgUrl.substring(idx + site.length());
                        mediaNew.setSavePath(savePath);
                        File fTest = new File(savePath);
                        mediaNew.setUrl(imgUrl);

                        if (fTest.exists()) {
                            mediaNew.setLength((int) fTest.length());
                        }

                    }else{
                        // 不是父母邦图片
                        //本地文件存储准备名字
                        UploadFileOutInfo uploadFileOutInfo = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, "fmbupload", "jpg");

                        //下载图片
                        final byte[] bytes = HttpUtil.downloadBytes(imgUrl);

                        try {
                            FileUtils.writeByteArrayToFile(new File(uploadFileOutInfo.getFilePath()),bytes);

                            mediaNew.setSavePath(uploadFileOutInfo.getFilePath());
                            mediaNew.setUrl(uploadFileOutInfo.getOutUrl());

                            imgUrl = uploadFileOutInfo.getOutUrl() ;

                        } catch (IOException ex) {
                            long errorid = SnowflakeIdWorker.getInstance().nextId();
                            logger.error("errorid " + errorid, ex);

                            continue;
                        }

                    }

                    mediaNew.setSourceFileName(StringUtils.substringAfterLast(imgUrl, "/"));
                    mediaNew.setCtime(LocalDateTime.now());
                    final String fileExtType = StringUtils.substringAfterLast(imgUrl, ".");
                    mediaNew.setFileExtType("");
                    if (fileExtType != null && fileExtType.length()<10) {
                        mediaNew.setFileExtType(fileExtType);
                    }

                    mediaNew.setAdminType(sessionUser.getUserType());
                    mediaNew.setAdminUid(sessionUser.getUserId());
                    mediaNew.setVideoSnapUrl("");
                    mediaNew.setMd5(md5);
                    mediaService.save(mediaNew);
                    counterMedia ++ ;

                    mediaId = mediaNew.getMediaId();

                } else {
                    mediaId = mediaCheck.getMediaId();
                }

                if (mediaId == null) {
                    continue;
                }

                Wrapper<FmbxBpsMedia> wrBpsMedia = new LambdaQueryWrapper<FmbxBpsMedia>()
                        .eq(FmbxBpsMedia::getMediaId, mediaId)
                        .eq(FmbxBpsMedia::getBpsId,bpsId)
                        ;
                if (bpsMediaService.count(wrBpsMedia) == 0) {

                    FmbxBpsMedia bpsMedia = new FmbxBpsMedia();

                    bpsMedia.setBpsId(bpsId);
                    bpsMedia.setRoomId(0);
                    bpsMedia.setMediaId(mediaId);
                    bpsMedia.setType(1);
                    bpsMedia.setDescription("");
                    bpsMedia.setGroupval(9);
                    bpsMedia.setStatus(1);
                    bpsMedia.setAdminUid(sessionUser.getUserId());
                    bpsMedia.setAdminType(sessionUser.getUserType());
                    bpsMedia.setAdminName(sessionUser.getUsername());
                    bpsMedia.setCtime(LocalDateTime.now());
                    bpsMedia.setLastopAdminInfo(sessionUser.myInfo());
                    bpsMedia.setRankval(0);

                    bpsMediaService.save(bpsMedia);

                    counterBpsMedia++;
                }

            }
        }

        resultMap.put("result",1) ;
        resultMap.put("message","新增商户图片资源"+counterBpsMedia  ) ;

    }



    public static String doPhpEvalStringMapJob(HashMap<String, Object> par){

        return HttpUtil.post("http://m.fumubang."+ FmbServerConfig.netOrCom() +"/ajax/ajax_analysis_array_group_str",par) ;

    }
}
