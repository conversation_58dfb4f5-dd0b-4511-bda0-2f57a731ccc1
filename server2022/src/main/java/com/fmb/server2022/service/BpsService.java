package com.fmb.server2022.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.ui.FmbCollectionsUtils;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.domain.BpContractDomain;
import com.fmb.server2022.domain.BpsInfoDomain;
import com.fmb.server2022.domain.BpsMediaFilterInfo;
import com.fmb.server2022.domain.BpsMediaInfoDomain;
import com.fmb.server2022.domain.BpsRoomInfoDomain;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxBpBpsBind;
import com.fmb.server2022.fmbx.entity.FmbxBpContract;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxBpsContent;
import com.fmb.server2022.fmbx.entity.FmbxBpsMedia;
import com.fmb.server2022.fmbx.entity.FmbxBpsRoom;
import com.fmb.server2022.fmbx.entity.FmbxMedia;
import com.fmb.server2022.fmbx.entity.FmbxOutfileInfo;
import com.fmb.server2022.fmbx.entity.FmbxThirdsysUser;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpBpsBindService;
import com.fmb.server2022.fmbx.service.IFmbxBpContractService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpsContentService;
import com.fmb.server2022.fmbx.service.IFmbxBpsMediaService;
import com.fmb.server2022.fmbx.service.IFmbxBpsRoomService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxMediaService;
import com.fmb.server2022.fmbx.service.IFmbxOutfileInfoService;
import com.fmb.server2022.fmbx.service.IFmbxThirdsysUserService;
import com.fmb.server2022.mapper.BpsDao;
import com.fmb.server2022.reqdomain.ReqBpContract;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqBpsMedia;
import com.fmb.server2022.reqdomain.ReqBpsMediaInfo;
import com.fmb.server2022.reqdomain.ReqBpsMediaUpdate;
import com.fmb.server2022.reqdomain.ReqBpsRoomInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomSku;
import com.fmb.server2022.reqdomain.ReqFmbxBpsRoom;
import com.fmb.server2022.reqdomain.ReqQuickBpBindBps;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.VideoThumbTaker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.JedisPool;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.fmb.basic.user.SessionUser.SESSIONUSER_BP;
import static com.fmb.server2022.domain.BpContractDomain.bpCreateTypeMap;
import static com.fmb.server2022.domain.BpsMediaInfoDomain.bpsTypeMap;
import static com.fmb.server2022.service.kafka.FmbKafkaTopic.PUBLIC_GROUP_BPS_FLAG_UPDATE;

@Service
public class BpsService {


    private static Logger logger = LoggerFactory.getLogger(BpsService.class);

    @Autowired
    IFmbxBpBpsBindService bindService;

    @Autowired
    IFmbxBpMainService mainService;

    @Autowired
    IFmbxBpsService bpsService;

    @Autowired
    IFmbxBpsContentService bpsContentService;

    @Autowired
    IFmbxBpsRoomService roomService;


    @Autowired
    IFmbxMediaService mediaService ;


    @Autowired
    IFmbxBpsMediaService bpsMediaService ;

    @Autowired
    private FmbAdminTokenService loginService;

    @Autowired
    IFmbxBpContractService contractService ;

    @Autowired
    IFmbxOutfileInfoService outfileInfoService ;

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxThirdsysUserService thirdsysUserService ;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    BpsDao bpsDao;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    UserUnionService userUnionService ;

    @Autowired
    BpsService  self ;


    @Autowired
    IFmbxBpsRoomService fmbxBpsRoomService;

    @Autowired
    IFmbxBpsMediaService fmbxBpsMediaService;

    @Autowired
    IFmbxBpsService fmbxBpsService;


    public static HashSet<String> videoExt  ;
    static {
        videoExt = new HashSet<>() ;
        videoExt.add("mp4");
        videoExt.add("mov");
    }

//    public static boolean ENV_PRO = "prod".equals( SpringUtils.getProperty("spring.profiles.active=prod")) ;

    @Value("${fmb.fmbImgDir}")
    private String fmbImgDir;
    @Value("${fmb.fmbImgUrl}")
    private String fmbImgUrl;

    /**
     * 通过商户ID 获取该商户关联的供应商信息
     * @param cpsId 商户ID
     * @param resultMap 结果
     */
    public void getBpListByCpsId(Integer cpsId,HashMap<String, Object> resultMap) {
        List<Map<String,Object>> list = bpsDao.selectBpListByCpsId(cpsId);
        resultMap.put("list",list);
    }


//    private static String fmbImgDir = SpringUtils.getProperty("fmb.fmbImgDir");
//    private static String fmbImgUrl = SpringUtils.getProperty("fmb.fmbImgUrl");

    /**
     * 增加 商户
     *
     * @param resultMap
     * @param input1
     */
    @DSTransactional
    public void addBps(HashMap<String, Object> resultMap, FmbxBps input1) {

        resultMap.put("result",0) ;
        SessionUser nowUserInfo = userUnionService.nowUserInfo();


//        FmbxBps input1 = new FmbxBps() ;

        LambdaQueryWrapper<FmbxBps> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBps::getName, input1.getName());
        FmbxBps one = bpsService.getOne(wr);
        if (one != null) {
            throw new BadLogicException("商户名称重复");
        }

        LocalDateTime now = LocalDateTime.now();
        input1.setBpsId(null);

        //现在就是酒店类型的
//        input1.setBpsType(1);
        input1.setStatus(1);
        input1.setCtime(now);
        input1.setUtime(now);
        input1.setFlagBasic(1);
        input1.setCreateUid(nowUserInfo.getUserId());
        input1.setAdminUid(nowUserInfo.getUserId());

        boolean save = bpsService.save(input1);
        if (save) {

            FmbxBpsContent content = new FmbxBpsContent();

            BeanUtils.copyProperties(input1, content);


            if(StringUtils.isBlank(content.getIntroduce())){
                content.setIntroduce("");
            }

            content.setBpsId(input1.getBpsId());
            content.setCtime(now);
            content.setUtime(now);

            bpsContentService.save(content);
            resultMap.put("content_id", content.getId());

        }

        resultMap.put("bps_id", input1.getBpsId());
        resultMap.put("result",1) ;

    }

    /**
     * 通过供应商ID判断 房间信息 图片信息 视频信息 是否都有数据 同时更新表中flg
     * @param bps_id
     * @return
     */
    public void updateBpsInfoStatus(int bps_id){

        logger.info("updateBpsInfoStatus bps_id={}",bps_id);

        int room_flg = 0, pic_flg = 0, video_flg = 0;
        //查询房间
        LambdaQueryWrapper<FmbxBpsRoom> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxBpsRoom::getRoomStatus,1);
        wr.eq(FmbxBpsRoom::getBpsId,bps_id);
        long dbBpRoom = fmbxBpsRoomService.count(wr);
        if(dbBpRoom > 0){
            room_flg = 1;
        }

        List<Integer> ids = Arrays.asList(1,2);
        Wrapper<FmbxBpsMedia> wr5 = new QueryWrapper<FmbxBpsMedia>().select(" type, count(id) as num ").in("type", ids).eq("bps_id",bps_id).eq("status",1).groupBy("type");
        List<Map<String,Object>> countMap = bpsMediaService.listMaps(wr5);
        if(countMap != null){
            for(Map item : countMap ){
                if((int)item.get("type") == 1 ){//图片
                    pic_flg = 1;
                }else if((int)item.get("type") == 2 ){//视频
                    video_flg = 1;
                }
            }
        }

        //更新赋值数据
        FmbxBps fmbxBps = new FmbxBps();
        fmbxBps.setBpsId(bps_id);
        fmbxBps.setFlagRoom(room_flg);
        fmbxBps.setFlagPic(pic_flg);
        fmbxBps.setFlagVideo(video_flg);
        boolean update_flg = fmbxBpsService.updateById(fmbxBps);
    }




    /**
     * 修改商家信息
     * @param resultMap
     * @param input1
     */
    public void updateBps(HashMap<String, Object> resultMap, FmbxBps input1) {

        resultMap.put("result",0) ;
        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        //先查查是否存在
        FmbxBps dbAim = bpsService.getById(input1.getBpsId());
        if (dbAim == null) {
            throw new BadLogicException("商户不存在"+input1.getBpsId());
        }

        checkUserCanEditBps(nowUserInfo ,dbAim.getBpsId());

        LocalDateTime now = LocalDateTime.now();

        input1.setUtime(now);
        input1.setAdminUid(nowUserInfo.getUserId());

        boolean b = bpsService.updateById(input1);
        if (b) {
            resultMap.put("bps_update", 1);
        }

        FmbxBpsContent fmbxBpsContent = getFmbxBpsContent(input1.getBpsId());
        if (fmbxBpsContent != null) {
            fmbxBpsContent.setIntroduce(input1.getIntroduce());
            fmbxBpsContent.setPolicy(input1.getPolicy());
            fmbxBpsContent.setFoodInfo(input1.getFoodInfo());
            fmbxBpsContent.setServices(input1.getServices());
            fmbxBpsContent.setUtime(now);

            boolean b1 = bpsContentService.updateById(fmbxBpsContent);
            if (b1) {
                resultMap.put("bps_content_update", 1);
            }
        }else {

            FmbxBpsContent fmbxBpsContent1 = new FmbxBpsContent();


            BeanUtils.copyProperties(input1,fmbxBpsContent1);

            fmbxBpsContent1.setId(null);


            fmbxBpsContent1.setCtime(now);
            fmbxBpsContent1.setUtime(now);

            boolean b1 = bpsContentService.save(fmbxBpsContent1);
            if (b1) {
                resultMap.put("bps_content_id", fmbxBpsContent1.getId());
            }

        }

        updatBpsFlagAfterEdit(input1.getBpsId()) ;

        resultMap.put("result",1) ;


    }

    /**
     * 发异步通知  检查更新商户 状态
     * @param bpsId
     */
    private void updatBpsFlagAfterEdit(Integer bpsId) {
        HashMap<String, Object> info = new HashMap<>();
        info.put("bpsid", bpsId);
        FmbKafkaProducerService.sendUsePublicGroup(info,PUBLIC_GROUP_BPS_FLAG_UPDATE,""+bpsId) ;
    }

    /**
     * 对比当前用户 是否可以编辑商户
     * @param nowUserInfo
     * @param bpsid
     */
    public void checkUserCanEditBps(SessionUser nowUserInfo,Integer bpsid) {
        if (nowUserInfo.getUserType()==SessionUser.SESSIONUSER_BP && bpsid!=null ) {

            FmbxBps byId = bpsService.getById(bpsid);

            if ( byId==null ||  nowUserInfo.getUserId().intValue()!=byId.getBpidOfEdit().intValue()) {
                throw new BadLogicException("您无权修改该商户");
            }
        }
    }


    public void bpsInfo(HashMap<String, Object> resultMap,  Integer bpsid,Integer simple) {


        resultMap.put("result",0) ;
        FmbxBps bps = bpsService.getById(bpsid);

        if(simple==1){

            resultMap.put("bpsname", bps.getName());
            resultMap.put("bpsTypeStr", bpsTypeMap.get(bps.getBpsType()));

            return ;
        }

        FmbxBpsContent contentServiceOne = getFmbxBpsContent(bpsid);

        resultMap.put("bps", bps);
        resultMap.put("bpscontent", contentServiceOne);

        resultMap.put("result",1) ;

    }

    private FmbxBpsContent getFmbxBpsContent(Integer bpsid) {
        LambdaQueryWrapper<FmbxBpsContent> wrap = new LambdaQueryWrapper<>();
        wrap.eq(FmbxBpsContent::getBpsId, bpsid);
        return bpsContentService.getOne(wrap);
    }

    /**
     * 增加房型
     * @param resultMap
     * @param input1
     */
    public void saveOrUpdateRoom(HashMap<String, Object> resultMap, ReqFmbxBpsRoom input1) {

        resultMap.put("result",0) ;
        FmbxBps one = bpsService.getById(input1.getBpsId());
        if (one == null) {
            throw new BadLogicException("商户不存在");
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,one.getBpsId()) ;

        FmbxBpsRoom dbRoom = roomService.getById(input1.getRoomId());

        LocalDateTime now = LocalDateTime.now();

        //修改房型
        if (dbRoom != null) {

            //更新房型文本信息
            BeanUtils.copyProperties(input1,dbRoom);

            dbRoom.setAdminUid(nowUserInfo.getUserId());
            dbRoom.setAdminType(nowUserInfo.getUserType());
            dbRoom.setUtime(now);
            roomService.updateById(dbRoom) ;


            //查询现有房型图片
            ReqBpsMediaInfo req = new ReqBpsMediaInfo();

            req.setType(1);
            req.setBps_id(dbRoom.getBpsId());
            req.setRoomId(input1.getRoomId());
            req.setGroupval(10);
            //数据库里现有的
            List<BpsMediaInfoDomain> mediaInfo = bpsDao.selectBpsMediaInfo(req);

            //最终需要的
            Set<String> resultMedias = input1.getRoomImages().stream().map(r -> r.getMediaId() + "").collect(Collectors.toSet());

            //找到 已经不存在的
            ArrayList<Integer> aimDel = new ArrayList<>();
            for (BpsMediaInfoDomain bpsMediaInfoDomain : mediaInfo) {

                if (!resultMedias.contains(bpsMediaInfoDomain.getMediaId().toString())) {
                    aimDel.add(bpsMediaInfoDomain.getId()) ;
                }
            }
            //删除已经去掉的
            bpsMediaService.removeByIds(aimDel) ;


            //
            ReqBpsMedia reqMedia = new ReqBpsMedia();

            reqMedia.setBpsId(input1.getBpsId());
            reqMedia.setRoomId(input1.getRoomId());
            List<Integer> collect = input1.getRoomImages().stream().map(r -> r.getMediaId()).collect(Collectors.toList());
            reqMedia.setMediaIds(collect);
            reqMedia.setDescription(dbRoom.getRoomName());
            reqMedia.setGroupval(10);
            reqMedia.setType(1);

            self.saveBpsMedia(reqMedia);


            resultMap.put("update", 1);


        }else {


            //新增
            input1.setRoomId(null);

            //现在就是酒店类型的
//        input1.setBpsType(1);
            input1.setRoomStatus(1);
            input1.setCtime(now);
//        input1.setUtime(now);
            input1.setAdminUid(nowUserInfo.getUserId());
            input1.setAdminType(nowUserInfo.getUserType());

            boolean save = roomService.save(input1);
            if (save) {

                //更新商户 房屋完成 度标识
                FmbxBps up = new FmbxBps();
                up.setBpsId(input1.getBpsId());
                up.setFlagRoom(1);
                bpsService.updateById(up);

                ReqBpsMedia reqMedia = new ReqBpsMedia();

                reqMedia.setBpsId(input1.getBpsId());
                reqMedia.setRoomId(input1.getRoomId());
                List<Integer> collect = input1.getRoomImages().stream().map(r -> r.getMediaId()).collect(Collectors.toList());
                reqMedia.setMediaIds(collect);
                reqMedia.setDescription(input1.getRoomName());
                reqMedia.setGroupval(0);
                reqMedia.setType(1);


                self.saveBpsMedia(reqMedia);


                resultMap.put("roomid", input1.getRoomId());
                resultMap.put("add", 1);

            }
        }

        updatBpsFlagAfterEdit(input1.getBpsId()) ;
        resultMap.put("result",1) ;

    }

    /**
     * 房间详情
     * @param resultMap
     * @param roomid
     */
    public void roomDetail(HashMap<String, Object> resultMap, Integer roomid) {

        FmbxBpsRoom room = roomService.getById(roomid);

        if (room == null) {
            throw new BadLogicException("房型错误");
        }
        resultMap.put("rooom", room);

        ReqBpsMediaInfo req = new ReqBpsMediaInfo();

        req.setType(1);
        req.setBps_id(room.getBpsId());
        req.setGroupval(10);
        req.setRoomId(roomid);

        req.setFrom(1);

        List<BpsMediaInfoDomain> mediaInfo = bpsDao.selectBpsMediaInfo(req);

        resultMap.put("mediaInfo",mediaInfo) ;


    }

    /**
     * 快速创建 供应商和 商户和的定关系
     *
     * @param resultMap
     * @param input
     */
    public void quickBpBindBps(HashMap<String, Object> resultMap, ReqQuickBpBindBps input) {

        resultMap.put("result",0) ;
        FmbxBpMain bpMain = mainService.getById(input.getBpid());
        if (bpMain == null) {
            throw new BadLogicException("供应商不存在:" + input.getBpsid());
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        FmbxBps dbBps = bpsService.getById(input.getBpsid());

        //商户已经存在
        LocalDateTime now = LocalDateTime.now();
        if (dbBps != null) {

            //如果 已经存在的商户 可编辑供应商和输入不同 就更新
            if (input.getCanEdit() == 1 && dbBps.getBpidOfEdit().intValue() != input.getBpid().intValue()) {
                FmbxBps up = new FmbxBps();
                up.setBpsId(input.getBpsid());
                up.setBpidOfEdit(input.getBpid());
                up.setUtime(now);

                //更新商户的 可编辑供应商
                bpsService.updateById(up);

            }

            //绑定关系处理
            {
                Integer bpsid = input.getBpsid();
                Integer bpid = input.getBpid();
                int userId = nowUserInfo.getUserId();

                doBindJob(bpsid, bpid, userId);
            }

        } else {
            //商户不存在

            FmbxBps fmbxBps = new FmbxBps();
            if (input.getCanEdit() == 1) {
                fmbxBps.setBpidOfEdit(input.getBpid());
            }

            fmbxBps.setName(input.getBpsName());
            fmbxBps.setBpsType(1);
            fmbxBps.setStatus(1);
            fmbxBps.setCtime(now);
            fmbxBps.setCreateUid(nowUserInfo.getUserId());
            fmbxBps.setUtime(now);
            fmbxBps.setAdminUid(nowUserInfo.getUserId());

            bpsService.save(fmbxBps);
            doBindJob(fmbxBps.getBpsId(), input.getBpid(), nowUserInfo.getUserId());

        }

        resultMap.put("result", 1);

    }

    /**
     * 供应商绑商户
     * @param bpsid
     * @param bpid
     * @param userId
     */
    private void doBindJob(Integer bpsid, Integer bpid, int userId) {
        LambdaQueryWrapper<FmbxBpBpsBind> bindWrap = new LambdaQueryWrapper<>();
        bindWrap.eq(FmbxBpBpsBind::getBpId, bpid)
                .eq(FmbxBpBpsBind::getBpsId, bpsid);

        if (bindService.getOne(bindWrap) == null) {
            FmbxBpBpsBind fmbxBpBpsBind = new FmbxBpBpsBind();

            fmbxBpBpsBind.setBpId(bpid);
            fmbxBpBpsBind.setBpsId(bpsid);
            fmbxBpBpsBind.setAdminUid(userId);
            fmbxBpBpsBind.setCtime(LocalDateTime.now());

            bindService.save(fmbxBpBpsBind);
        }
    }

    @Autowired
    ExcelService excelService ;

    /**
     * 商户查询
     */

    public void selectBpsInfo(HashMap<String, Object> resultMap, ReqBpsInfo req) {



            if (req.getDoExport().intValue()==0) {


                if (req.getReqSource()==1) {
                    IPage<Integer> page = new Page<>(req.getPageno(), req.getPagesize());
                    IPage<Integer> rlist = bpsDao.selectBpsInfo(page, req);

                    List<BpsInfoDomain> lists = new ArrayList<>() ;
                    if (!rlist.getRecords().isEmpty()) {
                        lists = bpsDao.selectBpsInfoStr(rlist.getRecords());
                    }

                    resultMap.put("list", lists);
                    resultMap.put("total", rlist.getTotal());
                    resultMap.put("pages", rlist.getPages());
                    resultMap.put("size", rlist.getSize());
                    resultMap.put("current", rlist.getCurrent());
                }
                if (req.getReqSource()==2) {
                    List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(req);

                    for (BpsInfoDomain infoDomain : lists) {
                        if ( req.getBpId().intValue()==infoDomain.getBpidOfEdit().intValue()) {
                            infoDomain.setEdit(1);
                        }else {
                            infoDomain.setEdit(0);
                        }
                    }

                    resultMap.put("list", lists);
                }


            }else {

                List<BpsInfoDomain> lists = bpsDao.selectBpsInfoR(req);

                UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists, BpsInfoDomain.class, "商户信息", "selectBpsInfo");

                resultMap.put("excelInfo",uploadFileOutInfo) ;

            }







    }

    public HashMap upload(MultipartFile uploadFile) {


        SessionUser userInfo = userUnionService.nowUserInfo();
        HashMap<String, Object> map = new HashMap<>();

        String originalFilename = uploadFile.getOriginalFilename();
        //取扩展名
        String ext = "" ;
        if(originalFilename.indexOf('.')!=-1){
            ext = StringUtils.substringAfterLast(originalFilename,".") ;
        }

        String type = "fmbupload" ;
        if (userInfo.getUserType()==SESSIONUSER_BP) {
            type = "bpsupload" ;
        }

        //本地文件存储准备名字
        UploadFileOutInfo uploadFileOutInfo = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, type, ext);

        try {
            IOUtils.copy(uploadFile.getInputStream(),new FileOutputStream(uploadFileOutInfo.getFilePath()));
        } catch (IOException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);

            throw new BadLogicException("文件读取异常");
        }



        FmbxMedia media = new FmbxMedia();

        media.setSavePath(uploadFileOutInfo.getFilePath());
        media.setSourceFileName(uploadFile.getOriginalFilename());
        media.setCtime(LocalDateTime.now());
        media.setFileExtType(ext);

        //如果是视频
        if (isVideoExt(ext)) {

            UploadFileOutInfo snap = UploadFileUtil.prepareFilePath(fmbImgDir, fmbImgUrl, "videoSnap", "jpg");
            final HashMap<String, String> stringObjectHashMap = VideoThumbTaker.readVideAndCreateKeySnap(uploadFileOutInfo.getFilePath(),
                    snap.getFilePath());
            if(stringObjectHashMap.containsKey("result") && "1".equals( stringObjectHashMap.get("result"))){
                String img = stringObjectHashMap.get("img");
                media.setVideoSnapUrl(snap.getOutUrl());
            }
        }


        media.setLength((int) uploadFile.getSize());

        media.setAdminType(userInfo.getUserType());
        media.setAdminUid(userInfo.getUserId());
        media.setUrl(uploadFileOutInfo.getOutUrl());

        boolean save = mediaService.save(media);
        if (save) {
            map.put("mediaId", media.getMediaId());
            map.put("url", uploadFileOutInfo.getOutUrl());
            map.put("name", media.getSourceFileName());
        }


        return map;
    }

    public  static boolean  isVideoExt(String ext){
        return videoExt.contains(ext.toLowerCase()) ;
    }


    /**
     *   同一个 商户 资源存在更新排序值, 不存在就保存
     * @param req
     * @return
     */
    public int saveBpsMedia(ReqBpsMedia req) {

        SessionUser userInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(userInfo,req.getBpsId());


        int addNum  = 0 ;
        LocalDateTime now = LocalDateTime.now();

        int rankVal =  10 ;
        for (Integer mediaId : req.getMediaIds()) {
            FmbxMedia dbMedia = mediaService.getById(mediaId);

            //资源找不到 跳过
            if (dbMedia == null) {
//                throw new BadLogicException("资源不存在"+mediaId);
                continue;
            }


            //检查是否存在
            LambdaQueryWrapper<FmbxBpsMedia> testWra = Wrappers.lambdaQuery(FmbxBpsMedia.class)
                    .eq(FmbxBpsMedia::getMediaId, mediaId)
                    .eq(FmbxBpsMedia::getBpsId, req.getBpsId())
                    .eq(req.getGroupval()!=null,FmbxBpsMedia::getGroupval,req.getGroupval())
                    .eq(req.getRoomId()!=null,FmbxBpsMedia::getRoomId,req.getRoomId())

                    ;

            FmbxBpsMedia testExistMedia = bpsMediaService.getOne(testWra);


            // 如果存在  更新排序值
            if (testExistMedia!=null) {

                FmbxBpsMedia upm = new FmbxBpsMedia();
                upm.setId(testExistMedia.getId());
                upm.setRankval(rankVal);
                upm.setUtime(now);
                bpsMediaService.updateById(upm) ;
                
            }else {

                //不存在 就新增
                FmbxBpsMedia media = new FmbxBpsMedia();
                BeanUtils.copyProperties(req,media);

                //如果有房型 id  写死Groupval 10
                //图片分组:1-外观,2-大厅,3-餐饮,4家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
                if(req.getRoomId()!=null ){
                    media.setGroupval(10);
                }


                media.setMediaId(mediaId);
                media.setAdminUid(userInfo.getUserId());
                media.setAdminType(userInfo.getUserType());
                media.setAdminName(userInfo.myInfo());

                media.setStatus(1);
                media.setCtime(now);
                media.setUtime(now);
                media.setRankval(rankVal);

                if (bpsMediaService.save(media)) {
                    addNum ++ ;
                }
            }

            rankVal +=10 ;

        }

        /**
         * 商户media 信息添加后 ,关联更新 商户的flag
         */
        if (addNum>0) {
            FmbxBps upBps = new FmbxBps();
            upBps.setBpsId(req.getBpsId());

            if (req.getType().intValue()==1) {
                upBps.setFlagPic(1);
            }else
            if (req.getType().intValue()==2) {
                upBps.setFlagVideo(1);
            }
            boolean b = bpsService.updateById(upBps);
        }


        updatBpsFlagAfterEdit(req.getBpsId()) ;


        return addNum ;
        

    }

    public void addBpsMedia(HashMap<String,Object> resultMap, ReqBpsMedia req) {

        resultMap.put("addNum", saveBpsMedia(req) );

    }

/**
 * 商户图片查询
 */
   
public void selectBpsMediaInfo(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {

    req.setFrom(2);
      if (req.getDoExport().intValue()==0) {
        IPage<BpsMediaInfoDomain> page = new Page<>(req.getPageno(),req.getPagesize());
        IPage<BpsMediaInfoDomain> rlist = bpsDao.selectBpsMediaInfo(page, req);

        resultMap.put("list", rlist.getRecords());
        resultMap.put("total", rlist.getTotal());
        resultMap.put("pages", rlist.getPages());
        resultMap.put("size", rlist.getSize());
        resultMap.put("current", rlist.getCurrent());
       }else{

          List<BpsMediaInfoDomain> lists = bpsDao.selectBpsMediaInfo(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,BpsMediaInfoDomain.class, "商户图片查询", "selectBpsMediaInfo");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }


    /**
     * 商户图片   筛选条件查询
     * @param resultMap
     * @param req
     */
    @DS(FmbConstants.DB_slave)
    public void bpsMediaSimpleInfo(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {

        LambdaQueryWrapper<FmbxBps> wrp = new LambdaQueryWrapper<>();
        wrp.select(FmbxBps::getName)
                .eq(FmbxBps::getBpsId,req.getBps_id()) ;

        FmbxBps one = bpsService.getOne(wrp);

        resultMap.put("bpsname",one.getName()) ;

        List<BpsMediaFilterInfo> bpsMediaFilterInfos = bpsDao.selectCreateInfo(req.getBps_id());

        resultMap.put("createInfo",bpsMediaFilterInfos) ;

    }

    public void bpsMediaToggleStatus(HashMap<String,Object> resultMap, ReqBpsMediaInfo req) {


        resultMap.put("result",0) ;
        SessionUser userInfo = userUnionService.nowUserInfo();

        updateBpsMediaOperate(resultMap, req,  userInfo);

    }




    public void updateBpsMediaOperate(HashMap<String, Object> resultMap, ReqBpsMediaInfo req, SessionUser userInfo) {
        FmbxBpsMedia dbInfo = bpsMediaService.getById(req.getId());

        if (dbInfo == null) {
            throw new BadLogicException("商户资源不存在:"+req.getId());
        }

        checkUserCanEditBps(userInfo,dbInfo.getBpsId());

        if (dbInfo.getStatus()==1) {
            dbInfo.setStatus(0);
        }else {
            dbInfo.setStatus(1);
        }

        dbInfo.setUtime(LocalDateTime.now());
        dbInfo.setLastopAdminInfo( SessionUser.userTypeMap.get( userInfo.getUserType()) + ":" + userInfo.getUsername() +"("+ userInfo
                .getUserId() +")");

        boolean b = bpsMediaService.updateById(dbInfo);


        updatBpsFlagAfterEdit(dbInfo.getBpsId()) ;

        if (b) {
            resultMap.put("result",1) ;
        }
    }

    @DS(FmbConstants.DB_slave)
    public void bpsMediaDetail(HashMap<String, Object> resultMap, Integer id) {
        FmbxBpsMedia bpsMedia = bpsMediaService.getById(id);

        if (bpsMedia == null || bpsMedia.getStatus().intValue()==0) {
            throw new BadLogicException("图片不存在或状态异常");
        }
        FmbxMedia media = mediaService.getById(bpsMedia.getMediaId());


        resultMap.put("bpsMedia",bpsMedia) ;
        resultMap.put("media",media) ;
        resultMap.put("groupval",FmbCollectionsUtils.mapToList(BpsMediaInfoDomain.groupvalMap)) ;


    }

    public void bpsMediaUpdate(HashMap<String, Object> resultMap, ReqBpsMediaUpdate req) {
        resultMap.put("result",0) ;
        SessionUser userInfo = userUnionService.nowUserInfo();

        FmbxBpsMedia bpsMedia = bpsMediaService.getById(req.getId());

        if (bpsMedia == null || bpsMedia.getStatus().intValue()==0 ) {
            throw new BadLogicException("图片不存在或状态异常");
        }

        FmbxBps bps = bpsService.getById(bpsMedia.getBpsId());
        //如果是 供应商后台 而且 当前供应商ID和登录的供应商ID 不一样 就不能编辑

        if (SESSIONUSER_BP==userInfo.getUserType() &&  bps.getBpidOfEdit().intValue()!=userInfo.getUserId()) {
            throw new BadLogicException("您不能编辑该商户信息");
        }

        FmbxBpsMedia dbup = new FmbxBpsMedia();

        BeanUtils.copyProperties(req,dbup);


        dbup.setLastopAdminInfo(userInfo.myInfo());

        boolean b = bpsMediaService.updateById(dbup);
        if (b) {
            resultMap.put("result",1) ;
        }

    }

/**
 * 商户管理房型管理
 */

@DS(FmbConstants.DB_slave)
public void selectBpsRoomInfo(HashMap<String,Object> resultMap, ReqBpsRoomInfo req) {

      if (req.getDoExport().intValue()==0) {

          List<BpsRoomInfoDomain> lists = bpsDao.selectBpsRoomInfo(req);

          resultMap.put("rooms", lists);

//        IPage<BpsRoomInfoDomain> page = new Page<>(req.getPageno(),req.getPagesize());
//        IPage<BpsRoomInfoDomain> rlist = bpsDao.selectBpsRoomInfo(page, req);
//
//        resultMap.put("list", rlist.getRecords());
//        resultMap.put("total", rlist.getTotal());
//        resultMap.put("pages", rlist.getPages());
//        resultMap.put("size", rlist.getSize());
//        resultMap.put("current", rlist.getCurrent());
       }else{

          List<BpsRoomInfoDomain> lists = bpsDao.selectBpsRoomInfo(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,BpsRoomInfoDomain.class, "商户管理房型管理", "selectBpsRoomInfo");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }

    @DS(FmbConstants.DB_slave)
    public void selectBpsRoomSku(HashMap<String,Object> resultMap, ReqBpsRoomSku req) {
        List<BpsRoomInfoDomain> lists = new ArrayList<>();
        if(req.getBpsId()!=null && req.getBpsId()>0) {
            lists = bpsDao.selectBpsRoomSku(req);
        }else{
            if(req.getXaid()!=null && req.getXaid()>0){
                LambdaQueryWrapper<FmbxActivity> wr = new LambdaQueryWrapper<>();
                wr.eq(FmbxActivity::getXaid, req.getXaid());
                FmbxActivity a = activityService.getOne(wr);

                req.setBpsId(a.getBpsId());
                lists = bpsDao.selectBpsRoomSku(req);
            }
        }
        resultMap.put("rooms", lists);

    }


    /**
     * 房型状态 开关
     * @param resultMap
     * @param roomid
     */
    public void bpsRoomStatusToggle(HashMap<String,Object> resultMap, Integer roomid) {

        resultMap.put("result",0) ;
        FmbxBpsRoom dbroom = roomService.getById(roomid);
        if (dbroom==null) {
            throw new BadLogicException("房型不存在" + roomid);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,dbroom.getBpsId());

        FmbxBpsRoom update = new FmbxBpsRoom();
        update.setRoomId(roomid);
        if (dbroom.getRoomStatus()==1) {
            update.setRoomStatus( 0 );
        }else {
            update.setRoomStatus( 1 );
        }
        update.setUtime(LocalDateTime.now());
        update.setAdminUid(nowUserInfo.getUserId());
        boolean b = roomService.updateById(update);

        updatBpsFlagAfterEdit(dbroom.getBpsId()) ;

        if (b) {
            resultMap.put("result",1) ;
            resultMap.put("nowStatus",update.getRoomStatus()) ;
        }

    }

    /**
     * 复制房型
     * @param resultMap
     * @param roomid
     */
    public void bpsRoomCopy(HashMap<String,Object> resultMap, Integer roomid) {

        resultMap.put("result",0) ;
        FmbxBpsRoom dbroom = roomService.getById(roomid);
        if (dbroom==null) {
            throw new BadLogicException("房型不存在" + roomid);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        checkUserCanEditBps(nowUserInfo,dbroom.getBpsId());

        FmbxBpsRoom nroom = new FmbxBpsRoom();

        BeanUtils.copyProperties(dbroom,nroom);

        nroom.setRoomId(null);


        nroom.setRoomName(dbroom.getRoomName()+"(复制)");
        nroom.setRoomStatus(0);
        nroom.setAdminUid(nowUserInfo.getUserId());
        LocalDateTime now = LocalDateTime.now();
        nroom.setCtime(now);
        nroom.setUtime(now);

        boolean save = roomService.save(nroom);
        if (save) {
            resultMap.put("newRoomId",nroom.getRoomId());
            resultMap.put("result",1);
        }

    }

    /**
     * 商户 状态 开关
     * @param resultMap
     * @param bpsId
     */
    public void bpsStatusToggle(HashMap<String,Object> resultMap, Integer bpsId) {

        resultMap.put("result",0) ;
        FmbxBps bpsDB = bpsService.getById(bpsId);
        if (bpsDB == null) {
            throw new BadLogicException("商户不存在"+bpsId);
        }

        SessionUser nowUserInfo = userUnionService.nowUserInfo();

        FmbxBps nbps = new FmbxBps();
        nbps.setBpsId(bpsId);
        if (bpsDB.getStatus().intValue()==1) {
            nbps.setStatus(0);
        }else {
            nbps.setStatus(1);
        }

        nbps.setUtime(LocalDateTime.now());
        nbps.setAdminUid(nowUserInfo.getUserId());
        boolean b = bpsService.updateById(nbps);
        if (b) {
            resultMap.put("result",1) ;
            resultMap.put("nowStatus",nbps.getStatus()) ;
        }

    }

/**
 * 供应商合同管理
 */
   @DS(FmbConstants.DB_slave)
public void selectBpContract(HashMap<String,Object> resultMap, ReqBpContract req) {

      if (req.getDoExport().intValue()==0) {
        IPage<BpContractDomain> page = new Page<>(req.getPageno(),req.getPagesize());
        IPage<BpContractDomain> rlist = bpsDao.selectBpContract(page, req);

        resultMap.put("list", rlist.getRecords());
        resultMap.put("total", rlist.getTotal());
        resultMap.put("pages", rlist.getPages());
        resultMap.put("size", rlist.getSize());
        resultMap.put("current", rlist.getCurrent());
       }else{

          List<BpContractDomain> lists = bpsDao.selectBpContract(req);

            UploadFileOutInfo uploadFileOutInfo = ExcelService.genExcel(lists,BpContractDomain.class, "供应商合同管理", "selectBpContract");

            resultMap.put("excelInfo",uploadFileOutInfo) ;
       }

    }


    /**
     * 查看合同详情
     * @param resultMap
     * @param bpcId
     */
    @DS(FmbConstants.DB_slave)
    public void contractDatail(HashMap<String,Object> resultMap, Integer bpcId) {

        FmbxBpContract bpContract = contractService.getById(bpcId);
        if (bpContract==null) {
            throw new BadLogicException("合同不存在"+bpcId);
        }

        LambdaQueryWrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<>();
        wr.select(FmbxOutfileInfo::getSourceFileName,FmbxOutfileInfo::getLength,FmbxOutfileInfo::getUrl,FmbxOutfileInfo::getType,FmbxOutfileInfo::getExtString1)

                .eq(FmbxOutfileInfo::getModelId,bpcId)
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract") ;

        List<FmbxOutfileInfo> list = outfileInfoService.list(wr);

        JSONArray ops = JSONArray.parseArray(bpContract.getDingdingOpDetail());
        bpContract.setDingdingOpDetail(null);

        resultMap.put("files",list);
        resultMap.put("contract",bpContract) ;
        resultMap.put("operations",ops) ;


    }

    /**
     * 定时检查是否有 即将过期的 合同,并发邮件
     *
     */
    public void checkContractWillEnd() {

        LambdaQueryWrapper<FmbxBpContract> wrap = new LambdaQueryWrapper<>();

        //查找符合条件的邮件
        wrap.select(FmbxBpContract::getBpcId,FmbxBpContract::getBpId, FmbxBpContract::getBpcNumber,FmbxBpContract::getCreaterDingUserid,
                FmbxBpContract::getBpCreateType
        ,FmbxBpContract::getBpTemplateType,FmbxBpContract::getBpStartDate,FmbxBpContract::getBpEndDate,FmbxBpContract::getPrePay)

                .in(FmbxBpContract::getBpcRunuingStatus,Arrays.asList(5,6))
                .eq(FmbxBpContract::getBpcAllStatus,1)
                .eq(FmbxBpContract::getWillendCheckStatus,0)
                .lt(FmbxBpContract::getBpEndDate,LocalDateTime.now().plusDays(300).toLocalDate())
                .gt(FmbxBpContract::getBpEndDate,LocalDateTime.now().toLocalDate())

        ;

        List<FmbxBpContract> contractList = contractService.list(wrap);



        for (FmbxBpContract c : contractList) {
//            System.out.println(fmbxBpContract.getBpcId());

            genMailAndSend( c,1);

            //更新 标记值 已经发起过提醒了 
            FmbxBpContract up = new FmbxBpContract();
            up.setBpcId(c.getBpcId());
            up.setWillendCheckStatus(1);
            contractService.updateById(up);

        }

    }

    /**
     *
     * @param c  合同
     * @param type  1-即将终止,2-已经到期
     */
    private void genMailAndSend( FmbxBpContract c,int type) {
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String mailTitle = "【提醒】合同即将过期" ;

        if (type==2) {
            mailTitle = "【提醒】合同已终止" ;
        }



        String parterName = "";
        if (c.getBpId().intValue()!=0) {
            FmbxBpParterInfo parterInfo = parterInfoService.getById(c.getBpId());
            if (parterInfo != null) {
                parterName = parterInfo.getPartnerName() ;
            }
        }

        LambdaQueryWrapper<FmbxThirdsysUser> wr = new LambdaQueryWrapper<>();
        wr.eq(FmbxThirdsysUser::getThirdSysUid,c.getCreaterDingUserid());
        FmbxThirdsysUser thirdsysUser = thirdsysUserService.getOne(wr);

        String fqr = thirdsysUser!=null?thirdsysUser.getSysUsername():"" ;

        String mailContent = StrUtil.format("合同编号：{}，关联供应商：{} ，发起人：{}，合同签署方式：新签合同，合同类型：{}，有效期：{}至{}，预付款保证金：{}元"
                , c.getBpcNumber()
                , parterName
                , fqr
                , bpCreateTypeMap.get(c.getBpCreateType())
                , c.getBpStartDate().format(formater)
                , c.getBpEndDate().format(formater)
                , c.getPrePay()
        );


        ArrayList<String> strings = new ArrayList<>();
        strings.add(FmbConstants.LEAGAL_EMAIL) ;
        strings.add(thirdsysUser.getEmail());

        String[] emails = strings.toArray(new String[]{});


        MailService.sendMimeMail(new String[]{"<EMAIL>"},mailTitle,mailContent,null,false);


    }

    /**
     * 找到已经过期的合同 发送邮件通知
     */
    public void checkContractEnd() {



        LambdaQueryWrapper<FmbxBpContract> wrap = new LambdaQueryWrapper<>();

        //查找符合条件的邮件
        wrap.select(FmbxBpContract::getBpcId,FmbxBpContract::getBpId, FmbxBpContract::getBpcNumber,FmbxBpContract::getCreaterDingUserid,
                FmbxBpContract::getBpCreateType
                ,FmbxBpContract::getBpTemplateType,FmbxBpContract::getBpStartDate,FmbxBpContract::getBpEndDate,FmbxBpContract::getPrePay)
                .in(FmbxBpContract::getBpcRunuingStatus,Arrays.asList(5,6))
                .eq(FmbxBpContract::getBpcAllStatus,1)
                .lt(FmbxBpContract::getBpEndDate,LocalDateTime.now().toLocalDate())

        ;

        List<FmbxBpContract> contractList = contractService.list(wrap);


        for (FmbxBpContract c : contractList) {
//            System.out.println(c.getBpcId());

            genMailAndSend( c,2);


            //更新合同状态 为终结
            FmbxBpContract updb = new FmbxBpContract();
            updb.setBpcId(c.getBpcId());
            updb.setBpcAllStatus(2);
            contractService.updateById(updb);

        }


    }


    public void downLoadFinalContract(HashMap<String,Object> resultMap, Integer bpcId) {

        LambdaQueryWrapper<FmbxOutfileInfo> wr = new LambdaQueryWrapper<>();
        wr.select(FmbxOutfileInfo::getSourceFileName, FmbxOutfileInfo::getSavePath, FmbxOutfileInfo::getLength,FmbxOutfileInfo::getUrl,
                FmbxOutfileInfo::getType,
                FmbxOutfileInfo::getExtString1)

                .eq(FmbxOutfileInfo::getModelId,bpcId)
                .eq(FmbxOutfileInfo::getExtString1,"盖章合同上传")
                .eq(FmbxOutfileInfo::getModelType,"fmbx_bp_contract") ;

        FmbxOutfileInfo outFile = outfileInfoService.getOne(wr);

        if (outFile == null) {
            throw new BadLogicException("合同数据不存在"+bpcId);
        }

//        List<FmbxOutfileInfo> list = outfileInfoService.list(wr);
        File fileContract = new File(fmbImgDir + outFile.getSavePath());

//        System.out.println(fileContract.getAbsolutePath());

        if (!fileContract.exists()) {
            throw new BadLogicException("合同文件损坏");
        }


        resultMap.put("url",fmbImgUrl+"/"+outFile.getSavePath()) ;
        resultMap.put("sourceFileName",outFile.getSourceFileName()) ;
        resultMap.put("type",outFile.getType()) ;

//        HttpServletResponse response = ServletUtils.getResponse();
//        response.reset();
//        response.addHeader("Content-Disposition", "attachment;filename="+outFile.getSourceFileName());
//
//        try {
//            IOUtils.copy(new FileInputStream(fileContract),response.getOutputStream()) ;
//        } catch (IOException ex) {
//            long errorid = SnowflakeIdWorker.getInstance().nextId();
//            logger.error("errorid " + errorid, ex);
//            throw new BadLogicException("文件传输异常");
//        }

    }




}
