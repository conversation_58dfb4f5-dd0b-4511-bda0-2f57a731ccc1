
package com.fmb.server2022.service;


import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class FmbDBDictInfo {
    //总字典
    public static final Map<String, Map<String, String>> dict;
    //字段含义
    public static final Map<String, String> dictOfColumnNote;

    //fmbx_activity
    public static final Map<String, String> FMBX_ACTIVITY__TICKET_TYPE_Map;
    public static final Map<String, String> FMBX_ACTIVITY__PLAT_ID_Map;
    public static final Map<String, String> FMBX_ACTIVITY__BUSINESS_TYPE_Map;
    public static final Map<String, String> FMBX_ACTIVITY__IS_CONFIRM_Map;
    public static final Map<String, String> FMBX_ACTIVITY__FLAG_DELETE_Map;
    public static final Map<String, String> FMBX_ACTIVITY__FLAG_HAVE_VALID_SKU_Map;
    public static final Map<String, String> FMBX_ACTIVITY__FLAG_PUBLISH_Map;

    //fmbx_bp_contract
    public static final Map<String, String> FMBX_BP_CONTRACT__BPC_RUNUING_STATUS_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BPC_ALL_STATUS_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__CITY_ID_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BUSINESS_TYPE_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BP_TEMPLATE_TYPE_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BP_CREATE_TYPE_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BP_SEAL_TYPE_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__BP_SEAL_USE_INFO_Map;
    public static final Map<String, String> FMBX_BP_CONTRACT__WILLEND_CHECK_STATUS_Map;

    //fmbx_bp_main
    public static final Map<String, String> FMBX_BP_MAIN__PARTER_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MAIN__SETTLE_GEN_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MAIN__SETTLE_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MAIN__INVOICE_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MAIN__ACCOUNT_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MAIN__ACCOUNT_STATUS_Map;
    public static final Map<String, String> FMBX_BP_MAIN__CHECK_STATUS_Map;

    //fmbx_bp_media
    public static final Map<String, String> FMBX_BP_MEDIA__CREATE_USER_TYPE_Map;
    public static final Map<String, String> FMBX_BP_MEDIA__FILE_TYPE_Map;

    //fmbx_bp_parter_hotel_info
    public static final Map<String, String> FMBX_BP_PARTER_HOTEL_INFO__HOTEL_CONFIRM_TYPE_Map;

    //fmbx_bp_parter_info
    public static final Map<String, String> FMBX_BP_PARTER_INFO__NOEND_Map;

    //fmbx_bp_review_his
    public static final Map<String, String> FMBX_BP_REVIEW_HIS__STATUS_Map;

    //fmbx_bps
    public static final Map<String, String> FMBX_BPS__BPS_TYPE_Map;
    public static final Map<String, String> FMBX_BPS__STAR_LEVEL_Map;
    public static final Map<String, String> FMBX_BPS__TAKE_PET_Map;
    public static final Map<String, String> FMBX_BPS__STATUS_Map;
    public static final Map<String, String> FMBX_BPS__FLAG_BASIC_Map;
    public static final Map<String, String> FMBX_BPS__FLAG_ROOM_Map;
    public static final Map<String, String> FMBX_BPS__FLAG_PIC_Map;
    public static final Map<String, String> FMBX_BPS__FLAG_VIDEO_Map;

    //fmbx_bps_media
    public static final Map<String, String> FMBX_BPS_MEDIA__TYPE_Map;
    public static final Map<String, String> FMBX_BPS_MEDIA__GROUPVAL_Map;
    public static final Map<String, String> FMBX_BPS_MEDIA__STATUS_Map;
    public static final Map<String, String> FMBX_BPS_MEDIA__ADMIN_TYPE_Map;

    //fmbx_bps_room
    public static final Map<String, String> FMBX_BPS_ROOM__ADD_BED_TYPE_Map;
    public static final Map<String, String> FMBX_BPS_ROOM__SMOKE_TYPE_Map;
    public static final Map<String, String> FMBX_BPS_ROOM__BATHTUB_TYPE_Map;
    public static final Map<String, String> FMBX_BPS_ROOM__ROOM_STATUS_Map;
    public static final Map<String, String> FMBX_BPS_ROOM__ADMIN_TYPE_Map;

    //fmbx_date_table
    public static final Map<String, String> FMBX_DATE_TABLE__WEEKNUM_Map;
    public static final Map<String, String> FMBX_DATE_TABLE__HOLIDAY_TYPE_Map;

    //fmbx_hotel_reserve_sku
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__CAN_DIVIDE_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__FLAG_BUY_LIMIT_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__SELL_COUNT_DOWN_SHOW_FLAG_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__IS_RETURN_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__IS_AUTO_RETURN_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__FLAG_SELL_Map;
    public static final Map<String, String> FMBX_HOTEL_RESERVE_SKU__STATUS_Map;

    //fmbx_kafka_message
    public static final Map<String, String> FMBX_KAFKA_MESSAGE__TYPE_Map;
    public static final Map<String, String> FMBX_KAFKA_MESSAGE__STATUS_Map;

    //fmbx_media
    public static final Map<String, String> FMBX_MEDIA__ADMIN_TYPE_Map;

    //fmbx_outfile_info
    public static final Map<String, String> FMBX_OUTFILE_INFO__ADMIN_TYPE_Map;

    //fmbx_sku
    public static final Map<String, String> FMBX_SKU__SKU_TYPE_Map;

    //fmbx_suite
    public static final Map<String, String> FMBX_SUITE__PRICE_INPUT_TYPE_Map;
    public static final Map<String, String> FMBX_SUITE__FLAG_HAVE_ORDER_Map;
    public static final Map<String, String> FMBX_SUITE__FLAG_STANDARD_HOTEL_Map;
    public static final Map<String, String> FMBX_SUITE__FLAG_HOTEL_RESERVE_Map;
    public static final Map<String, String> FMBX_SUITE__BUY_LIMIT_Map;
    public static final Map<String, String> FMBX_SUITE__CERT_FLAG_Map;
    public static final Map<String, String> FMBX_SUITE__FLAG_RISK_WARNING_Map;
    public static final Map<String, String> FMBX_SUITE__IS_RETURN_Map;
    public static final Map<String, String> FMBX_SUITE__IS_AUTO_RETURN_Map;
    public static final Map<String, String> FMBX_SUITE__IS_INVOICE_Map;

    //fmbx_suite_price_operate
    public static final Map<String, String> FMBX_SUITE_PRICE_OPERATE__CHANGE_TYPE_Map;
    public static final Map<String, String> FMBX_SUITE_PRICE_OPERATE__ADMIN_TYPE_Map;

    //fmbx_suite_room_sku
    public static final Map<String, String> FMBX_SUITE_ROOM_SKU__FLAG_SELL_Map;
    public static final Map<String, String> FMBX_SUITE_ROOM_SKU__STATUS_Map;

    //fmbx_suite_stock
    public static final Map<String, String> FMBX_SUITE_STOCK__STATUS_Map;

    //fmbx_suite_stock_operate
    public static final Map<String, String> FMBX_SUITE_STOCK_OPERATE__CHANGE_TYPE_Map;
    public static final Map<String, String> FMBX_SUITE_STOCK_OPERATE__ADMIN_TYPE_Map;

    //fmbx_thirdsys_user
    public static final Map<String, String> FMBX_THIRDSYS_USER__STATUS_Map;

    static {
        //fmbx_activity 表开始
        {
            // 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "演出");
            map.put("2", "景点");
            map.put("3", "通用");
            map.put("4", "酒店");
            map.put("6", "新酒店");
            map.put("7", "新酒店预约");
            FMBX_ACTIVITY__TICKET_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 显示平台(find_in_set查询):0-全平台,1-h5,2-web,3-app,9-全部不展示,10-小程序
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "全平台");
            map.put("1", "h5");
            map.put("2", "web");
            map.put("3", "app");
            map.put("9", "全部不展示");
            map.put("10", "小程序");
            FMBX_ACTIVITY__PLAT_ID_Map = Collections.unmodifiableMap(map);
        }
        {
            // 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
            HashMap<String, String> map = new HashMap<>();
            map.put("2", "酒店");
            map.put("3", "北京活动票务");
            map.put("5", "上海活动票务");
            map.put("11", "北京长线");
            map.put("12", "上海长线");
            FMBX_ACTIVITY__BUSINESS_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "无");
            map.put("1", "客服跟进");
            map.put("2", "商户确认");
            map.put("3", "市场跟进");
            FMBX_ACTIVITY__IS_CONFIRM_Map = Collections.unmodifiableMap(map);
        }
        {
            // 删除:0-正常,1-被删除
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "正常");
            map.put("1", "被删除");
            FMBX_ACTIVITY__FLAG_DELETE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 售卖状态:0-未售(sku无效),1-在售
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "未售(sku无效)");
            map.put("1", "在售");
            FMBX_ACTIVITY__FLAG_HAVE_VALID_SKU_Map = Collections.unmodifiableMap(map);
        }
        {
            // 上下架状态:0-待发布,1-已上架,2-已下架
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "待发布");
            map.put("1", "已上架");
            map.put("2", "已下架");
            FMBX_ACTIVITY__FLAG_PUBLISH_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_activity 表结束
        //fmbx_bp_contract 表开始
        {
            // 审批中合同状态:0-发起申请,1-直属经理审批,2-法务审批,3-上海财务审批,4-CEO审批,5-合同归档提交,6-合同确认无误,7-拒绝
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "发起申请");
            map.put("1", "直属经理审批");
            map.put("2", "法务审批");
            map.put("3", "上海财务审批");
            map.put("4", "CEO审批");
            map.put("5", "合同归档提交");
            map.put("6", "合同确认无误");
            map.put("7", "拒绝");
            FMBX_BP_CONTRACT__BPC_RUNUING_STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 合同有效期状态:1-有效期内,2-已经过期
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "有效期内");
            map.put("2", "已经过期");
            FMBX_BP_CONTRACT__BPC_ALL_STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 分站:1-北京,2-上海
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "北京");
            map.put("2", "上海");
            FMBX_BP_CONTRACT__CITY_ID_Map = Collections.unmodifiableMap(map);
        }
        {
            // 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线,20-运营
            HashMap<String, String> map = new HashMap<>();
            map.put("2", "酒店");
            map.put("3", "北京活动票务");
            map.put("5", "上海活动票务");
            map.put("11", "北京长线");
            map.put("12", "上海长线");
            map.put("20", "运营");
            FMBX_BP_CONTRACT__BUSINESS_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 合同形式:1-标准合同,2-非标准合同
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "标准合同");
            map.put("2", "非标准合同");
            FMBX_BP_CONTRACT__BP_TEMPLATE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 合同类型:1-新建,2-续签,3-补充协议
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "新建");
            map.put("2", "续签");
            map.put("3", "补充协议");
            FMBX_BP_CONTRACT__BP_CREATE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 合同类型:1-父母邦先用印,2-商家先用印
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦先用印");
            map.put("2", "商家先用印");
            FMBX_BP_CONTRACT__BP_SEAL_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 用印情况:1-双方已盖章已归档,2-我方先盖章未归档,3-不予受理
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "双方已盖章已归档");
            map.put("2", "我方先盖章未归档");
            map.put("3", "不予受理");
            FMBX_BP_CONTRACT__BP_SEAL_USE_INFO_Map = Collections.unmodifiableMap(map);
        }
        {
            // 定期检查即将结束标记:1-检查过了,0-没检查
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "检查过了");
            map.put("0", "没检查");
            FMBX_BP_CONTRACT__WILLEND_CHECK_STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_contract 表结束
        //fmbx_bp_main 表开始
        {
            // 商家类型:1-酒店,2-其他
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "酒店");
            map.put("2", "其他");
            FMBX_BP_MAIN__PARTER_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 结算发起方式:1-系统自动,2-结算人员手动发起
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "系统自动");
            map.put("2", "结算人员手动发起");
            FMBX_BP_MAIN__SETTLE_GEN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 结算方式:1-下单时间,2-消费时间,3-入住时间
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "下单时间");
            map.put("2", "消费时间");
            map.put("3", "入住时间");
            FMBX_BP_MAIN__SETTLE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 发票类型:1-结算发票,2-佣金发票
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "结算发票");
            map.put("2", "佣金发票");
            FMBX_BP_MAIN__INVOICE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 账号类型:1-主账号,2-子账号
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "主账号");
            map.put("2", "子账号");
            FMBX_BP_MAIN__ACCOUNT_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 账号类型:1-正常,0-禁用
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "正常");
            map.put("0", "禁用");
            FMBX_BP_MAIN__ACCOUNT_STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 审核状态:1-待供应商完善,2-待商务审核,3-审核通过
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "待供应商完善");
            map.put("2", "待商务审核");
            map.put("3", "审核通过");
            FMBX_BP_MAIN__CHECK_STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_main 表结束
        //fmbx_bp_media 表开始
        {
            // 用户的类型:1-父母邦后台用户,2-商家后台用户
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台用户");
            map.put("2", "商家后台用户");
            FMBX_BP_MEDIA__CREATE_USER_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 文件类型:1-营业执照,2-特殊经营
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "营业执照");
            map.put("2", "特殊经营");
            FMBX_BP_MEDIA__FILE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_media 表结束
        //fmbx_bp_parter_hotel_info 表开始
        {
            // 酒店订房确认方式:1-微信QQ,2-订房邮箱,3-订房传真,4-其他
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "微信QQ");
            map.put("2", "订房邮箱");
            map.put("3", "订房传真");
            map.put("4", "其他");
            FMBX_BP_PARTER_HOTEL_INFO__HOTEL_CONFIRM_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_parter_hotel_info 表结束
        //fmbx_bp_parter_info 表开始
        {
            // 永久有效:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_BP_PARTER_INFO__NOEND_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_parter_info 表结束
        //fmbx_bp_review_his 表开始
        {
            // 状态:1-待审,2-审核通过,3-审核拒绝
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "待审");
            map.put("2", "审核通过");
            map.put("3", "审核拒绝");
            FMBX_BP_REVIEW_HIS__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bp_review_his 表结束
        //fmbx_bps 表开始
        {
            // 商户类型:1-酒店,2-其他
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "酒店");
            map.put("2", "其他");
            FMBX_BPS__BPS_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 星级档次:1-豪华型,2-高档型,3-舒适型
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "豪华型");
            map.put("2", "高档型");
            map.put("3", "舒适型");
            FMBX_BPS__STAR_LEVEL_Map = Collections.unmodifiableMap(map);
        }
        {
            // 携带宠物:1-可以,0-不可以
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "可以");
            map.put("0", "不可以");
            FMBX_BPS__TAKE_PET_Map = Collections.unmodifiableMap(map);
        }
        {
            // 状态:0-屏蔽,1-正常
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "屏蔽");
            map.put("1", "正常 ");
            FMBX_BPS__STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 基础信息完成度:1-完成,0-未完成
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "完成");
            map.put("0", "未完成");
            FMBX_BPS__FLAG_BASIC_Map = Collections.unmodifiableMap(map);
        }
        {
            // 房型信息完成度:1-完成,0-未完成
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "完成");
            map.put("0", "未完成");
            FMBX_BPS__FLAG_ROOM_Map = Collections.unmodifiableMap(map);
        }
        {
            // 图片信息完成度:1-完成,0-未完成
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "完成");
            map.put("0", "未完成");
            FMBX_BPS__FLAG_PIC_Map = Collections.unmodifiableMap(map);
        }
        {
            // 视频信息完成度:1-完成,0-未完成
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "完成");
            map.put("0", "未完成");
            FMBX_BPS__FLAG_VIDEO_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bps 表结束
        //fmbx_bps_media 表开始
        {
            // 类型:1-图片,2-视频
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "图片");
            map.put("2", "视频");
            FMBX_BPS_MEDIA__TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 图片分组:1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "外观");
            map.put("2", "大厅");
            map.put("3", "餐饮");
            map.put("4", "家庭亲子");
            map.put("5", "休闲");
            map.put("6", "健身房");
            map.put("7", "公共区域");
            map.put("8", "周边");
            map.put("9", "其他");
            map.put("10", "房间");
            FMBX_BPS_MEDIA__GROUPVAL_Map = Collections.unmodifiableMap(map);
        }
        {
            // 是否有效:0-无效,1-有效
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "无效");
            map.put("1", "有效");
            FMBX_BPS_MEDIA__STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 后台用户类型:1-父母邦后台,2-供应商后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "供应商后台");
            FMBX_BPS_MEDIA__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bps_media 表结束
        //fmbx_bps_room 表开始
        {
            // 加床:1-未知,2-不可加床,3-免费加不含早,4-免费加含早,5-收费加床不含早,6-收费加床含早
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "未知");
            map.put("2", "不可加床");
            map.put("3", "免费加不含早");
            map.put("4", "免费加含早");
            map.put("5", "收费加床不含早");
            map.put("6", "收费加床含早");
            FMBX_BPS_ROOM__ADD_BED_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 吸烟信息:1-可吸烟,2-禁烟,3-部分客房可吸烟
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "可吸烟");
            map.put("2", "禁烟");
            map.put("3", "部分客房可吸烟");
            FMBX_BPS_ROOM__SMOKE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 有无浴缸:0-没有,1-有
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "没有");
            map.put("1", "有");
            FMBX_BPS_ROOM__BATHTUB_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 是否启用:0-屏蔽,1-启用
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "屏蔽");
            map.put("1", "启用");
            FMBX_BPS_ROOM__ROOM_STATUS_Map = Collections.unmodifiableMap(map);
        }
        {
            // 后台用户类型:1-父母邦后台,2-供应商后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "供应商后台");
            FMBX_BPS_ROOM__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_bps_room 表结束
        //fmbx_date_table 表开始
        {
            // 星期几:1-星期1,2-星期2,3-星期2,4-星期4,5-星期5,6-星期6,7-星期日
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "星期1");
            map.put("2", "星期2");
            map.put("3", "星期2");
            map.put("4", "星期4");
            map.put("5", "星期5");
            map.put("6", "星期6");
            map.put("7", "星期日");
            FMBX_DATE_TABLE__WEEKNUM_Map = Collections.unmodifiableMap(map);
        }
        {
            // 节日类型:0-非节日,1-阳历假日,2-农历假日
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "非节日");
            map.put("1", "阳历假日");
            map.put("2", "农历假日");
            FMBX_DATE_TABLE__HOLIDAY_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_date_table 表结束
        //fmbx_hotel_reserve_sku 表开始
        {
            // 是否可拆分:0-不能拆分,1-可以拆分
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不能拆分");
            map.put("1", "可以拆分");
            FMBX_HOTEL_RESERVE_SKU__CAN_DIVIDE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 是否限购:0-不限,1-限购
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不限");
            map.put("1", "限购");
            FMBX_HOTEL_RESERVE_SKU__FLAG_BUY_LIMIT_Map = Collections.unmodifiableMap(map);
        }
        {
            // 售卖倒计时显示:0-不显示,1-显示
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不显示");
            map.put("1", "显示");
            FMBX_HOTEL_RESERVE_SKU__SELL_COUNT_DOWN_SHOW_FLAG_Map = Collections.unmodifiableMap(map);
        }
        {
            // 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不支持");
            map.put("1", "有效期结束前未使用可退");
            map.put("2", "过期后未使用可退");
            map.put("3", "未使用随时退 (有效期内+过期后)");
            FMBX_HOTEL_RESERVE_SKU__IS_RETURN_Map = Collections.unmodifiableMap(map);
        }
        {
            // 开启自助退款:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_HOTEL_RESERVE_SKU__IS_AUTO_RETURN_Map = Collections.unmodifiableMap(map);
        }
        {
            // 售买状态(库存和当前时间在售卖时间内的结果):0-无效,1-有效
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "无效");
            map.put("1", "有效");
            FMBX_HOTEL_RESERVE_SKU__FLAG_SELL_Map = Collections.unmodifiableMap(map);
        }
        {
            // 上下架状态:0-已删除,1-已上架,2-已下架,3-待完善
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "已删除");
            map.put("1", "已上架");
            map.put("2", "已下架");
            map.put("3", "待完善");
            FMBX_HOTEL_RESERVE_SKU__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_hotel_reserve_sku 表结束
        //fmbx_kafka_message 表开始
        {
            // 状态:1-执行异常,2-超时warn
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "执行异常");
            map.put("2", "超时warn");
            FMBX_KAFKA_MESSAGE__TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 状态:0-新建,1-已经重试
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "新建");
            map.put("1", "已经重试");
            FMBX_KAFKA_MESSAGE__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_kafka_message 表结束
        //fmbx_media 表开始
        {
            // 后台用户类型:1-父母邦后台,2-供应商后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "供应商后台");
            FMBX_MEDIA__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_media 表结束
        //fmbx_outfile_info 表开始
        {
            // 后台用户类型:1-父母邦后台,2-商家后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "商家后台");
            FMBX_OUTFILE_INFO__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_outfile_info 表结束
        //fmbx_sku 表开始
        {
            // sku类型:1-酒店日历房,2-房券
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "酒店日历房");
            map.put("2", "房券");
            FMBX_SKU__SKU_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_sku 表结束
        //fmbx_suite 表开始
        {
            // 价格输入方式:1-加价模式,2-整价模式
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "加价模式");
            map.put("2", "整价模式");
            FMBX_SUITE__PRICE_INPUT_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 是否有订单:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_SUITE__FLAG_HAVE_ORDER_Map = Collections.unmodifiableMap(map);
        }
        {
            // 日历房:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_SUITE__FLAG_STANDARD_HOTEL_Map = Collections.unmodifiableMap(map);
        }
        {
            // 预售房券:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_SUITE__FLAG_HOTEL_RESERVE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 购买限制:0-无限制,1-有限制
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "无限制");
            map.put("1", "有限制");
            FMBX_SUITE__BUY_LIMIT_Map = Collections.unmodifiableMap(map);
        }
        {
            // 证件资料:0-不需要,1-需提供一位用户身份信息,2-需提供所有用户身份信息
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不需要");
            map.put("1", "需提供一位用户身份信息");
            map.put("2", "需提供所有用户身份信息");
            FMBX_SUITE__CERT_FLAG_Map = Collections.unmodifiableMap(map);
        }
        {
            // 风险提示:0-不需要,1-需要
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不需要");
            map.put("1", "需要");
            FMBX_SUITE__FLAG_RISK_WARNING_Map = Collections.unmodifiableMap(map);
        }
        {
            // 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不支持");
            map.put("1", "有效期结束前未使用可退");
            map.put("2", "过期后未使用可退");
            map.put("3", "未使用随时退 (有效期内+过期后)");
            FMBX_SUITE__IS_RETURN_Map = Collections.unmodifiableMap(map);
        }
        {
            // 开启自助退款:0-否,1-是
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "否");
            map.put("1", "是");
            FMBX_SUITE__IS_AUTO_RETURN_Map = Collections.unmodifiableMap(map);
        }
        {
            // 发票政策:0-不支持开票,1-父母邦开票,2-商家开票
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "不支持开票");
            map.put("1", "父母邦开票");
            map.put("2", "商家开票");
            FMBX_SUITE__IS_INVOICE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_suite 表结束
        //fmbx_suite_price_operate 表开始
        {
            // 修改类型:1-加价模式,2-整价模式,3-整价指定日期,4-excel上传,5-加价模式切换清除数据
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "加价模式");
            map.put("2", "整价模式");
            map.put("3", "整价指定日期");
            map.put("4", "excel上传");
            map.put("5", "加价模式切换清除数据");
            FMBX_SUITE_PRICE_OPERATE__CHANGE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 后台用户类型:1-父母邦后台,2-供应商后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "供应商后台");
            FMBX_SUITE_PRICE_OPERATE__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_suite_price_operate 表结束
        //fmbx_suite_room_sku 表开始
        {
            // 售买状态:0-无效（无可售日期）,1-有效
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "无效（无可售日期）");
            map.put("1", "有效");
            FMBX_SUITE_ROOM_SKU__FLAG_SELL_Map = Collections.unmodifiableMap(map);
        }
        {
            // 上下架状态:0-已删除,1-已上架,2-已下架
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "已删除");
            map.put("1", "已上架");
            map.put("2", "已下架");
            FMBX_SUITE_ROOM_SKU__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_suite_room_sku 表结束
        //fmbx_suite_stock 表开始
        {
            // 状态:0-未设置,1-开放,2-关闭
            HashMap<String, String> map = new HashMap<>();
            map.put("0", "未设置");
            map.put("1", "开放");
            map.put("2", "关闭");
            FMBX_SUITE_STOCK__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_suite_stock 表结束
        //fmbx_suite_stock_operate 表开始
        {
            // 修改类型:1-连续日期,2-指定日期
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "连续日期");
            map.put("2", "指定日期");
            FMBX_SUITE_STOCK_OPERATE__CHANGE_TYPE_Map = Collections.unmodifiableMap(map);
        }
        {
            // 后台用户类型:1-父母邦后台,2-供应商后台
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "父母邦后台");
            map.put("2", "供应商后台");
            FMBX_SUITE_STOCK_OPERATE__ADMIN_TYPE_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_suite_stock_operate 表结束
        //fmbx_thirdsys_user 表开始
        {
            // 是否在职:1-在职,0-不在职
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "在职");
            map.put("0", "不在职");
            FMBX_THIRDSYS_USER__STATUS_Map = Collections.unmodifiableMap(map);
        }
        //fmbx_thirdsys_user 表结束

        {

//字段含义开始
            {
                Map<String, String> map = new HashMap<>();
                map.put("fmbx_activity__ticket_type", "票务类型");
                map.put("fmbx_activity__plat_id", "显示平台(find_in_set查询)");
                map.put("fmbx_activity__business_type", "所属业务线");
                map.put("fmbx_activity__is_confirm", "二次确认方式");
                map.put("fmbx_activity__flag_delete", "删除");
                map.put("fmbx_activity__flag_have_valid_sku", "售卖状态");
                map.put("fmbx_activity__flag_publish", "上下架状态");
                map.put("fmbx_bp_contract__bpc_runuing_status", "审批中合同状态");
                map.put("fmbx_bp_contract__bpc_all_status", "合同有效期状态");
                map.put("fmbx_bp_contract__city_id", "分站");
                map.put("fmbx_bp_contract__business_type", "所属业务线");
                map.put("fmbx_bp_contract__bp_template_type", "合同形式");
                map.put("fmbx_bp_contract__bp_create_type", "合同类型");
                map.put("fmbx_bp_contract__bp_seal_type", "合同类型");
                map.put("fmbx_bp_contract__bp_seal_use_info", "用印情况");
                map.put("fmbx_bp_contract__willend_check_status", "定期检查即将结束标记");
                map.put("fmbx_bp_main__parter_type", "商家类型");
                map.put("fmbx_bp_main__settle_gen_type", "结算发起方式");
                map.put("fmbx_bp_main__settle_type", "结算方式");
                map.put("fmbx_bp_main__invoice_type", "发票类型");
                map.put("fmbx_bp_main__account_type", "账号类型");
                map.put("fmbx_bp_main__account_status", "账号类型");
                map.put("fmbx_bp_main__check_status", "审核状态");
                map.put("fmbx_bp_media__create_user_type", "用户的类型");
                map.put("fmbx_bp_media__file_type", "文件类型");
                map.put("fmbx_bp_parter_hotel_info__hotel_confirm_type", "酒店订房确认方式");
                map.put("fmbx_bp_parter_info__noend", "永久有效");
                map.put("fmbx_bp_review_his__status", "状态");
                map.put("fmbx_bps__bps_type", "商户类型");
                map.put("fmbx_bps__star_level", "星级档次");
                map.put("fmbx_bps__take_pet", "携带宠物");
                map.put("fmbx_bps__status", "状态");
                map.put("fmbx_bps__flag_basic", "基础信息完成度");
                map.put("fmbx_bps__flag_room", "房型信息完成度");
                map.put("fmbx_bps__flag_pic", "图片信息完成度");
                map.put("fmbx_bps__flag_video", "视频信息完成度");
                map.put("fmbx_bps_media__type", "类型");
                map.put("fmbx_bps_media__groupval", "图片分组");
                map.put("fmbx_bps_media__status", "是否有效");
                map.put("fmbx_bps_media__admin_type", "后台用户类型");
                map.put("fmbx_bps_room__add_bed_type", "加床");
                map.put("fmbx_bps_room__smoke_type", "吸烟信息");
                map.put("fmbx_bps_room__bathtub_type", "有无浴缸");
                map.put("fmbx_bps_room__room_status", "是否启用");
                map.put("fmbx_bps_room__admin_type", "后台用户类型");
                map.put("fmbx_date_table__weeknum", "星期几");
                map.put("fmbx_date_table__holiday_type", "节日类型");
                map.put("fmbx_hotel_reserve_sku__can_divide", "是否可拆分");
                map.put("fmbx_hotel_reserve_sku__flag_buy_limit", "是否限购");
                map.put("fmbx_hotel_reserve_sku__sell_count_down_show_flag", "售卖倒计时显示");
                map.put("fmbx_hotel_reserve_sku__is_return", "退货政策");
                map.put("fmbx_hotel_reserve_sku__is_auto_return", "开启自助退款");
                map.put("fmbx_hotel_reserve_sku__flag_sell", "售买状态(库存和当前时间在售卖时间内的结果)");
                map.put("fmbx_hotel_reserve_sku__status", "上下架状态");
                map.put("fmbx_kafka_message__type", "状态");
                map.put("fmbx_kafka_message__status", "状态");
                map.put("fmbx_media__admin_type", "后台用户类型");
                map.put("fmbx_outfile_info__admin_type", "后台用户类型");
                map.put("fmbx_sku__sku_type", "sku类型");
                map.put("fmbx_suite__price_input_type", "价格输入方式");
                map.put("fmbx_suite__flag_have_order", "是否有订单");
                map.put("fmbx_suite__flag_standard_hotel", "日历房");
                map.put("fmbx_suite__flag_hotel_reserve", "预售房券");
                map.put("fmbx_suite__buy_limit", "购买限制");
                map.put("fmbx_suite__cert_flag", "证件资料");
                map.put("fmbx_suite__flag_risk_warning", "风险提示");
                map.put("fmbx_suite__is_return", "退货政策");
                map.put("fmbx_suite__is_auto_return", "开启自助退款");
                map.put("fmbx_suite__is_invoice", "发票政策");
                map.put("fmbx_suite_price_operate__change_type", "修改类型");
                map.put("fmbx_suite_price_operate__admin_type", "后台用户类型");
                map.put("fmbx_suite_room_sku__flag_sell", "售买状态");
                map.put("fmbx_suite_room_sku__status", "上下架状态");
                map.put("fmbx_suite_stock__status", "状态");
                map.put("fmbx_suite_stock_operate__change_type", "修改类型");
                map.put("fmbx_suite_stock_operate__admin_type", "后台用户类型");
                map.put("fmbx_thirdsys_user__status", "是否在职");
                dictOfColumnNote = Collections.unmodifiableMap(map);
            }

        }

//总字典的初始化
        {
            Map<String, Map<String, String>> map = new HashMap<>();
            map.put("fmbx_activity__ticket_type", FMBX_ACTIVITY__TICKET_TYPE_Map);
            map.put("fmbx_activity__plat_id", FMBX_ACTIVITY__PLAT_ID_Map);
            map.put("fmbx_activity__business_type", FMBX_ACTIVITY__BUSINESS_TYPE_Map);
            map.put("fmbx_activity__is_confirm", FMBX_ACTIVITY__IS_CONFIRM_Map);
            map.put("fmbx_activity__flag_delete", FMBX_ACTIVITY__FLAG_DELETE_Map);
            map.put("fmbx_activity__flag_have_valid_sku", FMBX_ACTIVITY__FLAG_HAVE_VALID_SKU_Map);
            map.put("fmbx_activity__flag_publish", FMBX_ACTIVITY__FLAG_PUBLISH_Map);
            map.put("fmbx_bp_contract__bpc_runuing_status", FMBX_BP_CONTRACT__BPC_RUNUING_STATUS_Map);
            map.put("fmbx_bp_contract__bpc_all_status", FMBX_BP_CONTRACT__BPC_ALL_STATUS_Map);
            map.put("fmbx_bp_contract__city_id", FMBX_BP_CONTRACT__CITY_ID_Map);
            map.put("fmbx_bp_contract__business_type", FMBX_BP_CONTRACT__BUSINESS_TYPE_Map);
            map.put("fmbx_bp_contract__bp_template_type", FMBX_BP_CONTRACT__BP_TEMPLATE_TYPE_Map);
            map.put("fmbx_bp_contract__bp_create_type", FMBX_BP_CONTRACT__BP_CREATE_TYPE_Map);
            map.put("fmbx_bp_contract__bp_seal_type", FMBX_BP_CONTRACT__BP_SEAL_TYPE_Map);
            map.put("fmbx_bp_contract__bp_seal_use_info", FMBX_BP_CONTRACT__BP_SEAL_USE_INFO_Map);
            map.put("fmbx_bp_contract__willend_check_status", FMBX_BP_CONTRACT__WILLEND_CHECK_STATUS_Map);
            map.put("fmbx_bp_main__parter_type", FMBX_BP_MAIN__PARTER_TYPE_Map);
            map.put("fmbx_bp_main__settle_gen_type", FMBX_BP_MAIN__SETTLE_GEN_TYPE_Map);
            map.put("fmbx_bp_main__settle_type", FMBX_BP_MAIN__SETTLE_TYPE_Map);
            map.put("fmbx_bp_main__invoice_type", FMBX_BP_MAIN__INVOICE_TYPE_Map);
            map.put("fmbx_bp_main__account_type", FMBX_BP_MAIN__ACCOUNT_TYPE_Map);
            map.put("fmbx_bp_main__account_status", FMBX_BP_MAIN__ACCOUNT_STATUS_Map);
            map.put("fmbx_bp_main__check_status", FMBX_BP_MAIN__CHECK_STATUS_Map);
            map.put("fmbx_bp_media__create_user_type", FMBX_BP_MEDIA__CREATE_USER_TYPE_Map);
            map.put("fmbx_bp_media__file_type", FMBX_BP_MEDIA__FILE_TYPE_Map);
            map.put("fmbx_bp_parter_hotel_info__hotel_confirm_type", FMBX_BP_PARTER_HOTEL_INFO__HOTEL_CONFIRM_TYPE_Map);
            map.put("fmbx_bp_parter_info__noend", FMBX_BP_PARTER_INFO__NOEND_Map);
            map.put("fmbx_bp_review_his__status", FMBX_BP_REVIEW_HIS__STATUS_Map);
            map.put("fmbx_bps__bps_type", FMBX_BPS__BPS_TYPE_Map);
            map.put("fmbx_bps__star_level", FMBX_BPS__STAR_LEVEL_Map);
            map.put("fmbx_bps__take_pet", FMBX_BPS__TAKE_PET_Map);
            map.put("fmbx_bps__status", FMBX_BPS__STATUS_Map);
            map.put("fmbx_bps__flag_basic", FMBX_BPS__FLAG_BASIC_Map);
            map.put("fmbx_bps__flag_room", FMBX_BPS__FLAG_ROOM_Map);
            map.put("fmbx_bps__flag_pic", FMBX_BPS__FLAG_PIC_Map);
            map.put("fmbx_bps__flag_video", FMBX_BPS__FLAG_VIDEO_Map);
            map.put("fmbx_bps_media__type", FMBX_BPS_MEDIA__TYPE_Map);
            map.put("fmbx_bps_media__groupval", FMBX_BPS_MEDIA__GROUPVAL_Map);
            map.put("fmbx_bps_media__status", FMBX_BPS_MEDIA__STATUS_Map);
            map.put("fmbx_bps_media__admin_type", FMBX_BPS_MEDIA__ADMIN_TYPE_Map);
            map.put("fmbx_bps_room__add_bed_type", FMBX_BPS_ROOM__ADD_BED_TYPE_Map);
            map.put("fmbx_bps_room__smoke_type", FMBX_BPS_ROOM__SMOKE_TYPE_Map);
            map.put("fmbx_bps_room__bathtub_type", FMBX_BPS_ROOM__BATHTUB_TYPE_Map);
            map.put("fmbx_bps_room__room_status", FMBX_BPS_ROOM__ROOM_STATUS_Map);
            map.put("fmbx_bps_room__admin_type", FMBX_BPS_ROOM__ADMIN_TYPE_Map);
            map.put("fmbx_date_table__weeknum", FMBX_DATE_TABLE__WEEKNUM_Map);
            map.put("fmbx_date_table__holiday_type", FMBX_DATE_TABLE__HOLIDAY_TYPE_Map);
            map.put("fmbx_hotel_reserve_sku__can_divide", FMBX_HOTEL_RESERVE_SKU__CAN_DIVIDE_Map);
            map.put("fmbx_hotel_reserve_sku__flag_buy_limit", FMBX_HOTEL_RESERVE_SKU__FLAG_BUY_LIMIT_Map);
            map.put("fmbx_hotel_reserve_sku__sell_count_down_show_flag", FMBX_HOTEL_RESERVE_SKU__SELL_COUNT_DOWN_SHOW_FLAG_Map);
            map.put("fmbx_hotel_reserve_sku__is_return", FMBX_HOTEL_RESERVE_SKU__IS_RETURN_Map);
            map.put("fmbx_hotel_reserve_sku__is_auto_return", FMBX_HOTEL_RESERVE_SKU__IS_AUTO_RETURN_Map);
            map.put("fmbx_hotel_reserve_sku__flag_sell", FMBX_HOTEL_RESERVE_SKU__FLAG_SELL_Map);
            map.put("fmbx_hotel_reserve_sku__status", FMBX_HOTEL_RESERVE_SKU__STATUS_Map);
            map.put("fmbx_kafka_message__type", FMBX_KAFKA_MESSAGE__TYPE_Map);
            map.put("fmbx_kafka_message__status", FMBX_KAFKA_MESSAGE__STATUS_Map);
            map.put("fmbx_media__admin_type", FMBX_MEDIA__ADMIN_TYPE_Map);
            map.put("fmbx_outfile_info__admin_type", FMBX_OUTFILE_INFO__ADMIN_TYPE_Map);
            map.put("fmbx_sku__sku_type", FMBX_SKU__SKU_TYPE_Map);
            map.put("fmbx_suite__price_input_type", FMBX_SUITE__PRICE_INPUT_TYPE_Map);
            map.put("fmbx_suite__flag_have_order", FMBX_SUITE__FLAG_HAVE_ORDER_Map);
            map.put("fmbx_suite__flag_standard_hotel", FMBX_SUITE__FLAG_STANDARD_HOTEL_Map);
            map.put("fmbx_suite__flag_hotel_reserve", FMBX_SUITE__FLAG_HOTEL_RESERVE_Map);
            map.put("fmbx_suite__buy_limit", FMBX_SUITE__BUY_LIMIT_Map);
            map.put("fmbx_suite__cert_flag", FMBX_SUITE__CERT_FLAG_Map);
            map.put("fmbx_suite__flag_risk_warning", FMBX_SUITE__FLAG_RISK_WARNING_Map);
            map.put("fmbx_suite__is_return", FMBX_SUITE__IS_RETURN_Map);
            map.put("fmbx_suite__is_auto_return", FMBX_SUITE__IS_AUTO_RETURN_Map);
            map.put("fmbx_suite__is_invoice", FMBX_SUITE__IS_INVOICE_Map);
            map.put("fmbx_suite_price_operate__change_type", FMBX_SUITE_PRICE_OPERATE__CHANGE_TYPE_Map);
            map.put("fmbx_suite_price_operate__admin_type", FMBX_SUITE_PRICE_OPERATE__ADMIN_TYPE_Map);
            map.put("fmbx_suite_room_sku__flag_sell", FMBX_SUITE_ROOM_SKU__FLAG_SELL_Map);
            map.put("fmbx_suite_room_sku__status", FMBX_SUITE_ROOM_SKU__STATUS_Map);
            map.put("fmbx_suite_stock__status", FMBX_SUITE_STOCK__STATUS_Map);
            map.put("fmbx_suite_stock_operate__change_type", FMBX_SUITE_STOCK_OPERATE__CHANGE_TYPE_Map);
            map.put("fmbx_suite_stock_operate__admin_type", FMBX_SUITE_STOCK_OPERATE__ADMIN_TYPE_Map);
            map.put("fmbx_thirdsys_user__status", FMBX_THIRDSYS_USER__STATUS_Map);
            dict = Collections.unmodifiableMap(map);
        }

    }

}