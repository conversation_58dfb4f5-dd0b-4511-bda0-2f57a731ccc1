package com.fmb.server2022.service.schedule.impel;

import com.fmb.server2022.fmbx.entity.FmbxScheduleJob;
import com.fmb.server2022.service.schedule.ConsumeResult;
import org.springframework.stereotype.Service;

@Service(value = "test01ConsumeJob")
public class Test01ConsumeJob implements ConsumeJob {
    @Override
    public ConsumeResult processJob(FmbxScheduleJob job) {

//        System.out.println(job.toString());

        return ConsumeResult.success("ok");
    }
}
