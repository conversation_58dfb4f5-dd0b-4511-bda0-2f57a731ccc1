package com.fmb.server2022.service;


import com.fmb.util.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.ArrayList;

import static com.fmb.basic.FmbConstants.IMPORTANT_INFO_LOGGER;

@Service
@Slf4j
public class MailService  {

    private static JavaMailSender javaMailSender;

    private static Logger loggerImportant = LoggerFactory.getLogger(IMPORTANT_INFO_LOGGER);


    @Autowired
    public MailService(JavaMailSender javaMailSender) {
        MailService.javaMailSender = javaMailSender ;
    }

    public static final String FROM = "<EMAIL>" ;


    /**
     *
     * @param to 发给谁
     * @param title 标题
     * @param content 内容
     * @param path 附件 list 每个里是数组, 0是文件绝对路径,1是附件名字
     * @param useHtml 是否使用html
     */
    public  static void sendMimeMail( String[] to,String title, String content, ArrayList<String[]> path,  boolean useHtml){

        loggerImportant.info("mailSend {} {}",to,title);
        try {
            MimeMessage mimeMessge = javaMailSender.createMimeMessage();
            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessge,true);

            mimeMessageHelper.setTo(to);
            mimeMessageHelper.setFrom(FROM);
            mimeMessageHelper.setSubject(title);

            mimeMessageHelper.setText(content,useHtml);

            if (path != null) {
                for (String[] strings : path) {
                    FileSystemResource img = new FileSystemResource(new File(strings[0]));
                    mimeMessageHelper.addAttachment(strings[1],img);
                }
            }
            javaMailSender.send(mimeMessge);

        } catch (MessagingException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);
        }

    }


    public  static void sendMimeMail( ArrayList<String> to ,String title, String content){
        sendMimeMail(to.toArray(new String[]{}),title,content,new ArrayList<>(),true);
    }


    public static void sendSystemErrorMail(String errorContent) {
        sendMimeMail(new String[]{"<EMAIL>"},"fmbx系统异常",errorContent,null,false);
    }
}
