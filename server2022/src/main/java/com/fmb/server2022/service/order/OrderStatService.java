package com.fmb.server2022.service.order;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.server2022.domain.HotelDateMinPriceDomain;
import com.fmb.server2022.domain.HotelReserveMinPriceDomain;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.mapper.FrontHotelDao;
import com.fmb.server2022.service.RedissonLockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date: 2023/2/16 2:34 下午
 */
@Service
public class OrderStatService {


    @Autowired
    OrderStatService self ;

    @Autowired
    FrontHotelDao frontHotelDao;

    @Autowired
    IFmbxActivityService activityService ;

    @Autowired
    OrderService orderService ;

    /**
     * 查询活动 酒店日历房最低售价
     * @param aid
     * @return
     */
    public HotelDateMinPriceDomain queryHotelDateMinPrice(Integer aid){
        HashMap<String, Object> p = new HashMap<>();
        p.put("aid", aid);
        final HotelDateMinPriceDomain hotelDateMinPriceDomain = frontHotelDao.selectHotelDateMinPriceOne(p);
        return hotelDateMinPriceDomain ;
    }


    public HotelReserveMinPriceDomain queryHotelReserveMinPrice(Integer aid){
        HashMap<String, Object> p = new HashMap<>();
        p.put("aid", aid);
        final HotelReserveMinPriceDomain min = frontHotelDao.selectHotelReserveMinPriceOne(p);
        return min ;
    }

    public void updateHotelDateActivityMinPrice(Integer aid){
        final FmbxActivity activity = orderService.queryFmbxActivity(aid);
        if (activity != null) {
            if (activity.getTicketType()==6) {
                RedissonLockService.doLockJobNoWait("UpdateActivityHotelDateMinPrice",aid.toString(),3000 ,()->{
                    hotelDateMinPriceRunCore(aid);
                });
            }
        }
    }

    public void hotelDateMinPriceRunCore(Integer aid) {
        final HotelDateMinPriceDomain hotelDateMinPriceDomain = self.queryHotelDateMinPrice(aid);
        if (hotelDateMinPriceDomain != null) {
            Wrapper<FmbxActivity> wrUpdate = new LambdaUpdateWrapper<FmbxActivity>()
                    .eq(FmbxActivity::getXaid, aid)
                    .set(FmbxActivity::getMinGoodsPrice, BigDecimal.valueOf( hotelDateMinPriceDomain.getGoodsPrice()))
                    .set(FmbxActivity::getMinMarketPrice, BigDecimal.valueOf( hotelDateMinPriceDomain.getMarketPrice()))
                    ;
            activityService.update(wrUpdate);
        }
    }
//    public void hotelReserveMinPriceRunCore(Integer aid) {
//        final HotelReserveMinPriceDomain x = self.queryHotelReserveMinPrice(aid);
//        if (x != null) {
//            Wrapper<FmbxActivity> wrUpdate = new LambdaUpdateWrapper<FmbxActivity>()
//                    .eq(FmbxActivity::getXaid, aid)
//                    .set(FmbxActivity::getMinGoodsPrice, BigDecimal.valueOf( x.getGoodsPrice()))
//                    .set(FmbxActivity::getMinMarketPrice, BigDecimal.valueOf( x.getMarketPrice()))
//                    ;
//            activityService.update(wrUpdate);
//        }
//    }

}
