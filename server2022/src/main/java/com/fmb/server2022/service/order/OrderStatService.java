package com.fmb.server2022.service.order;

import cn.hutool.core.date.ChineseDate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.domain.FrontResSkuDomain;
import com.fmb.server2022.domain.HotelDateMinPriceDomain;
import com.fmb.server2022.domain.HotelReserveMinPriceDomain;
import com.fmb.server2022.domain.stock.DatePrimeInfo;
import com.fmb.server2022.fmb_new.entity.FmbProductIndex;
import com.fmb.server2022.fmb_new.entity.JishigouCommonDistrict;
import com.fmb.server2022.fmb_new.service.IFmbProductIndexService;
import com.fmb.server2022.fmb_new.service.IJishigouCommonDistrictService;
import com.fmb.server2022.fmbx.entity.FmbMycpsProduct;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxActivityPublishLog;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.entity.FmbxDateHotelInfo;
import com.fmb.server2022.fmbx.entity.FmbxDateTable;
import com.fmb.server2022.fmbx.entity.FmbxOrderStat;
import com.fmb.server2022.fmbx.service.IFmbMycpsProductService;
import com.fmb.server2022.fmbx.service.IFmbOrderInfoService;
import com.fmb.server2022.fmbx.service.IFmbxActivityPublishLogService;
import com.fmb.server2022.fmbx.service.IFmbxActivityService;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpsService;
import com.fmb.server2022.fmbx.service.IFmbxDateHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxDateTableService;
import com.fmb.server2022.fmbx.service.IFmbxOrderStatService;
import com.fmb.server2022.mapper.FrontHotelDao;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.mapper.SuiteDao;
import com.fmb.server2022.service.RedisLockUtil;
import com.fmb.server2022.service.RedissonLockService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.DatePrimeUtil;
import com.fmb.util.FmbDateUtil;
import com.fmb.util.FmbNumberUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fmb.basic.FmbConstants.*;

/**
 * <AUTHOR>
 * @Date: 2023/2/16 2:34 下午
 */
@Service
public class OrderStatService {

    public static final String FMBX_HOTEL_DATE_PRIME_INFO = "fmbx:hotelDatePrimeInfo:";
    public static final String FMBX_SYNC_ACTIVITY = "fmbx:syncActivity:";
    private static Logger logger = LoggerFactory.getLogger(OrderStatService.class);

    private static Logger loggerImportant = LoggerFactory.getLogger(IMPORTANT_INFO_LOGGER);

    @Autowired
    OrderStatService self;

    static JedisPool jedisPool ;

    @Autowired
    SuiteService suiteService ;


    @Autowired
    IFmbMycpsProductService mycpsProductService ;


    @Autowired
    SuiteDao suiteDao ;

    @Autowired
    public void setJedisPool(JedisPool jedisPool) {
        OrderStatService.jedisPool = jedisPool;
    }


    @Autowired
    IFmbxDateHotelInfoService dateHotelInfoService ;
    @Autowired
    IFmbxOrderStatService fmbxOrderStatService ;
    @Autowired
    FrontSkuDao frontSkuDao;
    @Autowired
    FrontHotelDao frontHotelDao;
    @Autowired
    IFmbxBpMainService bpMainService ;
    @Autowired
    IFmbxActivityService activityService;
    @Autowired
    IFmbProductIndexService productIndexService ;
    @Autowired
    OrderService orderService;
    @Autowired
    IFmbOrderInfoService orderInfoService ;

    @Autowired
    IFmbxDateTableService dateTableService ;

    @Autowired
    IFmbxActivityPublishLogService activityPublishLogService ;

    @Autowired
    IJishigouCommonDistrictService jishigouCommonDistrictService ;

    @Autowired
    IFmbxBpsService bpsService ;

    @Autowired
    IJishigouCommonDistrictService districtService ;


//    public HotelDateMinPriceDomain queryHotelDatePrice(Integer aid) {
//
//        return queryHotelDatePrice(aid,1) ;
//    }
//
//    public HotelDateMinPriceDomain queryHotelDatePrice(Integer aid,int type) {
//        return queryHotelDatePrice(aid,type,LocalDate.now().toString()) ;
//    }
//    /**
//     * 查询活动 酒店日历房最低或最高售价
//     *
//     * @param aid
//     * @param type
//     * @return
//     */
//    public HotelDateMinPriceDomain queryHotelDatePrice(Integer aid,int type,String targetDateBegin) {
//        HashMap<String, Object> p = new HashMap<>();
//        p.put("aid", aid);
//        p.put("type", type);
//        p.put("targetDateBegin", targetDateBegin);
//
//        final HotelDateMinPriceDomain hotelDateMinPriceDomain = frontHotelDao.selectHotelDateMinPriceOne(p);
//        return hotelDateMinPriceDomain;
//    }


    public HotelDateMinPriceDomain queryHotelDatePriceBySku(Integer skuid,int type,String targetDateBegin) {
        HashMap<String, Object> p = new HashMap<>();
        p.put("skuid", skuid);
        p.put("type", type);
        p.put("targetDateBegin", targetDateBegin);

        final HotelDateMinPriceDomain hotelDateMinPriceDomain = frontHotelDao.selectHotelDateMinPriceBySku(p) ;
        return hotelDateMinPriceDomain;
    }




    public HotelReserveMinPriceDomain queryHotelReserveMinPrice(Integer aid) {

        return queryHotelReserveMinPrice(aid,1) ;
    }

    /**
     *
     * @param aid
     * @param type 1-取最小 2-取最大
     * @return
     */
    public HotelReserveMinPriceDomain queryHotelReserveMinPrice(Integer aid,int type ) {
        HashMap<String, Object> p = new HashMap<>();
        p.put("aid", aid);
        p.put("type", type);
        final HotelReserveMinPriceDomain min = frontHotelDao.selectHotelReserveMinPriceOne(p);

//        final List<HotelReserveMinPriceDomain> hotelReserveMinPriceDomains = frontHotelDao.selectHotelReserveMinPriceList(p);

        return min;
    }

//    public void updateHotelDateActivityMinPrice(Integer aid) {
//        final FmbxActivity activity = orderService.queryFmbxActivity(aid);
//        if (activity != null) {
//            if (activity.getTicketType() == 6) {
//                RedissonLockService.doLockJobNoWait("UpdateActivityHotelDateMinPrice", aid.toString(), 3000, () -> {
//                    hotelDateMinPriceRunCore(aid);
//                });
//            }
//        }
//    }
//
//    public void hotelDateMinPriceRunCore(Integer aid) {
//        final HotelDateMinPriceDomain hotelDateMinPriceDomain = self.queryHotelDatePrice(aid,1);
//        if (hotelDateMinPriceDomain != null) {
//            Wrapper<FmbxActivity> wrUpdate = new LambdaUpdateWrapper<FmbxActivity>()
//                    .eq(FmbxActivity::getXaid, aid)
//                    .set(FmbxActivity::getMinGoodsPrice, BigDecimal.valueOf(hotelDateMinPriceDomain.getGoodsPrice()))
//                    .set(FmbxActivity::getMinMarketPrice, BigDecimal.valueOf(hotelDateMinPriceDomain.getMarketPrice()));
//            activityService.update(wrUpdate);
//        }
//    }

    /**
     * 根据酒店活动id 更新 销售量
     *
     * @param aid
     */
    public void updateHotelActivitySellNumber(Integer aid) {
        final List<Map> maps = frontSkuDao.queryHotelSellNum(aid);

        final int sell_num1 = Integer.parseInt(maps.get(0).get("sell_num").toString());
        final int sell_num2 = Integer.parseInt(maps.get(1).get("sell_num").toString());

        Wrapper<FmbxActivity> acUp = new LambdaUpdateWrapper<FmbxActivity>()
                .eq(FmbxActivity::getXaid, aid)
                .set(FmbxActivity::getSellNumTotal, sell_num1 + sell_num2)
                .set(FmbxActivity::getSellNumHotelDate, sell_num1)
                .set(FmbxActivity::getSellNumHotelReserve, sell_num2);
        activityService.update(acUp);

        FmbScheduleUtil.doAsyncJob(()->{
            self.syncActivityInfo2ProductIndex(aid);
        });

    }

    /**
     * 根据活动id 构建 sku的日历信息 为后续查询 酒店活动 是否可以再特定日期入住做准备
     * @param aid
     */
//    @DS(DB_slave)
//    public void buildHotelDatePrimeInfo(Integer aid) {
//
//        List<DatePrimeInfo> datePrimeInfos = frontSkuDao.queryActivitySkuDateInfo(aid);
//
//        final Map<Integer, List<String>> skuDate = datePrimeInfos.stream().collect(Collectors.groupingBy(DatePrimeInfo::getSkuId,
//                Collectors.mapping(info -> info.getDateCol(), Collectors.toList())));
//
//        final Map<String, String> collect2 = datePrimeInfos.stream().collect(Collectors.groupingBy(DatePrimeInfo::getDateCol,
//                Collectors.mapping(info -> aid + "#" + info.getSkuId(), Collectors.joining(","))));
//
//        logger.info( "skuDate={}" ,skuDate);
//        logger.info( "collect2={}" ,collect2);
//
//        final ArrayList<String> skuPrime = new ArrayList<>();
//        for (Map.Entry<Integer, List<String>> entry : skuDate.entrySet()) {
//            skuPrime.add( DatePrimeUtil.createPrime(entry.getValue()));
//        }
//
//        Jedis jedis = null;
//        try {
//            jedis = jedisPool.getResource();
//            jedis.select(FmbConstants.REDIS_DEFAULT_DB);
//
//            jedis.set(FMBX_HOTEL_DATE_PRIME_INFO +aid, JSON.toJSONString(skuPrime)) ;
//
//        } catch (Exception ex) {
//            long errorid = SnowflakeIdWorker.getInstance().nextId();
//            logger.error("errorid " + errorid, ex);
//        } finally {
//            if (jedis != null) {
//                jedis.close();
//            }
//        }
//
//
//    }


    /**
     * 根据活动id 构建 sku的日历信息 为后续查询 酒店活动 是否可以再特定日期入住做准备
     * @param aid
     */

    public  void buildHotelDateActSkuInfo(Integer aid) {
        logger.info("buildHotelDateActSkuInfo {}",aid);

        DynamicDataSourceContextHolder.push(DB_slave);
        //当前 活动的 日期sku 信息
        List<DatePrimeInfo> datePrimeInfos = queryDatePrimeInfos(aid);

        //根据日期分组
        final Map<String, String> newActSkuInfo = datePrimeInfos.stream().collect(Collectors.groupingBy(DatePrimeInfo::getDateCol,
                Collectors.mapping(info -> aid + "#" + info.getSkuId(), Collectors.joining(","))));

        //这个活动的当前 所有日期
        final List<String> collect = datePrimeInfos.stream().map(DatePrimeInfo::getDateCol).collect(Collectors.toList());

        final ArrayList<FmbxDateHotelInfo> addDB = new ArrayList<>();
        final ArrayList<FmbxDateHotelInfo> updateDB = new ArrayList<>();


        HashMap<String, Object> par = new HashMap<>();
        par.put("collect", collect);

        if (!collect.isEmpty()) {


            // 数据库里和当前活动日期重合的 信息
            List<FmbxDateHotelInfo> infos = frontSkuDao.queryDateHotelInfoOfExists(par) ;

            final Map<String, List<FmbxDateHotelInfo>> dbCollect = infos.stream().collect(Collectors.groupingBy(x -> FmbDateUtil.FORMATTER10.format(x.getDatecol())));


            for (Map.Entry<String, String> entry : newActSkuInfo.entrySet()) {

                final String key = entry.getKey();
                if (dbCollect.containsKey(key)) {
                    //去除原有的活动sku 信息
                    final String dbTxtInfo = dbCollect.get(key).get(0).getActivitySkuInfo();
                    final String collect1 =
                            Stream.of(dbTxtInfo.split(",")).filter(s -> !s.startsWith("" + aid)).collect(Collectors.joining(","));

                    FmbxDateHotelInfo info = new FmbxDateHotelInfo() ;
                    info.setDatecol(LocalDate.parse(key));
                    if (StringUtils.isBlank(collect1)) {
                        info.setActivitySkuInfo(txtSort( newActSkuInfo.get(key)));
                    }else {
                        info.setActivitySkuInfo(txtSort(collect1 +","+newActSkuInfo.get(key)));
                    }

                    if (StringUtils.isNotBlank(dbTxtInfo) && !dbTxtInfo.equals(info.getActivitySkuInfo())) {
                        updateDB.add(info) ;
                    }

                }else {
                    FmbxDateHotelInfo info = new FmbxDateHotelInfo() ;
                    info.setDatecol(LocalDate.parse(key));
                    info.setActivitySkuInfo(txtSort(newActSkuInfo.get(key)));
                    addDB.add(info) ;
                }
            }

        }

        {

            //找到该活动部覆盖的日期 .清除当前活动的信息
            List<FmbxDateHotelInfo> infosDel = frontSkuDao.queryDateHotelInfoOfDel(par) ;

            for (FmbxDateHotelInfo info : infosDel) {
                final String collect1 = Arrays.asList(info.getActivitySkuInfo().split(",")).stream().filter(s -> !s.startsWith("" + aid)).collect(Collectors.joining(","));

                if (StringUtils.isNotBlank(collect1) && collect1.equals(info.getActivitySkuInfo())) {
//                    logger.info("skip work");
                    continue;
                }

                FmbxDateHotelInfo info1 = new FmbxDateHotelInfo() ;
                info1.setDatecol(info.getDatecol());
                if (StringUtils.isBlank(collect1)) {
                    info.setActivitySkuInfo("");
                }else {
                    info.setActivitySkuInfo(txtSort(collect1));
                }
                updateDB.add(info) ;

            }
        }


        DynamicDataSourceContextHolder.push(DB_master);
        if (!updateDB.isEmpty()) {
            for (FmbxDateHotelInfo info : updateDB) {
                dateHotelInfoService.updateById(info) ;
            }
//                dateHotelInfoService.updateBatchById(updateDB) ;
        }else {
            logger.info("fmbx_date_hotel_info not_need_update_price {}",aid);
        }
        if (!addDB.isEmpty()) {
//            logger.info("addDB = {}",JSON.toJSONString(updateDB));
            dateHotelInfoService.saveBatch(addDB) ;
        }else {
            logger.info("fmbx_date_hotel_info not_need_add_price {}",aid);
        }


    }

    public List<DatePrimeInfo> queryDatePrimeInfos(Integer aid) {



        List<DatePrimeInfo> all = new ArrayList<>() ;
        HashMap<String, Object> par = new HashMap<>();
        par.put("xaid", aid);
        final List<Map> infos = frontSkuDao.queryHotelDateSkuAndSuite(par);

        for (Map info : infos) {
            final int preDay =  suiteService.querySuitePreDay((Integer) info.get("suite_id"));

            Integer skuid = (Integer) info.get("sku_id");
            HashMap<String, Object> p = new HashMap<>();
            p.put("skuid", skuid);
            p.put("aid", aid);

            LocalDate now = LocalDate.now() ;
            now = now.plus(preDay, ChronoUnit.DAYS) ;

            p.put("dateStart", now.toString()) ;

            final List<DatePrimeInfo> datePrimeInfos = frontSkuDao.queryActivitySkuDateInfo(p);

            all.addAll(datePrimeInfos) ;
        }



        return all;
    }

    public static String txtSort(String in){

        return  Arrays.asList(in.split(",")).stream().sorted().collect(Collectors.joining(",")) ;

    }


    public boolean testHotelDatePrimeInfo(Integer aid, String dateBegin, String dateEnd) {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            final String s = jedis.get(FMBX_HOTEL_DATE_PRIME_INFO + aid);

            final ArrayList<String> arrayList = JSON.parseObject(s, new ArrayList().getClass());

            return DatePrimeUtil.isActivityHaveValidDateInfo(arrayList,dateBegin,dateEnd);


        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return false;
    }


    /**
     * 查询房券的距结束时间
     * @param aid
     * @return
     */
    @DS(DB_slave)
    public Timestamp queryActivityOfHotelCodeEndTime(Integer aid){



        Timestamp sellEndTime = null ;
        ReqHotel input = new ReqHotel();
        input.setXaid(aid);
        List<FrontResSkuDomain> resList = frontHotelDao.selectFrontResSku(input);

        if (!resList.isEmpty()) {
            final long nowMill = System.currentTimeMillis();
            final Optional<FrontResSkuDomain> first = resList.stream().filter(r -> r.getSellCountDownShowFlag() == 1 && r.getSellEndTime().getTime() > nowMill && (r.getSellEndTime().getTime() - nowMill) < (24 * 3600 * 7 * 1000L)).sorted(Comparator.comparing(FrontResSkuDomain::getSellEndTime)).findFirst();
            if (first.isPresent()) {
                sellEndTime = first.get().getSellEndTime();
            }
        }

        logger.info("queryActivityOfHotelCodeEndTime aid={} , sellEndTime={}",aid ,sellEndTime);

        return sellEndTime ;

    }


    public void syncActivityInfo2ProductIndex(Integer aid){
        syncActivityInfo2ProductIndex(aid,"") ;
    }

    /**
     * 同步 fmbx_activity到 productIndex
     * @param aid
     * @param from
     */
    public void syncActivityInfo2ProductIndex(Integer aid,String from){

        if (logger.isInfoEnabled()) {
            logger.info("syncActivityInfo2ProductIndex begin {}",aid);
        }

//        final boolean testFlag = RedisLockUtil.testIsRunTooMuchInShotTime("fmbx:syncActivityInfo2ProductIndex:" + aid, 1500);
//
//        if (!testFlag) {
//            logger.info("syncActivityInfo2ProductIndex {}",aid);
//            return;
//        }
//        //防止频繁更新 增加 最多 20秒一次
//        boolean skip = false ;
//        {
//            Jedis jedis = null;
//            try {
//                jedis = jedisPool.getResource();
//                jedis.select(REDIS_DEFAULT_DB);
//                skip  = jedis.exists(FMBX_SYNC_ACTIVITY + aid) ;
//
//            } catch (Exception ex) {
//                long errorid = SnowflakeIdWorker.getInstance().nextId();
//                logger.error("errorid " + errorid, ex);
//            } finally {
//                if (jedis != null) {
//                    jedis.close();
//                }
//            }
//        }
//
//        if (skip) {
//            return;
//        }


        final Timestamp timestamp = self.queryActivityOfHotelCodeEndTime(aid);
        //防止循环调用 因为  touchActivity 方法也要调用本方法
        if (!"suiteTouchActivity".equals(from)) {
            suiteService.touchActivity(aid,false,false,true,false);
        }

        DynamicDataSourceContextHolder.push(DB_master);
        final FmbxActivity updateDBAct = new FmbxActivity();
        updateDBAct.setXaid(aid);
        if (timestamp != null) {

            updateDBAct.setCountDownTime(timestamp.toLocalDateTime());

        }



        Integer hotelReserveSkuid =   suiteDao.selectSkuOfAvailableReserveHotel(aid) ;

//        updateDBAct.setFlagHotelDate(hotelDateSkuid!=null?1:0);
        updateDBAct.setFlagHotelReserve(hotelReserveSkuid!=null?1:0);

        Wrapper<FmbxActivityPublishLog> wrPub = new LambdaQueryWrapper<FmbxActivityPublishLog>()
                .eq(FmbxActivityPublishLog::getXaid,aid)
                .eq(FmbxActivityPublishLog::getOpType,1)
                .orderByDesc(FmbxActivityPublishLog::getPublishLogId)
                .last( " limit 1  ")
                .select(FmbxActivityPublishLog::getCtime)
                ;
        final FmbxActivityPublishLog lastPubTime = activityPublishLogService.getOne(wrPub);

        activityService.updateById(updateDBAct) ;


        final FmbxActivity fmbxActivity = activityService.getById(aid);

        if (fmbxActivity == null) {
            logger.error("FmbxActivityIsNUll_{}_{}",aid,from);
            return;
        }

        if (fmbxActivity.getFlagHotelDate()==1 || fmbxActivity.getFlagHotelReserve()==1) {

            if (fmbxActivity.getFlagHaveValidSku()==0) {
                FmbxActivity updateDB2 = new FmbxActivity();
                updateDB2.setXaid(aid);
                updateDB2.setFlagHaveValidSku(1);
                activityService.updateById(updateDB2) ;
            }

            fmbxActivity.setFlagHaveValidSku(1);
        }else {

            if (fmbxActivity.getFlagHaveValidSku()==1) {
                FmbxActivity updateDB2 = new FmbxActivity();
                updateDB2.setXaid(aid);
                updateDB2.setFlagHaveValidSku(0);
                activityService.updateById(updateDB2) ;
            }


            fmbxActivity.setFlagHaveValidSku(0);
        }


        if (fmbxActivity != null) {

            final FmbxBps bps = orderService.queryBps(fmbxActivity.getBpsId());

            final FmbxBpMain bpMain = bpMainService.getById(fmbxActivity.getBpId());

            Wrapper<FmbProductIndex> wrTest = new LambdaQueryWrapper<FmbProductIndex>()
                    .eq(FmbProductIndex::getApId,aid)
                    .select(FmbProductIndex::getApId,FmbProductIndex::getSellStat)
                    .last(" limit 1 ")
                    ;

            final FmbProductIndex productIndexTest = productIndexService.getOne(wrTest);

            //不存在
            if (productIndexTest==null) {

                FmbProductIndex indexNew = new FmbProductIndex();
                indexNew.setApId(aid);
                buildProductIndex(indexNew,fmbxActivity,bps,bpMain,lastPubTime,productIndexTest) ;
                productIndexService.save(indexNew) ;

            }else {
                FmbProductIndex indexUpdate = new FmbProductIndex();
                indexUpdate.setApId(aid);
                buildProductIndex(indexUpdate,fmbxActivity,bps,bpMain,lastPubTime,productIndexTest) ;
                productIndexService.updateById (indexUpdate) ;
            }
//            Jedis jedis = null;
//            try {
//                jedis = jedisPool.getResource();
//                jedis.select(REDIS_DEFAULT_DB);
//
//                jedis.setex(FMBX_SYNC_ACTIVITY + aid,20,"") ;
//            } catch (Exception ex) {
//                long errorid = SnowflakeIdWorker.getInstance().nextId();
//                logger.error("errorid " + errorid, ex);
//            } finally {
//                if (jedis != null) {
//                    jedis.close();
//                }
//            }

            //更新活动最低,最高价格到 fmb_mycps_product
            updateMycpsProduct(aid, fmbxActivity);

            if (!"lotUpdate".equals(from)) {
                RedissonLockService.doLockJob("buildHotelDateActSkuInfo","",10*1000,2000,()->self.buildHotelDateActSkuInfo(aid),"获取锁失败");
            }

        }
    }

    public void updateMycpsProduct(Integer aid, FmbxActivity fmbxActivity) {
        //更新活动最低,最高价格到 fmb_mycps_product

        logger.info("updateMycpsProduct min {},{} ",fmbxActivity.getMinGoodsPrice(),fmbxActivity.getMinGoodsPriceHotelReserve());
        logger.info("updateMycpsProduct max {},{}  ",fmbxActivity.getMaxGoodsPrice(),fmbxActivity.getMaxGoodsPriceHotelReserve());

        BigDecimal myCpsMin = BigDecimal.ZERO ;

        if (fmbxActivity.getMinGoodsPrice().compareTo(BigDecimal.ZERO)==1) {
            myCpsMin = fmbxActivity.getMinGoodsPrice() ;
        }
        if (myCpsMin.compareTo(BigDecimal.ZERO)==1) {
            if (fmbxActivity.getMinGoodsPriceHotelReserve().compareTo(BigDecimal.ZERO)==1 && fmbxActivity.getMinGoodsPriceHotelReserve().compareTo(myCpsMin)==-1 ) {
                myCpsMin = fmbxActivity.getMinGoodsPriceHotelReserve() ;
            }
        }else {
            if (fmbxActivity.getMinGoodsPriceHotelReserve().compareTo(BigDecimal.ZERO)==1){
                myCpsMin = fmbxActivity.getMinGoodsPriceHotelReserve() ;
            }
        }


        BigDecimal myCpsMax = BigDecimal.ZERO ;
        //取大的
        if (fmbxActivity.getMaxGoodsPrice().compareTo(BigDecimal.ZERO)==1) {
            myCpsMax = fmbxActivity.getMaxGoodsPrice() ;
        }
        if (fmbxActivity.getMaxGoodsPriceHotelReserve().compareTo(BigDecimal.ZERO)==1 && fmbxActivity.getMaxGoodsPriceHotelReserve().compareTo(myCpsMax)==1 ) {
            myCpsMax = fmbxActivity.getMaxGoodsPriceHotelReserve() ;
        }

        logger.info("min_max {},{}  ",myCpsMin,myCpsMax);

        if(myCpsMin.compareTo(BigDecimal.ZERO)==1 || myCpsMax.compareTo(BigDecimal.ZERO)==1){
            Wrapper<FmbMycpsProduct> upMycps = new LambdaUpdateWrapper<FmbMycpsProduct>()
                    .eq(FmbMycpsProduct::getApId, aid)
                    .set(myCpsMin.compareTo(BigDecimal.ZERO)==1,FmbMycpsProduct::getSellMinPrice,myCpsMin)
                    .set(myCpsMax.compareTo(BigDecimal.ZERO)==1,FmbMycpsProduct::getSellMaxPrice,myCpsMax)
                    ;
            mycpsProductService.update(upMycps) ;
        }
    }

    private void buildProductIndex(FmbProductIndex indexNew, FmbxActivity dbAct, FmbxBps bps, FmbxBpMain bpMain, FmbxActivityPublishLog lastPubTime, FmbProductIndex productIndexTest) {


        BeanUtils.copyProperties(dbAct,indexNew);
        // INSERT INTO fmb_product_index ( ap_id, title, sub_title, activity_group_type, city_id, sales_city_ids, business_type, plat_id, utime, ctime )
        // VALUES ( 105761, '兆龙酒店', '兆龙酒店图标题', 16, 1, '1', 2, '0', '2023-02-21T14:44:14', '2022-12-14T15:13:58' )
        //

        indexNew.setIsFclub(0);
        indexNew.setActivityType(1);
        if (6==dbAct.getTicketType()) {
            indexNew.setActivityGroupType(4);

            indexNew.setBusinessType(2);
        }

        indexNew.setTypeName("");

        indexNew.setBanner("");
        if (StringUtils.isNotBlank( dbAct.getBannerListUrl() )) {

            logger.info("dbAct.getBannerList()={}",dbAct.getBannerListUrl());
            final ArrayList arrayList = JSONArray.parseObject(dbAct.getBannerListUrl(), new ArrayList<>().getClass());
            if (!arrayList.isEmpty()) {
                indexNew.setBanner(arrayList.get(0).toString());
            }
        }

        indexNew.setAgeMin(0);
        indexNew.setAgeMax(0);
        indexNew.setCatId(dbAct.getCategoryId());

        //下面3个待定
        indexNew.setProvincesId(0);
        indexNew.setAreaId(0);
        indexNew.setDistrictId(0);

//        final FmbxBps bpsInfo = bpsService.getById(dbAct.getBpsId());

        indexNew.setDestinationProvincesIds("");
        indexNew.setDestinationCityIds("");
        //将商户信息的 省份和地区写入
        if (bps != null) {

            {
                Wrapper<JishigouCommonDistrict> wrDist = new LambdaQueryWrapper<JishigouCommonDistrict>()
                        .eq(JishigouCommonDistrict::getName,bps.getProvinces())
                        .eq(JishigouCommonDistrict::getLevel,1)
                        .select(JishigouCommonDistrict::getId)
                        .last(" limit 1 ")
                        ;
                final JishigouCommonDistrict pro = districtService.getOne(wrDist);

                if (pro != null) {
                    indexNew.setProvincesId(pro.getId());
                    indexNew.setDestinationProvincesIds(""+pro.getId());
                }
            }
            {
                Wrapper<JishigouCommonDistrict> wrDist = new LambdaQueryWrapper<JishigouCommonDistrict>()
                        .eq(JishigouCommonDistrict::getName,bps.getArea())
                        .eq(JishigouCommonDistrict::getLevel,2)
                        .select(JishigouCommonDistrict::getId)
                        .last(" limit 1 ")
                        ;
                final JishigouCommonDistrict area = districtService.getOne(wrDist);
                if (area != null) {
                    indexNew.setAreaId(area.getId());
                    indexNew.setDestinationCityIds(""+area.getId());
                }
            }
        }


//        Integer nPro   =queryJishigouDistrict(bps.getProvinces());
//        if (nPro != null) {
//            indexNew.setDestinationProvincesIds(""+nPro);
//            final Integer nArea = queryJishigouDistrictWithPid(bps.getArea(), nPro);
//            if (nArea != null) {
//                indexNew.setDestinationCityIds(""+nArea);
//            }
//        }

        indexNew.setLongitude(bps.getLongitude());
        indexNew.setLatitude(bps.getLatitude());

        indexNew.setAdminUid(bpMain.getAdminUid());
        indexNew.setCreatorUid(dbAct.getCreateUid());

        //待定 5个
        indexNew.setStartTime(DEFAULT_DATETIME);
        indexNew.setEndTime(DEFAULT_DATETIME);

        indexNew.setEndSellTime(DEFAULT_DATETIME);
        indexNew.setApplyEndTime(DEFAULT_DATETIME);

        if (productIndexTest==null){
            indexNew.setStartSellTime(DEFAULT_DATETIME);
        }


        indexNew.setRecommend("0");
        indexNew.setShopWarehouseId(0);
        indexNew.setThirdPart("");
        //待定
        indexNew.setIsCert(0);

        if (dbAct.getFlagDelete()==0 && dbAct.getFlagHaveValidSku()==1 && dbAct.getFlagPublish()==1) {
            indexNew.setSellStat(16);

            //在新插入 或者 原有的售卖状态不是16 变为16时 设置开始售卖时间
            if (productIndexTest==null  || productIndexTest.getSellStat()!=16) {
                indexNew.setStartSellTime(LocalDateTime.now());
                loggerImportant.info("fmbx_activity_startSell_{} {}",dbAct.getXaid());
            }
//            indexNew.setStatus(1);
        }else {
            if (dbAct.getFlagPublish()!=1) {
                indexNew.setSellStat(8);
//                indexNew.setStatus(0);
            }else if (dbAct.getFlagHaveValidSku()!=1 ){
                indexNew.setSellStat(2);
//                indexNew.setStatus(0);
            }else {
                indexNew.setSellStat(0);
//                indexNew.setStatus(2);
            }
        }

        indexNew.setStatus(dbAct.getFlagPublish());

        indexNew.setListorder(0);
        indexNew.setTicket("");
        indexNew.setClickNum(0);
        indexNew.setSellNum( dbAct.getSellNumTotal() );
        indexNew.setPeopleNum(0);

        indexNew.setPartnerStatus(0);
        indexNew.setWxappShow(1);

        //最小售价
        ArrayList<BigDecimal> prices = new ArrayList<>();
        prices.add(dbAct.getMinGoodsPrice()) ;
        prices.add(dbAct.getMinGoodsPriceHotelReserve()) ;
        BigDecimal minGoods =  FmbNumberUtil.getMinPriceThan0(prices) ;
        if (minGoods.compareTo(BigDecimal.ZERO)==1) {
            indexNew.setSellMinPrice((float) FmbNumberUtil.bigDecimal2Double(minGoods));
        }

        //最小市场价
        prices = new ArrayList<>();
        prices.add(dbAct.getMinMarketPrice()) ;
        prices.add(dbAct.getMinMarketPriceHotelReserve()) ;
        BigDecimal minMarket =   FmbNumberUtil.getMinPriceThan0(prices) ;
        if (minMarket.compareTo(BigDecimal.ZERO)==1) {
            indexNew.setMarketMinPrice((float) FmbNumberUtil.bigDecimal2Double(minMarket));
        }

//        if (dbAct.getMinGoodsPrice().compareTo(BigDecimal.ZERO)==1) {
//            indexNew.setSellMinPrice((float) FmbNumberUtil.bigDecimal2Double(dbAct.getMinGoodsPrice()));
//            indexNew.setMarketMinPrice((float) FmbNumberUtil.bigDecimal2Double(dbAct.getMinMarketPrice()));
//        }else if (dbAct.getMinGoodsPriceHotelReserve().compareTo(BigDecimal.ZERO)==1){
//            indexNew.setSellMinPrice((float) FmbNumberUtil.bigDecimal2Double(dbAct.getMinGoodsPriceHotelReserve()));
//            indexNew.setMarketMinPrice((float) FmbNumberUtil.bigDecimal2Double(dbAct.getMinMarketPriceHotelReserve()));
//        }

        indexNew.setOnShelvesTime(DEFAULT_DATETIME);
        if(lastPubTime!=null){
            indexNew.setOnShelvesTime(lastPubTime.getCtime());
        }
        
        indexNew.setApplyStartTime(DEFAULT_DATETIME);
        indexNew.setUtime(LocalDateTime.now());
        indexNew.setCtime(dbAct.getCtime());

        indexNew.setFmbxEndTime(dbAct.getCountDownTime());
        indexNew.setFmbxActivityFlag(1);
        indexNew.setFmbxHotelStarLevel(bps.getStarLevel());

        indexNew.setFmbxFlagHotelDate(dbAct.getFlagHotelDate());
        indexNew.setFmbxFlagHotelReserve(dbAct.getFlagHotelReserve());

        if (dbAct.getMarketUid() != null) {
            indexNew.setMarketUid(dbAct.getMarketUid());
        }


    }

    public Integer queryJishigouDistrict(String provinces) {
        Wrapper<JishigouCommonDistrict> wrDis = new LambdaQueryWrapper<JishigouCommonDistrict>()
                .eq(JishigouCommonDistrict::getName, provinces)
                .select(JishigouCommonDistrict::getId)
                ;
        final JishigouCommonDistrict one = jishigouCommonDistrictService.getOne(wrDis);

        if (one != null) {
            return one.getId() ;
        }

        return null ;
    }

    public Integer queryJishigouDistrictWithPid(String provinces,Integer pid) {
        Wrapper<JishigouCommonDistrict> wrDis = new LambdaQueryWrapper<JishigouCommonDistrict>()
                .eq(JishigouCommonDistrict::getName, provinces)
                .eq(JishigouCommonDistrict::getUpid, pid)
                .select(JishigouCommonDistrict::getId)
                ;
        final JishigouCommonDistrict one = jishigouCommonDistrictService.getOne(wrDis);

        if (one != null) {
            return one.getId() ;
        }

        return null ;
    }



    @DS(DB_slave)
    public void syncActInfoLot(){

        /**
         * 只查询一个 活动下的 最多2个sku ,日历房1个,房券1个
         * 防止活动状态异常
         */
        List<Map> maps  =  frontHotelDao.queryOnlineActivityAndSku() ;

        for (Map map : maps) {
            SuiteService.sendHotelSkuChangeNotify((Integer) map.get("skuId"));
        }


    }


    public void syncHotelActPriceLot(){

        DynamicDataSourceContextHolder.push(DB_slave);
        List<Map> maps  =  frontHotelDao.queryOnlineActivityAndSku() ;

        final Set<Integer> aidSet = maps.stream().map(map -> {
            return Integer.parseInt(map.get("aid").toString());
        }).collect(Collectors.toSet());


        //buildHotelDateActSkuInfo
        for (Integer aid : aidSet) {
            RedissonLockService.doLockJob("buildHotelDateActSkuInfo","",10*1000,2000,()->self.buildHotelDateActSkuInfo(aid),"获取锁失败");
        }

    }




    /**
     * 订单发生变化的时候 调用这个方法 把订单号记录下来
     * @param orderSn
     */
    public static void orderChange(String orderSn){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DEFAULT_DB);
//            jedis.sadd(FmbConstants.ORDERSN_STAT,orderSn) ;

            jedis.rpush(FmbConstants.ORDERSN_STAT,orderSn) ;

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 统一处理订单统计数据
     * @param orderSet
     */
    public void makeOrderStatData(LinkedHashSet<String> orderSet) {
        logger.info("makeOrderStatData={}",orderSet);

        orderSet.stream().forEach(ordersn->{
            self.makeOrderStatDataCore(ordersn) ;
        });

    }


    @DS(DB_slave)
    public void makeOrderStatDataCore(String orderSn) {

        Wrapper<FmbOrderInfo> wr = new LambdaQueryWrapper<FmbOrderInfo>()
                .eq(FmbOrderInfo::getOrderSn,orderSn);
        final FmbOrderInfo orderInfo = orderInfoService.getOne(wr);
        if (orderInfo != null) {

            Wrapper<FmbxOrderStat> wrC = new LambdaQueryWrapper<FmbxOrderStat>()
                    .eq(FmbxOrderStat::getOrderSn,orderInfo.getOrderSn())
                    .select(FmbxOrderStat::getStatId);
            final FmbxOrderStat one = fmbxOrderStatService.getOne(wrC);

            FmbxOrderStat bdUpdate = new FmbxOrderStat() ;

            if (one==null) {
                buildStatInfo(bdUpdate,orderInfo) ;
                fmbxOrderStatService.save(bdUpdate) ;
            }else{
                bdUpdate.setStatId(one.getStatId());
                buildStatInfo(bdUpdate,orderInfo) ;
                fmbxOrderStatService.updateById(bdUpdate) ;
            }
        }

    }

    /**
     * 构造订单统计 信息表
     * @param bdUpdate
     * @param orderInfo
     */
    public void buildStatInfo(FmbxOrderStat bdUpdate, FmbOrderInfo orderInfo) {

        BeanUtils.copyProperties(orderInfo,bdUpdate);

        bdUpdate.setCreateDate(orderInfo.getCreateTime().toLocalDate());

        final FmbxActivity activity = orderService.queryFmbxActivity(orderInfo.getAid());
        BeanUtils.copyProperties(activity,bdUpdate);

    }


    public void saveDateInfo(){

        dateTableService.saveBatch(genDateInfos());

        final Date date = DateTime.now().minusYears(1).toDate();

        Wrapper<FmbxDateTable> wr = new LambdaQueryWrapper<FmbxDateTable>()
                .le(FmbxDateTable::getDatecol,date)
                ;
        dateTableService.remove(wr) ;

    }

    public ArrayList<FmbxDateTable> genDateInfos() {


        // 春节、劳动节、中秋节、端午节、元旦、清明、国庆
        DateTime now = DateTime.now();
        String dFormat = "yyyy-MM-dd";

        ArrayList<FmbxDateTable> fmbxDateTables = new ArrayList<>();

        for (int i = 0; i < 10*365 ; i++) {


            DateTime dateTimeAdd = now.plusDays(i);
            ChineseDate chineseDate = new ChineseDate(dateTimeAdd.toDate());
            String dateStrTemp = dateTimeAdd.toString(dFormat);

            int dayOfWeek = dateTimeAdd.getDayOfWeek()  ;

            FmbxDateTable insert = new FmbxDateTable();

//            dateTimeAdd.toLocalDate());
            insert.setWeeknum(dayOfWeek);
            insert.setDatecol(  LocalDate.parse (dateStrTemp));

            insert.setHolidayType(0);
            if (dateStrTemp.endsWith("01-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("元旦");
            }
            else if (dateStrTemp.endsWith("05-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("劳动节");
            }
            else if (dateStrTemp.endsWith("10-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("国庆");
            }


//            if (StringUtils.isNotBlank(chineseDate.getFestivals()) || StringUtils.isNotBlank(chineseDate.getTerm())) {
//
//                System.out.println(dateStrTemp+"--星期(1-7):"+  dayOfWeek  +"--"+chineseDate.toString() +" 闰月:"+chineseDate.isLeapMonth());
//                System.out.println( chineseDate.getChineseMonth()+ chineseDate.getChineseDay() +"--"+chineseDate.getFestivals()
//                        +chineseDate.getTerm());
//            }

            if (chineseDate.getFestivals().indexOf("除夕")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("除夕");
            }
            if (chineseDate.getFestivals().indexOf("春节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("春节");
            }
            if (chineseDate.getFestivals().indexOf("元宵节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("元宵节");
            }
            if (chineseDate.getFestivals().indexOf("元宵节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("元宵节");
            }
            if (chineseDate.getFestivals().indexOf("端午节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("端午节");
            }
            if (chineseDate.getTerm().equals("清明")) {
                insert.setHolidayType(2);
                insert.setHoliday("清明");
            }

            insert.setYearMonthStr(dateStrTemp.substring(0,7));

            if (dateTableService.getById(insert.getDatecol())!=null) {
                fmbxDateTables.add(insert);
            }

        }

        return fmbxDateTables;


    }


}
