package com.fmb.server2022.service.kafka;


import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.LogIfLevelEnabled;

import java.util.Map;

@Configuration
public class FmbKafkaConfig {


    @Autowired
    private KafkaProperties properties;


    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(properties.buildProducerProperties());
    }


    /**
     * 创建一个新的消费者工厂
     * 创建多个工厂的时候 SpringBoot就不会自动帮忙创建工厂了；所以默认的还是自己创建一下
     *
     * @return
     */
    public ConsumerFactory<String, String> kafkaConsumerFactory() {
        Map<String, Object> map = properties.buildConsumerProperties();
        DefaultKafkaConsumerFactory<String, String> factory = new DefaultKafkaConsumerFactory<>(map);
        map.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);

        return factory;
    }

    /**
     * 创建一个新的消费者工厂
     * 但是修改为不自动提交
     *
     * @return
     */
    public ConsumerFactory<String, String> kafkaManualConsumerFactory() {
        Map<String, Object> map = properties.buildConsumerProperties();
        map.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        map.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,"32") ;
        DefaultKafkaConsumerFactory<String, String> factory = new DefaultKafkaConsumerFactory<>(map);
        return factory;
    }


    /**
     * 手动提交的监听器工厂 (使用的消费组工厂必须 kafka.consumer.enable-auto-commit = false)
     *
     * @return
     */
    @Bean("manualBatchAck")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> manualBatchAck() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(kafkaManualConsumerFactory());
        //设置提交偏移量的方式 当Acknowledgment.acknowledge()侦听器调用该方法时，立即提交偏移量
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.getContainerProperties().setCommitLogLevel(LogIfLevelEnabled.Level.INFO);
        factory.setBatchListener(true);
        return factory;
    }

    /**
     * 监听器工厂 批量消费 自动提交
     * @return
     */
    @Bean("autoBatchAck")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> autoBatchAck() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(kafkaConsumerFactory());
        factory.setBatchListener(true);

        factory.getContainerProperties().setCommitLogLevel(LogIfLevelEnabled.Level.INFO);
        return factory;
    }

}