package com.fmb.server2022.service;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.fmbx.entity.FmbxBpMain;
import com.fmb.server2022.fmbx.entity.FmbxBpMedia;
import com.fmb.server2022.fmbx.entity.FmbxBpParterHotelInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpParterInfo;
import com.fmb.server2022.fmbx.entity.FmbxBpReviewHis;
import com.fmb.server2022.fmbx.service.IFmbxBpMainService;
import com.fmb.server2022.fmbx.service.IFmbxBpMediaService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpReviewHisService;
import com.fmb.server2022.reqdomain.ReqUpdateBp;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;

import static com.fmb.server2022.service.kafka.FmbKafkaTopic.PUBLIC_GROUP_BP_NEED_CHECK;

@Service
public class BpService  {


    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;


    @Autowired
    FmbKafkaProducerService kafkaService ;

    @Autowired
    IFmbxBpMainService fmbxBpMainService ;

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxBpParterHotelInfoService  hotelInfoService ;


    @Autowired
    IFmbxBpMediaService bpMediaService ;

    @Autowired
    IFmbxBpMainService mainService ;

    @Autowired
    UserUnionService unionService ;

    @Autowired
    IFmbxBpReviewHisService reviewHisService ;

    @Autowired
    BpService self ;


    /**
     * 供应商完善信息
     * @param resultMap
     * @param input
     */

    @DSTransactional
    public void updateBp( HashMap<String, Object> resultMap,ReqUpdateBp input) {

        resultMap.put("result",0) ;
        SessionUser nowUserInfo = unionService.nowUserInfo();
        Integer bpId = input.getBpId();


        LocalDateTime now = LocalDateTime.now();
        FmbxBpMain bp = mainService.getById(bpId);
        if (bp == null) {
            throw new BadLogicException("供应商ID不存在:"+ bpId);
        }

        if (bp.getCheckStatus()==2) {
            throw new BadLogicException("该供应商的信息正在等待审核,不能提交修改");
        }

        if (input.getUpdateType()==2) {

//            LambdaUpdateWrapper<FmbxBpMain> wrm = new LambdaUpdateWrapper<>();
//            wrm.set(FmbxBpMain::getCheckStatus,2)
//                    .eq(FmbxBpMain::getBpId,bpId) ;
//
//            mainService.update(wrm) ;

            FmbxBpMain umain = new FmbxBpMain();
            umain.setBpId(bpId);
            umain.setCheckStatus(2);
            mainService.updateById(umain);

        }

        //更新供应商信息
        FmbxBpParterInfo update = new FmbxBpParterInfo();
        BeanUtils.copyProperties(input,update);
        update.setUtime(now);
        parterInfoService.updateById(update) ;


        //更正证照信息
        Integer licenseMediaid = input.getLicenseMediaid();
        int type =1 ;
        String licenceNmae = "营业执照";
        self.saveOrUpdateBpMedia(nowUserInfo, bpId, now, licenseMediaid, type, licenceNmae);
        //特种经营许可 不为空
        if (input.getSpecialBpMediaid() != null) {

            int sptype =2 ;
//            //先清除
            Wrapper<FmbxBpMedia> wrapDel = Wrappers.lambdaQuery(FmbxBpMedia.class)
                    .eq(FmbxBpMedia::getBpId,bpId)
                    .eq(FmbxBpMedia::getFileType,sptype)
                    ;
            bpMediaService.remove(wrapDel);

            //再添加
            for (int i = 0; i < input.getSpecialBpMediaid().length; i++) {

                    FmbxBpMedia fmbxBpMedia = new FmbxBpMedia();
                    fmbxBpMedia.setBpId(bpId);
                    fmbxBpMedia.setMediaId(input.getSpecialBpMediaid()[i]);
                    fmbxBpMedia.setFileTypeName("特种经营许可证");
                    fmbxBpMedia.setFileType(sptype);
                    fmbxBpMedia.setCreateUserId(nowUserInfo.getUserId());
                    fmbxBpMedia.setCreateUserType(nowUserInfo.getUserType());
                    fmbxBpMedia.setCtime(now);
                    fmbxBpMedia.setUtime(now);
                    bpMediaService.save(fmbxBpMedia) ;

            }
        }

        //更新酒店新
        if (bp.getParterType()==1) {
            FmbxBpParterHotelInfo hotelInfo = new FmbxBpParterHotelInfo();
            BeanUtils.copyProperties(input,hotelInfo);
            hotelInfo.setUtime(now);
            hotelInfoService.updateById(hotelInfo) ;
        }


        //增加 review 记录
        if (input.getUpdateType()==2) {
            FmbxBpReviewHis fmbxBpReviewHis = new FmbxBpReviewHis();
            fmbxBpReviewHis.setBpId(input.getBpId());
            fmbxBpReviewHis.setCtime(now);
            fmbxBpReviewHis.setCheckResult("");
            fmbxBpReviewHis.setRejectReason("");
            fmbxBpReviewHis.setStatus(1);
            reviewHisService.save(fmbxBpReviewHis) ;
        }

        //最后 通知 【提醒】供应商入驻信息审批
        if (input.getUpdateType()==2) {

            HashMap<String, Object> info = new HashMap<>();
            info.put("bpId", bpId);

            FmbKafkaProducerService.sendUsePublicGroup(info,PUBLIC_GROUP_BP_NEED_CHECK,""+bpId) ;

        }

        resultMap.put("result",1) ;


    }





    /**
     * 存储 营业执照
     * @param nowUserInfo
     * @param bpId
     * @param now
     * @param mediaId
     * @param type
     * @param licenceName
     */
    public void saveOrUpdateBpMedia(SessionUser nowUserInfo, Integer bpId, LocalDateTime now, Integer mediaId, int type, String licenceName) {
        LambdaQueryWrapper<FmbxBpMedia> queryWrapper = Wrappers.lambdaQuery(FmbxBpMedia.class)
                .eq(FmbxBpMedia::getBpId, bpId)
//                .eq(FmbxBpMedia::getMediaId, mediaId)
                .eq(FmbxBpMedia::getFileType,type)
                ;

        FmbxBpMedia mediaLicence = bpMediaService.getOne(queryWrapper);
        //营业执照文件
        if (mediaLicence == null) {
            FmbxBpMedia fmbxBpMedia = new FmbxBpMedia();
            fmbxBpMedia.setBpId(bpId);
            fmbxBpMedia.setMediaId(mediaId);
            fmbxBpMedia.setFileTypeName(licenceName);
            fmbxBpMedia.setFileType(type);
            fmbxBpMedia.setCreateUserId(nowUserInfo.getUserId());
            fmbxBpMedia.setCreateUserType(nowUserInfo.getUserType());
            fmbxBpMedia.setCtime(now);
            fmbxBpMedia.setUtime(now);

            bpMediaService.save(fmbxBpMedia) ;

        }else  if(mediaLicence.getMediaId().intValue()!=mediaId.intValue()) {

            mediaLicence.setMediaId(mediaId);
            mediaLicence.setFileTypeName(licenceName);
            mediaLicence.setCreateUserId(nowUserInfo.getUserId());
            mediaLicence.setCreateUserType(nowUserInfo.getUserType());
            mediaLicence.setUtime(now);

            bpMediaService.updateById(mediaLicence) ;

        }
    }
//
//    /**
//     * 查询需要待审 action 记录
//     * @param bpId
//     * @return
//     */
//    private FmbxBpReviewHis queryCheckIngAction(@NotNull Integer bpId) {
//        LambdaQueryWrapper<FmbxBpReviewHis> actionCheck = new LambdaQueryWrapper<>();
//
//        actionCheck.eq(FmbxBpReviewHis::getBpId, bpId)
//                   .eq(FmbxBpReviewHis::getStatus,1) ;
//
//        return actionService.getOne(actionCheck);
//    }
//
//
//    public void bpCheck(JSONObject par, HashMap<String, Object> resultMap) {
//
//        SessionUserInfoOfFmbAdmin userInfoBytoken = tokenService.getUserInfoBytoken(MDC.get(FmbConstants.TOKEN));
//
//        int opType = par.getInteger("opType").intValue();
//        int bpId = par.getInteger("bpId").intValue();
//        String reason = par.getString("reason") ;
//
//        FmbxBusinessPartner dbBp = getFmbxBusinessPartner(bpId);
//
//
//        FmbxBpReviewHis checkAction = queryCheckIngAction(bpId);
//
//        if (checkAction == null) {
//            throw new BadLogicException("应存在待审记录,数据异常") ;
//        }
//        LocalDateTime now = LocalDateTime.now();
//
//
//        resultMap.put("msg","") ;
//        //审核通过
//        if (opType== 1) {
//
//            FmbxBusinessPartner update = new FmbxBusinessPartner();
//            update.setBpId(bpId);
//            update.setCheckStatus(3);
//
//            bpService.updateById(update) ;
//
//
//            checkAction.setRejectReason("");
//            checkAction.setAdminUid(userInfoBytoken.getUserId());
//            checkAction.setChecktime(now);
//            checkAction.setCheckResult("审核通过");
//            checkAction.setStatus(2);
//            actionService.updateById(checkAction) ;
//
//            resultMap.put("msg","审核通过") ;
//
//        }
//        //拒绝
//        if (opType== 2) {
//
//            FmbxBusinessPartner update = new FmbxBusinessPartner();
//            update.setBpId(bpId);
//            update.setCheckStatus(1);
//            bpService.updateById(update) ;
//
//            checkAction.setRejectReason(reason);
//            checkAction.setAdminUid(userInfoBytoken.getUserId());
//            checkAction.setChecktime(now);
//            checkAction.setCheckResult("审核拒绝");
//            checkAction.setStatus(3);
//
//            actionService.updateById(checkAction) ;
//
//            resultMap.put("msg","审核拒绝") ;
//
//        }
//
//
//        resultMap.put("result",1) ;
//
//
//
//
//
//
//    }
//
//
//    public void bpActionList(JSONObject par, HashMap<String, Object> resultMap) {
//
//            LambdaQueryWrapper<FmbxBpReviewHis> wraper = new LambdaQueryWrapper<>(FmbxBpReviewHis.class)
//                    .eq(StringUtils.isNotBlank(par.getString("bpId")),  FmbxBpReviewHis::getBpId,par.getString("bpId"))
//                    ;
//            Integer reqPageNo = par.getInteger("pageno");
//            Integer pagesize = par.getInteger("pagesize");
//            //构造分页参数
//            IPage<FmbxBpReviewHis> page = new Page<>(reqPageNo ==null?1: reqPageNo, pagesize ==null?10: pagesize);
//            //查询得到结果
//            IPage<FmbxBpReviewHis> pageInfo = fmbxBusinessPartnerCheckActionService.page(page,wraper);
//
//            resultMap.put("listsData",pageInfo.getRecords()) ;
//            resultMap.put("total",pageInfo.getTotal()) ;
//            resultMap.put("pagesize",pageInfo.getSize()) ;
//            resultMap.put("pageno",pageInfo.getCurrent()) ;
//        }
//
//
//    public void bpList(JSONObject par, HashMap<String, Object> resultMap) {
//
//
//            String key = par.getString("key");
//            boolean haveKey = StringUtils.isNotBlank(key) ;
//
//            //构造查询
//            LambdaQueryWrapper<FmbxBusinessPartner> wraper = new LambdaQueryWrapper<>(FmbxBusinessPartner.class) ;
//
//            if (haveKey) {
//                wraper .and(wr ->
//                        {
//
//                            wr.eq(
//                                    FmbxBusinessPartner::getBpId, key)
//                                    .or()
//                                    // 账号名字
//                                    .like( FmbxBusinessPartner::getAccountName, key)
//                                    .or()
//                                    // 联系电话
//                                    .eq( FmbxBusinessPartner::getPartnerContactsPhone, key)
//                                    .or()
//                                    // 联系人
//                                    .like(  FmbxBusinessPartner::getPartnerContactsName, key)
//                                    .or()
//                                    // 联系人
//                                    .like( FmbxBusinessPartner::getPartnerName, key);
//                        }
//
//                ) ;
//            }
//
//            // 账号类型:1-主账号,2-子账号
//            wraper.eq(null!=par.getInteger("accountType"),  FmbxBusinessPartner::getAccountType,par.getInteger("accountType"))
//
//                    // 账号类型:1-正常,0-禁用
//                    .eq(null!=par.getInteger("accountStatus"),  FmbxBusinessPartner::getAccountStatus,par.getInteger("accountStatus"))
//
//                    // 审核状态:1-待供应商完善,2-待商务审核,3-审核通过
//                    .eq(null!=par.getInteger("checkStatus"),  FmbxBusinessPartner::getCheckStatus,par.getInteger("checkStatus"))
//
//                    // 我司对接人,后台用户uid
//                    .eq(null!=par.getInteger("adminUid"),  FmbxBusinessPartner::getAdminUid,par.getInteger("adminUid"))
//
//                    // 商家类型:1-酒店,2-其他
//                    .eq(null!=par.getInteger("parterType"),  FmbxBusinessPartner::getParterType,par.getInteger("parterType"))
//
//                    // 省份
//                    .eq(StringUtils.isNotBlank(par.getString("partnerProvince")),  FmbxBusinessPartner::getPartnerProvince,par.getString("partnerProvince"))
//
//                    // 市
//                    .eq(StringUtils.isNotBlank(par.getString("partnerCity")),  FmbxBusinessPartner::getPartnerCity,par.getString("partnerCity"))
//
//
//                    .orderBy("1".equals(par.getString("sort")), false, FmbxBusinessPartner::getCtime)
//                    .orderBy("2".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderCount)
//                    .orderBy("3".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleGoodsCount)
//                    .orderBy("4".equals(par.getString("sort")), false, FmbxBusinessPartner::getSaleOrderMoney)
//
//            ;
//
//            Integer reqPageNo = par.getInteger("pageno");
//            Integer pagesize = par.getInteger("pagesize");
//            //构造分页参数
//            IPage<FmbxBusinessPartner> page = new Page<>(reqPageNo ==null?1: reqPageNo, pagesize ==null?10: pagesize);
//            //查询得到结果
//            IPage<FmbxBusinessPartner> pageInfo = bpService.page(page,wraper);
//
//            resultMap.put("listsData",pageInfo.getRecords()) ;
//            resultMap.put("total",pageInfo.getTotal()) ;
//            resultMap.put("pagesize",pageInfo.getSize()) ;
//            resultMap.put("pageno",pageInfo.getCurrent()) ;
//        }
//
//
//    public void bpDetail(Integer bpId, HashMap<String, Object> resultMap) {
//
//        FmbxBusinessPartner bp = getFmbxBusinessPartner(bpId);
//
//        resultMap.put("bp",bp) ;
//
//    }
//
//    private FmbxBusinessPartner getFmbxBusinessPartner(Integer bpId) {
//        FmbxBusinessPartner bp = bpService.getById(bpId);
//        if(bp==null ){
//            throw new BadLogicException("供应商不存在:"+bpId);
//        }
//        return bp;
//    }
//
//
//    public void bpDetailNoPass(Integer bpId, HashMap<String, Object> resultMap) {
//
//        FmbxBusinessPartner bp = getFmbxBusinessPartner(bpId);
//        //清除敏感信息
//        bp.setPasswd(null);
//        bp.setSalt(null);
//
//        resultMap.put("bp",bp) ;
//
//    }
//
//
//    public void fmbxBpUpdate(FmbxBusinessPartner input, HashMap<String, Object> resultMap) {
//
//        FmbxBusinessPartner dbBp = getFmbxBusinessPartner(input.getBpId());
//
//
//        BeanUtils.copyProperties(input,dbBp);
//
//        //如果设置了密码
//        if(StringUtils.isNotBlank( input.getPasswd())){
//            String salt = RandomUtil.randomString(8);
//            dbBp.setPasswd(MD5Util.md5(input.getPasswd()+salt));
//            dbBp.setSalt(salt) ;
//        }
//
//        dbBp.setUtime(LocalDateTime.now());
//
//        bpService.updateById(dbBp) ;
//
//
//        resultMap.put("result",1) ;
//
//
//    }

}
