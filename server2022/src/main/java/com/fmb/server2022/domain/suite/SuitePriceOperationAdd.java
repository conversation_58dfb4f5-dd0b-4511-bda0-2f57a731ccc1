package com.fmb.server2022.domain.suite;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;

@Data
public class SuitePriceOperationAdd {


    /**
     * 加价模式
     * 现价单晚加价
     */
    private BigDecimal goodsPriceAdd;

    /**
     * 加价模式
     * 结算价加价
     */
    private BigDecimal settlePriceAdd;

    ArrayList<AddDateInfo> dateInfo ;

    public SuitePriceOperationAdd() {
        this.dateInfo = new ArrayList<>() ;
    }


    /**
     * 整价模式
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 整价模式
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 整价模式
     * 结算价
     */
    private BigDecimal settlePrice;


}
