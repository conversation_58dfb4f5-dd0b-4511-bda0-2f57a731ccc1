package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BpsRoomInfoDomain  implements Serializable {


    public  static final Map<Integer, String> addBedTypeMap;
    public  static final Map<Integer, String> smokeTypeMap;
    public  static final Map<Integer, String> bathtubTypeMap;
    public  static final Map<Integer, String> roomStatusMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"未知");
                map.put(2,"不可加床");
                map.put(3,"免费加不含早");
                map.put(4,"免费加含早");
                map.put(5,"收费加床不含早");
                map.put(6,"收费加床含早");
            addBedTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"可吸烟");
                map.put(2,"禁烟");
                map.put(3,"部分客房可吸烟");
            smokeTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(0,"没有");
                map.put(1,"有");
            bathtubTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(0,"屏蔽");
                map.put(1,"启用");
            roomStatusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer roomId ;


    @ExcelProperty(value = "商户id",index = 1)
    private Long bpsId ;


    @ExcelProperty(value = "房型名称",index = 2)
    private String roomName ;


    @ExcelProperty(value = "房型说明",index = 3)
    private String roomIntroduce ;


    @ExcelProperty(value = "房间总数",index = 4)
    private Integer roomNum ;


    @ExcelProperty(value = "楼层",index = 5)
    private String floorNumber ;


    @ExcelProperty(value = "建筑面积",index = 6)
    private String buildArea ;


    @ExcelProperty(value = "床型",index = 7)
    private String bedType ;


    @ExcelProperty(value = "床型值",index = 8)
    private String bedTypeVal ;


    @ExcelProperty(value = "床数量",index = 9)
    private Integer bedNum ;


    @ExcelProperty(value = "床宽度",index = 10)
    private String bedWidth ;


    // 加床:1-未知,2-不可加床,3-免费加不含早,4-免费加含早,5-收费加床不含早,6-收费加床含早
    @ExcelIgnore
    private Integer addBedType ;

    @ExcelProperty(value = "加床",index = 11)
    private String addBedTypeStr ;
    public String getAddBedTypeStr() {return addBedTypeMap.get(addBedType);}


    @ExcelProperty(value = "最大入住成人数",index = 12)
    private Integer maxPeopleNum ;


    // 吸烟信息:1-可吸烟,2-禁烟,3-部分客房可吸烟
    @ExcelIgnore
    private Integer smokeType ;

    @ExcelProperty(value = "吸烟信息",index = 13)
    private String smokeTypeStr ;
    public String getSmokeTypeStr() {return smokeTypeMap.get(smokeType);}


    // 有无浴缸:0-没有,1-有
    @ExcelIgnore
    private Integer bathtubType ;

    @ExcelProperty(value = "有无浴缸",index = 14)
    private String bathtubTypeStr ;
    public String getBathtubTypeStr() {return bathtubTypeMap.get(bathtubType);}


    // 是否启用:0-屏蔽,1-启用
    @ExcelIgnore
    private Integer roomStatus ;

    @ExcelProperty(value = "是否启用",index = 15)
    private String roomStatusStr ;
    public String getRoomStatusStr() {return roomStatusMap.get(roomStatus);}


    @ExcelProperty(value = "台后操作用户###用户uid",index = 16)
    private Integer adminUid ;


@ExcelProperty(value = "创建时间",index = 17,converter = TimestampConverter.class)
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Timestamp ctime ;



@ExcelProperty(value = "更新时间",index = 18,converter = TimestampConverter.class)
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Timestamp utime ;


    @ExcelIgnore
    private List<HashMap<String,Object>> images  ;

    @ExcelIgnore
    private Boolean isSelected  ;

//    @ExcelProperty(value = "文件id",index = 19)
//    private Integer mediaId ;
//
//
//    @ExcelProperty(value = "网页地址",index = 20)
//    private String url ;

}