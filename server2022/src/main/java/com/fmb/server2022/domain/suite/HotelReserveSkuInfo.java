package com.fmb.server2022.domain.suite;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class HotelReserveSkuInfo {
    private Integer skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 套餐id
     */
    private Integer suiteId;

    /**
     * 是否可拆分:0-不能拆分,1-可以拆分
     */
    private Integer canDivide;

    /**
     * 售卖开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sellStartTime;

    /**
     * 售卖结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime sellEndTime;

    /**
     * 是否限购:0-不限,1-限购
     */
    private Integer flagBuyLimit;

    /**
     * 每单最多购买
     */
    private Integer eachOrderMaxNum;

    /**
     * 每单最少购买
     */
    private Integer eachOrderMinNum;

    /**
     * 每个用户最多买多少
     */
    private Integer maxBuyAllNum;

    /**
     * 售卖倒计时显示:0-不显示,1-显示
     */
    private Integer sellCountDownShowFlag;

    /**
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;

    /**
     * 库存
     */
    private Integer stockNum;

    /**
     * 可预约入住开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveChooseStartTime;

    /**
     * 可预约入住结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveChooseEndTime;

    /**
     * 预约有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveValidityStartTime;

    /**
     * 预约有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime reserveValidityEndTime;

    /**
     * 不可入住星期,多个以逗号分隔
     */
    private String invalidReserveWeekDays;

    /**
     * 不可入住日期,多个以逗号分隔
     */
    private String invalidReserveDate;

    /**
     * 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
     */
    private Integer isReturn;

    /**
     * 退换政策
     */
    private String returnPolicy;

    /**
     * 退款有效期天数
     */
    private Integer returnValidDay;

    /**
     * 退款有效期时间
     */
    private String returnValidTime;

    /**
     * 退款有效期秒数(计算值)
     */
    private Integer returnValidSecond;

    /**
     * 开启自助退款:0-否,1-是
     */
    private Integer isAutoReturn;

    /**
     * 购买须知
     */
    private String buyNote;

    /**
     * 支付成功短信
     */
    private String smsPaySuccess;

    public List<Integer> getInvalidReserveWeekDaysList() {
        String[] arr = invalidReserveWeekDays.split(",");
        List<Integer> re = new ArrayList<>();
        try {
            for(String s : arr){
                re.add(Integer.valueOf(s));
            }
        }catch (Exception e){}
        return re;
    }

    public void setInvalidReserveWeekDaysList(List<Integer> invalidReserveWeekDaysList) {
        StringBuffer sb = new StringBuffer();
        invalidReserveWeekDaysList.stream().forEach (item -> { sb.append(item).append(","); } );
        this.invalidReserveWeekDays = sb.toString();
    }

    public List<String> getInvalidReserveDateList() {
        if (StringUtils.isNotBlank(invalidReserveDate)) {
            return Arrays.asList(invalidReserveDate.split(","));
        }else{
            return new ArrayList<String>(0);
        }
    }

    public void setInvalidReserveDateList(List<String> invalidReserveDateList) {
        if(invalidReserveDateList.size()>0) {
            StringBuffer sb = new StringBuffer();
            invalidReserveDateList.stream().forEach(item -> {
                sb.append(item).append(",");
            });
            this.invalidReserveDate = sb.toString();
        }else{
            this.invalidReserveDate = "";
        }
    }
}
