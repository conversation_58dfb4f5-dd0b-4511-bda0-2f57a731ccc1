package com.fmb.server2022.domain ;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class FrontRoomSkuDomain  implements Serializable {
    public  static final Map<Integer, String> bathtubTypeMap;
    public  static final Map<String, String> showStatusMap;
    public static String STATUS_ORDEROOM = "orderoom";
    public static String STATUS_SCHEDULED = "scheduled";

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"没浴缸");
            map.put(1,"有浴缸");
            bathtubTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
            map.put("resvroom","预约");
            map.put("scheduled","选择日期");
            showStatusMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer skuId ;

    @JsonIgnore
    private Integer suiteId ;

    @JsonIgnore
    private String suiteName ;


    private Integer nightMin ;


    private Integer roomId ;


    private String roomName ;


    private Integer buildArea ;

    @JsonIgnore
    private Integer bathtubType ;

    public String getBathtubTypeStr() {
        return bathtubTypeMap.get(bathtubType);
    }

    private Integer maxPeopleNum ;

    @JsonIgnore
    private String bedType ;

    @JsonIgnore
    private String suiteContent ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        return suiteContentList;
    }

    public String getSuiteContentIntro(){
        String re = "";
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        for(SuiteContent suiteContent : suiteContentList){
            re += suiteContent.getIntro() + "+";
        }
        return re;
    }

    public List<SuiteContent> getSuiteContentGroupList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        return getSuiteContentsGroup(getNightMin(),getBuildArea(),getBathtubTypeStr(),getMaxPeopleNum(),getBedTypeStr(),suiteContentList);
    }

    public static List<SuiteContent> getSuiteContentsGroup(Integer nightMin,Integer buildArea,String bathtubTypeStr, Integer maxPeopleNum, String bedTypeStr,List<SuiteContent> suiteContentList) {
        List<SuiteContent> reList = new ArrayList<>();
        SuiteContent s0 = new SuiteContent();
        s0.setType("住");
        s0.setCount(nightMin);
        s0.setCountUnit("晚");
        s0.setIntro(buildArea+"㎡|"+bathtubTypeStr+"|"+maxPeopleNum+"人|"+bedTypeStr);
        reList.add(s0);
        Map<String, List<SuiteContent>> groupBy = suiteContentList.stream().collect(Collectors.groupingBy(SuiteContent::getType, LinkedHashMap::new,Collectors.toList()));
        for(Map.Entry<String, List<SuiteContent>> ele : groupBy.entrySet()){
            SuiteContent s = new SuiteContent();
            s.setType(ele.getKey());
            String intro = "";
            List<SuiteContent> list = ele.getValue();
            for (SuiteContent sc : list){
                intro += sc.getIntro()+"+";
            }
            if(intro.endsWith("+")){
                intro = intro.substring(0,intro.lastIndexOf("+"));
            }
            s.setCount(ele.getValue().size());
            s.setIntro(intro);
            reList.add(s);
        }
        return reList;
    }

    private String bedTypeStr ;

    public String getBedTypeStr(){
        return FrontRoomDomain.getBedTypeStr(this.bedType);
    }

    private BigDecimal goodsPrice;

    private BigDecimal marketPrice;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        return "起";
    }


    private String mediaUrl;

    private Integer mediaCount;

    private String status;

    @JsonIgnore
    private Integer sellNum ;

    private Integer sortValue;

}