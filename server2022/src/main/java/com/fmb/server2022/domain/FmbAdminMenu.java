package com.fmb.server2022.domain;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FmbAdminMenu {


    /**
     * text : 首页
     * hasChildren : false
     * path : /indexConfig/systemNotice
     * children : [{"text":"供应商管理","hasChildren":false,"path":"/supplierManagement"},{"text":"供应商合同管理","hasChildren":false,"path":"/supplierManagement/contractManagement"}]
     */

    private  Integer id ;
    private String text;

    private boolean hasChildren;

    public boolean isHasChildren() {
        return children!=null && !children.isEmpty();
    }
    private String path;
    private List<FmbAdminMenu> children;


    public FmbAdminMenu() {
        this.children = new ArrayList<>() ;
    }
}
