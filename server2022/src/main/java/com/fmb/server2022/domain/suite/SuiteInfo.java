package com.fmb.server2022.domain.suite;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SuiteInfo {
    private Integer suiteId;

    /**
     * 活动id
     */
    private Integer xaid;

    /**
     * 套餐名字
     */
    private String name;

    /**
     * 套餐食享(json)
     */
    private String suiteContent;

    /**
     * 总间夜量
     */
    private Integer totalNeight;

    /**
     * 连住晚数
     */
    private Integer nightMin;

    /**
     * 价格输入方式:1-加价模式,2-整价模式
     */
    private Integer priceInputType;

    /**
     * 是否有订单:0-否,1-是
     */
    private Integer flagHaveOrder;

    /**
     * 日历房:0-否,1-是
     */
    private Integer flagStandardHotel;

    /**
     * 预售房券:0-否,1-是
     */
    private Integer flagHotelReserve;

    /**
     * 购买限制:0-无限制,1-有限制
     */
    private Integer buyLimit;

    /**
     * 预售房券需提前预定天数
     */
    private Integer preReserveDay;

    /**
     * 预售房券需提前预定时间(String)
     */
    private String preReserveTime;

    /**
     * 提前预定秒数(计算值)
     */
    private Integer preReserveSecond;

    /**
     * 下单后未付款订单关闭时间(单位秒)
     */
    private Integer autoCloseOrderSecond;

    /**
     * 证件资料:0-不需要,1-需提供一位用户身份信息,2-需提供所有用户身份信息

     */
    private Integer certFlag;

    /**
     * 证件信息提示文本
     */
    private String certTip;

    /**
     * 风险提示:0-不需要,1-需要
     */
    private Integer flagRiskWarning;

    /**
     * 风险提示文本
     */
    private String riskWarningTip;

    /**
     * 退货政策:0-不支持,1-有效期结束前未使用可退,2-过期后未使用可退,3-未使用随时退 (有效期内+过期后)
     */
    private Integer isReturn;

    /**
     * 退换政策
     */
    private String returnPolicy;

    /**
     * 退款有效期天数
     */
    private Integer returnValidDay;

    /**
     * 退款有效期时间
     */
    private String returnValidTime;

    /**
     * 退款有效期秒数(计算值)
     */
    private Integer returnValidSecond;

    /**
     * 开启自助退款:0-否,1-是
     */
    private Integer isAutoReturn;

    /**
     * 发票政策:0-不支持开票,1-父母邦开票,2-商家开票
     */
    private Integer isInvoice;

    /**
     * 版本号
     */
    private Integer versionNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime utime;

    /**
     * 购买须知
     */
    private String buyNote;

    /**
     * 支付成功短信
     */
    private String smsPaySuccess;

    /**
     * 二次确认短信
     */
    private String smsSecondConfirm;

    /**
     * 改签短信
     */
    private String smsReorder;

    /**
     * 订房单备注
     */
    private String roomOrderNote;

    /**
     * 内部备注
     */
    private String interiorNote;

    /**
     * 关联房型
     */
    private List<FmbxSuiteRoomSku> roomList;

}
