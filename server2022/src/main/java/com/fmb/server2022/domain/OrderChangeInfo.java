package com.fmb.server2022.domain;

import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.util.FmbOrderInfoUtil;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/7/6 3:50 下午
 */
@Data
public class OrderChangeInfo {
    /**
     * 改签次序
      */
    String serialNumber;
    /**
     * 订单号
     */
    String orderSn ;
    /**
     * 订单状态
     */
    String orderStatus ;
    /**
     * 改签原因
     */
    String changeReason  ;
    /**
     * 支付方式
     */
    String paySource ;
    /**
     * 交易流水号
     */
    String paySn ;
    /**
     * 订单金额
     */
    BigDecimal money ;
    /**
     * 支付金额
     */
    BigDecimal realPayMoney ;
    /**
     * 现金退款
     */
    BigDecimal returnMoney ;
    /**
     * 余额退款
     */
    BigDecimal returnCash ;


    public static  OrderChangeInfo toOrderChangeInfo(FmbOrderInfo orderInfo){

        final OrderChangeInfo orderChangeInfo = new OrderChangeInfo();

        orderChangeInfo.setSerialNumber("");
        orderChangeInfo.setOrderSn(orderInfo.getOrderSn());
        orderChangeInfo.setOrderStatus(AdminOrderActionDomain.orderStatusMap.get(""+orderInfo.getOrderStatus()));
        orderChangeInfo.setChangeReason("");
        orderChangeInfo.setPaySource(FmbOrderInfoUtil.orderPaySource(orderInfo));
        orderChangeInfo.setPaySn(orderInfo.getPaySn());
        orderChangeInfo.setMoney(orderInfo.getMoney());
        orderChangeInfo.setRealPayMoney(FmbOrderInfoUtil.oldOrderRealPayMoney( orderInfo));
        orderChangeInfo.setReturnMoney(new BigDecimal("0"));
        orderChangeInfo.setReturnCash(new BigDecimal("0"));





        return  orderChangeInfo ;

    }

}
