package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fmb.server2022.fmbx.entity.FmbMycpsCommissionOrder;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbOrderReturns;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbxHotelGoodsDetail;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 10:33 上午
 */

@Data
public class FmbOrderDetailQueryResult {


    /**
     * 订单生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderSn;
    //活动标题
    private String activityTitle;

    private Integer goodsType;
    private Integer orderType;

    //单价
    private BigDecimal skuGoodsPrice ;
    //扣除了分摊优惠券的sku价格
    private BigDecimal skuGoodsPriceSubCoupon ;
    //结算价
    private BigDecimal skuSettlePrice ;

    //房券就是购买数量 ,日历房就是 房间的数量
    private int skuNumber ;

    //本月已退
    private int last30DayReturnNum ;

    /**
     * 订单名称
     */
    private String orderName;


    /**
     * 套餐名字
     */
    private  String suiteName ;

    /**
     * 酒店 入离时间
     */
    private String hotelCheckInDate ;
    private String hotelCheckOutDate ;
    private String hotelCheckDateStr ;
    private ArrayList<Integer> hotelRoomReturnOption ;
    private Integer hotelRoomNum ;
    //电子码总个数
    private Integer hotelExchangeCode ;
    //每个电子码对应的间夜数
    private Integer eachExchangeCodeTotalNightRoom ;
    //订单 晚数
    private Integer dayDiff ;
    //酒店房间最多可退
    private int hotelRemainMaxRoomCanCancel;

    /**
     * 整单 优惠券退款金额
     */
    private BigDecimal couponMoney ;
    private BigDecimal cashMoney ;
    private BigDecimal returnedCashMoney ;

    private BigDecimal eachSkuCouponDivide ;


    List<FmbMycpsCommissionOrder> mycpsOrderList  ;

    /**
     * 订单扣除 优惠券和运费
     */
    private BigDecimal moneyNoCouponAndShippingFee  ;

    /**
     * 订单实际支付金额
     */
    private BigDecimal realPayMoney ;

    List<FmbxHotelGoodsDetail> listHotelDetail ;


    private String skuName ;

    private String returnStr ;
    private String receiver ;
    private String mobile ;



    //已经发起的退款单个数
    private int alreadyReturnSkuNumber ;
    //本地已退
    private BigDecimal alreadyReturnMoney ;
    private String alreadyReturnMoneyDesc ;

    /**
     * 剩余最大可退
     */
    private BigDecimal remainReturnMaxMoney ;
    private BigDecimal remainReturnMaxMoneySubUsed ;

    private String  remainReturnMaxMoneyDesc ;
    /**
     * 酒店房券相关----------------开始
     */
    /**
     * 房券总数量
     */
    private int hotelReserveCodeTotal ;
    /**
     * 可退房券数量
     */
    private int hotelReserveCodeCanReturnTotal ;

    private int hotelReserveCodeNotReturn ;

    //入住 有效期
    private String reserveChooseDate ;

    List<FmbReserveHotelCodes> hotelCodeCanReturn ;

    List<FmbReserveHotelCodes> hotelCodeCanReturnInCludePartUse ;


    /**
     * 酒店房券相关----------------结束
     */


    @JsonIgnore
    FmbOrderInfo orderInfo ;

    @JsonIgnore
    FmbOrderGoods orderGoods;

    @JsonIgnore
    List<FmbOrderReturns> returnsList ;

    @JsonIgnore
    boolean existsSettle ;
}
