package com.fmb.server2022.domain.suite;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExcelPriceAllInfo {

    @ExcelProperty(value = "年", index = 0)
    String year;
    @ExcelProperty(value = "月", index = 1)
    String month;
    @ExcelProperty(value = "日", index = 2)
    String day;

    /**
     * 整价模式
     * 原价
     */
    @ExcelProperty(value = "划线价", index = 3)
    Double marketPrice;

    /**
     * 整价模式
     * 现价
     */
    @ExcelProperty(value = "售价", index = 4)
    Double goodsPrice;

    /**
     * 整价模式
     * 结算价
     */
    @ExcelProperty(value = "结算价", index = 5)
    Double settlePrice;

}
