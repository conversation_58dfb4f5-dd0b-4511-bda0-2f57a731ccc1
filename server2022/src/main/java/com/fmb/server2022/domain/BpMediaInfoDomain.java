package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class BpMediaInfoDomain  implements Serializable {


    public  static final Map<Integer, String> fileTypeMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"营业执照");
                map.put(2,"特殊经营");
            fileTypeMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer bpmId ;

    @ExcelProperty(value = "文件id",index = 1)
    private Integer mediaId ;

    @ExcelProperty(value = "网页地址",index = 2)
    private String url ;

    @ExcelProperty(value = "文件的业务名称,如营业执照",index = 3)
    private String fileTypeName ;

    // 文件类型:1-营业执照,2-特殊经营
    @ExcelIgnore
    private Integer fileType ;

    @ExcelProperty(value = "文件类型",index = 4)
    private String fileTypeStr ;
    public String getFileTypeStr() {return fileTypeMap.get(fileType);}

    private LocalDate expireDate ;

    public String getExpireStatus() {

        if (expireDate != null) {
            if (expireDate.compareTo(LocalDate.now())>0) {
                return "" ;
            }

            if (expireDate.compareTo(LocalDate.now())<0) {
                return "(已过期)" ;
            }
        }

        return "";
    }

}