package com.fmb.server2022.domain.suite;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PriceAddInfo {

    /**
     * 现价
     */
    private BigDecimal goodsPriceAdd;

    /**
     * 结算价
     */
    private BigDecimal settlePriceAdd;

    public PriceAddInfo(SuitePriceOperationAdd operationAdd) {
    }

    public PriceAddInfo() {
    }

    public PriceAddInfo(BigDecimal goodsPriceAdd, BigDecimal settlePriceAdd) {
        this.goodsPriceAdd = goodsPriceAdd;
        this.settlePriceAdd = settlePriceAdd;
    }
}
