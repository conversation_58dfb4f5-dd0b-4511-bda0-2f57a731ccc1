package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class ReserveSkuInfoDomain  implements Serializable {







    @ExcelProperty(value = "套餐id",index = 0)
    private Integer suiteId ;

    @ExcelProperty(value = "预售房券需提前预定天数",index = 1)
    private Integer preReserveDay ;

    @ExcelProperty(value = "预售房券需提前预定时间(String)",index = 2)
    private String preReserveTime ;

    @ExcelProperty(value = "日历房skuid",index = 3)
    private Integer skuId ;
}