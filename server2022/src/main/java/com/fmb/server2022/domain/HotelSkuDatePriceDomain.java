package com.fmb.server2022.domain ;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
public class HotelSkuDatePriceDomain  implements Serializable {


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "日期",index = 0)
    private Date datecol ;

    @ExcelProperty(value = "原价",index = 1)
    private Double marketPrice ;

    @ExcelProperty(value = "现价",index = 2)
    private Double goodsPrice ;

    @ExcelProperty(value = "结算价",index = 3)
    private Double settlePrice ;

    @ExcelProperty(value = "商户房型id",index = 4)
    private Long roomId ;

    @ExcelProperty(value = "skuid",index = 5)
    private Long skuId ;

    @ExcelProperty(value = "库存",index = 6)
    private Long stockNum ;

    @ExcelProperty(value = "房态",index = 7)
    private Integer stockStatus ;

    @ExcelIgnore
    private Integer priceId ;
    @ExcelIgnore
    private Integer stockId ;
    @ExcelIgnore
    private Integer versionNum ;

    @ExcelIgnore
    private boolean havePrice  ;
    @ExcelIgnore
    private boolean haveStock  ;

    public boolean isHavePrice() {
        return   priceId.intValue()!=0;
    }

    public boolean isHaveStock() {
        return stockNum>0  &&  1==stockStatus.intValue() ;
    }

    @ExcelIgnore
    private Integer aimStock ;
}