package com.fmb.server2022.domain ;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class HotelReserveMinPriceDomain  implements Serializable {







    @ExcelProperty(value = "",index = 0)
    private Integer skuId ;

    @ExcelProperty(value = "现价",index = 1)
    private Double goodsPrice ;

    @ExcelProperty(value = "原价",index = 2)
    private Double marketPrice ;

    @ExcelProperty(value = "间夜",index = 3)
    private Integer totalNeight ;
}