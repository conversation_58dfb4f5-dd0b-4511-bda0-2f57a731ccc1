package com.fmb.server2022.domain;

import com.fmb.server2022.fmbx.entity.SysMenu;
import com.fmb.server2022.fmbx.entity.SysPermission;
import com.fmb.util.TreeNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class FmbTreeInfo implements TreeNode  {

    SysMenu menu ;

    String name ;
    Integer menuId ;

    ArrayList<SysPermission> samePagePermission ;

    public FmbTreeInfo() {
        this.children = new ArrayList<>();
    }

    List<FmbTreeInfo> children ;

    public FmbTreeInfo(SysMenu menu) {
        this.menu = menu ;
        this.children = new ArrayList<>();
        this.samePagePermission = new ArrayList<>() ;
    }

    @Override
    public Object id() {
        return menu.getMenuId();
    }

    @Override
    public Object parentId() {
        return menu.getParentMenuid();
    }

    @Override
    public Boolean root() {
        return menu.getMenuId().intValue()==1;
    }

    @Override
    public List<? extends TreeNode> getChildren() {
        return this.children;
    }

    @Override
    public void setChildren(List children) {
        this.children = children ;
    }


    public String getName() {
        return this.menu.getMenuName();
    }

    public Integer getMenuId() {
        return this.menu.getMenuId() ;
    }

}
