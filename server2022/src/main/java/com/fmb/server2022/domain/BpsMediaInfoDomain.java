package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BpsMediaInfoDomain  implements Serializable {


    public  static final Map<Integer, String> groupvalMap;
    public  static final Map<Integer, String> bpsTypeMap;
    public  static final Map<Integer, String> adminTypeMap;
    public  static final Map<Integer, String> statusMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"外观");
                map.put(2,"大厅");
                map.put(3,"餐饮");
                map.put(4,"家庭亲子");
                map.put(5,"休闲");
                map.put(6,"健身房");
                map.put(7,"公共区域");
                map.put(8,"周边");
                map.put(9,"其他");
                map.put(10,"房间");
            groupvalMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"酒店");
                map.put(2,"其他");
            bpsTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"父母邦后台");
                map.put(2,"供应商后台");
            adminTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(0,"无效");
                map.put(1,"有效###状态:0");
                map.put(1,"正常 ");
            statusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer id ;


    @ExcelProperty(value = "文件id",index = 1)
    private Integer mediaId ;


    @ExcelProperty(value = "网页地址",index = 2)
    private String url ;


    // 图片分组:1-外观,2-大厅,3-餐饮,4-家庭亲子,5-休闲,6-健身房,7-公共区域,8-周边,9-其他,10-房间
    @ExcelIgnore
    private Integer groupval ;

    @ExcelProperty(value = "图片分组",index = 3)
    private String groupvalStr ;
    public String getGroupvalStr() {return groupvalMap.get(groupval);}


    @ExcelProperty(value = "名称描述",index = 4)
    private String description ;


    @ExcelProperty(value = "商户名称",index = 5)
    private String name ;


    // 商户类型:1-酒店,2-其他
    @ExcelIgnore
    private Integer bpsType ;

    @ExcelProperty(value = "商户类型",index = 6)
    private String bpsTypeStr ;
    public String getBpsTypeStr() {return bpsTypeMap.get(bpsType);}


    // 后台用户类型:1-父母邦后台,2-供应商后台
    @ExcelIgnore
    private Integer adminType ;

    @ExcelProperty(value = "后台用户类型",index = 7)
    private String adminTypeStr ;
    public String getAdminTypeStr() {return adminTypeMap.get(adminType);}


    @ExcelProperty(value = "后台用户名字",index = 8)
    private String adminName ;


@ExcelProperty(value = "创建时间",index = 9,converter = TimestampConverter.class)
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
private Timestamp ctime ;



    // 是否有效:0-无效,1-有效###状态:0-屏蔽,1-正常 
    @ExcelIgnore
    private Integer status ;

    @ExcelProperty(value = "是否有效",index = 10)
    private String statusStr ;
    public String getStatusStr() {return statusMap.get(status);}


    @ExcelProperty(value = "排序值",index = 11)
    private Integer rankval ;

}