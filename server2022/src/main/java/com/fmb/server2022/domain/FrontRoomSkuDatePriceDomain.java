package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class FrontRoomSkuDatePriceDomain implements Serializable {
    public  static final Map<Integer, String> showStatusMap;
    public  static final Map<Integer, String> chooseStatusMap;
    public  static final Integer REMAINDER_NUM = 10;//充足阈值
    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"");
            map.put(1,"低价");
            map.put(2,"无库存");
            map.put(3,"充足");
            map.put(4,"余");
            showStatusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"不可选");
            map.put(1,"可选");
            chooseStatusMap = Collections.unmodifiableMap(map);
        }
    }

    private String dateStr;

    private String holiday;

    @JsonIgnore
    private Integer roomId ;

    @JsonIgnore
    private Integer suiteId ;

    @JsonIgnore
    private Integer skuId ;

    private BigDecimal goodsPrice;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        String signnum = "";
        if (goodsPrice.signum()==-1){
            signnum = "-";
        }else if (goodsPrice.signum()==1) {
            signnum = "+";
        }
        return signnum+"￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice.abs());
    }

    @JsonIgnore
    private BigDecimal minGoodsPrice;

    @JsonIgnore
    private BigDecimal minMarketPrice;

    @JsonIgnore
    private Integer stockNum ;

    @JsonIgnore
    private Integer stockStatus ;


    private Integer showStatus;

    public String getShowStatusStr() {
        String re = showStatusMap.get(showStatus);
        if (showStatus==4){
            re += stockNum;
        }
        return re;
    }

    private Integer chooseStatus;

    public Integer getChooseStatus(){
        int re = 1;
        Integer showStatus = getShowStatus();
        if(showStatus==null || getShowStatus()==2){
            re = 0;
        }
        return re;
    }

    public String getChooseStatusStr() {
        return chooseStatusMap.get(getChooseStatus());
    }

}
