package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class ActivityPublishLogDomain  implements Serializable {



    public  static final Map<String, String> opTypeMap;

    static
    {
        {
            HashMap<String, String> map = new HashMap<>();
            map.put("1","上架");
            map.put("2","下架");
            opTypeMap = Collections.unmodifiableMap(map);
        }
    }



    @ExcelProperty(value = "id",index = 0)
    private Integer publishLogId ;

    @ExcelProperty(value = "活动id",index = 1)
    private Long xaid ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 2,converter = TimestampConverter.class)
private Timestamp ctime ;


    @ExcelProperty(value = "操作结果",index = 3)
    private String result ;

    @ExcelProperty(value = "创建人的uid",index = 4)
    private Long createUid ;

    @ExcelProperty(value = "创建人的名字",index = 5)
    private String createUidName ;

    @ExcelProperty(value = "操作类型",index = 6)
    private String opTypeStr ;

    @ExcelIgnore
    private Integer opType ;

    public String getOpTypeStr() {
        return opTypeMap.get(opType+"");
    }
}