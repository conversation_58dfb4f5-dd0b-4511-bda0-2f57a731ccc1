package com.fmb.server2022.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class BpsInfoDomain implements Serializable {


    public static final Map<Integer, String> bpsTypeMap;
    public static final Map<Integer, String> starLevelMap;
    public static final Map<Integer, String> statusMap;
    public static final Map<Integer, String> flagBasicMap;
    public static final Map<Integer, String> flagRoomMap;
    public static final Map<Integer, String> flagPicMap;
    public static final Map<Integer, String> flagVideoMap;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "酒店");
            map.put(2, "其他");
            bpsTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "豪华型");
            map.put(2, "高档型");
            map.put(3, "舒适型");
            starLevelMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "屏蔽");
            map.put(1, "正常 ");
            statusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "完成");
            map.put(0, "未完成");
            flagBasicMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "完成");
            map.put(0, "未完成");
            flagRoomMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "完成");
            map.put(0, "未完成");
            flagPicMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "完成");
            map.put(0, "未完成");
            flagVideoMap = Collections.unmodifiableMap(map);
        }
    }


    @ExcelProperty(value = "", index = 0)
    private Integer bpsId;


    @ExcelProperty(value = "商户名称", index = 1)
    private String name;


    // 商户类型:1-酒店,2-其他
    @ExcelIgnore
    private Integer bpsType;

    @ExcelProperty(value = "商户类型", index = 2)
    private String bpsTypeStr;

    public String getBpsTypeStr() {
        return bpsTypeMap.get(bpsType);
    }


    @ExcelProperty(value = "联系号码", index = 3)
    private String phone;


    @ExcelProperty(value = "省份", index = 4)
    private String provinces;


    @ExcelProperty(value = "区", index = 5)
    private String area;


    @ExcelProperty(value = "酒店地址", index = 6)
    private String address;


    @ExcelProperty(value = "位置经度", index = 7)
    private Float longitude;


    @ExcelProperty(value = "位置纬度", index = 8)
    private Float latitude;


    @ExcelProperty(value = "开业时间哪年", index = 9)
    private String openTime;


    @ExcelProperty(value = "装修时间哪年", index = 10)
    private String decorateTime;


    @ExcelProperty(value = "客房总数", index = 11)
    private Integer houseNum;


    // 星级档次:1-豪华型,2-高档型,3-舒适型
    @ExcelIgnore
    private Integer starLevel;

    @ExcelProperty(value = "星级档次", index = 12)
    private String starLevelStr;

    public String getStarLevelStr() {
        return starLevelMap.get(starLevel);
    }


    @ExcelProperty(value = "入住时间几点后", index = 13)
    private String playTime;


    @ExcelProperty(value = "退房时间几点前", index = 14)
    private String leaveTime;


//    private ArrayList<BpInfo> listBp ;
    @ExcelIgnore
    private ArrayList<HashMap<String,Object>> listBp ;

    // 状态:0-屏蔽,1-正常 
    @ExcelIgnore
    private Integer status;

    @ExcelProperty(value = "状态", index = 15)
    private String statusStr;

    public String getStatusStr() {
        return statusMap.get(status);
    }


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 16, converter = TimestampConverter.class)
    private Timestamp ctime;


    @ExcelProperty(value = "创建者ID", index = 17)
    private Integer createUid;


    @ExcelProperty(value = "更新时间", index = 18, converter = TimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp utime;


    @ExcelProperty(value = "台后操作用户", index = 19)
    private Integer adminUid;


    @ExcelProperty(value = "可编辑的供应商id", index = 20)
    private Integer bpidOfEdit;


    // 基础信息完成度:1-完成,0-未完成
    @ExcelIgnore
    private Integer flagBasic;

    @ExcelProperty(value = "基础信息完成度", index = 21)
    private String flagBasicStr;

    public String getFlagBasicStr() {
        return flagBasicMap.get(flagBasic);
    }


    // 房型信息完成度:1-完成,0-未完成
    @ExcelIgnore
    private Integer flagRoom;

    @ExcelProperty(value = "房型信息完成度", index = 22)
    private String flagRoomStr;

    public String getFlagRoomStr() {
        return flagRoomMap.get(flagRoom);
    }


    // 图片信息完成度:1-完成,0-未完成
    @ExcelIgnore
    private Integer flagPic;

    @ExcelProperty(value = "图片信息完成度", index = 23)
    private String flagPicStr;

    public String getFlagPicStr() {
        return flagPicMap.get(flagPic);
    }


    // 视频信息完成度:1-完成,0-未完成
    @ExcelIgnore
    private Integer flagVideo;

    @ExcelProperty(value = "视频信息完成度", index = 24)
    private String flagVideoStr;

    public String getFlagVideoStr() {
        return flagVideoMap.get(flagVideo);
    }


    @ExcelIgnore
    private Integer edit ;

}