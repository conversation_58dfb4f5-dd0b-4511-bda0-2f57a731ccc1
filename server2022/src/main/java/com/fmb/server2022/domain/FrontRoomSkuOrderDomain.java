package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fmb.server2022.domain.suite.SuiteContent;
import lombok.Data;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FrontRoomSkuOrderDomain {
    public static final Map<Integer, String> bathtubTypeMap;
    public static final Map<String,Object> userInfoMap;

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"");
            map.put(1,"有浴缸");
            bathtubTypeMap = Collections.unmodifiableMap(map);
        }
        {
            Map<String,Object> map = new HashMap<>();
            String[] reArr = {"用户姓名需要与实际消费者身份证件上的姓名保持一致","中文姓名请填写汉字","英文姓名填写格式为姓在前，名在后，中间用“/”隔开，如Abraham <PERSON>，填写为<PERSON>/<PERSON>"};
            map.put("nameDesc","用户姓名填写说明");
            map.put("nameDescList",reArr);
            userInfoMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer xaid ;

    private String title ;

    private Integer skuId ;

    @JsonIgnore
    private Integer suiteId ;

    @JsonIgnore
    private String suiteName ;


    private Integer nightMin ;

    private Integer totalNeight;

    private Integer roomId ;


    private String roomName ;

    public String getRoomName() {
        return this.suiteName+"-"+this.roomName;
    }

    private String buildArea ;

    @JsonIgnore
    private Integer bathtubType ;

    public String getBathtubTypeStr() {
        return bathtubTypeMap.get(bathtubType);
    }

    private Integer maxPeopleNum ;

    @JsonIgnore
    private String bedType ;

    @JsonIgnore
    private String suiteContent ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        return suiteContentList;
    }

    public String getSuiteContentIntro(){
        String re = "";
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        for(SuiteContent suiteContent : suiteContentList){
            re += suiteContent.getIntro() + "+";
        }
        if(re.endsWith("+")){
            re = re.substring(0,re.lastIndexOf("+"));
        }
        return re;
    }

    public List<SuiteContent> getSuiteContentGroupList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        return FrontRoomSkuDomain.getSuiteContentsGroup(getTotalNeight(),getBuildArea(),getBathtubTypeStr(),getMaxPeopleNum(),getBedTypeStr(),suiteContentList);
    }

    private String bedTypeStr ;

    public String getBedTypeStr(){
        return FrontRoomDomain.getBedTypeStr(this.bedType,false);
    }

    private String buyNote;

    private String policy;

    private Integer flagRiskWarning;

    private String riskWarningTip;

    private Map userInfoTxt;

    public Map<String,Object> getUserInfoTxt(){
         return userInfoMap;
    }

}
