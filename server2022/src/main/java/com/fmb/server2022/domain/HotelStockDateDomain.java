package com.fmb.server2022.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class HotelStockDateDomain {


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "日期",index = 0)
    private Date datecol ;

    @ExcelProperty(value = "库存id",index = 1)
    private Long stockId ;


    @ExcelProperty(value = "库存",index = 2)
    private Long stockNum ;

    @ExcelProperty(value = "房态",index = 3)
    private Integer stockStatus ;

    @ExcelProperty(value = "库存是否有效",index = 4)
    private boolean haveStock  ;

    public boolean isHaveStock() {
        return stockId!=0 &&  stockNum>0  &&  1==stockStatus.intValue() ;
    }
}
