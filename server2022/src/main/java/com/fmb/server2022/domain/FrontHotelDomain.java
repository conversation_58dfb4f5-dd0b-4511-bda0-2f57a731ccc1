package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fmb.server2022.controller.front.reqdomain.FrontElement;
import com.fmb.server2022.domain.suite.HotelServices;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FrontHotelDomain implements Serializable {


    public static final Map<Integer, String> starLevelMap;
    public static final Map<Integer, String> starLevelInfoMap;
    public static final Map<Integer, String> takePetMap;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "豪");
            map.put(2, "高");
            map.put(3, "舒");
            starLevelMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "豪华型酒店，酒店装修豪华，可提供优质的服务");
            map.put(2, "高档型酒店，酒店布置典雅，可提供优良的服务");
            map.put(3, "舒适型酒店，酒店设施齐备，可提供良好的服务");
            starLevelInfoMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "可以携带宠物");
            map.put(0, "不可以携带宠物");
            takePetMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer bpsId;

    private String name;

    @JsonIgnore
    private String openTime;

    @JsonIgnore
    private String openTimeType;

    @JsonIgnore
    private Integer houseNum;


    private String phone;

    @JsonIgnore
    private Integer starLevel;

    @JsonIgnore
    private String starLevelStr;

    public String getStarLevelStr() {
        return starLevelMap.get(starLevel);
    }

    @JsonIgnore
    private String starLevelStr2;

    public String getStarLevelStr2() {
        return starLevelInfoMap.get(starLevel);
    }


    private String introduce;


    private String policy;


    private String playTime;


    private String leaveTime;


    private String foodInfo;

    @JsonIgnore
    private Integer takePet;

    private String takePetStr;

    public String getTakePetStr() {
        return takePetMap.get(takePet);
    }

    public FrontElement getOpenTimeInfo(){
        FrontElement e = new FrontElement();
        e.setTitle("营");
        e.setValue(getOpenTimeType()+":"+getOpenTime()+"年"+getOpenTimeType());
        return e;
    }

    public FrontElement getHouseNumInfo(){
        FrontElement e = new FrontElement();
        e.setTitle("房");
        e.setValue("房间:"+getHouseNum()+"间客房");
        return e;
    }

    public FrontElement getStarLevelInfo(){
        FrontElement e = new FrontElement();
        e.setTitle(getStarLevelStr());
        e.setValue("等级:"+getStarLevelStr2());
        return e;
    }

    @JsonIgnore
    private String services;

    private List<HotelServices> hotelServicesList;

    public List<HotelServices> getHotelServicesList() {
        hotelServicesList = FrontRoomDomain.getHotelServicesList(this.services);
        return hotelServicesList;
    }

    private String bgImgUrl;
}