package com.fmb.server2022.domain;

import lombok.Data;

@Data
public class DingProcessNotifyResult {


    /**
     * processInstanceId : w_FfVRutQP-LGeqxo_goFw00681660827316
     * finishTime : 1660827374000
     * corpId : ding16d2df0e7a277ffd
     * EventType : bpms_instance_change
     * businessId : 202208182055000167826
     * title : 侯友法提交的OA审批测试
     * type : finish
     * url : https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid=ding16d2df0e7a277ffd&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId=w_FfVRutQP-LGeqxo_goFw00681660827316&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval
     * result : agree
     * createTime : 1660827316000
     * processCode : PROC-A057AAB1-A778-4DE2-9C1E-D8DEF8A82E5E
     * bizCategoryId :
     * businessType :
     * staffId : 4827080520296281
     */

    private String processInstanceId;
    private long finishTime;
    private String corpId;
    private String EventType;
    private String businessId;
    private String title;
    private String type;
    private String url;
    private String result;
    private long createTime;
    private String processCode;
    private String bizCategoryId;
    private String businessType;
    private String staffId;

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getEventType() {
        return EventType;
    }

    public void setEventType(String EventType) {
        this.EventType = EventType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public String getBizCategoryId() {
        return bizCategoryId;
    }

    public void setBizCategoryId(String bizCategoryId) {
        this.bizCategoryId = bizCategoryId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }
}
