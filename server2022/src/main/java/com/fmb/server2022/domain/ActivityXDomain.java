package com.fmb.server2022.domain ;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import com.fmb.server2022.fmbx.entity.FmbxActivityPublishLog;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class ActivityXDomain  implements Serializable {


    public  static final Map<String, String> businessTypeMap;
    public  static final Map<String, String> isConfirmMap;
    public  static final Map<String, String> flagPublishMap;
    public  static final Map<String, String> flagHaveValidSkuMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("2","酒店");
                map.put("3","北京活动票务");
                map.put("5","上海活动票务");
                map.put("11","北京长线");
                map.put("12","上海长线");
            businessTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","无");
                map.put("1","客服跟进");
                map.put("2","商户确认");
                map.put("3","市场跟进");
            isConfirmMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","待发布");
                map.put("1","已上架");
                map.put("2","已下架");
            flagPublishMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","未售(sku无效)");
                map.put("1","在售");
            flagHaveValidSkuMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "活动id",index = 0)
    private Integer xaid ;

    @ExcelProperty(value = "活动标题",index = 1)
    private String title ;

    @ExcelProperty(value = "活动bannerurl地址",index = 2)
    private String bannerListUrl ;

    // 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线
    @ExcelIgnore
    private Integer businessType ;

    @ExcelProperty(value = "所属业务线",index = 3)
    private String businessTypeStr ;
    public String getBusinessTypeStr() {return businessTypeMap.get(""+businessType);}

    @ExcelProperty(value = "分类id",index = 4)
    private Integer categoryId ;

    @ExcelProperty(value = "商户名称",index = 5)
    private String name ;

    @ExcelProperty(value = "商户id",index = 6)
    private Integer bpsId ;

    @ExcelProperty(value = "商家名称",index = 7)
    private String partnerName ;

    @ExcelProperty(value = "供应商id",index = 8)
    private Integer bpId ;

    @ExcelProperty(value = "我司对接人手机号",index = 9)
    private String adminMobile ;

    @ExcelProperty(value = "我司对接人姓名",index = 10)
    private String adminName ;

    @ExcelProperty(value = "我司对接人台后uid",index = 11)
    private Integer adminUid ;

    // 二次确认方式:0-无,1-客服跟进,2-商户确认,3-市场跟进
    @ExcelIgnore
    private Integer isConfirm ;

    @ExcelProperty(value = "二次确认方式",index = 12)
    private String isConfirmStr ;
    public String getIsConfirmStr() {return isConfirmMap.get(""+isConfirm);}

    @ExcelProperty(value = "创建人的uid",index = 13)
    private Long createUid ;

    @ExcelProperty(value = "创建人的名字",index = 14)
    private String createUidName ;

    @ExcelProperty(value = "最后操作人uid",index = 15)
    private Long lastModifyUid ;

    @ExcelProperty(value = "最后操作人的名字",index = 16)
    private String lastModifyUidName ;

    // 上下架状态:0-待发布,1-已上架,2-已下架
    @ExcelIgnore
    private Integer flagPublish ;

    @ExcelProperty(value = "上下架状态",index = 17)
    private String flagPublishStr ;
    public String getFlagPublishStr() {return flagPublishMap.get(""+flagPublish);}

    // 售卖状态:0-未售(sku无效),1-在售
    @ExcelIgnore
    private Integer flagHaveValidSku ;

    @ExcelProperty(value = "售卖状态",index = 18)
    private String flagHaveValidSkuStr ;
    public String getFlagHaveValidSkuStr() {return flagHaveValidSkuMap.get(""+flagHaveValidSku);}

    @ExcelProperty(value = "省份",index = 19)
    private String provinces ;

    @ExcelProperty(value = "省份下的区",index = 20)
    private String provincesSub ;

    @ExcelProperty(value = "产品所属分站",index = 21)
    private Long cityId ;

    @ExcelProperty(value = "已经删除的sku个数",index = 22)
    private Integer deletedSkuNum ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 23, converter = TimestampConverter.class)
    private Timestamp ctime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间", index = 24, converter = TimestampConverter.class)
    private Timestamp utime;

    @ExcelProperty(value = "产品类型", index = 24, converter = TimestampConverter.class)
    private String cateName ;


    @ExcelIgnore
    private JSONArray banners ;

    @ExcelIgnore
    private FmbxActivityPublishLog publishLog ;
}