package com.fmb.server2022.domain ;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HotelSkuDomain  implements Serializable {


    public  static final Map<String, String> flagSellMap;
    public  static final Map<String, String> statusMap;

static
    {
        {
            //售买状态:0-无效（无可售日期）,1-有效
            HashMap<String, String> map = new HashMap<>();
                map.put("0","无效");
                map.put("1","有效");
            flagSellMap = Collections.unmodifiableMap(map);
        }
        {
            //上下架状态:0-已删除,1-已上架,2-已下架
            HashMap<String, String> map = new HashMap<>();
                map.put("0","已删除");
                map.put("1","已上架");
                map.put("2","已下架");
            statusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "套餐id",index = 0)
    private Integer suiteId ;

    @ExcelProperty(value = "套餐名字",index = 1)
    private String name ;

    @ExcelProperty(value = "总间夜量",index = 2)
    private Integer totalNeight ;

    @ExcelProperty(value = "skuid",index = 3)
    private Integer skuId ;

    @ExcelProperty(value = "套餐加房型名称",index = 4)
    private String skuName ;


    // 售买状态:0-无效（无可售日期）,1-有效
    @ExcelIgnore
    private Integer flagSell ;

    @ExcelIgnore
    private Integer status ;

    @ExcelIgnore
    private Integer priceInputType ;


    @ExcelProperty(value = "价格设置状态",index = 5)
    private String flagSellStr ;
    public String getFlagSellStr() {return flagSellMap.get(flagSell+"");}

    @ExcelProperty(value = "上下架状态",index = 6)
    private String statusStr ;
    public String getStatusStr() {return statusMap.get(status+"");}


    @ExcelProperty(value = "销售量",index = 7)
    private Integer sellNum ;

    @ExcelIgnore
    private ArrayList<HashMap<String,Object>> listSku ;

    @ExcelIgnore
    List<HotelSkuDatePriceDomain> priceList ;

    private Integer flagStandardHotel;

    private Integer flagHotelReserve;

    private String sellTypeStr;


    @ExcelIgnore
    private Set<String> uniqPrice ;
    @ExcelIgnore
    private List<String> months ;
    @ExcelIgnore
    private ArrayList<HashMap<String, Object>> monthList ;
}