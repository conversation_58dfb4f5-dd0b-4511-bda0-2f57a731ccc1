package com.fmb.server2022.domain.suite;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;

@Data
public class AddDateInfo {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField (format="yyyy-MM-dd")
    Date addDateBegin ;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format="yyyy-MM-dd")
    Date addDateEnd ;

    ArrayList<Integer> weeks ;


    public AddDateInfo() {
        this.weeks = new ArrayList<>();
    }

    public boolean isValid(){

        if (addDateBegin != null  && addDateEnd!=null ) {
            return  addDateBegin.getTime()<=addDateEnd.getTime() ;
        }

        return  false ;
    }

}
