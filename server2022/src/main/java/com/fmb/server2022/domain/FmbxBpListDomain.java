package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import com.fmb.server2022.fmbx.entity.FmbxBpContract;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FmbxBpListDomain  implements Serializable {


    public  static final Map<Integer, String> parterTypeMap;
    public  static final Map<Integer, String> accountTypeMap;
    public  static final Map<Integer, String> accountStatusMap;
    public  static final Map<Integer, String> checkStatusMap;
    public  static final Map<Integer, String> orderbyMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"酒店");
                map.put(2,"其他");
            parterTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"主账号");
                map.put(2,"子账号");
            accountTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"正常");
                map.put(0,"禁用");
            accountStatusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"待供应商完善");
                map.put(2,"待商务审核");
                map.put(3,"审核通过");
            checkStatusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"创建时间");
                map.put(2,"销售单数");
                map.put(3,"销售件数");
                map.put(4,"销售金额");
            orderbyMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "供应商ID",index = 0)
    private Integer bpId ;


    @ExcelProperty(value = "账号名字",index = 1)
    private String accountName ;


    // 商家类型:1-酒店,2-其他
    @ExcelIgnore
    private Integer parterType ;

    @ExcelProperty(value = "商家类型",index = 3)
    private String parterTypeStr ;
    public String getParterTypeStr() {return parterTypeMap.get(parterType);}


    @ExcelProperty(value = "创建时间", index = 4, converter = TimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp ctime;

    @ExcelProperty(value = "供应商名称",index = 5)
    private String partnerName ;

    @ExcelProperty(value = "省份",index = 6)
    private String partnerProvince ;


    @ExcelProperty(value = "市",index = 7)
    private String partnerCity ;


    @ExcelProperty(value = "联系人",index = 8)
    private String partnerContactsName ;


    @ExcelProperty(value = "联系手机号",index = 9)
    private String partnerContactsMobile ;


    @ExcelProperty(value = "我司对接人,后台用户uid",index = 10)
    private Integer adminUid ;

    private String createName ;

    private String adminName ;

    @ExcelProperty(value = "电话",index = 11)
    private String phone ;


    @ExcelProperty(value = "登录信息,登录次数",index = 12)
    private Integer loginStatCount ;


    @ExcelProperty(value = "最后登录时间", index = 13, converter = TimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp loginStatLasttime;



    @ExcelProperty(value = "销售统计,订单数",index = 14)
    private Integer saleOrderCount ;


    @ExcelProperty(value = "销售统计,货物数",index = 15)
    private Integer saleGoodsCount ;


    @ExcelProperty(value = "销售统计,销售金额",index = 16)
    private Double saleOrderMoney ;

    @ExcelProperty(value = "父级id",index = 17)
    private Integer pid ;

    // 账号类型:1-主账号,2-子账号
    @ExcelIgnore
    private Integer accountType ;

    @ExcelProperty(value = "账号类型",index = 18)
    private String accountTypeStr ;
    public String getAccountTypeStr() {return accountTypeMap.get(accountType);}


    // 账号类型:1-正常,0-禁用
    @ExcelIgnore
    private Integer accountStatus ;

    @ExcelProperty(value = "账号类型",index = 19)
    private String accountStatusStr ;
    public String getAccountStatusStr() {return accountStatusMap.get(accountStatus);}


    // 审核状态:1-待供应商完善,2-待商务审核,3-审核通过
    @ExcelIgnore
    private Integer checkStatus ;

    @ExcelProperty(value = "审核状态",index = 20)
    private String checkStatusStr ;
    public String getCheckStatusStr() {return checkStatusMap.get(checkStatus);}


    @ExcelIgnore
    private List<FmbxBpContract> contractList;

    @ExcelIgnore
    private List<FmbxBps> shopList;

}
