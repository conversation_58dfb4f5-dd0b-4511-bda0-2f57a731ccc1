package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class HotelDateSkuDomain  implements Serializable {


    public  static final Map<String, String> flagHotelReserveMap;
    public  static final Map<String, String> flagStandardHotelMap;
    public  static final Map<String, String> flagSellMap;
    public  static final Map<String, String> statusMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","否");
                map.put("1","是");
            flagHotelReserveMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","否");
                map.put("1","是");
            flagStandardHotelMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","无效（无可售日期）");
                map.put("1","有效");
            flagSellMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","已删除");
                map.put("1","已上架");
                map.put("2","已下架");
            statusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer skuId ;

    @ExcelProperty(value = "套餐id",index = 1)
    private Integer suiteId ;

    @ExcelProperty(value = "套餐名字",index = 2)
    private String name ;

    @ExcelProperty(value = "商户房型id",index = 3)
    private Integer roomId ;

    @ExcelProperty(value = "房型名称",index = 4)
    private String roomName ;

    @ExcelProperty(value = "总间夜量",index = 5)
    private Integer totalNeight ;

    @ExcelProperty(value = "连住晚数",index = 6)
    private Integer nightMin ;

    // 预售房券:0-否,1-是
    @ExcelIgnore
    private Integer flagHotelReserve ;

    @ExcelProperty(value = "预售房券",index = 7)
    private String flagHotelReserveStr ;
    public String getFlagHotelReserveStr() {return flagHotelReserveMap.get(""+flagHotelReserve);}

    // 日历房:0-否,1-是
    @ExcelIgnore
    private Integer flagStandardHotel ;

    @ExcelProperty(value = "日历房",index = 8)
    private String flagStandardHotelStr ;
    public String getFlagStandardHotelStr() {return flagStandardHotelMap.get(""+flagStandardHotel);}

    @ExcelProperty(value = "预售房券需提前预定天数",index = 9)
    private Integer preReserveDay ;

    @ExcelProperty(value = "预售房券需提前预定时间(String)",index = 10)
    private String preReserveTime ;

    @ExcelProperty(value = "创建人uid",index = 11)
    private Long createUid ;

    @ExcelProperty(value = "创建人用户名",index = 12)
    private String createUidName ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 13,converter = TimestampConverter.class)
private Timestamp ctime ;


    @ExcelProperty(value = "最后修改人uid",index = 14)
    private Long lastUpdateUid ;

    @ExcelProperty(value = "最后修改人用户名",index = 15)
    private String lastUpdateUidName ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "更新时间",index = 16,converter = TimestampConverter.class)
private Timestamp utime ;


    // 售买状态:0-无效（无可售日期）,1-有效
    @ExcelIgnore
    private Integer flagSell ;

    @ExcelProperty(value = "售买状态",index = 17)
    private String flagSellStr ;
    public String getFlagSellStr() {return flagSellMap.get(""+flagSell);}

    // 上下架状态:0-已删除,1-已上架,2-已下架
    @ExcelIgnore
    private Integer status ;

    @ExcelProperty(value = "上下架状态",index = 18)
    private String statusStr ;
    public String getStatusStr() {return statusMap.get(""+status);}


    @ExcelProperty(value = "sku排序值",index = 19)
    private Integer suiteSortValue ;

    @ExcelProperty(value = "套餐排序值",index = 20)
    private Integer skuSortValue ;
    @ExcelProperty(value = "销售量",index = 21)
    private Integer sellNum ;
}