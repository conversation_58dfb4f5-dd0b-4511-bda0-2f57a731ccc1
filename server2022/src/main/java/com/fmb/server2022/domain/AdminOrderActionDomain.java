package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class AdminOrderActionDomain  implements Serializable {


    public  static final Map<String, String> orderStatusMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","未付款");
                map.put("1","等待发货");
                map.put("2","已发货");
                map.put("3","退货中");
                map.put("4","已退货");
                map.put("5","交易成功");
                map.put("6","交易关闭");
                map.put("7","支付尾款");
                map.put("8","支付成功后续处理中");
            orderStatusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "订单号",index = 0)
    private String orderSn ;

    // 订单状态:0-未付款,1-等待发货,2-已发货,3-退货中,4-已退货,5-交易成功,6-交易关闭,7-支付尾款,8-支付成功后续处理中
    @ExcelIgnore
    private Integer orderStatus ;

    @ExcelProperty(value = "订单状态",index = 1)
    private String orderStatusStr ;
    public String getOrderStatusStr() {return orderStatusMap.get(""+orderStatus);}

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "操作时间",index = 2,converter = TimestampConverter.class)
private Timestamp logTime ;


    @ExcelProperty(value = "动作说明",index = 3)
    private String actionNote ;

    @ExcelProperty(value = "后台操作的用户UID",index = 4)
    private Integer adminUid ;

    @ExcelProperty(value = "用户名",index = 5)
    private String name ;

    @ExcelProperty(value = "真实姓名",index = 6)
    private String realname ;
}