package com.fmb.server2022.domain.suite;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SuiteContent {
    private String type;

    private String intro;

    private String content;

    private Integer count;

    @JsonIgnore
    private String countUnit="项";

    public String getCountStr(){
        if (count == null){
            return "";
        }
        return count+countUnit;
    }
}
