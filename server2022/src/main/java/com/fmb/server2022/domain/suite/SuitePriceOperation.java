package com.fmb.server2022.domain.suite;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.util.FmbDateUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.stream.Collectors;

@Data
public class SuitePriceOperation {

    private   static DecimalFormat decimalFormat = new DecimalFormat("##.##") ;

    //加价模式
    public static int TYPE_ADD_MODE  =1 ;
    //整价模式
    public static int TYPE_FULL_RANGE_MODE  =2 ;
    //固定日期整价模式操作
    public static int TYPE_FULL_FIXED_DATE_MODE =3 ;
    //excel整价上传
    public static int TYPE_FULL_BY_EXCEL =4 ;
    //加价模式切换 清除数据
    public static int TYPE_SWITCH_ADDMODE_REMOVE_ALL =5 ;

//    /**
//     * 加价模式使用
//     */
//    private Integer  roomid ;
    //加价模式 开始使用的是 roomIds 加  suiteId 方式,后来改为直接使用 skuId
    // 20221205
    private ArrayList<Integer>  roomIds ;
    private Integer suiteId ;
    private ArrayList<Integer> skuId ;

    private Integer totalNeight;

    /**
     * 原价
     */
    private BigDecimal marketPrice;

    /**
     * 现价
     */
    private BigDecimal goodsPrice;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;


    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @JSONField (format="yyyy-MM-dd")
    Date dateBegin ;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @JSONField(format="yyyy-MM-dd")
    Date dateEnd ;

    ArrayList<SuitePriceOperationAdd> adds  ;
    ArrayList<String>  fixedDate ;


    HashMap<String, Object> result = new HashMap<>();

    public SuitePriceOperation() {
        this.adds = new ArrayList<>();
        this.totalNeight = 1 ;
        this.roomIds = new ArrayList<>() ;
        this.fixedDate = new ArrayList<>() ;
    }


    /**
     * 构造加价模式日期信息
     * @return
     */
    public String buildDateInfoOfAddMode(){
        final StringBuffer stringBuffer = new StringBuffer();

        stringBuffer.append("加价起始日期:");
        stringBuffer.append(FmbDateUtil.toDate10Str(getDateBegin()));
        stringBuffer.append("至");
        stringBuffer.append(FmbDateUtil.toDate10Str(getDateEnd()));

        for (int i = 0; i < getAdds().size(); i++) {

            SuitePriceOperationAdd add = adds.get(i) ;

            String result = "" ;
            for (AddDateInfo addDateInfo : add.getDateInfo()) {
                    result += "从"
                            +FmbDateUtil.toDate10Str(  addDateInfo.getAddDateBegin())+"至"
                            +FmbDateUtil.toDate10Str(addDateInfo.getAddDateEnd())
                            +genWeekStr(addDateInfo.getWeeks())+"," ;
                if(result.endsWith(",")){
                    result = result.substring(0,result.length()-1) ;
                }
            }
            stringBuffer.append("  "+(i+1)+":"+result +",售价增加"+ decimalFormat.format( add.getGoodsPriceAdd())+",结算价增加"+decimalFormat.format(add.getSettlePriceAdd()) ) ;
        }

        return stringBuffer.toString() ;
    }

    /**
     * 构造加价模式 价格信息
     * @return
     */
    public String buildPriceInfoOfAddMode(){
        final StringBuffer stringBuffer = new StringBuffer();

        stringBuffer.append("基准价格:划线价:"+ decimalFormat.format( getMarketPrice())) ;
        stringBuffer.append(",售价:"+ decimalFormat.format( getGoodsPrice())) ;
        stringBuffer.append(",结算价:"+ decimalFormat.format( getSettlePrice())) ;

        return stringBuffer.toString() ;
    }



    private   String genWeekStr(ArrayList<Integer> weeks) {

        if (weeks.isEmpty()){
            return "" ;
        }

        final StringBuffer stringBuffer = new StringBuffer();
        for (Integer week : weeks) {

            if (week==7){
                stringBuffer.append("星期日,") ;
            }else {
                stringBuffer.append("星期") ;
                stringBuffer.append(week) ;
                stringBuffer.append(",") ;
            }
        }
        if (stringBuffer.length()==0)
        {
            return "" ;
        }

        return "里的("+ stringBuffer.toString().substring(0,stringBuffer.length()-1)+")";
    }


    public String buildDateInfoOfFullMode() {

        final StringBuffer stringBuffer = new StringBuffer();


        for (int i = 0; i < getAdds().size(); i++) {

            SuitePriceOperationAdd add = adds.get(i) ;

            String result = "" ;
            for (AddDateInfo addDateInfo : add.getDateInfo()) {
                result += "从"
                        +FmbDateUtil.toDate10Str(  addDateInfo.getAddDateBegin())+"至"
                        +FmbDateUtil.toDate10Str(addDateInfo.getAddDateEnd())
                        +genWeekStr(addDateInfo.getWeeks())+"," ;
                if(result.endsWith(",")){
                    result = result.substring(0,result.length()-1) ;
                }
            }
            stringBuffer.append(""+(i+1)+":"+result +"划线价"+decimalFormat.format(add.getMarketPrice())+",售价"+ decimalFormat.format( add.getGoodsPrice())+",结算价"+decimalFormat.format(add.getSettlePrice()) ) ;
        }

        return stringBuffer.toString() ;

    }


    public String buildDateInfoOfFixDateMode() {
        return getFixedDate().stream().collect(Collectors.joining(","));

    }

    public String buildPriceInfoOfFixDateMode() {

        return "划线价" + decimalFormat.format(getMarketPrice())
                + ",售价" + decimalFormat.format(getGoodsPrice()) + ",结算价" + decimalFormat.format(getSettlePrice());
    }
}
