package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.reqdomain.RoomNumStatus;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 酒店确认订单 费用列表
 * <AUTHOR>
 * @Date: 2023/1/18 3:39 下午
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfirmOrderMoneyInfo {

    String mainTitle ;
    String subTitle ;

    // 总价
    BigDecimal totalMoney ;

    //优惠券号
    String couponSn ;
    BigDecimal  couponDiscountMoney ;

    // 1-10间 房间的状态和当前选中状态
    List<RoomNumStatus> roomNumStatus ;

    //酒店 可选的最小 最大房间数
    Integer minRoomNum ;
    Integer maxRoomNum ;

    Integer adviseSkuNumber ;

    List<ConfirmOrderMoneyGroup> groups ;
    private FmbxSku fmbxSku;

    public ConfirmOrderMoneyInfo() {
        this.groups = new ArrayList<>() ;
        this.roomNumStatus = new ArrayList<>() ;
        this.couponSn = "" ;
        this.couponDiscountMoney = BigDecimal.ZERO ;
    }

    public BigDecimal countAllGoodsMoney() {

         BigDecimal bigDecimal = new BigDecimal("0");
        for (ConfirmOrderMoneyGroup group : groups) {
            for (ConfirmOrderMoneyItem c : group.getItemList()) {
                bigDecimal  =bigDecimal.add(c.getPriceBigAll());
            }
        }
        return bigDecimal;
    }

    /**
     * 增加 优惠券信息 如果之前已经存在 就增加条目
     * @param t
     * @param item
     * @param couponMoney
     */
    public void addNewGroupOfDiscount(String t, String item, BigDecimal couponMoney) {

        final Optional<ConfirmOrderMoneyGroup> first = groups.stream().filter(r -> r.getGroupType() == ConfirmOrderMoneyGroup.GROUPTYPE_2).findFirst();

        ConfirmOrderMoneyItem it = new ConfirmOrderMoneyItem();
        it.setLeftTitle(item);
        it.setSkuNumber("");
        it.setSkuNumberN(0);
        it.setPrice("-"+ FmbConstants.RMB_CHAR+ FmbNumberUtil.bigDecimal2Str( couponMoney));
        it.setPriceBig(couponMoney.multiply(new BigDecimal("-1")));


        if (first.isPresent()) {
            first.get().getItemList().add(it) ;
        }else {
            ConfirmOrderMoneyGroup group = new ConfirmOrderMoneyGroup();
            group.setTitle(t);
            group.setGroupType(ConfirmOrderMoneyGroup.GROUPTYPE_2);
            List<ConfirmOrderMoneyItem> list = new ArrayList<>();
            list.add(it);
            group.setItemList(list);
            groups.add(group) ;
        }

    }

    public void setFmbxSku(FmbxSku fmbxSku) {
        this.fmbxSku = fmbxSku;
    }

    public FmbxSku getFmbxSku() {
        return fmbxSku;
    }



}
