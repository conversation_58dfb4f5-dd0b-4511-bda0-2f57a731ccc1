package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class FmbxBpReviewListDomain  implements Serializable {


    public  static final Map<Integer, String> statusMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"待审");
                map.put(2,"审核通过");
                map.put(3,"审核拒绝");
            statusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer actionId ;


    @ExcelProperty(value = "账号名字",index = 1)
    private String accountName ;


    @ExcelProperty(value = "审核状态说明",index = 2)
    private String checkResult ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 3,converter = TimestampConverter.class)
private Timestamp ctime ;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "审核时间",index = 4,converter = TimestampConverter.class)
private Timestamp checktime ;



    // 状态:1-待审,2-审核通过,3-审核拒绝
    @ExcelIgnore
    private Integer status ;

    @ExcelProperty(value = "状态",index = 5)
    private String statusStr ;
    public String getStatusStr() {return statusMap.get(status);}


    @ExcelProperty(value = "拒绝原因",index = 6)
    private String rejectReason ;

}