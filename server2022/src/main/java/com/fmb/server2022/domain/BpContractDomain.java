package com.fmb.server2022.domain ;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class BpContractDomain  implements Serializable {


    public  static final Map<Integer, String> bpcRunuingStatusMap;
    public  static final Map<Integer, String> bpcAllStatusMap;
    public  static final Map<Integer, String> citypMap;
    public  static final Map<Integer, String> businessTypeMap;
    public  static final Map<Integer, String> bpTemplateTypeMap;
    public  static final Map<Integer, String> bpCreateTypeMap;
    public  static final Map<Integer, String> bpSealTypeMap;
    public  static final Map<Integer, String> bpSealUseInfoMap;

static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(0,"发起申请");
                map.put(1,"直属经理审批");
                map.put(2,"法务审批");
                map.put(3,"上海财务审批");
                map.put(4,"CEO审批");
                map.put(5,"合同归档提交");
                map.put(6,"合同确认无误");
                map.put(7,"拒绝");
            bpcRunuingStatusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"新创建");
                map.put(2,"审批中");
                map.put(3,"被终止");
                map.put(4,"完成");
                map.put(5,"取消");
            bpcAllStatusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"北京");
                map.put(2,"上海");
            citypMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(2,"酒店");
                map.put(3,"北京活动票务");
                map.put(5,"上海活动票务");
                map.put(11,"北京长线");
                map.put(12,"上海长线");
                map.put(20,"运营");
            businessTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"标准合同");
                map.put(2,"非标准合同");
            bpTemplateTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"新建");
                map.put(2,"续签");
                map.put(3,"补充协议");
            bpCreateTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"父母邦先用印");
                map.put(2,"商家先用印");
            bpSealTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
                map.put(1,"双方已盖章已归档");
                map.put(2,"我方先盖章未归档");
                map.put(3,"不予受理");
            bpSealUseInfoMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "",index = 0)
    private Integer bpcId ;

    @ExcelProperty(value = "供应商id",index = 1)
    private Long bpId ;

    // 审批中合同状态:0-发起申请,1-直属经理审批,2-法务审批,3-上海财务审批,4-CEO审批,5-合同归档提交,6-合同确认无误,7-拒绝
    @ExcelIgnore
    private Integer bpcRunuingStatus ;

    @ExcelProperty(value = "审批中合同状态",index = 2)
    private String bpcRunuingStatusStr ;
    public String getBpcRunuingStatusStr() {return bpcRunuingStatusMap.get(bpcRunuingStatus);}

    @ExcelProperty(value = "合同编号",index = 3)
    private String bpcNumber ;

    @ExcelIgnore
    private Integer cityId ;


    @ExcelProperty(value = "分站",index = 4)
    private String cityIdStr ;


    public void setCityIdStr(String cityIdStr) {
        this.cityIdStr = cityIdStr;
    }

    public String getCityIdStr() {
        return citypMap.get(cityId);
    }



@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 5,converter = TimestampConverter.class)
private Timestamp ctime ;


    // 所属业务线:2-酒店,3-北京活动票务,5-上海活动票务,11-北京长线,12-上海长线,20-运营
    @ExcelIgnore
    private Integer businessType ;

    @ExcelProperty(value = "所属业务线",index = 6)
    private String businessTypeStr ;
    public String getBusinessTypeStr() {return businessTypeMap.get(businessType);}

    // 合同形式:1-标准合同,2-非标准合同
    @ExcelIgnore
    private Integer bpTemplateType ;

    @ExcelProperty(value = "合同形式",index = 7)
    private String bpTemplateTypeStr ;
    public String getBpTemplateTypeStr() {return bpTemplateTypeMap.get(bpTemplateType);}

    // 合同类型:1-新建,2-续签,3-补充协议
    @ExcelIgnore
    private Integer bpCreateType ;

    @ExcelProperty(value = "合同类型",index = 8)
    private String bpCreateTypeStr ;
    public String getBpCreateTypeStr() {return bpCreateTypeMap.get(bpCreateType);}

    @ExcelProperty(value = "合同名称",index = 9)
    private String bpcName ;

    @ExcelProperty(value = "合同开始日期",index = 10)
    private Date bpStartDate ;

    @ExcelProperty(value = "合同结束日期",index = 11)
    private Date bpEndDate ;

    @ExcelIgnore
    private String accountName ;

    @ExcelProperty(value = "供应商名字",index = 12)
    private String partnerName ;

    @ExcelProperty(value = "发起人名字",index = 13)
    private String sysUsername ;


    @ExcelIgnore
    private int contractDays ;

    @ExcelIgnore
    private String contractStatus ;

    public int getContractDays() {

        if (getBpEndDate() != null && getBpStartDate()!=null) {
           return (int) ((getBpEndDate().getTime() - getBpStartDate().getTime())/(1000*3600*24L));
        }


        return contractDays;
    }

    public void setContractDays(int contractDays) {
        this.contractDays = contractDays;
    }


    public String getContractStatus() {

        if (getBpEndDate() != null && getBpStartDate()!=null) {
            if (getBpEndDate().getTime()> System.currentTimeMillis()) {
                return "有效" ;
            }

            if (getBpEndDate().getTime()< System.currentTimeMillis()) {
                return "无效" ;
            }
        }

        return "异常";
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }
}