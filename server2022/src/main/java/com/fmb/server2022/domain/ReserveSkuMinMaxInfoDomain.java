package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class ReserveSkuMinMaxInfoDomain  implements Serializable {



    @ExcelProperty(value = "",index = 0)
    private BigDecimal minPrice ;

    @ExcelProperty(value = "",index = 1)
    private BigDecimal maxPrice ;

    @ExcelProperty(value = "",index = 2)
    private BigDecimal minMarketPrice ;

    @ExcelProperty(value = "",index = 3)
    private BigDecimal maxMarketPrice ;
}