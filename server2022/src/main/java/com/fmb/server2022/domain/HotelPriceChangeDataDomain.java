package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class HotelPriceChangeDataDomain  implements Serializable {


    public  static final Map<String, String> changeTypeMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("1","加价模式");
                map.put("2","整价模式");
                map.put("3","整价指定日期");
                map.put("4","excel上传");
                map.put("5","加价模式切换清除数据");
            changeTypeMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "price_operate_id",index = 0)
    private Long priceOperateId ;

    @ExcelProperty(value = "fmbx活动主表id",index = 1)
    private Long xaid ;

    @ExcelProperty(value = "活动名称",index = 2)
    private String activityName ;

    @ExcelProperty(value = "套餐房型名字",index = 3)
    private String skuName ;

    @ExcelProperty(value = "sku_id",index = 4)
    private String skuId ;

    @ExcelProperty(value = "操作数据(json格式)",index = 5)
    private String operateData ;

    // 修改类型:1-加价模式,2-整价模式,3-整价指定日期,4-excel上传,5-加价模式切换清除数据
    @ExcelIgnore
    private Long changeType ;

    @ExcelProperty(value = "修改类型",index = 6)
    private String changeTypeStr ;
    public String getChangeTypeStr() {return changeTypeMap.get(""+changeType);}

    @ExcelProperty(value = "后台用户名字",index = 7)
    private String adminName ;

    @ExcelProperty(value = "用户uid",index = 8)
    private Long adminUid ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 9,converter = TimestampConverter.class)
private Timestamp ctime ;


@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "套餐修改日期",index = 10,converter = TimestampConverter.class)
private Timestamp targetDate ;

    @ExcelProperty(value = "日期操作信息",index = 11)
    private String operateDateInfo ;

    @ExcelProperty(value = "价格信息",index = 12)
    private String operatePriceInfo ;
}
