package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HotelReserveSkuDomain  implements Serializable {


    public  static final Map<String, String> flagHotelReserveMap;
    public  static final Map<String, String> flagStandardHotelMap;
    public  static final Map<String, String> flagSellMap;
    public  static final Map<String, String> statusMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","否");
                map.put("1","是");
            flagHotelReserveMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","否");
                map.put("1","是");
            flagStandardHotelMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","无效");
                map.put("1","有效");
            flagSellMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","已删除");
                map.put("1","已上架");
                map.put("2","已下架");
                map.put("3","待完善");
            statusMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "skuid",index = 0)
    private Integer skuId ;

    @ExcelProperty(value = "套餐id",index = 1)
    private Integer suiteId ;

    @ExcelProperty(value = "套餐名字",index = 2)
    private String suiteName ;

    @ExcelProperty(value = "总间夜量",index = 3)
    private Integer totalNeight ;

    @ExcelProperty(value = "sku名字",index = 4)
    private String skuName ;

    // 预售房券:0-否,1-是
    @ExcelIgnore
    private Integer flagHotelReserve ;

    @ExcelProperty(value = "预售房券",index = 5)
    private String flagHotelReserveStr ;
    public String getFlagHotelReserveStr() {return flagHotelReserveMap.get(""+flagHotelReserve);}

    // 日历房:0-否,1-是
    @ExcelIgnore
    private Integer flagStandardHotel ;

    @ExcelProperty(value = "日历房",index = 6)
    private String flagStandardHotelStr ;
    public String getFlagStandardHotelStr() {return flagStandardHotelMap.get(""+flagStandardHotel);}

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "可预约入住开始时间",index = 7,converter = TimestampConverter.class)
private Timestamp reserveChooseStartTime ;


@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "可预约入住结束时间",index = 8,converter = TimestampConverter.class)
private Timestamp reserveChooseEndTime ;


@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "售卖开始时间",index = 9,converter = TimestampConverter.class)
private Timestamp sellStartTime ;


@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "售卖结束时间",index = 10,converter = TimestampConverter.class)
private Timestamp sellEndTime ;


    @ExcelProperty(value = "原价",index = 11)
    private Double marketPrice ;

    @ExcelProperty(value = "现价",index = 12)
    private Double goodsPrice ;

    @ExcelProperty(value = "结算价",index = 13)
    private Double settlePrice ;

    @ExcelProperty(value = "库存",index = 14)
    private Long stockNum ;

    @ExcelProperty(value = "创建人uid",index = 15)
    private Long createUid ;

    @ExcelProperty(value = "创建人用户名",index = 16)
    private String createUidName ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 17,converter = TimestampConverter.class)
private Timestamp ctime ;


    @ExcelProperty(value = "最后修改人uid",index = 18)
    private Long lastUpdateUid ;

    @ExcelProperty(value = "最后修改人用户名",index = 19)
    private String lastUpdateUidName ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "更新时间",index = 20,converter = TimestampConverter.class)
private Timestamp utime ;


    // 售买状态(库存和当前时间在售卖时间内的结果):0-无效,1-有效
    @ExcelIgnore
    private Integer flagSell ;

    @ExcelProperty(value = "售买状态(库存和当前时间在售卖时间内的结果)",index = 21)
    private String flagSellStr ;
    public String getFlagSellStr() {return flagSellMap.get(""+flagSell);}

    // 上下架状态:0-已删除,1-已上架,2-已下架,3-待完善
    @ExcelIgnore
    private Integer status ;
    @ExcelIgnore
    private Integer hotelDateSkuId ;

    @ExcelProperty(value = "上下架状态",index = 22)
    private String statusStr ;
    public String getStatusStr() {return statusMap.get(""+status);}

//    s.sort_value as suite_sort_value ,
//    sku.sort_value as  sku_sort_value ,
//    @ExcelProperty(value = "套餐排序值",index = 22)
//    private Long suiteSortValue ;
    @ExcelProperty(value = "房券排序值",index = 23)
    private Long skuSortValue ;
    @ExcelProperty(value = "销售量",index = 24)
    private Long sellNum ;

    @ExcelIgnore
    private ArrayList roomInfos ;

    /**
     *   1-拆分房券,2-单一房券
     */
    @ExcelIgnore
    private Integer hotelReserveSplit ;

    @ExcelIgnore
    private Integer roomId ;


}
