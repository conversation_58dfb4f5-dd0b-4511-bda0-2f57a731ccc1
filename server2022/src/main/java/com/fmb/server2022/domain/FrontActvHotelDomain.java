package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fmb.server2022.controller.front.reqdomain.FrontElement;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FrontActvHotelDomain implements Serializable {


    public static final Map<Integer, String> statusMap;
    public static final Map<Integer, String> starLevelMap;

    public static final Integer ROOM_TYPE = 6;
    public static final Integer RES_TYPE = 7;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "热销中");
            map.put(2, "距开抢");
            map.put(3, "距结束");
            statusMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "豪华型酒店");
            map.put(2, "高档型酒店");
            map.put(3, "舒适型酒店");
            starLevelMap = Collections.unmodifiableMap(map);
        }
    }

    @JsonIgnore
    private Integer xaid;

    @JsonIgnore
    private String bannerListUrl;

    @JsonIgnore
    private List<String> bannerUrl;

    public List<String> getBannerUrl() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, String.class);
            bannerUrl = mapper.readValue(bannerListUrl, javaType);
        }catch (Exception e){
        }
        return bannerUrl;
    }

    @JsonIgnore
    private List<Map<String,String>> mediaUrl;

    public List<Map<String,String>> getMediaUrl(){
        List<Map<String,String>> reList = new ArrayList<>();
        List<String> bannerList = getBannerUrl();
        if (videoUrl != null) {
            Map<String, String> vMap = new HashMap<>(2);
            vMap.put("type", "video");
            vMap.put("url", videoUrl);
            vMap.put("imgUrl", videoSnapUrl);
            reList.add(vMap);
        }
        for (String banner : bannerList){
            Map<String,String> reMap = new HashMap<>(2);
            reMap.put("type","img");
            reMap.put("url",banner);
            reList.add(reMap);
        }
        return reList;
    }

    @JsonIgnore
    private String videoUrl;

    @JsonIgnore
    private String videoSnapUrl;


    private Integer bpsId;

    private String actInfoStr = "酒店套餐";

    private String distanceStr;

    private String bdInfoUrl = "https://ms.fumubang.com/mobile/child_news/index/41/2.3?is_app=1";

    private BigDecimal goodsPrice;

    private BigDecimal marketPrice;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice==null?BigDecimal.ZERO:goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice==null?BigDecimal.ZERO:marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        return "/晚起";
    }

    private Integer status;

    private String statusStr;

    public String getStatusStr() {
        return statusMap.get(status);
    }

    @JsonIgnore
    private Timestamp startTime ;

    @JsonIgnore
    private Timestamp endTime ;

    @JsonIgnore
    private Duration startTimeDuration;
    public Duration getStartTimeDuration() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime tt = LocalDateTime.ofInstant(startTime.toInstant(), ZoneOffset.ofHours(8));
        this.startTimeDuration = Duration.between(now,tt);
        return startTimeDuration;
    }

    private Long getStartTimeInterval() {
        if (getStartTimeDuration()!=null) {
            return getStartTimeDuration().getSeconds();
        }else{
            return 0l;
        }
    }

    @JsonIgnore
    private Duration endTimeDuration;
    public Duration getEndTimeDuration() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime tt = LocalDateTime.ofInstant(endTime.toInstant(), ZoneOffset.ofHours(8));
        this.endTimeDuration = Duration.between(now,tt);
        return endTimeDuration;
    }

    private Long getEndTimeInterval() {
        if (getEndTimeDuration()!=null){
            return getEndTimeDuration().getSeconds();
        }else {
            return 0l;
        }
    }

    public String getStartTimeDurationStr() {
        if (this.startTimeDuration == null) {
            this.startTimeDuration = getStartTimeDuration();
        }
        return getDurationStr(startTimeDuration);
    }

    public String getEndTimeDurationStr() {
        if (this.endTimeDuration == null) {
            this.endTimeDuration = getEndTimeDuration();
        }
        return getDurationStr(endTimeDuration);
    }

    private String getDurationStr(Duration duration) {
        long seconds = duration.getSeconds();
        long s = seconds % 60;
        long m = seconds /60 % 60;
        long h = seconds /60 /60 % 24;
        long d = seconds /60 /60 /24 ;
        return d+"天"+h+":"+m+":"+s;
    }

    public Long getTimeInterval() {
        if (this.getStatus()==2){
            return getStartTimeInterval();
        }else if(this.getStatus()==3){
            return getEndTimeInterval();
        }else {
            return 0l;
        }
    }

    private Integer saleNum;

    private String saleNumStr;

    public String getSaleNumStr() {
        return "已售"+saleNum+"份";
    }

    private String title;


    private String subTitle;


    private List<String> actvTag;

    public List<String> getActvTag(){
        List<String> reList = new ArrayList<>();
        reList.add("未预约随时退");
        reList.add("特价促销");
        reList.add("周末不加价");
        return reList;
    }

    private String bpsInfo;

    public Map<String,FrontElement> getBpsInfo(){
        Map<String,FrontElement> reMap = new HashMap<>();
        FrontElement e = new FrontElement();
        String starLevelStr = getStarLevelStr();
        e.setTitle((starLevelStr!=null&&starLevelStr.length()>0)?starLevelStr.substring(0,1):null);
        e.setValue(getStarLevelStr());
        reMap.put("startLevelInfo",e);
        e = new FrontElement();
        e.setTitle("营");
        e.setValue(getOpenTime());
        reMap.put("openTimeInfo",e);
        return reMap;
    }

    private String bpsMoreStr="酒店详情";

    private String bpsUrl;

    // 星级档次:1-豪华型,2-高档型,3-舒适型
    @JsonIgnore
    private Integer starLevel;

    @JsonIgnore
    private String starLevelStr;

    public String getStarLevelStr() {
        return starLevelMap.get(starLevel);
    }

    @JsonIgnore
    private String openTime;


    private String address;


    private Double longitude;


    private Double latitude;


    private String addrMoreStr = "地图导航";


    private String aidContentInfoStr = "图文详情";


    private String aidContentInfoUrl;


    private String aidContent;


    private String otherNoteInfoStr = "其他说明";


    private String otherNote = "<p>1、单独购买价:是您单独购买商品的价格<br/>2、拼团价:是您拼团购买商品的价格<br/>3、划线价：划线价为参考价，该价格指商品的门市价、零售价、市场价、服务提供商的指导价或该商品曾经展示过的销售价等，并非《价格法》、《禁止价格欺诈行为的规定》规定的“原价”。由于产品/服务信息实时更新、服务提供者差异、市场价格波动等原因，该价格可能会与您预订时展示的不一致，仅供您参考。<br/>4、特别提示：实际的成交价格可能因您使用优惠券等发生变化，最终以订单结算页的价格为准。此说明仅当出现价格比较时有效。若服务提供商单独对价格进行说明的，以商家表述为准。<br/></p>";


    private String shareImg = "http://img3.fumubang.net/jupload/20221014/fmbupload_1665728447540.jpg";


    private Integer hasReserveSuite;


    private Integer hasRoomSuite;


}