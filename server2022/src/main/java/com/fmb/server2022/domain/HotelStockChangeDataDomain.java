package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class HotelStockChangeDataDomain  implements Serializable {


    public  static final Map<String, String> changeTypeMap;
    public  static final Map<String, String> statusChangeTypeMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("1","连续日期");
                map.put("2","指定日期");
            changeTypeMap = Collections.unmodifiableMap(map);
        }
        {
            //房态变更类型:0-不变,1-打开,2-关闭
            HashMap<String, String> map = new HashMap<>();
                map.put("0","不变");
                map.put("1","打开");
                map.put("2","关闭");
            statusChangeTypeMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "###stock_operate_id",index = 0)
    private Long stockOperateId ;

    @ExcelProperty(value = "房型id###room_id",index = 1)
    private Long roomId ;

    @ExcelProperty(value = "房型名称",index = 2)
    private String roomName ;

    @ExcelProperty(value = "商户名称",index = 3)
    private String bpsName ;

    @ExcelProperty(value = "操作数据(json格式)",index = 4)
    private String operateData ;

    // 修改类型:1-连续日期,2-指定日期
    @ExcelIgnore
    private Integer changeType ;

    @ExcelProperty(value = "修改类型",index = 5)
    private String changeTypeStr ;
    public String getChangeTypeStr() {return changeTypeMap.get(""+changeType);}

    // 房态变更类型:0-不变,1-打开,2-关闭
    @ExcelIgnore
    private Integer statusChangeType ;

    @ExcelProperty(value = "修改类型",index = 6)
    private String statusChangeTypeStr ;
    public String getStatusChangeTypeStr() {return statusChangeTypeMap.get(""+statusChangeType);}




    @ExcelProperty(value = "后台用户名字",index = 7)
    private String adminName ;

    @ExcelProperty(value = "用户uid",index = 8)
    private Integer adminUid ;

    @ExcelProperty(value = "商户id",index = 10)
    private Integer bpsId ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "创建时间",index = 9,converter = TimestampConverter.class)
private Timestamp ctime ;

    @ExcelProperty(value = "日期信息",index = 11)
    private String dateInfo ;

    @ExcelProperty(value = "库存信息",index = 12)
    private String stockInfo ;

}