package com.fmb.server2022.domain;

import com.fmb.server2022.reqdomain.RoomNumStatus;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/1/18 3:48 下午
 */
@Data
public class ConfirmOrderMoneyGroup {

    public static final  int GROUPTYPE_1  = 1 ;
    public static final  int GROUPTYPE_2  = 2 ;

    /**
     * 1  -- 商品
     * 2  -- 优惠
     */
    int groupType ;

    String title ;
    List<ConfirmOrderMoneyItem> itemList ;

    List<RoomNumStatus> roomNumStatus ;


    String skuDesc ;

    public ConfirmOrderMoneyGroup() {
        this.itemList = new ArrayList<>() ;
        this.roomNumStatus = new ArrayList<>() ;
        this.skuDesc = "" ;
    }

}
