package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class NewUserOrderStatDomain  implements Serializable {

    @ExcelProperty(value = "活动ID",index = 0)
    private Integer aid ;

    @ExcelProperty(value = "活动标题",index = 1)
    private String title ;

    @ExcelProperty(value = "BD名字",index = 2)
    private String realname ;

    @ExcelProperty(value = "城市",index = 3)
    private String city ;

    @ExcelProperty(value = "订单总金额",index = 4)
    private Double money ;

    @ExcelProperty(value = "订单数量",index = 5)
    private Long ordercount ;

    @ExcelProperty(value = "下单用户数",index = 6)
    private Long distorderusercount ;


    @ExcelProperty(value = "新注册用户数",index = 7)
    private Integer userCountNew ;

    @ExcelProperty(value = "C端分销用户数",index = 8)
    private Integer userCountMycps ;

    @ExcelProperty(value = "社群用户数",index = 9)
    private Integer userCountShequn ;

    @ExcelProperty(value = "微信公众号用户数",index = 10)
    private Integer userCountWechat ;

    @ExcelProperty(value = "老用户数",index = 11)
    private Integer userCountOld ;

    @ExcelProperty(value = "获新用户数",index = 12)
    private Integer userCountFinal ;

    @ExcelIgnore
    private Integer uid ;


}