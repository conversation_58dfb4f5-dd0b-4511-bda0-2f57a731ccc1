package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class FrontResSkuDomain implements Serializable {
    // type 1日历房 2预售房券
    private Integer type;


    private Integer skuId;


    private Integer suiteId;


    private String suiteName;

    @JsonIgnore
    private Integer nightMin;

    @JsonIgnore
    private String suiteContent;


    private BigDecimal goodsPrice;

    private BigDecimal marketPrice;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        return "起";
    }


    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Timestamp reserveChooseStartTime;


    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Timestamp reserveChooseEndTime;

    public String getReserveDateStr(){
        LocalDateTime start = LocalDateTime.ofInstant(reserveChooseStartTime.toInstant(), ZoneOffset.ofHours(8));
        LocalDateTime end = LocalDateTime.ofInstant(reserveChooseEndTime.toInstant(), ZoneOffset.ofHours(8));
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return "入住有效期："+ df.format(start)+"至"+df.format(end);
    }

    @JsonIgnore
    private Integer sellCountDownShowFlag;

    @JsonIgnore
    private Timestamp sellStartTime;

    @JsonIgnore
    private Timestamp sellEndTime;

    @JsonIgnore
    private String roomIds;

    private String mediaUrl="";

    private Integer mediaCount=0;

    public String getResvDateInfoStr(){
        return "可约日期";
    }

    public String getButtonStr(){
        return "抢购";
    }

    @JsonIgnore
    private Integer sellNum ;

    private Integer sortValue;

    @JsonIgnore
    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        return suiteContentList;
    }


    public String getSuiteContentIntro(){
        String re = "";
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        for(SuiteContent suiteContent : suiteContentList){
            re += suiteContent.getIntro() + "+";
        }
        if(re.endsWith("+")){
            re = re.substring(0,re.lastIndexOf("+"));
        }
        return re;
    }

    public List<SuiteContent> getSuiteContentGroupList() {
        if (suiteContentList == null){
            suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
        }
        List<SuiteContent> reList = new ArrayList<>();
        SuiteContent s0 = new SuiteContent();
        s0.setType("住");
        s0.setCount(this.nightMin);
        s0.setCountUnit("晚");
        s0.setIntro("热销房型入住"+this.nightMin+"晚");
        s0.setContent("可约房型");
        reList.add(s0);
        Map<String, List<SuiteContent>> groupBy = suiteContentList.stream().collect(Collectors.groupingBy(SuiteContent::getType, LinkedHashMap::new,Collectors.toList()));
        for(Map.Entry<String, List<SuiteContent>> ele : groupBy.entrySet()){
            SuiteContent s = new SuiteContent();
            s.setType(ele.getKey());
            String intro = "";
            List<SuiteContent> list = ele.getValue();
            for (SuiteContent sc : list){
                intro += sc.getIntro()+"+";
            }
            if(intro.endsWith("+")){
                intro = intro.substring(0,intro.lastIndexOf("+"));
            }
            s.setCount(ele.getValue().size());
            s.setIntro(intro);
            reList.add(s);
        }
        return reList;
    }


}