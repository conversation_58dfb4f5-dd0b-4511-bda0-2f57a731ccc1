package com.fmb.server2022.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date: 2023/2/8 9:35 上午
 */
@Data
public class AdminOrder {
    //订单号
    String orderSn ;

//      `return_class` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 正常退款,1 补偿退款,4 赔偿退款',
    /**
     * 退款类型  0 正常退款,1 补偿退款,4 赔偿退款
      */
    Integer returnClass ;

    /**
     * 如果后台直接发起退 酒店预约单 这个值是 1
     */
    int hotelOrderOfFromReserveReturn  ;

    public boolean isHotelOrderOfFromReserveReturn(){
        return 1==hotelOrderOfFromReserveReturn ;
    }

    ArrayList<String> reserveCode ;

    //要退几间房
    private Integer roomNumber ;
    private BigDecimal returnMoney ;

    /**
     * 退货理由
     */
    private String reason;

    /**
     * 退货原因详细描述
     */
    private String reasonTxt;

    /**
     * 退货相关凭证
     */
    private ArrayList<String> uploadFile;
    private ArrayList<String> internalUploadFile;

    /**
     * 联系人
     */
    private String receiver;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 1-发送 0-不发送
     */
    private int sendSms ;
    /**
     * 短信模板内容
     */
    private String smsTemplate ;


    /**
     * 内部备注
     */
    private String internalNote = "";


    //php cookie asid 值
    private String asid ;



    PhpAdminInfo phpAdminInfo ;


    //入店日期
    String playDate ;
    //离店日期
    String leaveDate ;


    Integer skuId ;

    /**
     * 前台用户发起房券退款标记,设置为1
     */
    Integer frontReturnReserveCode ;

}
