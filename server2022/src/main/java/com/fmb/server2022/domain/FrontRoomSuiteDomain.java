package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.util.FmbDateUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.protocol.types.Field;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontRoomSuiteDomain implements Serializable {
    private Integer suiteId ;

    private String suiteName ;

    private String skuName ;

    private String roomName ;

    private Integer sortValue ;

    private Integer hotelReserveSplit;

    private Integer skuCount ;


    private List<FrontRoomSkuDomain> skuList ;

    @JsonIgnore
    private String suiteContent ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (suiteContentList == null){
            suiteContentList = new ArrayList<>();
            if (getTotalNeight()!=null && getTotalNeight()>0) {
                List<SuiteContent> reList = new ArrayList<>();
                SuiteContent s0 = new SuiteContent();
                s0.setType("住");
                s0.setIntro(roomName+" " + getTotalNeight() + "晚");
                suiteContentList.add(s0);
            }
            if (this.suiteContent != null) {
                suiteContentList.addAll(FrontRoomDomain.getSuiteContentList(this.suiteContent));
            }
        }
        return suiteContentList;
    }

    private String buyNote ;

    private BigDecimal goodsPrice;

    private Integer totalNeight;

    private Integer nightMin;

    private Integer flagBuyLimit;

    private Integer eachOrderMaxNum;

    private Integer eachOrderMinNum;

    private Integer maxBuyAllNum;

    private Integer canDivide;

    private Integer stockNum;

    private LocalDate reserveChooseStartTime;

    private LocalDate reserveChooseEndTime;

    private String invalidReserveWeekDays;

    public Set<Integer> getInvalidReserveWeekDaySet(){
        Set<Integer> re = new HashSet<>();
        if (StringUtils.isBlank(invalidReserveWeekDays)){
            return re;
        }
        String[] arr = invalidReserveWeekDays.split(",");
        try {
            for(String day : arr){
                re.add(Integer.parseInt(day));
            }
        }catch (Exception e){
        }
        return re;
    }

    private String invalidReserveDate;

    public Set<LocalDate> getInvalidReserveDateSet(){
        Set<LocalDate> re = new HashSet<>();
        if (StringUtils.isBlank(invalidReserveDate)){
            return re;
        }
        if (getInvalidReserveWeekDaySet().contains(8)) {
            String[] arr = invalidReserveDate.split(",");
            try {
                for (String date : arr) {
                    re.add(FmbDateUtil.dateToLocalDate(date));
                }
            } catch (Exception e) {
            }
        }
        return re;
    }

}
