package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.server2022.domain.suite.SuiteContent;
import com.fmb.util.FmbDateUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontRoomSuiteDomain implements Serializable {
    private Integer suiteId ;


    private String suiteName ;


    private String skuName ;


    private Integer sortValue ;


    private Integer skuCount ;


    private List<FrontRoomSkuDomain> skuList ;

    @JsonIgnore
    private String suiteContent ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (suiteContentList == null){
            if (this.suiteContent != null) {
                suiteContentList = FrontRoomDomain.getSuiteContentList(this.suiteContent);
            }else {
                suiteContentList = new ArrayList<>() ;
            }

        }
        return suiteContentList;
    }

    private String buyNote ;

    private BigDecimal goodsPrice;

    private Integer totalNeight;

    private Integer nightMin;

    private Integer flagBuyLimit;

    private Integer eachOrderMaxNum;

    private Integer eachOrderMinNum;

    private Integer maxBuyAllNum;

    private Integer stockNum;

    private LocalDate reserveChooseStartTime;

    private LocalDate reserveChooseEndTime;

    private String invalidReserveWeekDays;

    public Set<Integer> getInvalidReserveWeekDaySet(){
        Set<Integer> re = new HashSet<>();
        if (StringUtils.isBlank(invalidReserveWeekDays)){
            return re;
        }
        String[] arr = invalidReserveWeekDays.split(",");
        try {
            for(String day : arr){
                re.add(Integer.parseInt(day));
            }
        }catch (Exception e){
        }
        return re;
    }

    private String invalidReserveDate;

    public Set<LocalDate> getInvalidReserveDateSet(){
        Set<LocalDate> re = new HashSet<>();
        if (StringUtils.isBlank(invalidReserveDate)){
            return re;
        }
        if (getInvalidReserveWeekDaySet().contains(8)) {
            String[] arr = invalidReserveDate.split(",");
            try {
                for (String date : arr) {
                    re.add(FmbDateUtil.dateToLocalDate(date));
                }
            } catch (Exception e) {
            }
        }
        return re;
    }

}
