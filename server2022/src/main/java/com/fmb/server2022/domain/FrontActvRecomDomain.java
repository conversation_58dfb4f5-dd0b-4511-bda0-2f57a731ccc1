package com.fmb.server2022.domain;

import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class FrontActvRecomDomain implements Serializable {

    private Integer xaid;

    private String title;

    private String intro;

    private String subTitle;

    private String mediaUrl;

    private BigDecimal goodsPrice;

    private BigDecimal marketPrice;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        return "/晚起";
    }

}
