package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontActvRecomDomain implements Serializable {

    private Integer xaid;

    private String title;

    private String intro;

    private String subTitle;

    private String mediaUrl;

    private BigDecimal goodsPrice = BigDecimal.ZERO;

    private BigDecimal marketPrice = BigDecimal.ZERO;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice==null?BigDecimal.ZERO:goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice==null?BigDecimal.ZERO:marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        if (fmbxActivityFlag!=null &&  fmbxActivityFlag==0){
            return "起";
        }else if (fmbxActivityFlag!=null &&fmbxActivityFlag==1){
            if (fmbxFlagHotelDate!=null && fmbxFlagHotelDate == 1){
                return "起/晚";
            }else if (fmbxFlagHotelReserve!=null && fmbxFlagHotelReserve == 1){
                return "起";
            }
        }
        return "起";
    }

    private Integer fmbxActivityFlag;

    private Integer activityGroupType;

    private Integer goodsType;

    public Integer getGoodsType(){
        if (fmbxActivityFlag!=null && fmbxActivityFlag==0){
            return null;
        }else if (fmbxActivityFlag!=null && fmbxActivityFlag==1){
            if (fmbxFlagHotelDate!=null && fmbxFlagHotelDate == 1){
                return 6;
            }else if (fmbxFlagHotelReserve!=null &&  fmbxFlagHotelReserve == 1){
                return 7;
            }
        }
        return null;
    }

    @JsonIgnore
    private Integer fmbxFlagHotelDate;

    @JsonIgnore
    private Integer fmbxFlagHotelReserve;

}
