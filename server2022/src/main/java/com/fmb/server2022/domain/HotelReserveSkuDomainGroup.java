package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/6/19 6:06 下午
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HotelReserveSkuDomainGroup {


    private Integer suiteId;
    private Long suiteSortValue;
    private String name;
    private List<HotelReserveSkuDomain> list ;

    public HotelReserveSkuDomainGroup() {
        this.list = new ArrayList<>() ;
    }

}
