package com.fmb.server2022.domain ;



import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fmb.basic.converter.TimestampConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


@Data
public class FrontCouponDomain  implements Serializable {


    public  static final Map<String, String> platIdMap;
    public  static final Map<String, String> limitDateMap;

static
    {
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("0","全平台");
                map.put("1","PC");
                map.put("2","APP");
                map.put("3","微信");
                map.put("4","APP或微信");
                map.put("5","微信小程序");
            platIdMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<String, String> map = new HashMap<>();
                map.put("1","平日");
                map.put("2","周末");
                map.put("3","中秋");
                map.put("4","国庆");
            limitDateMap = Collections.unmodifiableMap(map);
        }
    }




    @ExcelProperty(value = "排序",index = 0)
    private Integer rank ;

    @ExcelProperty(value = "单个优惠券订单最小金额###优惠券最小订单金额",index = 1)
    private Double minAmount ;

    @ExcelProperty(value = "",index = 2)
    private Double uMinAmount ;

    @ExcelProperty(value = "折扣率",index = 3)
    private Integer discountRate ;



    private String discountRateStr ;

    public String getDiscountRateStr() {
        String bigDecimal2 = "";
        if(discountRate != null){
            BigDecimal bigDecimal = new BigDecimal(discountRate / 10.0);
            BigDecimal bigDecimal1 = bigDecimal.stripTrailingZeros();
            bigDecimal2 = bigDecimal1.toPlainString();
        }
        return bigDecimal2;
    }

    @ExcelProperty(value = "使用状态 0为未使用 1为使用中 2为已使用 3是已冻结",index = 4)
    private Integer useStatus ;

    @ExcelProperty(value = "优惠券类型 （1为代金券 2为满减 3叠加券 4折扣券）",index = 5)
    private Integer couponType ;

    @ExcelProperty(value = "优惠券名",index = 6)
    private String couponName ;

    @ExcelProperty(value = "优惠券组自增ID",index = 7)
    private Long couponId ;

    // 优惠券支持平台ID:0-全平台,1-PC,2-APP,3-微信,4-APP或微信,5-微信小程序
    @ExcelIgnore
    private Integer platId ;

    @ExcelProperty(value = "优惠券支持平台ID",index = 8)
    private String platIdStr ;
    public String getPlatIdStr() {return platIdMap.get(""+platId);}

    @ExcelProperty(value = "优惠券限制品类",index = 9)
    private Integer limitType ;

    // 限制出行日期为数组序列化以后的格式,反序列化后KEY为日期类型:1-平日,2-周末,3-中秋,4-国庆
    @ExcelIgnore
    private String limitDate ;

    @ExcelProperty(value = "限制出行日期为数组序列化以后的格式,反序列化后KEY为日期类型",index = 10)
    private String limitDateStr ;
    public String getLimitDateStr() {return limitDateMap.get(""+limitDate);}

    @ExcelProperty(value = "优惠券分类",index = 11)
    private String limitCate ;

    @ExcelProperty(value = "优惠券支持的活动ID",index = 12)
    private Long activityId ;

    @ExcelProperty(value = "优惠券生成码",index = 13)
    private String couponSn ;

    @ExcelProperty(value = "优惠券使用范围 0为无限制",index = 14)
    private Integer useLimit ;

    @ExcelProperty(value = "优惠券金额",index = 15)
    private Long couponMoney ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "单个优惠券开始时间",index = 16,converter = TimestampConverter.class)
private Timestamp couponStartTime ;


@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "单个优惠券结束时间",index = 17,converter = TimestampConverter.class)
private Timestamp couponEndTime ;


    @ExcelProperty(value = "订单编号",index = 18)
    private String orderSn ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "使用时间",index = 19,converter = TimestampConverter.class)
private Timestamp useTime ;


    @ExcelProperty(value = "优惠券组标签",index = 20)
    private String couponTag ;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ExcelProperty(value = "派发时间",index = 21,converter = TimestampConverter.class)
private Timestamp distributeTime ;


    @ExcelProperty(value = "是否可叠加1-可以0-不可以",index = 22)
    private Integer stacked ;

    @ExcelIgnore
    boolean canUse  ;

    /**
     * 数量
     */
    @ExcelIgnore
    Integer number;

    /**
     * 可用状态
     * 0不可用，1可用，2被选中
     */
    @ExcelIgnore
    Integer isUsed;

    /**
     * 折扣券 省的钱
     */
    BigDecimal discountMoney;

    /**
     * 父类分类名称
     */
    @ExcelIgnore
    String parentCateName;

    /**
     * 父类分类ID
     */
    @ExcelIgnore
    Integer parentCateId;

    /**
     * 分类名称
     */
    @ExcelIgnore
    String CateName;

    /**
     * 活动标题 优惠券标题
     */
    @ExcelIgnore
    String  activityTitle;

    /**
     * 票种类型
     */
    @ExcelIgnore
    Integer ticketType;

    /**
     * 快过期
     * 1是 0否
     */
    @ExcelIgnore
    Integer isKdq;

    /**
     * 优惠券显示状态
     */
    String couponShowStatus;

    /**
     * 满减提示
     */
    String minAmountStr;

    /**
     * 是否选中 0否 1是
     */
    Integer selected;

    /**
     * 优惠券开始时间
     */
    String startTime;

    /**
     * 优惠券结束时间
     */
    String endTime;

    /**
     * 优惠券结类型名称
     */
    String couponTypeCn;


    /**
     * 获取时 增加逻辑
     * @return
     */
    public BigDecimal getDiscountMoney() {
        final BigDecimal val = BigDecimal.valueOf(couponMoney);
        if(discountMoney.compareTo(val)==1){
            return  discountMoney ;
        }
        return val;
    }

    public FrontCouponDomain() {
        this.canUse   = true ;
        discountMoney  = new BigDecimal(0);


    }
}