package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2023/4/10 5:00 下午
 */
@NoArgsConstructor
@Data
public class PhpAdminInfo {


    @JsonProperty("uid")
    private String uid;
    @JsonProperty("name")
    private String name;
    @JsonProperty("role")
    private String role;
    @JsonProperty("area")
    private String area;
    @JsonProperty("token")
    private String token;
    @JsonProperty("token_expires")
    private String tokenExpires;
    @JsonProperty("disabled")
    private String disabled;
    @JsonProperty("ip")
    private Object ip;
}
