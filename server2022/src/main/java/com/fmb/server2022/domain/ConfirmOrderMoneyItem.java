package com.fmb.server2022.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/1/18 3:44 下午
 */
@Data
public class ConfirmOrderMoneyItem {
    String leftTitle ;
    String skuNumber ;
    int skuNumberN ;
    String price ;
    BigDecimal priceBig ;
    BigDecimal priceBigAll ;


    public BigDecimal getPriceBigAll() {
        if(skuNumberN>=0 &&  priceBig.compareTo(BigDecimal.ZERO)==1){
            return priceBig.multiply(new BigDecimal(skuNumberN)) ;
        }else if( priceBig!=null){
            return priceBig ;
        }
        return BigDecimal.ZERO;
    }
}
