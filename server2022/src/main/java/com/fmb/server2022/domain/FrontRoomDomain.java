package com.fmb.server2022.domain ;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fmb.server2022.controller.front.reqdomain.FrontElement;
import com.fmb.server2022.domain.suite.BedType;
import com.fmb.server2022.domain.suite.BedTypeDb;
import com.fmb.server2022.domain.suite.HotelServices;
import com.fmb.server2022.domain.suite.SuiteContent;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontRoomDomain  implements Serializable {

    public  static final Map<Integer, String> smokeTypeMap;
    public  static final Map<Integer, String> bathtubTypeMap;

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"可吸烟");
            map.put(2,"禁烟");
            map.put(3,"部分客房可吸烟");
            smokeTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"没浴缸");
            map.put(1,"有浴缸");
            bathtubTypeMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer bpsId ;

    private Integer roomId ;

    private Integer skuId ;

    @JsonIgnore
    private Integer suiteId ;

    private String skuName ;

    private String roomName ;

    @JsonIgnore
    private String bedType ;

    private Integer nightMin ;

    @JsonIgnore
    private Integer buildArea ;

    @JsonIgnore
    private String floorNumber ;

    @JsonIgnore
    private Integer maxPeopleNum ;

    @JsonIgnore
    private Integer smokeType ;

    @JsonIgnore
    private String smokeTypeStr;

    public String getSmokeTypeStr() {
        return smokeTypeMap.get(smokeType);
    }

    @JsonIgnore
    private Integer bathtubType ;

    @JsonIgnore
    private String bathtubTypeStr;

    public String getBathtubTypeStr() {
        return bathtubTypeMap.get(bathtubType);
    }

    public List<FrontElement> getRoomInfo(){
        List<FrontElement> reList = new ArrayList<>();
        FrontElement e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon15.png");
        e.setValue(getBedTypeStr());
        reList.add(e);
        e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon16.png");
        e.setValue(getBuildArea()+"㎡");
        reList.add(e);
        e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon17.png");
        e.setValue(getFloorNumber()+"层");
        reList.add(e);
        e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon18.png");
        e.setValue(getMaxPeopleNum()+"人");
        reList.add(e);
        e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon12.png");
        e.setValue(getSmokeTypeStr());
        reList.add(e);
        e = new FrontElement();
        e.setIcon("http://d.fmbimg.com/temp/images/icon/icon13.png");
        e.setValue(getBathtubTypeStr());
        reList.add(e);
        return reList;
    }

    @JsonIgnore
    private String suiteContent ;

    private String buyNote ;

    private BigDecimal minGoodsPrice ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (getNightMin()!=null && getNightMin()>0) {
            List<SuiteContent> reList = new ArrayList<>();
            SuiteContent s0 = new SuiteContent();
            s0.setType("住");
            s0.setIntro(getRoomName() + " " + getNightMin() + "晚");
            reList.add(s0);
            reList.addAll(getSuiteContentList(this.suiteContent));
            return reList;
        }
        return null;
    }

    private List<String> mediaUrl;

    @JsonIgnore
    private String bedTypeStr ;

    public String getBedTypeStr(){
        return getBedTypeStr(this.bedType);
    }

    public static String getBedTypeStr(String bedType){
        String bedTypeStr = "";
        try {
            ObjectMapper mapper = new ObjectMapper();
            BedType type = mapper.readValue(bedType, BedType.class);
            Integer value = type.getValue();
            List<BedTypeDb> dblist = type.getDb();
            for (BedTypeDb db : dblist){
                if (value.equals(db.getValue())){
                    bedTypeStr = db.getList().get(0).getNum() + "张" + db.getList().get(0).getWidth() + "米" + db.getType();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return bedTypeStr;
    }

    public static List<SuiteContent> getSuiteContentList(String suiteContent) {
        List<SuiteContent> reList = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, SuiteContent.class);
            reList = mapper.readValue(suiteContent, javaType);
        }catch (Exception e){
            e.printStackTrace();
        }
        return reList;
    }

    public static List<HotelServices> getHotelServicesList(String services) {
        List<HotelServices> reList = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HotelServices.class);
            reList = mapper.readValue(services, javaType);
        }catch (Exception e){
            e.printStackTrace();
        }
        return reList;
    }

}