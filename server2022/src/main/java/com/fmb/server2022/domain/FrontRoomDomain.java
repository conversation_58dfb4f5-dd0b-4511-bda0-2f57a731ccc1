package com.fmb.server2022.domain ;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.controller.front.reqdomain.FrontElement;
import com.fmb.server2022.domain.suite.*;
import com.fmb.server2022.service.FmbDBDictInfo;
import com.fmb.server2022.service.FmbXDictUtils;
import com.fmb.util.FmbNumberUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontRoomDomain  implements Serializable {

    public  static final Map<Integer, String> smokeTypeMap;
    public  static final Map<Integer, String> bathtubTypeMap;

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"可吸烟");
            map.put(2,"禁烟");
            map.put(3,"部分客房可吸烟");
            smokeTypeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0,"");
            map.put(1,"有浴缸");
            bathtubTypeMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer bpsId ;

    private Integer roomId ;

    private Integer skuId ;

    private Integer resSkuId ;

    @JsonIgnore
    private Integer suiteId ;

    private String skuName ;

    /**
     * 加床:1-未知,2-不可加床,3-免费加不含早,4-免费加含早,5-收费加床不含早,6-收费加床含早
     */
    private Integer addBedType;

    public String getAddBedTypeStr() {
        return addBedTypeStr;
    }

    private String addBedTypeStr = "";


    public void buildBedTypeStr(){
        if ( this.addBedType!=null &&  1!=this.addBedType &&  FmbDBDictInfo.FMBX_BPS_ROOM__ADD_BED_TYPE_Map.containsKey(""+this.addBedType)) {
            addBedTypeStr =  FmbDBDictInfo.FMBX_BPS_ROOM__ADD_BED_TYPE_Map.get(""+this.addBedType)  ;
            if (StringUtils.isNotBlank(addBedFeeInfo)) {
                addBedTypeStr += ","+addBedFeeInfo ;
            }
        }
    }

    /**
     * 加床费用说明
     */
    private String addBedFeeInfo;

    private String roomName ;

    private String resRoomName ;

    @JsonIgnore
    private String bedType ;

    private Integer nightMin ;

    private Integer totalNeight ;

    @JsonIgnore
    private String buildArea ;

    @JsonIgnore
    private String floorNumber ;

    @JsonIgnore
    private Integer maxPeopleNum ;

    @JsonIgnore
    private Integer smokeType ;

    @JsonIgnore
    private String smokeTypeStr;

    public String getSmokeTypeStr() {
        return smokeTypeMap.get(smokeType);
    }

    @JsonIgnore
    private Integer bathtubType ;

    @JsonIgnore
    private String bathtubTypeStr;

    public String getBathtubTypeStr() {
        return bathtubTypeMap.get(bathtubType);
    }

    public List<FrontElement> getRoomInfo(){
        List<FrontElement> reList = new ArrayList<>();
        FrontElement e = new FrontElement();
        e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313361460.png");
        e.setValue(getBedTypeStr());
        reList.add(e);
        e = new FrontElement();
        e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313541468.png");
        e.setValue(getBuildArea()+"㎡");
        reList.add(e);
        e = new FrontElement();
        e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313590622.png");
        e.setValue(getFloorNumber()+"层");
        reList.add(e);
        if (getMaxPeopleNum()>0) {
            e = new FrontElement();
            e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313598859.png");
            e.setValue(getMaxPeopleNum() + "人入住");
            reList.add(e);
        }
        e = new FrontElement();
        e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313595240.png");
        e.setValue(getSmokeTypeStr());
        reList.add(e);
        if (StringUtils.isNotBlank(getBathtubTypeStr())) {
            e = new FrontElement();
            e.setIcon(FmbServerConfig.getFmbImgUrl()+"/20230320/fmbupload_1679313601832.png");
            e.setValue(getBathtubTypeStr());
            reList.add(e);
        }
        return reList;
    }

    @JsonIgnore
    private String suiteContent ;

    private String buyNote ;

    private BigDecimal minGoodsPrice ;

    private List<SuiteContent> suiteContentList;

    public List<SuiteContent> getSuiteContentList() {
        if (getTotalNeight()!=null && getTotalNeight()>0) {
            List<SuiteContent> reList = new ArrayList<>();
            SuiteContent s0 = new SuiteContent();
            s0.setType("住");
            s0.setIntro(getRoomName() + " " + getTotalNeight() + "晚");
            if (hotelReserveSplit!=null && hotelReserveSplit==2 && StringUtils.isNotBlank(resRoomName)){
                s0.setIntro(getResRoomName() + " " + getTotalNeight() + "晚");
            }
            reList.add(s0);
            reList.addAll(getSuiteContentList(this.suiteContent));
            return reList;
        }
        return null;
    }

    private List<String> mediaUrl;

    @JsonIgnore
    private String bedTypeStr ;

    public String getBedTypeStr(){
        return getBedTypeStr(this.bedType,true);
    }

    public static String getBedTypeStr(String bedType,boolean isList){
        String bedTypeStr = "";
        try {
            ObjectMapper mapper = new ObjectMapper();
            BedType type = mapper.readValue(bedType, BedType.class);
            Integer value = type.getValue();
            List<BedTypeDb> dblist = type.getDb();
            for (BedTypeDb db : dblist){
                if (value.equals(db.getValue())){
                    int size = db.getList().size();
                    for (BedTypeDbList item : db.getList()) {
                        bedTypeStr = (StringUtils.isBlank(bedTypeStr)?"":bedTypeStr+"+")
                                + (item.getNum()!=null &&  item.getNum()>1 ? item.getNum() + "张":"")
                                + (isList && size>1 ? "": item.getWidth() + "米")
                                + item.getName();
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return bedTypeStr;
    }

    public static List<SuiteContent> getSuiteContentList(String suiteContent) {
        List<SuiteContent> reList = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, SuiteContent.class);
            List<SuiteContent> tempList = mapper.readValue(suiteContent, javaType);
            reList = tempList.stream().filter(s->s.getCount()>0).collect(Collectors.toList());
        }catch (Exception e){
            e.printStackTrace();
        }
        return reList;
    }

    public static List<HotelServices> getHotelServicesList(String services) {
        List<HotelServices> reList = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, HotelServices.class);
            reList = mapper.readValue(services, javaType);
        }catch (Exception e){
            e.printStackTrace();
        }
        return reList;
    }

    private String roomIntroduce;

    private BigDecimal goodsPrice = BigDecimal.ZERO;

    private BigDecimal marketPrice = BigDecimal.ZERO;

    private String goodsPriceStr;

    public String getGoodsPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(goodsPrice==null?BigDecimal.ZERO:goodsPrice.abs());
    }

    private String marketPriceStr;

    public String getMarketPriceStr(){
        return "￥"+ FmbNumberUtil.FORMATTER_PRICE.format(marketPrice==null?BigDecimal.ZERO:marketPrice.abs());
    }

    private String goodsPriceUnit;

    public String getGoodsPriceUnit(){
        return "起";
    }

    private Integer hotelReserveSplit;

}