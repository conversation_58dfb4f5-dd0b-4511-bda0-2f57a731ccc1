package com.fmb.server2022.domain.stock;

import cn.hutool.json.JSONUtil;
import com.fmb.server2022.domain.suite.AddDateInfo;
import com.fmb.util.FmbDateUtil;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Data
public class ReqroomStockSet {

    public static final int STATUS_NONE = 0 ;
    public static final int STATUS_OPEN = 1 ;
    public static final int STATUS_CLOSE = 2 ;


    public static final int STATUS_CHANGE_TYPE_KEEP_NOW = 0 ;
    public static final int STATUS_CHANGE_TYPE_OPEN = 1 ;
    public static final int STATUS_CHANGE_TYPE_CLOSE = 2 ;


    public static final int STOCK_CHANGE_TYPE_EQUAL = 0 ;
    public static final int STOCK_CHANGE_TYPE_ADD = 1 ;
    public static final int STOCK_CHANGE_TYPE_SUB = 2 ;

    public static final int FORM_TYPE_DATE_BEGIN_END = 1 ;
    public static final int FORM_TYPE_FIXED_DATE = 2 ;

    @NotNull
    Integer statusChangeType  ;

    @NotNull
    Integer stockChangeType  ;

//    @NotNull
//    Integer roomId  ;

    ArrayList<Integer> roomIds ;


    Integer stockChangeNumber ;


    ArrayList<AddDateInfo> dateInfo ;

    ArrayList<String> fixedDate ;
    // 1- 日期范围 2-指定日期
    int formType ;

    public ReqroomStockSet() {
        this.dateInfo = new ArrayList<>() ;
        this.roomIds = new ArrayList<>() ;
        this.fixedDate = new ArrayList<>() ;
    }

    public String genStockInfo() {

        final StringBuffer stringBuffer = new StringBuffer();

        if (stockChangeType == 0) {
            stringBuffer.append("库存等于");
        } else if (stockChangeType == 1) {
            stringBuffer.append("库存增加");
        } else if (stockChangeType == 2) {
            stringBuffer.append("库存减少");
        }

        if(stockChangeNumber!=null){
            stringBuffer.append(stockChangeNumber) ;
        }

        return stringBuffer.toString();
    }

    public String genDateInfo(){
        String result = "" ;
        if(formType==1){

            for (AddDateInfo addDateInfo : dateInfo) {
//                System.out.println(JSONUtil.toJsonStr(addDateInfo));

                if (addDateInfo.getAddDateBegin() != null && addDateInfo.getAddDateEnd()!=null) {
                    result += "指定日期范围:从"
                            +FmbDateUtil.toDate10Str(  addDateInfo.getAddDateBegin())+"至"
                            +FmbDateUtil.toDate10Str(addDateInfo.getAddDateEnd())
                            +genWeekStr(addDateInfo.getWeeks())+"," ;
                }


            }
            if(result.endsWith(",")){
                result = result.substring(0,result.length()-1) ;
            }

        }
        if(formType==2){

            final String collect = fixedDate.stream().collect(Collectors.joining(","));

            result = "指定日期:"+ collect  ;
        }


        return result;
    }

    private   String genWeekStr(ArrayList<Integer> weeks) {

        if (weeks.isEmpty()){
            return "" ;
        }

        final StringBuffer stringBuffer = new StringBuffer();
        for (Integer week : weeks) {

            if (week==7){
                stringBuffer.append("星期日,") ;
            }else {
                stringBuffer.append("星期") ;
                stringBuffer.append(week) ;
                stringBuffer.append(",") ;
            }
        }
        if (stringBuffer.length()==0)
        {
            return "" ;
        }

        return "里的("+ stringBuffer.toString().substring(0,stringBuffer.length()-1)+")";
    }


}
