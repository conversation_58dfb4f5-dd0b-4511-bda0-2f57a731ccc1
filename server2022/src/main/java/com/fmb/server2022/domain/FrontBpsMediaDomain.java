package com.fmb.server2022.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrontBpsMediaDomain implements Serializable {
    public static final Map<Integer, String> typeMap;
    public static final Map<Integer, String> groupvalMap;

    static {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1, "img");
            map.put(2, "video");
            typeMap = Collections.unmodifiableMap(map);
        }
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"外观");
            map.put(2,"大厅");
            map.put(3,"餐饮");
            map.put(4,"家庭亲子");
            map.put(5,"休闲");
            map.put(6,"健身房");
            map.put(7,"公共区域");
            map.put(8,"周边");
            map.put(9,"其他");
            map.put(10,"房间");
            map.put(11,"视频");
            groupvalMap = Collections.unmodifiableMap(map);
        }
    }

    private Integer mediaId;

    private String description;

    private Integer type;

    public String getTypeStr() {
        return typeMap.get(type);
    }

    @JsonIgnore
    private String fileExtType;

    private String url;

    @JsonIgnore
    private String videoSnapUrl;

    private String poster;

    public String getPoster() {
        if(type != null && type==2) {
            return videoSnapUrl;
        }
        return null;
    }

    private Integer groupval;

    public String getGroupvalName() {
        return groupvalMap.get(groupval);
    }

    private Integer num;

    private List<FrontBpsMediaDomain> imgList ;
}
