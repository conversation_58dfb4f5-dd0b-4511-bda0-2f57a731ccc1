package com.fmb.server2022.config;

import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

//@ConfigurationProperties(prefix = "fmb")
@Component("fmbServerConfig")
@Order(1)
public class FmbServerConfig {

    public static final String HTTP_M_FUMUBANG_COM = "http://m.fumubang.com";
    public static final String HTTP_M_FUMUBANG_NET = "http://m.fumubang.net";
    private static Logger logger = LoggerFactory.getLogger(FmbServerConfig.class);

    private static String logPath ;

    private static String downPrefix ;

    private static String fmbImgDir ;

    private static String fmbImgUrl ;

    private static String env ;

    private static String ffmpegPath ;

    private static int categoryLong ;
    private static int categoryShow ;
    private static int categoryChild ;
    private static int categoryTravel ;
    private static int categoryBuy ;
    private static String serviceTel;
    private static String serviceFax;
    private static String fmbKefuUrl ;

    private static String kafkaBootstrapServers ;

    public static String getKafkaBootstrapServers() {
        return kafkaBootstrapServers;
    }

    @Value("${spring.kafka.producer.bootstrap-servers}")
    public  void setKafkaBootstrapServers(String kafkaBootstrapServers) {
        FmbServerConfig.kafkaBootstrapServers = kafkaBootstrapServers;
    }

    @Value("${fmb.ffmpegPath}")
    public void setFfmpegPath(String ffmpegPath) { FmbServerConfig.ffmpegPath = ffmpegPath; }

    @Value("${fmb.categoryLong}")
    public  void setCategoryLong(int categoryLong) {
        FmbServerConfig.categoryLong = categoryLong;
    }

    @Value("${fmb.categoryShow}")
    public  void setCategoryShow(int categoryShow) {
        FmbServerConfig.categoryShow = categoryShow;
    }

    @Value("${fmb.categoryChild}")
    public  void setCategoryChild(int categoryChild) {
        FmbServerConfig.categoryChild = categoryChild;
    }

    @Value("${fmb.categoryTravel}")
    public  void setCategoryTravel(int categoryTravel) {
        FmbServerConfig.categoryTravel = categoryTravel;
    }

    @Value("${fmb.categoryBuy}")
    public  void setCategoryBuy(int categoryBuy) {
        FmbServerConfig.categoryBuy = categoryBuy;
    }

    @Value("${spring.profiles.active}")
    public  void setEnv(String env) {
        FmbServerConfig.env = env;
        logger.info("FmbServerConfig_setEnv_run env="+env);
        logger.warn("trigger SnowflakeIdWorker init",SnowflakeIdWorker.getInstance().nextId());
    }


    @Value("${fmb.logPath}")
    public void setLogPath(String logPath) {
        FmbServerConfig.logPath = logPath;
    }

    @Value("${fmb.downPrefix}")
    public void setDownPrefix(String downPrefix) {
        FmbServerConfig.downPrefix = downPrefix;
    }

    @Value("${fmb.fmbImgDir}")
    public void setFmbImgDir(String fmbImgDir) {
        FmbServerConfig.fmbImgDir = fmbImgDir;
    }

    @Value("${fmb.fmbImgUrl}")
    public void setFmbImgUrl(String fmbImgUrl) {
        FmbServerConfig.fmbImgUrl = fmbImgUrl;
    }

    @Value("${fmb.serviceTel}")
    public  void setCategoryBuy(String serviceTel) {
        FmbServerConfig.serviceTel = serviceTel;
    }

    @Value("${fmb.serviceFax}")
    public  void setServiceFax(String serviceFax) {
        FmbServerConfig.serviceFax = serviceFax;
    }

    @Value("${fmb.fmbKefuUrl}")
    public  void setFmbKefuUrl(String fmbKefuUrl) {
        FmbServerConfig.fmbKefuUrl = fmbKefuUrl;
    }


    public static String getLogPath() {
        return logPath;
    }

    public static String getDownPrefix() {
        return downPrefix;
    }

    public static String getFmbImgDir() {
        return fmbImgDir;
    }

    public static String getFmbImgUrl() {
        return fmbImgUrl;
    }

    public static String getEnv() {
        return env;
    }

    /**
     * 是否是生产环境
     * @return
     */
    public static boolean isPro(){
        return "pro".equals(env) ;
    }


    public static String mSiteDomainHttp(){
       return isPro()? HTTP_M_FUMUBANG_COM : HTTP_M_FUMUBANG_NET;
    }
    public static String netOrCom(){
       return isPro()? "com" : "net";
    }


    public static int getCategoryLong() {
        return categoryLong;
    }

    public static int getCategoryShow() {
        return categoryShow;
    }

    public static int getCategoryChild() {
        return categoryChild;
    }

    public static int getCategoryTravel() {
        return categoryTravel;
    }

    public static int getCategoryBuy() {
        return categoryBuy;
    }


    public static String getFfmpegPath() {  return ffmpegPath; }

    public static String getServiceTel() {
        return serviceTel;
    }

    public static String getServiceFax() {
        return serviceFax;
    }

    public static String getFmbKefuUrl() {
        return fmbKefuUrl;
    }
}
