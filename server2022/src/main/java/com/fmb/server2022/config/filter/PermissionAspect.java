package com.fmb.server2022.config.filter;

import com.fmb.basic.BadAccessException;
import com.fmb.basic.NoPermissionException;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.service.SysTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;

/**
 * @description: [角色权限]控制拦截器
 */
@Aspect
@Slf4j
@Component
@Order(3)
public class PermissionAspect {
    @Autowired
    SysTokenService tokenService;

    @Before("@annotation(com.fmb.server2022.config.filter.RequiresPermissions)")
    public void before(JoinPoint joinPoint) {
        log.debug("开始校验[操作权限]");


        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        RequiresPermissions requiresPermissions = methodSignature.getMethod().getAnnotation(RequiresPermissions.class);
        if (requiresPermissions == null) {

            System.out.println("###NO PERMISSIONS###");
            return;
        }

        String[] params = requiresPermissions.value();


        SessionUserInfoOfFmbAdmin userInfo = tokenService.getUserInfo();

        if(userInfo==null){
            throw new BadAccessException("找不到对应token");
        }

        //当前用户的权限
        Set<String> myCodes = userInfo.getPermissionList();


        String joinCode = StringUtils.join(params, ",");
        log.debug("校验权限code: {}", Arrays.toString(params));
        log.debug("用户已有权限: {}", myCodes);
        //5.对比[要求]的code和[用户实际拥有]的code
        if (requiresPermissions.logical() == Logical.AND) {
            //必须包含要求的每个权限
            for (String perm : params) {
                if (!myCodes.contains(perm)) {
//                    log.warn("用户缺少权限 code : {}", perm);
                    throw new NoPermissionException(  "nopermit_visit [and]["+ userInfo.getUsername()+"]["+ joinCode+"]" );//抛出[权限不足]的异常
                }
            }
        } else {
            //多个权限只需包含其中一种即可
            boolean flag = false;
            for (String perm : params) {
                if (myCodes.contains(perm)) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
//                log.warn("用户缺少权限 code= : {} (任意有一种即可)", Arrays.toString(params));
                throw new NoPermissionException(" nopermit_visit [or]["+ userInfo.getUsername()+"]["+ joinCode+"]");//抛出[权限不足]的异常
            }
        }
    }
}
