package com.fmb.server2022.config;

import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.annotation.PreDestroy;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Configuration
@Order(5)
public class ExecutorConfig
{


    private static Logger logger = LoggerFactory.getLogger(ExecutorConfig.class);

    @Value("${async.core_pool_size}")
    private int corePoolSize;
    @Value("${async.max_pool_size}")
    private int maxPoolSize;
    @Value("${async.queue_capacity}")
    private int queueCapacity;


    private ThreadPoolExecutor executor ;

    @Bean(name = "asyncServiceExecutor")
    public ThreadPoolExecutor asyncServiceExecutor() {
        logger.info("start asyncServiceExecutor [corePoolSize={}] [maxPoolSize={}]",corePoolSize,maxPoolSize);

        final SynchronousQueue<Runnable> queue = new SynchronousQueue<>();
//        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 30L, TimeUnit.SECONDS,queue, new
//            ThreadPoolExecutor.CallerRunsPolicy());
        ThreadPoolExecutor threadPoolExecutor = new FmbxThreadPoolExecutor(corePoolSize, maxPoolSize, 300L, TimeUnit.SECONDS, new
            ArrayBlockingQueue<>(queueCapacity), new
            ThreadPoolExecutor.CallerRunsPolicy());

        threadPoolExecutor.prestartAllCoreThreads() ;

        this.executor = threadPoolExecutor ;

        return threadPoolExecutor;
    }

    @PreDestroy
    public void beforeDestory(){
        logger.info("beforeDestoryRun");
        if (this.executor != null) {
            this.executor.shutdown();
            try {
                //最多等60秒 完成已经提交的任务
                this.executor.awaitTermination(60,TimeUnit.SECONDS) ;
            } catch (InterruptedException ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            }
        }
        logger.info("beforeDestoryDone");
    }

}
