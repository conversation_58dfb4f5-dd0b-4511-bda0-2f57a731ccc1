package com.fmb.server2022.config;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fmb.util.SnowflakeIdWorker;
import com.fmb.util.upload.UploadFileOutInfo;
import com.fmb.util.upload.UploadFileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;

@Component
public class ExcelService {


//    private static String saveBaseDir = SpringUtils.getProperty("fmb.fmbImgDir");
//    private static String excelDownPrefix = SpringUtils.getProperty("fmb.fmbImgUrl");


//    @Value("${fmb.fmbImgDir}")
//    private String saveBaseDir;
//    @Value("${fmb.fmbImgUrl}")
//    private String excelDownPrefix;


    private static Logger logger = LoggerFactory.getLogger(ExcelService.class);

    /**
     * @param domainPrizePackage excel数据
     * @param c                  单行数据类
     * @param sheetName          excel工作表名字
     * @param filePre            excel文件前缀名
     */
    public static   UploadFileOutInfo genExcel(List domainPrizePackage, Class c, String sheetName, String filePre) {

        UploadFileOutInfo xlsx = UploadFileUtil.prepareFilePath(FmbServerConfig.getFmbImgDir(), FmbServerConfig.getFmbImgUrl(), filePre, "xlsx");

        ExcelWriter excelWriterBuilder = null;


        try {
            File file = new File(xlsx.getFilePath());
            if (!file.exists()) {
                file.createNewFile();
            }
            excelWriterBuilder = EasyExcel.write(new FileOutputStream(file)).build();

            WriteSheet writeSheet0 = EasyExcel.writerSheet(0, sheetName).head(c).build();
            excelWriterBuilder.write(domainPrizePackage, writeSheet0);
            excelWriterBuilder.finish();


        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
        }

        xlsx.setFilePath("");

        return xlsx;

    }


}
