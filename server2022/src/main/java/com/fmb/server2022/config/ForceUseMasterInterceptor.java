package com.fmb.server2022.config;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.fmb.basic.FmbConstants;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @Date: 2023/3/2 10:02 上午
 */
public class ForceUseMasterInterceptor implements InnerInterceptor {

    private static Logger logger = LoggerFactory.getLogger(ForceUseMasterInterceptor.class);

    @Override
    public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter) throws SQLException {

        if(!FmbConstants.DB_master.equals(DynamicDataSourceContextHolder.peek())){
            logger.warn("BAD_LOGIC_DB_USE_NOT_MASTER");
            DynamicDataSourceContextHolder.push(FmbConstants.DB_master);
        }
        InnerInterceptor.super.beforeUpdate(executor, ms, parameter);
    }
}
