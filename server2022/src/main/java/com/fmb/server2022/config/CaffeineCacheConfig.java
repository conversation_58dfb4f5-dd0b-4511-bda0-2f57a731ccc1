package com.fmb.server2022.config;


import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
/**
 * Caffeine 本地缓存
 */
public class CaffeineCacheConfig {


    @Bean
    public Cache<String, Object> caffeineCache() {
        @NonNull Cache<String, Object> build = Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterAccess(10, TimeUnit.MINUTES)
//                .evictionListener((String key, Object graph, RemovalCause cause) ->
//                        System.out.printf("Key %s was evicted (%s)%n", key, cause))
                // 初始的缓存空间大小
                .initialCapacity(30)
                // 缓存的最大条数
                .maximumSize(1000)
                .recordStats()
                .build();
        return build;
    }

}
