package com.fmb.server2022.config.springconfig;

import com.fmb.basic.BadAccessException;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbRespBean;
import com.fmb.basic.FrontApiSignException;
import com.fmb.basic.NoPermissionException;
import com.fmb.basic.RespBeanEnum;
import com.fmb.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

import static com.fmb.server2022.config.filter.WebLogAspect.getRequestInfo;

/**
 * 异常拦截处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice(annotations = {Controller.class,RestController.class})
public class GlobalExceptionHandler {

    private static Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    //其他错误
    @ExceptionHandler({Exception.class})
    @ResponseBody
    public FmbRespBean exception(Exception ex, HttpServletRequest request) {


        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",ex.getMessage()) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.ERROR , objectObjectHashMap) ;
    }

    private void logError(Exception ex, long errorid, HttpServletRequest request) {

        logger.error("errorid "+errorid,ex);
        if (request != null) {
            logger.error("请求路径 {} \n {}", request.getRequestURI(),getRequestInfo(request).toJSONString());
        }



    }


    @ExceptionHandler(BadAccessException.class)
    @ResponseBody
    public FmbRespBean exceptionNoAuth(BadAccessException ex, HttpServletRequest request) {

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
//        objectObjectHashMap.put("detailError",ex.getMessage()) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.LOGIN_NO_ACCESS , objectObjectHashMap) ;
    }

    @ExceptionHandler({NoPermissionException.class})
    @ResponseBody
    public FmbRespBean exceptionNoPermission(NoPermissionException ex, HttpServletRequest request) {

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",ex.getMessage()) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.LOGIN_INVALID_VISIT , objectObjectHashMap) ;
    }


    @ExceptionHandler({BadLogicException.class})
    @ResponseBody
    public FmbRespBean exceptionBadLogicException(BadLogicException ex, HttpServletRequest request) {

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",ex.getMessage()) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.LOGIN_BAD_LOGIC , objectObjectHashMap) ;
    }


    @ExceptionHandler({FrontApiSignException.class})
    @ResponseBody
    public FmbRespBean exceptionFrontApiSignException(FrontApiSignException ex, HttpServletRequest request) {

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",ex.getMessage()) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.FrontApiSignException , objectObjectHashMap) ;
    }



    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public FmbRespBean handleMethodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        BindingResult result = ex.getBindingResult();
        FieldError error = result.getFieldError();
        String field = error.getField();
        String code = error.getDefaultMessage();
        String message = String.format("%s:%s", field, code);

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",message) ;
        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.INVALID_REQPAR , objectObjectHashMap) ;

    }






    /**
     * 校验异常
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public FmbRespBean validationExceptionHandler(BindException ex, HttpServletRequest request) {
        BindingResult bindingResult = ex.getBindingResult();
        String errorMesssage = "";
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            errorMesssage += fieldError.getDefaultMessage() + "!";
        }

        long errorid = SnowflakeIdWorker.getInstance().nextId() ;
        logError(ex, errorid,request);

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("detailError",errorMesssage) ;
//        objectObjectHashMap.put("requestURI",request.getRequestURI()) ;
        objectObjectHashMap.put("errorid",errorid+"") ;
        return FmbRespBean.error(RespBeanEnum.INVALID_REQPAR , objectObjectHashMap) ;
    }


}
