package com.fmb.server2022.config.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FrontApiSignException;
import com.fmb.util.AuthCodeUtil;
import com.fmb.util.MD5Util;
import com.fmb.util.RegStringUtil;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.fmb.basic.FmbConstants.FmbxFront_UID_KEY;

@Aspect
@Component
public class FrontApiAspect {

    public static final Map<String, String> saltMap ;


    /**
     * 微信小程序
     */
    public static final String WX_APP_FMB_MAIN = "wxef0aac3d44dcda51";
    /**
     * 微信分享官
     */
    public static final String WX_APP_FMB_SHARE = "wxe90a0393c59a4ae7";

    /**
     * APP ios
     */
    public static final String APP_IOS = "638481987";
    /**
     * APP android
     */
    public static final String APP_ANDROID = "915527343";

    /**
     * H5
     */
    public static final String FMB_H5 = "202316888";
    /**
     * 系统
     */
    public static final String FMB_SYS = "202316000";



    static {
        HashMap<String, String> map = new HashMap<>();


//        父母邦：wxef0aac3d44dcda51
        map.put(WX_APP_FMB_MAIN,"f6848cbf03ea74e8fa29edd8cd5ab794") ;
//        分享官：wxe90a0393c59a4ae7
        map.put(WX_APP_FMB_SHARE,"efed7e1793d2318ae13573d4fc2e7e4d") ;

        map.put(APP_IOS,"0cd0f3e150e1dc2e9b29897b3fa6b566") ;
        map.put(APP_ANDROID,"ec09e4f55684c908160edc32e5bcbd29") ;

        map.put(FMB_H5,"22183b96de2c2a164ab80029e0a46ae2") ;
        saltMap = Collections.unmodifiableMap(map);

    }

    public static final String REG_UID = "\"uid\";s:\\d+:\"(\\d+)\";";
    private static Logger logger = LoggerFactory.getLogger(FrontApiAspect.class);

    @Autowired
    JedisPool jedisPool;

    @Pointcut("execution(public * com.fmb.server2022.controller.front.*Controller.*(..))")
    public void frontApi() {
    }


    @Before("frontApi()")
    public void doBeforeFrontApi(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {

            HttpServletRequest request = attributes.getRequest();



            //如果是前台对外提供的接口 统一走这个逻辑
            if (request.getRequestURI().startsWith(FmbConstants.FmbxFrontPath_PRE)) {

                String jsonBody = IOUtils.toString(((ContentCachingRequestWrapper) request).getContentAsByteArray(), "UTF-8");
                JSONObject jsonObject = JSON.parseObject(jsonBody);
                frontApiSignCheck(request,jsonBody,jsonObject);
                String uidEncode = jsonObject.getString("uid");
                String uid = null ;
                if (uidEncode != null && !"0".equals(uidEncode)) {
                    uid = AuthCodeUtil.fmbAuthCodeDecode(uidEncode);
                }

                final String headerip = request.getHeader("X-Real-IP");
                MDC.put(FmbConstants.USER_IP, headerip ==null?"":headerip);
                //如果请求post body里含有合法的uid
                if(StringUtils.isNotBlank(uid) && uid.matches("\\d+")){
                    putUidInfoToMdc(uid,"frontNew") ;
                }else {
                    h5OrWxChatH5ReadUserInfo(request);
                }
            }
        }
    }

    /**
     * 前端接口 签名校验
     * @param request
     * @param jsonBody
     * @param jsonObject
     */
    private void frontApiSignCheck(HttpServletRequest request,String jsonBody , JSONObject jsonObject) {
        String sign = request.getParameter("sign");
        if (StringUtils.isBlank(sign)) {
            throw new FrontApiSignException("未按要求携带sign参数");
        }
//        String jsonBody = IOUtils.toString(((ContentCachingRequestWrapper) request).getContentAsByteArray(), "UTF-8");
//        final JSONObject jsonObject = JSON.parseObject(jsonBody);
        if (jsonObject == null) {
            throw new FrontApiSignException("post请求体需要是json格式");
        }

        final String appid = jsonObject.getString("appid");
        if (StringUtils.isBlank(appid)) {
            throw new FrontApiSignException("未按要求携带appid参数");
        }

        final String seconds = jsonObject.getString("seconds");
        if (StringUtils.isBlank(seconds)) {
            throw new FrontApiSignException("未按要求携带seconds参数");
        }


        if (!saltMap.containsKey(appid)) {
            throw new FrontApiSignException("appid不合法");
        }

        if (StringUtils.isBlank(jsonObject.getString("version"))) {
            throw new FrontApiSignException("未按要求携带version参数");
        }

        final String aimSign = MD5Util.md5(jsonBody + saltMap.get(appid));
        if (!aimSign.equals(sign)) {
            logger.info("aimSign=[{}]",aimSign);
            throw new FrontApiSignException("API签名校验失败");
        }
    }

    /**
     * 解析h5请求头 如果携带 fsid 就登录 并创建session
     * 统一在MDC 里设置 用户uid 信息
     * MDC.put(FmbxFront_UID_KEY, attribute);
     *
     * @param request
     */
    private void h5OrWxChatH5ReadUserInfo(HttpServletRequest request) {
        String attribute = (String) request.getSession().getAttribute(FmbxFront_UID_KEY);
        if (attribute == null && request.getCookies() != null) {
            //从php 存储的客户端 cookie里读取fsid 信息
            final Optional<Cookie> fsid = Arrays.asList(request.getCookies()).stream().filter(cookie -> cookie.getName().equals("fsid")).findFirst();

            if (fsid.isPresent()) {

                Jedis jedis = null;

                try {
                    //从 redis 的1号库里 读取出php的用uid数据
                    jedis = jedisPool.getResource();
                    jedis.select(FmbConstants.REDIS_DB_1);
                    //读取php session 里存储的用户信息
                    final String phpUserinfo = jedis.get("sessions:" + fsid.get().getValue());

                    if (StringUtils.isNotBlank(phpUserinfo)) {
                        final String uid = RegStringUtil.regSlice(phpUserinfo, REG_UID);
//                            System.out.println(uid);
                        if (StringUtils.isNotBlank(uid)) {
                            //最终把用户的uid 放入mdc里
                            putUidInfoToMdc(uid,"restoreFromPhp");
                            request.getSession().setAttribute(FmbxFront_UID_KEY, uid);
                            logger.info("set session uid ={}", uid);
                        }
                    }
                } catch (Exception ex) {

                    long errorid = SnowflakeIdWorker.getInstance().nextId();
                    logger.error("errorid " + errorid, ex);
                } finally {
                    if (jedis != null) {
                        jedis.close();
                    }
                }
            }
        } else if (StringUtils.isNotBlank(attribute)) {
            putUidInfoToMdc(attribute,"javaSession");
            logger.info("read_session_uid={} putIntoMdc ", attribute);
        }
    }

    /**
     * 将用户信息写入mdc
     * @param uid
     * @param from
     */
    private void putUidInfoToMdc(String uid,String from) {
        HashMap<String, Object> uMap = new HashMap<>();
        uMap.put("uid", uid);
        uMap.put("from", from);
        MDC.put(FmbxFront_UID_KEY, JSON.toJSONString(uMap));
    }

}
