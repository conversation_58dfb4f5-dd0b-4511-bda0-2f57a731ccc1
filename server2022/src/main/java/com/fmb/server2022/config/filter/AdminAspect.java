package com.fmb.server2022.config.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.user.SessionUserInfoOfFmbAdmin;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.service.FmbAdminLoginService;
import com.fmb.server2022.service.SysTokenService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;

import static com.fmb.basic.FmbConstants.*;


/**
 ******************************************************************
 *   为了解决老后台php系统 直接打开新系统页面提示未登录问题增加这个类
 *   1. 读取请求头里的cookie asid 值
 *   2. 读取 redis 4号库里的 admin_info_"asid值" 获取uid
 *   3. 根据uid 读取数据库获取fmb_admin (只要不是disable的) 信息
 *   4. 将用户信息写入redis 设置为登录状态
 *   5. 后续再次请求 代码"find_asid_token"逻辑处 可以直接判断为登录状态
 ******************************************************************
 **/
@Aspect
@Component
public class AdminAspect {

    @Autowired
    SysTokenService tokenService ;

    @Autowired
    JedisPool jedisPool ;

    @Autowired
    IFmbAdminService fmbAdminService ;


    @Autowired
    FmbAdminLoginService loginService ;


    //后台接口 登录 校验白名单
    //以下接口 路径不需要登录校验  
    private static   HashSet<String> noCheckLogin = new HashSet<>() ;
    static {
        noCheckLogin.add("/fmbx/admin/login/authFmb") ;
        noCheckLogin.add("/fmbx/admin/login/logout") ;
        noCheckLogin.add("/fmbx/admin/login/authFmbFromAdmin001") ;
    }

    private static Logger logger = LoggerFactory.getLogger(AdminAspect.class);
    @Pointcut("execution(public * com.fmb.server2022.controller.admin.*Controller.*(..))")
    public void adminApi() {
    }

    @Pointcut("execution(public * com.fmb.server2022.controller.bp.BpsController.upload(..))")
    public void bpUpload() {
    }

    @Before("adminApi() || bpUpload()  ")
    public void doBeforeAdminApi(JoinPoint joinPoint) {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            final String requestURI = request.getRequestURI();

            if (noCheckLogin.contains(requestURI)){
                return;
            }

            final String token = MDC.get(TOKEN);
            if (StringUtils.isNotBlank(token) && tokenService.getUserInfoBytoken(token)!=null) {

                //这里用户携带了合法新后台 token
            }else {
                //读取老php 后台 cookie asid值
                readCookieAsid(request);
                if (StringUtils.isBlank(MDC.get(TOKEN)) && StringUtils.isBlank(MDC.get(BPSTOKEN))) {
                    throw new BadLogicException("后台接口调用需要登录后才能操作");
                }
            }
        }
    }

    private void readCookieAsid(HttpServletRequest request) {

        if (request.getCookies()==null) {
            return ;
        }

        final Optional<Cookie> first = Arrays.asList(request.getCookies()).stream().filter(s -> "asid".equals(s.getName())).findFirst();

        if (!first.isPresent()) {
            return;
        }
        final Cookie cookie = first.get();

        String asidValue = cookie.getValue();

        if (logger.isInfoEnabled()) {
            logger.info("asidValue={} ",asidValue);
        }

        if (StringUtils.isNotBlank(asidValue)) {

            Jedis jedis = null;

            try {
                jedis = jedisPool.getResource();
                jedis.select(FmbConstants.REDIS_DEFAULT_DB);

                //判断 是否存在以 asid为 token的 登录信息 如果有 就是等同认为登录
                //这里的redis 信息是 下面代码  asid_as_token_write (本文件里搜索) 处写入的
                if (jedis.exists(TOKEN_PRE_OF_FMBADMIN+asidValue)) {
                    if (logger.isInfoEnabled()) {
                        logger.info("find_asid_token {}",asidValue);
                    }
                    MDC.put(TOKEN,asidValue);
                }else{
                    String key = "admin_info_"+asidValue ;
                    String jsonValue = jedis.get(key);
                    final JSONObject jsonObject = JSON.parseObject(jsonValue);
                    //找到了
                    if (StringUtils.isNotBlank(jsonValue) && jsonObject.containsKey("uid") ) {

                        final FmbAdmin user = fmbAdminService.getById(Integer.parseInt(jsonObject.getString("uid")));
                        if (user.getDisabled()==0) {

                            SessionUserInfoOfFmbAdmin sessionUserInfoOfFmbAdmin = new SessionUserInfoOfFmbAdmin();
                            sessionUserInfoOfFmbAdmin.setUserId(user.getUid());
                            sessionUserInfoOfFmbAdmin.setUsername(user.getName());
                            sessionUserInfoOfFmbAdmin.setNickname(user.getRealname());
                            sessionUserInfoOfFmbAdmin.setPhone(user.getPhone());
                            //  asid_as_token_write
                            loginService.writeUserInfo2Cache(asidValue, sessionUserInfoOfFmbAdmin);

                            MDC.put(TOKEN,asidValue);
                        }

                    }
                }

            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid, ex);
            } finally {
                if (jedis != null) {
                    jedis.close();
                }
            }

        }
    }
}
