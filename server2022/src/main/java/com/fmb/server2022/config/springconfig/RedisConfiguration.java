package com.fmb.server2022.config.springconfig;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fmb.server2022.config.redis.CustomKeyGenerator;
import com.fmb.server2022.config.redis.FmbRedisConfig;
import com.fmb.server2022.config.redis.FmbxRedisCachaManager;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.TransportMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Configuration
@EnableCaching
public class RedisConfiguration {



    private static Logger logger = LoggerFactory.getLogger(RedisConfiguration.class);

    @Autowired
    FmbRedisConfig redisConfigInfo ;




    @Bean
    public JedisPool redisPoolFactory() {
        logger.info("JedisPool注入成功！！");
        logger.info("redis地址：" + redisConfigInfo.getHost() + ":" + redisConfigInfo.getPort());
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxIdle(redisConfigInfo.getMaxIdle());
        config.setMaxTotal(redisConfigInfo.getMax_active());
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        config.setTestWhileIdle(true);
        config.setNumTestsPerEvictionRun(10);
        config.setTimeBetweenEvictionRunsMillis(12000);
        config.setSoftMinEvictableIdleTimeMillis(60000);
        config.setBlockWhenExhausted(false);

        JedisPool jedisPool ;
//        int timeout = Integer.parseInt( redisConfigInfo.getTimeout().replaceAll("[^\\d]]",""));
        int timeout =  redisConfigInfo.getTimeout() ;


        if(StringUtils.isBlank(redisConfigInfo.getPassword())){
            jedisPool = new JedisPool(config, redisConfigInfo.getHost(), redisConfigInfo.getPort(), timeout, null,
                    redisConfigInfo.getDatabase());
        }else {
            jedisPool = new JedisPool(config, redisConfigInfo.getHost(), redisConfigInfo.getPort(), timeout,
                    redisConfigInfo.getPassword(),redisConfigInfo.getDatabase());
        }

        return jedisPool;
    }


    @Bean
    public RedissonClient getRedisson(){
        Config config = new Config();
//        int timeout = Integer.parseInt( redisConfigInfo.getTimeout().replaceAll("[^\\d]]",""));
        int timeout =  redisConfigInfo.getTimeout() ;


        if (StringUtils.isNotBlank(redisConfigInfo.getPassword())) {

            config
                    .setTransportMode(TransportMode.NIO)
                    .useSingleServer().setAddress("redis://" + redisConfigInfo.getHost() + ":" + redisConfigInfo.getPort())
                    .setPassword(redisConfigInfo.getPassword())
                    .setDatabase(redisConfigInfo.getDatabase())
                    .setTimeout(timeout)
                    .setConnectionPoolSize(redisConfigInfo.getMax_active())
                    .setConnectionMinimumIdleSize(redisConfigInfo.getMaxIdle());
        }else {

            config
                    .setTransportMode(TransportMode.NIO)
                    .useSingleServer().setAddress("redis://" + redisConfigInfo.getHost() + ":" + redisConfigInfo.getPort())
                    .setDatabase(redisConfigInfo.getDatabase())
                    .setTimeout(timeout)
                    .setConnectionPoolSize(redisConfigInfo.getMax_active())
                    .setConnectionMinimumIdleSize(redisConfigInfo.getMaxIdle());


        }


        RedissonClient redissonClient = Redisson.create(config);

        return redissonClient;
    }




    @Bean("customKeyGenerator")
    public KeyGenerator keyGenerator() {
        logger.info("_____keyGenerator_run_____");
        return new CustomKeyGenerator();
    }


    @Bean("blank")
    public KeyGenerator balnk() {
        logger.info("_____keyGenerator_run_____");
        return new KeyGenerator() {
            @Override
            public Object generate(Object target, Method method, Object... params) {
                return "";
            }
        };
    }


    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        return new FmbxRedisCachaManager(
                RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory),
                this.getRedisCacheConfigurationWithTtl(0)
        );
    }



//    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
//        Map<String, org.springframework.data.redis.cache.RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>();
//        redisCacheConfigurationMap.put("ttl30", this.getRedisCacheConfigurationWithTtl(30));
//        redisCacheConfigurationMap.put("ttl60", this.getRedisCacheConfigurationWithTtl(60));
//
//
//        //10分钟
//        redisCacheConfigurationMap.put("ttl600", this.getRedisCacheConfigurationWithTtl(10*60));
//        //一小时
//        redisCacheConfigurationMap.put("ttl3600", this.getRedisCacheConfigurationWithTtl(3600));
//
//        redisCacheConfigurationMap.put("ttl2H", this.getRedisCacheConfigurationWithTtl(3600*2));
//        redisCacheConfigurationMap.put("ttl12H", this.getRedisCacheConfigurationWithTtl(3600*12));
//
//        return redisCacheConfigurationMap;
//    }


    private org.springframework.data.redis.cache.RedisCacheConfiguration getRedisCacheConfigurationWithTtl(Integer seconds) {
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        om.registerModule(javaTimeModule);


        jackson2JsonRedisSerializer.setObjectMapper(om);

        org.springframework.data.redis.cache.RedisCacheConfiguration redisCacheConfiguration = org.springframework.data.redis.cache.RedisCacheConfiguration.defaultCacheConfig();
        redisCacheConfiguration = redisCacheConfiguration.serializeValuesWith(
                RedisSerializationContext
                        .SerializationPair
                        .fromSerializer(jackson2JsonRedisSerializer)
        ).entryTtl(Duration.ofSeconds(seconds));

        return redisCacheConfiguration;
    }


}
