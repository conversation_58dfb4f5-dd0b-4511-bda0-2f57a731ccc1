package com.fmb.server2022.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JacksonStdImpl;
import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Date: 2023/2/22 7:41 下午
 */
@JacksonStdImpl
class BigDecimalToStringSerializer extends ToStringSerializer {
    public final static BigDecimalToStringSerializer instance = new BigDecimalToStringSerializer();

    public BigDecimalToStringSerializer() {
        super(Object.class);
    }

    public BigDecimalToStringSerializer(Class<?> handledType) {
        super(handledType);
    }

    @Override
    public boolean isEmpty(SerializerProvider prov, Object value) {
        if (value == null) {
            return true;
        }
        String str = ((BigDecimal) value).stripTrailingZeros().toPlainString();
        return str.isEmpty();
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider provider)
            throws IOException {
        final String s1 = ((BigDecimal) value).stripTrailingZeros().toPlainString();
        final String s2 = ((BigDecimal) value).setScale(2, RoundingMode.HALF_UP)
                .stripTrailingZeros().toPlainString();

        if (s1.equals(s2)){
            gen.writeNumber(s1);
        }else {
            gen.writeNumber(s2);
        }

    }

    @Override
    public void serializeWithType(Object value, JsonGenerator gen,
                                  SerializerProvider provider, TypeSerializer typeSer)
            throws IOException {
        // no type info, just regular serialization
        serialize(value, gen, provider);
    }
}
