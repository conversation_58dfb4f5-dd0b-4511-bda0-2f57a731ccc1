package com.fmb.server2022.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@ConfigurationProperties(prefix = "dingding")
@Component
@Data
public class DingDingConfig {

//    finance: 092311205924241632
//    legal: 06306400291235399
//    ceo: 062904404929312818
//    manager:
//            - manager4802
//          - 045610272032925613
//                  - 063249595023341948
//                  - 011017315532903910
    String finance ;
    String legal ;
    String ceo ;
    ArrayList<String> manager ;

}
