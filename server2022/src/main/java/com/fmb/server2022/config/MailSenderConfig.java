package com.fmb.server2022.config;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@AllArgsConstructor
public class MailSenderConfig {

    private final MailConfig mailConfig;

    private final Map<String,JavaMailSenderImpl> senderMap;

    /**
     * 初始化 sender
     */
    @PostConstruct
    public void buildMailSender(){
        Map<String,MailConfig.MailProperties> mailConfigs = mailConfig.getConfigs();
        log.info("初始化mailSender");
        mailConfigs.forEach((key,mailProperties) -> {

            // 邮件发送者
            JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
            javaMailSender.setHost(mailProperties.getHost());
            if (mailProperties.getPort() != null) {
                javaMailSender.setPort(mailProperties.getPort());
            }
            if (mailProperties.getDefaultEncoding() != null) {
                javaMailSender.setDefaultEncoding(mailProperties.getDefaultEncoding());
            }
            if (mailProperties.getProtocol() != null) {
                javaMailSender.setProtocol(mailProperties.getProtocol());
            }
            javaMailSender.setUsername(mailProperties.getUsername());
            javaMailSender.setPassword(mailProperties.getPassword());

            // 添加数据
            senderMap.put(key,javaMailSender);
        });
    }

    /**
     * 获取MailSender
     * @return CustomMailSender
     */
    public JavaMailSenderImpl getSender(String name){
        if(senderMap.isEmpty()){
            buildMailSender();
        }
        // 随机返回一个JavaMailSender
        return senderMap.get(name);
    }

    /**
     * 清理 sender
     */
    public void clear(){
        senderMap.clear();
    }

}
