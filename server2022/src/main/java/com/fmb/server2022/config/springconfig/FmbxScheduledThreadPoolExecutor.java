package com.fmb.server2022.config.springconfig;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR>
 * @Date: 2023/3/2 11:38 上午
 */
public class FmbxScheduledThreadPoolExecutor extends ScheduledThreadPoolExecutor {
    private static Logger logger = LoggerFactory.getLogger(FmbxScheduledThreadPoolExecutor.class);
    public FmbxScheduledThreadPoolExecutor(int corePoolSize) {
        super(corePoolSize);
    }

    public FmbxScheduledThreadPoolExecutor(int corePoolSize, ThreadFactory threadFactory) {
        super(corePoolSize, threadFactory);
    }

    public FmbxScheduledThreadPoolExecutor(int corePoolSize, RejectedExecutionHandler handler) {
        super(corePoolSize, handler);
    }

    public FmbxScheduledThreadPoolExecutor(int corePoolSize, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, threadFactory, handler);
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        DynamicDataSourceContextHolder.clear();
        super.afterExecute(r, t);
    }
}
