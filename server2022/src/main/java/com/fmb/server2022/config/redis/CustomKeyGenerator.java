package com.fmb.server2022.config.redis;

import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

public class CustomKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        return ":" + target.getClass().getSimpleName() + ":"
                + method.getName() + "_"
                + StringUtils.arrayToDelimitedString(params, "_");
    }
}
