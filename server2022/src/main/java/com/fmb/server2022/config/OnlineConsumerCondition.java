package com.fmb.server2022.config;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * <AUTHOR>
 * @Date: 2023/4/18 8:17 下午
 *
 * 规划某些特定的工作任务只在 测试环境 或者 fmb.node-type 是 admin001 时运行
 * 线上的服务器 节点类型
 * 1. 前台请求的服务节点 fmb.node-type 默认是 front
 * 2. 应用启动可以指定 fmb.node-type 为admin001 , 可以指定这种机器执行定时任务 ,减轻节点类型为front 的节点压力.
 *
 *
 */
public class OnlineConsumerCondition  implements Condition {

    private static Logger logger = LoggerFactory.getLogger(OnlineConsumerCondition.class);

    public static final String DEV = "dev";
    public static final String ADMIN001 = "admin001";

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata annotatedTypeMetadata) {

        String propertyValue = context.getEnvironment().getProperty("spring.profiles.active");
        String nodeType = context.getEnvironment().getProperty("fmb.node-type");

        logger.info("propertyValue=[{}] nodeType=[{}]",propertyValue,nodeType);
        if (DEV.equals(propertyValue)) {
            return  true ;
        }
        if (StringUtils.isNotBlank(nodeType) &&  ADMIN001.equals(nodeType)) {
            return  true ;
        }

        return false;
    }
}
