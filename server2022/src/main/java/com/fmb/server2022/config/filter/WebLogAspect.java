package com.fmb.server2022.config.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.FmbConstants;
import com.fmb.util.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @description: 打印每个请求的入参、出参等信息
 */

@Aspect
@Component
@Slf4j
@Profile({"local","dev"})
public class WebLogAspect {
    @Pointcut("execution(public * com.fmb.server2022.controller..*.*(..))")
    public void webLog() {
    }



//    @Pointcut(" execution(public * com.fmb.server2022.config.springconfig.GlobalExceptionHandler.*(..))")
//    public void exceptions() {
//    }

    /**
     * 只在进入controller时记录请求信息
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            if (log.isInfoEnabled()) {
                log.info("请求路径 {} ,进入方法 {} \n {}", request.getRequestURI(), joinPoint.getSignature().getDeclaringTypeName() + ":" + joinPoint
                        .getSignature().getName(),getRequestInfo(request).toJSONString());
            }

            MDC.put("startTime", String.valueOf(System.currentTimeMillis()));
        }

    }

    /**
     * 打印请求日志
     */
//    @AfterReturning(pointcut = "webLog()|| exceptions()", returning = "result")
    @AfterReturning(pointcut = "webLog() ", returning = "result")
    public void afterReturning(Object result) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Map<String, String> map = MDC.getCopyOfContextMap();
            if (map != null) {
                JSONObject jsonObject = new JSONObject(true);
                jsonObject.put("uri", request.getRequestURI());
                jsonObject.put("took", System.currentTimeMillis() - Long.parseLong(map.getOrDefault("startTime", String.valueOf(System.currentTimeMillis()))));
//            jsonObject.put("req", JSON.parseObject( map.getOrDefault("req","{}")));
                if (result != null) {
                    jsonObject.put("res", result);
                }
                log.info(jsonObject.toJSONString());
            }
        }

    }

    /**
     * 读取请求信息,转换为json
     */
    public static JSONObject getRequestInfo(HttpServletRequest req) {
        JSONObject requestInfo = new JSONObject();
        try {
            StringBuffer requestURL = req.getRequestURL();
            requestInfo.put("requestURL", requestURL);
            String method = req.getMethod();
            requestInfo.put("method", method);
            if (req.getQueryString() != null) {
                requestInfo.put("queryString", URLDecoder.decode(req.getQueryString(), "UTF-8"));
            }

            String t1 = MDC.get(FmbConstants.TOKEN);
            if (t1 != null) {
                requestInfo.put(FmbConstants.TOKEN, t1) ;
            }
            String t2 = MDC.get(FmbConstants.BPSTOKEN);
            if (t2 != null) {
                requestInfo.put(FmbConstants.BPSTOKEN, t2) ;
            }

//            requestInfo.put("traceId",MDC.get("traceId"));

//            String remoteAddr = req.getRemoteAddr();
            String ip = req.getHeader("X-Real-IP");

            final String headerToken = req.getHeader(FmbConstants.TOKEN);
            final String headerBpsToken = req.getHeader(FmbConstants.BPSTOKEN);
            if (headerToken != null) {
                requestInfo.put(FmbConstants.TOKEN,headerToken);
            }
            if (headerBpsToken != null) {
                requestInfo.put(FmbConstants.BPSTOKEN,headerBpsToken);
            }

            requestInfo.put("remoteAddr", ip==null?req.getRemoteAddr():ip);
            if (req instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) req;
                String bodyStr = new String(wrapper.getContentAsByteArray(), StandardCharsets.UTF_8);
                if (bodyStr.startsWith("{")) {
                    JSONObject jsonObject = JSON.parseObject(bodyStr);
                    requestInfo.put("requestBody", jsonObject);
                }
            }
        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            log.error("errorid " + errorid, ex);

        }
        return requestInfo;
    }
}
