package com.fmb.server2022.config.filter;

import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.service.SysTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

import static com.fmb.basic.FmbConstants.BPSTOKEN;
import static com.fmb.basic.FmbConstants.TOKEN;

@Component
@Slf4j
public class RequestFilter extends OncePerRequestFilter implements Filter {


    private static Logger logger = LoggerFactory.getLogger(RequestFilter.class);
    @Autowired
    SysTokenService tokenService;



    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            //每个请求记录一个traceId,可以根据traceId搜索出本次请求的全部相关日志

            if (!FmbServerConfig.isPro()) {
                MDC.put("traceId", UUID.randomUUID().toString().replace("-", "").substring(0, 12));
                MDC.put("startTime", ""+System.currentTimeMillis());
            }

            readToken(request);
            //使request中的body可以重复读取 https://juejin.im/post/6858037733776949262#heading-4
            ContentCachingRequestWrapper cachingRequestWrapper = new ContentCachingRequestWrapper(request);
            filterChain.doFilter(cachingRequestWrapper, response);
        } catch (Exception e) {
            throw e;
        } finally {
            //清理ThreadLocal
            MDC.clear();
        }
    }


    private void readToken(HttpServletRequest request) {
        //通过token解析出username
        String token = request.getHeader(TOKEN);
        if (StringUtils.isNotBlank(token)) {
            MDC.put(TOKEN, token);
        }
        String bpstoken = request.getHeader(BPSTOKEN);
        if (StringUtils.isNotBlank(bpstoken)) {
            MDC.put(BPSTOKEN, bpstoken);
        }



    }
}
