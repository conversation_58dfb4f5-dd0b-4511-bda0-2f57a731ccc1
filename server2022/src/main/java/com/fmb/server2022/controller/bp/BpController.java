package com.fmb.server2022.controller.bp;

import com.fmb.basic.FmbRespBean;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.reqdomain.ReqBpContract;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqFmbxBpReviewList;
import com.fmb.server2022.service.BpService;
import com.fmb.server2022.service.BpsService;
import com.fmb.server2022.service.FmbxBpService;
import com.fmb.server2022.service.UserUnionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.basic.user.SessionUser.SESSIONUSER_BP;

@RestController
@RequestMapping("/bp")
public class BpController {




    @Autowired
    BpService bpService ;


    @Autowired
    FmbxBpService fmbBpService ;

    @Autowired
    UserUnionService userUnionService ;



    @Autowired
    BpsService bpsService ;


    /**
     * 供应商更新 自己的信息
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateBp")
    @ResponseBody
    public FmbRespBean updateBp(@RequestBody @Valid com.fmb.server2022.reqdomain.ReqUpdateBp input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        //商家后台 直接去当前登录 供应商的id
        SessionUser sessionUser = userUnionService.nowUserInfo();
        if (sessionUser.getUserType()== SESSIONUSER_BP) {
            input.setBpId(sessionUser.getUserId());
        }

        bpService.updateBp(resultMap, input);

        return FmbRespBean.success(resultMap);
    }


    @GetMapping(value = "/detailFmbxBp")
    public FmbRespBean fmbxBpMainDetail(
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        SessionUser userInfo = userUnionService.nowUserInfo();

        fmbBpService.bpDetailNoPass(userInfo.getUserId(), resultMap);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/fmbxBpReviewList")
    @ResponseBody
    public FmbRespBean fmbxBpReviewList() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        SessionUser userInfo = userUnionService.nowUserInfo();
        ReqFmbxBpReviewList req = new ReqFmbxBpReviewList() ;
        req.setBpId(userInfo.getUserId());
        fmbBpService.selectFmbxBpReviewList(resultMap, req);

        return FmbRespBean.success(resultMap);
    }




    /**
     * 供应商合同管理
     */
    @RequestMapping(value = "/bpContractList")
    @ResponseBody
    public FmbRespBean bpContract(@RequestBody @Valid ReqBpContract req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        SessionUser userInfo = userUnionService.nowUserInfo();

        if (userInfo.getUserType()==SESSIONUSER_BP) {
            req.setBpId(userInfo.getUserId());
        }


        bpsService.selectBpContract(resultMap, req);


        return FmbRespBean.success(resultMap);
    }


    /**
     * 商户查询
     * @param req
     * @return
     */
    @RequestMapping(value = "/bpsList")
    @ResponseBody
    public FmbRespBean bpsList(@RequestBody @Valid ReqBpsInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        SessionUser userInfo = userUnionService.nowUserInfo();
        //供应商后台需要把当前商户 bpid放进去
        req.setBpId(userInfo.getUserId());

        bpsService.selectBpsInfo(resultMap, req);


        return FmbRespBean.success(resultMap);
    }



}
