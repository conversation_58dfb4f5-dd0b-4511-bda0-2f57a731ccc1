package com.fmb.server2022.controller.publictool;


import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.basic.RespBeanEnum;
import com.fmb.util.SnowflakeIdWorker;
import com.google.code.kaptcha.Producer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;

/**
 * 图片验证码 接口类
 */
@RestController
@RequestMapping("/publictool")
public class CaptchaController {

    private static Logger logger = LoggerFactory.getLogger(CaptchaController.class);

//    @Resource(name = "captchaProducer")
//    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

//
//    @Autowired
//    FileUploadService uploadService ;


    @Autowired
    JedisPool jedisPool;


    @RequestMapping(value = "/queryCaptcha")
    @ResponseBody
    public FmbRespBean queryCaptcha(@RequestParam(value = "imageAnswerKey", required = true) String code) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("answer","") ;

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String verifyKey = FmbConstants.CAPTCHA_CODE_KEY + code;

            String s = jedis.get(verifyKey);
            if (StringUtils.isNotBlank(s)) {
                resultMap.put("answer",s) ;
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return FmbRespBean.success(resultMap);
    }

    @GetMapping("/captchaImage")
    public FmbRespBean getCode(HttpServletRequest req) {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        // 保存验证码信息
        String uuid = SnowflakeIdWorker.getInstance().nextId() + "";
        String verifyKey = FmbConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        //生成文本问题
        String capText = captchaProducerMath.createText();
        String split = "#";
        //文本问题
        capStr = capText.substring(0, capText.lastIndexOf(split));
        //答案
        code = capText.substring(capText.lastIndexOf(split) + 1);
        image = captchaProducerMath.createImage(capStr);

        //将信息写入redis
        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            jedis.setex(verifyKey, 2 * 60, code);

        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return FmbRespBean.error(RespBeanEnum.ERROR, "服务异常");
        }

        resultMap.put("imageAnswerKey", uuid);
        //完整的文本问题注释掉
//        resultMap.put("capText", capText);
        String value = java.util.Base64.getEncoder().encodeToString((os.toByteArray()));
        resultMap.put("img", value);

//        String htmlcontent = "<!DOCTYPE html>\n" +
//                "<html lang=\"en\">\n" +
//                "\n" +
//                "<head>\n" +
//                "    <meta charset=\"UTF-8\">\n" +
//                "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
//                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
//                "    <style type=\"text/css\">\n" +
//                "        #app p {\n" +
//                "            text-align: center;\n" +
//                "        }\n" +
//                "    </style>\n" +
//                "</head>\n" +
//                "\n" +
//                "<body>\n" +
//                "\n" +
//                "    <img height=\"60px\" width=\"160px\" src=\"data:image/jpeg;base64,"+value+"\"  />\n" +
//                "\n" +
//                "</body>\n" +
//                "\n" +
//                "</html>";
//        try {
//            FileUtils.writeStringToFile(new File("/Users/<USER>/Downloads/"+System.currentTimeMillis()+".html"),htmlcontent);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        FmbRespBean ajax = FmbRespBean.success(resultMap);

        return ajax;
    }





}
