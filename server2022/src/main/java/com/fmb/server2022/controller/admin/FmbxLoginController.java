package com.fmb.server2022.controller.admin;


import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.server2022.service.FmbAdminTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

@RestController
@RequestMapping("/admin/login")
public class FmbxLoginController {


    @Autowired
    private FmbAdminTokenService loginService;


    /**
     * 老系统登录
     * @param user
     * @return
     */
    @RequestMapping(value = "/authFmb")
    @ResponseBody
    public FmbRespBean auth2(@RequestBody @Valid ReqLoginUser user) {


        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("token", loginService.authFmb(user));
        return FmbRespBean.success(resultMap);
    }


    /**
     * 老admin001 快速登录 获取token
     * @param userInfo
     * @return
     */
    @RequestMapping(value = "/authFmbFromAdmin001")
    @ResponseBody
    public FmbRespBean authFmbFromAdmin001(@RequestParam(value = "userInfo", required = true) String userInfo) {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("token", loginService.authFmbFromAdmin001(userInfo));
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/logout")
    @ResponseBody
    public FmbRespBean logout() {


        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        loginService.logout(resultMap) ;
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/nowUser")
    @ResponseBody
    public FmbRespBean nowUser() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("user", loginService.nowUser());
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/menuTree")
    @ResponseBody
    public FmbRespBean menuTree() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        loginService.menuTree(resultMap);

        return FmbRespBean.success(resultMap);
    }



}
