package com.fmb.server2022.controller.admin;

import com.fmb.server2022.config.ExcelService;
import com.fmb.server2022.domain.NewUserOrderStatDomain;
import com.fmb.server2022.reqdomain.DingdingReq;
import com.fmb.server2022.service.NewUserStatService;
import com.fmb.util.upload.UploadFileOutInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;

import static com.fmb.util.FmbCommonUtil.reqCheck;

/**
 * <AUTHOR>
 * @Date: 2023/8/3 3:41 下午
 */
@RequestMapping("/admin/manualRun")
@Controller
public class ManualRunController {


    private  final   NewUserStatService newUserStatService ;

    @Autowired
    public ManualRunController(NewUserStatService newUserStatService) {
        this.newUserStatService = newUserStatService;
    }


    @RequestMapping(value = "/newUserStat.do")
    @ResponseBody
    public HashMap<String, Object> newUserStat(
            @RequestBody DingdingReq input
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        if (reqCheck(input)){

            final ArrayList<NewUserOrderStatDomain> lists = new ArrayList<>();

            for (Integer aid : input.getAids()) {
                final NewUserOrderStatDomain newUserOrderStatDomain = newUserStatService.newUserStat( aid,input.getDateBegin(),
                        input.getDateEnd(),input.getSecond());
                lists.add(newUserOrderStatDomain) ;

            }

            if (input.getDownExcel() != null && 1==input.getDownExcel()) {
                final UploadFileOutInfo outInfo = ExcelService.genExcel(lists, NewUserOrderStatDomain.class, "新注册用户统计", "newUserStat");
                resultMap.put("excelOutInfo", outInfo);
            }
            resultMap.put("lists", lists);

//        }

        return resultMap;
    }
}
