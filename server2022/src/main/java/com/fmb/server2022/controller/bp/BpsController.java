package com.fmb.server2022.controller.bp;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.fmbx.service.IFmbxBpParterHotelInfoService;
import com.fmb.server2022.fmbx.service.IFmbxBpParterInfoService;
import com.fmb.server2022.reqdomain.ReqBpsMedia;
import com.fmb.server2022.reqdomain.ReqBpsMediaInfo;
import com.fmb.server2022.reqdomain.ReqBpsMediaUpdate;
import com.fmb.server2022.service.BpsService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;

/**
 * 商家后台
 */
@RestController
@RequestMapping("/bp")
public class BpsController {


    private static Logger logger = LoggerFactory.getLogger(BpsController.class);

    @Autowired
    IFmbxBpParterInfoService parterInfoService ;

    @Autowired
    IFmbxBpParterHotelInfoService hotelInfoService ;



    @Autowired
    BpsService bpsService ;


    @RequestMapping(value = "/upload")
    @ResponseBody
    public FmbRespBean upload(
            @RequestParam(value = "file", required = true) MultipartFile uploadFile
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        resultMap.put("fileinfo", bpsService.upload(uploadFile) );

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/uploadOfRichText")
    public void uploadOfRichText(
            @RequestParam(value = "file", required = true) MultipartFile uploadFile
            , HttpServletResponse rep
    ) {

        PrintWriter writer = null ;
        rep.setContentType("application/json");
        rep.setCharacterEncoding("utf-8");
        try {

            HashMap<String, Object> resultMap = bpsService.upload(uploadFile);

            HashMap<String, Object> hr = new HashMap<>();
            HashMap<String, Object> data = new HashMap<>();
            hr.put("errno", 0);
            hr.put("data",data) ;

            data.put("url", resultMap.get("url"));
            data.put("alt", "");
            data.put("href", resultMap.get("url"));


            writer = rep.getWriter();
            IOUtils.write(JSON.toJSONString(hr), writer);

        }catch (Exception ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);

            HashMap<String, Object> hr = new HashMap<>();
            hr.put("errno", 1);
            hr.put("message", ex.getMessage());
            try {
                IOUtils.write(JSON.toJSONString(hr), writer);
            } catch (IOException e) {
            }

        }finally {
            if (writer != null) {
                writer.close();
            }
        }


    }



    @RequestMapping(value = "/addBpsMedia")
    @ResponseBody
    public FmbRespBean addBpsMedia(
       @RequestBody     ReqBpsMedia req
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.addBpsMedia(resultMap, req)  ;

        return FmbRespBean.success(resultMap);
    }




    /**
     * 商户media查询
     */

    @RequestMapping(value = "/bpsMediaInfo")
    @ResponseBody
    public FmbRespBean bpsMediaInfo(@RequestBody @Valid ReqBpsMediaInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.selectBpsMediaInfo(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 商户图片 其他基础信息 包括商户名字 筛选条件
     * @param req
     * @return
     */
    @RequestMapping(value = "/bpsMediaFilter")
    @ResponseBody
    public FmbRespBean bpsMediaFilter(@RequestBody @Valid ReqBpsMediaInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsMediaSimpleInfo(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 商户 media  屏蔽或打开
     * @param req
     * @return
     */
    @RequestMapping(value = "/bpsMediaToggleStatus")
    @ResponseBody
    public FmbRespBean bpsMediaToggleStatus(@RequestBody @Valid ReqBpsMediaInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsMediaToggleStatus(resultMap, req);

        return FmbRespBean.success(resultMap);
    }




    /**
     * 单张图片酒店图片详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/bpsMediaDetail")
    @ResponseBody
    public FmbRespBean bpsMediaDetail(
            @RequestParam(value = "id", required = true) Integer id

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsMediaDetail(resultMap, id);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 更新 单张图片
     * @param req
     * @return
     */
    @RequestMapping(value = "/bpsMediaUpdate")
    @ResponseBody
    public FmbRespBean bpsMediaUpdate(
           @RequestBody @Valid ReqBpsMediaUpdate req

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsMediaUpdate(resultMap, req);

        return FmbRespBean.success(resultMap);
    }




    @RequestMapping(value = "/bpsupdate")
    @ResponseBody
    public FmbRespBean bpsupdate(@RequestBody @Valid FmbxBps input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.updateBps(resultMap,input); ;


        return FmbRespBean.success(resultMap);
    }







}
