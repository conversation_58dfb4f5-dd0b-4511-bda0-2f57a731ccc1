package com.fmb.server2022.controller.demo;

import com.alibaba.fastjson.JSONObject;
import com.fmb.server2022.fmbx.entity.FmbxKafkaMessage;
import com.fmb.server2022.fmbx.service.IFmbxKafkaMessageService;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class FmbKafkaWorkerNeedAck implements  Runnable{

    //期望执行最长时长 超过这个目前打印warn日志,并记录到数据库
    //因为过长会堵塞整个kafka 消费的速度
    public static final int EXPECT_MAX_MILLIONS = 2000;
    public static final int KEY_LEN = 18;
    private static Logger logger = LoggerFactory.getLogger(FmbKafkaWorkerNeedAck.class);

    private  JSONObject binfo;
    /**
     * 门栓
     */
    CountDownLatch countDownLatch ;
    /**
     * kafka 消息
     */
    ConsumerRecord<String, String> record ;

    private String messageid ;
    FmbxKafkaMessage entity ;


    private String consumerGroupId ;

    IFmbxKafkaMessageService kafkaMessageService ;

    public String getConsumerGroupId() {
        return consumerGroupId;
    }


    public JSONObject getBinfo() {
        return binfo;
    }

    public ConsumerRecord<String, String> getRecord() {
        return record;
    }

    public String getMessageid() {
        return messageid;
    }

    /**
     * 门栓打开标志
     */
    AtomicBoolean countDown = new AtomicBoolean(false) ;

    public FmbKafkaWorkerNeedAck(CountDownLatch countDownLatch, ConsumerRecord<String, String> record, String consumerGroupId, IFmbxKafkaMessageService kafkaMessageService) {
        this.countDownLatch = countDownLatch;
        this.record = record;
        this.consumerGroupId = consumerGroupId ;
        this.kafkaMessageService = kafkaMessageService ;

        final JSONObject jsonObject = JSONObject.parseObject(record.value());
        if (jsonObject != null) {
            final String messageid = jsonObject.getString(FmbKafkaProducerService.MESSAGEID);
            if (messageid != null  && messageid.length()== KEY_LEN) {
                this.messageid = messageid ;
                this.binfo = jsonObject.getJSONObject("binfo");
            }
        }

    }

    /**
     * 通过这个方法可以释放 门栓
     */
    void doCountDown(){
        if (countDown.compareAndSet(false,true)) {
            countDownLatch.countDown();
        }
    }

    public void doJob() {
        final long start = System.currentTimeMillis();
        long use = 0L ;
        try {
//            insertRedisJob();
            doBusinessJob(this.record) ;
            use = System.currentTimeMillis() - start;
//            delRedisJob();
            if (use > EXPECT_MAX_MILLIONS) {
                logger.warn("kafkaDoJobUseTimeMillions=[{}],topic=[{}], consumerGroupId=[{}], msg=[{}]",use ,record.topic(),consumerGroupId,
                        record.value()  );
                createDbMessage(2,"run_use:"+use,use);
            }
        }catch (Exception ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
            use = System.currentTimeMillis() - start;
            createDbMessage(1,"errorid:"+errorid +"\n"+ex.getMessage(),use);
        }finally {
            doCountDown() ;
        }
    }

//    public void insertRedisJob(){
//        if (messageid != null  && messageid.length()== KEY_LEN) {
//
//            HashMap<String, Object> source = new HashMap<>();
//            source.put("topic",this.record.topic() );
//            source.put("binfo",binfo );
//            source.put("messageid",this.messageid );
//
//            KafkaRedisRecorder.recordJob(this.record.topic(),this.record.value());
//        }
//    }
//    public void delRedisJob(){
//        if (messageid != null  && messageid.length()== KEY_LEN) {
//            KafkaRedisRecorder.delJob(this.record.value());
//        }
//    }


    /**
     * 创建数据库消息
     */
    public void createDbMessage(int type, String exceptionMessage, long use) {
        if (messageid != null  && messageid.length()==KEY_LEN ) {
            FmbxKafkaMessage entity = new FmbxKafkaMessage();
            entity.setType(type);
            entity.setMessageId(messageid);
            entity.setTopic(this.record.topic());
            entity.setGroupval(this.consumerGroupId);
            entity.setMessageVal(this.record.value());
            entity.setStatus(0);
            entity.setVersionNum(0);
            entity.setRetryNum(0);
            entity.setUseMillions((int) use);
            entity.setCtime(LocalDateTime.now());
            entity.setExceptionInfo(exceptionMessage);
            kafkaMessageService.save(entity);
        }else {
            logger.warn("messageNotHaveMessageId topic={},message={}",this.record.topic(), this.record.value());
        }
    }

    public abstract void doBusinessJob(ConsumerRecord<String, String> record)  ;

    @Override
    public void run() {
        doJob() ;
    }
}
