package com.fmb.server2022.controller.front;


import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.config.filter.FrontApiAspect;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.ReqConfirmOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.util.MD5Util;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.server2022.service.order.OrderService.ADMIN001;
import static com.fmb.server2022.service.order.OrderService.FRONT;

/**
 * 订单相关
 */
@RestController
@RequestMapping("/front/order")
public class OrderController extends BaseFrontController {


    @Autowired
    OrderService orderService ;

    @RequestMapping(value = "/createOrder")
    @ResponseBody
    public FmbRespBean createOrder(@RequestBody @Valid ReqGenOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.createOrderMain(resultMap, input,nowUser());

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/createOrderTest01")
    @ResponseBody
    public FmbRespBean createOrderTest01(@RequestBody @Valid ReqGenOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        final ArrayList<String> codes = new ArrayList<>();
//
//        for (int i = 0; i < 100; i++) {
//            codes.add(orderService.createOrderSn());
//        }
//
//        resultMap.put("codes",codes);

        System.out.println(FmbServerConfig.getCategoryBuy());

        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/apiTestPing")
    @ResponseBody
    public FmbRespBean apiTest(@RequestBody @Valid com.fmb.server2022.controller.front.reqdomain.ReqapiTest input, HttpServletRequest request ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("ping",input.toString()) ;

        String jsonBody = IOUtils.toString(((ContentCachingRequestWrapper) request).getContentAsByteArray(), "UTF-8");

        resultMap.put("sourceJson",jsonBody) ;
        resultMap.put("beforeSign",jsonBody+ FrontApiAspect.saltMap.get(input.getAppid())) ;
        resultMap.put("sign",MD5Util.md5(jsonBody+ FrontApiAspect.saltMap.get(input.getAppid()))) ;




        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/confirmOrder")
    @ResponseBody
    public FmbRespBean confirmOrder(@RequestBody @Valid ReqConfirmOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        orderService.chooseBestCoupon(nowUser().getUserId(),input,resultMap,"confirmOrder");

        orderService.confirmOrder(resultMap,nowUser(),input) ;


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/hotelCodeQueryInConfirmPage")
    @ResponseBody
    public FmbRespBean hotelCodeQueryInConfirmPage(@RequestBody @Valid ReqConfirmOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        orderService.chooseBestCoupon(nowUser().getUserId(),input,resultMap,"confirmOrder");

        orderService.hotelCodeQueryInConfirmPage(resultMap,nowUser(),input) ;


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/orderDetail")
    @ResponseBody
    public FmbRespBean orderDetail(@RequestBody @Valid ReqorderDetail input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.orderDetail(resultMap, input,nowUser(),FRONT);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/orderCancel")
    @ResponseBody
    public FmbRespBean orderCancel(@RequestBody @Valid ReqorderDetail input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.cancelOrderByOrderSn(resultMap, input,nowUser(),FRONT);

        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/orderDetailOfAdmin")
    @ResponseBody
    public FmbRespBean orderDetailOfAdmin(@RequestBody @Valid ReqorderDetail input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.orderDetail(resultMap, input,nowUser(),ADMIN001);

        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/saveConfirmInfo")
//    @ResponseBody
//    public FmbRespBean saveConfirmInfo(@RequestBody @Valid ReqorderDetail input) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        orderService.saveConfirmInfo(input.getOrderSn());
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/getLastOrderReceiver")
    @ResponseBody
    public FmbRespBean getLastOrderReceiver(@RequestBody @Valid ReqCoupon input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        final NowUser nowUser = nowUser();
        orderService.queryPreOrderReceiver(nowUser.getUserId(),nowUser.getUserInfo().getPhoneNumber());
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/saveOrderNote")
    @ResponseBody
    public FmbRespBean saveOrderNote(@RequestBody @Valid ReqGenOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.saveOrderNote("20230213210557674585",input.getNote());

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/dfdtest")
    @ResponseBody
    public FmbRespBean dfdtest(@RequestBody @Valid ReqorderDetail input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.getOrderHotelEmailTemp(input.getOrderSn());

        return FmbRespBean.success(resultMap);
    }


}
