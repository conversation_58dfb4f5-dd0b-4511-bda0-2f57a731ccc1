package com.fmb.server2022.controller.publictool;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.reqdomain.OrderSmsInfo;
import com.fmb.server2022.service.order.OrderSuccessHandler;
import com.fmb.util.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;

@RestController
@RequestMapping("/publictool")
public class AdminApiController {
    @Autowired
    OrderSuccessHandler orderSuccessHandler;

    @RequestMapping(value = "/getSmsContent")
    @ResponseBody
    public FmbRespBean getSmsContent(@RequestBody @Valid OrderSmsInfo orderSmsInfo) {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        if (orderSmsInfo == null || StringUtils.isBlank(orderSmsInfo.getOrderSn())) {
            throw new BadLogicException("请求参数异常");
        }
        if (!MD5Util.md5(orderSmsInfo.getOrderSn() + "_" + orderSmsInfo.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY).equals(orderSmsInfo.getMd5())) {
            System.out.println(MD5Util.md5(orderSmsInfo.getOrderSn() + "_" + orderSmsInfo.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY));
            throw new BadLogicException("请求参数校验失败");
        }

        resultMap.put("smsContent", orderSuccessHandler.getSmsContentByOrderSn(orderSmsInfo.getOrderSn(),"fmbx_second_confirm"));

        return FmbRespBean.success(resultMap);
    }
}