package com.fmb.server2022.controller.publictool;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.reqdomain.ReqMap;
import com.fmb.server2022.service.FmbXDictUtils;
import com.fmb.server2022.service.schedule.FmbScheduleService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import com.fmb.util.AuthCodeUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.fmb.server2022.service.FmbXDictUtils.queryDictAndSetInMap;

@RestController
@RequestMapping("/publictool")
public class BaiduMapController implements ApplicationContextAware {


    ApplicationContext applicationContext ;

    public static final String ak = "F2i6AaneF9MV6YKd6TQK2a9wk43Euay6" ;


    @RequestMapping(value = "/mapSuggest")
    @ResponseBody
    public FmbRespBean mapSuggest(@RequestBody @Valid ReqMap input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        String url = "https://api.map.baidu.com/place/v2/suggestion?query="+input.getQuery()
                +"&region="+input.getRegion()+"&city_limit=true&output=json&ak="+ak ;

        JSONObject mapInfo = JSON.parseObject(HttpUtil.get(url));
        resultMap.put("mapInfo",mapInfo) ;

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/listDictsInfo")
    @ResponseBody
    public FmbRespBean listDictsInfo() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        final ArrayList<Map> infos = new ArrayList<>();
        resultMap.put("k1", FmbXDictUtils.queryDict("fmbx_suite__cert_flag")) ;
        resultMap.put("k3", FmbXDictUtils.queryDict("fmbx_bp_contract__bp_seal_use_info")) ;

        queryDictAndSetInMap("fmbx_suite__cert_flag",resultMap,"k2") ;

        return FmbRespBean.success(resultMap);
    }


    @Autowired
    FmbScheduleService fmbScheduleService ;

    @RequestMapping(value = "/addScheduleJob01")
    @ResponseBody
    public FmbRespBean addScheduleJob01(@RequestParam(value = "sec", defaultValue = "10") Integer sec) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        HashMap<String, Object> hmap = new HashMap<>();
        hmap.put("xx", ""+System.currentTimeMillis());

//        resultMap.put("opInfo",fmbScheduleService.addJobWithSecond("test", (int) (System.currentTimeMillis()/(sec*1000)),hmap,sec)) ;

        try {
            resultMap.put("info",FmbScheduleUtil.addScheduleJob("test01ConsumeJob",12,hmap,"2022-12-22 12:50:00")) ;
        } catch (ParseException e) {
            e.printStackTrace();
        }

//        FmbScheduleUtil.addJob();

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/delSchedule.do")
    @ResponseBody
    public HashMap<String, Object> delSchedule(
            @RequestParam(value = "type", required = true) String type,
            @RequestParam(value = "id", required = true) Integer id
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", fmbScheduleService.cancelJobBecauseRepeat(type,id));
        return resultMap;
    }


    @RequestMapping(value = "/beanList.do")
    @ResponseBody
    public HashMap<String, Object> beanList() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        return resultMap;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext ;
    }


    @RequestMapping(value = "/uidEncode")
    @ResponseBody
    public FmbRespBean uidGen(@RequestParam(value = "uid", required = true) Integer uid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final String s = AuthCodeUtil.fmbAuthCodeEncode("" + uid);
        resultMap.put("uid", uid);
        resultMap.put("uidEncode", s);

        return FmbRespBean.success(resultMap);
    }
    @RequestMapping(value = "/uidDecode")
    @ResponseBody
    public FmbRespBean uidDecode(@RequestParam(value = "uid", required = true) String uid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final String s = AuthCodeUtil.fmbAuthCodeDecode(uid);
        resultMap.put("uid", s);
        resultMap.put("uidEncode", uid);

        return FmbRespBean.success(resultMap);
    }

}
