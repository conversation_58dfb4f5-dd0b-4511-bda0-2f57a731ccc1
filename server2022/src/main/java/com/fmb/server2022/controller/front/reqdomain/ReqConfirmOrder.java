package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;

import java.util.List;

/**
 * 产生订单的载体
 */
@Data
public class ReqConfirmOrder implements FrontApiRequestDomain  {

    /**
     * 活动id
     */
    Integer xaid ;

    //如果是使用房券 来预定酒店
    String hotelReserveCode ;

    /**
     * sku信息
     */
    List<ReqGenSku> reqGenSkus ;

    String couponSn ;

    /**
     * 是否选择优惠券
     */
    int setDefault;

    /**
     * 选中优惠券
     */
    String couponStr;

    String appid ;
    String opVersion ;
    String version  ;


    /**
     * 用户选择不使用优惠券
     */
    int userChooseNotUseCoupon ;

    /**
     * 是否使用余额
     * 0 不使用   1-使用
     */

    int useBalance  ;


}
