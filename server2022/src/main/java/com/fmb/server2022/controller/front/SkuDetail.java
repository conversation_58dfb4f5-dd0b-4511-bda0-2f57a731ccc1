package com.fmb.server2022.controller.front;

import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.domain.HotelSkuDatePriceDomain;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import com.fmb.server2022.fmbx.entity.FmbxSuiteRoomSku;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SkuDetail implements FmbPriceInfoGetter{

    int skuType ;


    Integer recId ;
    /**
     * 这里是前端收集来的基础sku 信息
     */
    ReqGenSku reqGenSku ;

    /**
     * 各个类型sku 的具体信息   开始---------------------------------------------------------------
     */

    FmbxSku fmbxSku ;
    // 新酒店和和房券存在
    FmbxSuite suite ;
    //新酒店存在
    FmbxSuiteRoomSku roomSku ;


    List<HotelSkuDatePriceDomain> priceList ;


    //房券存在
    FmbxHotelReserveSku reserveSku ;
    /**
     * 各个类型sku 的具体信息   结束---------------------------------------------------------------
     */

    ///// 价格信息 这里是----开始---------------------------------------------------------------
    /**
     * 原价
     */
    private BigDecimal marketPrice;
    private BigDecimal marketPriceAll;
    /**
     * 现价
     */
    private BigDecimal goodsPrice;
    private BigDecimal goodsPriceAll;

    /**
     * 结算价
     */
    private BigDecimal settlePrice;
    private BigDecimal settlePriceAll;

    private BigDecimal totalHotelCodeSubMoney;

    /**
     *      房券:购买份数
     *      日历房: 房间数量
     */
    private Integer skuNumber ;
    /**
     *      房券:购买完后需要修改为的份数
     *      日历房: 日历房的 修改后数量不在这里 ,因为日历房是要按日期扣减房型库存,对应的是要放在 priceList的 aimStock 里
     */
    private Integer skuAimStockNumber ;
    ///// 价格信息 ----结束---------------------------------------------------------------


    public SkuDetail() {
        this.marketPrice = new BigDecimal("0") ;
        this.marketPriceAll = new BigDecimal("0") ;
        this.goodsPrice = new BigDecimal("0") ;
        this.goodsPriceAll = new BigDecimal("0") ;
        this.settlePrice = new BigDecimal("0") ;
        this.settlePriceAll = new BigDecimal("0") ;
        this.totalHotelCodeSubMoney = new BigDecimal("0") ;
    }


}
