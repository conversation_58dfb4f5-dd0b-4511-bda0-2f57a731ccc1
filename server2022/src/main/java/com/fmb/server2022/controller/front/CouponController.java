package com.fmb.server2022.controller.front;

import com.alibaba.fastjson.JSON;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.ReqConfirmOrder;
import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import com.fmb.server2022.service.CouponService;
import com.fmb.server2022.service.order.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 优惠券相关
 */
@RestController
@RequestMapping("/front/coupon")
public class CouponController extends BaseFrontController {

    private static final Logger logger = LoggerFactory.getLogger(CouponController.class);

    @Autowired
    OrderService orderService ;

    @Autowired
    CouponService couponService ;

//    @RequestMapping(value = "/getCouponList")
//    @ResponseBody
//    public FmbRespBean getCouponList(@RequestBody @Valid ReqGenOrder input) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        orderService.orderGuide(resultMap, input,nowUser());
//
//        return FmbRespBean.success(resultMap);
//    }


//    @RequestMapping(value = "/getCouponBySku")
//    @ResponseBody
//    public FmbRespBean getCouponBySku(@RequestBody @Valid ReqCouponBySku input) {
//        System.out.println(input.toString());
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
////        int uid = 36507;
//        couponService.getCouponBySku(resultMap, input, nowUser());
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/getCouponBySku")
    @ResponseBody
    public FmbRespBean getCouponBySku(@RequestBody @Valid ReqConfirmOrder input) {
//        System.out.println(input.toString());
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        couponService.getCouponBySku1(resultMap, input, nowUser());
////        orderService.chooseBestCoupon(nowUser().getUserId(),  input, resultMap,"userChooseCoupon");
//        return FmbRespBean.success(resultMap);
        logger.info("=== 调试开始 ===");
        logger.info("接收到getCouponBySku请求，参数: {}", JSON.toJSONString(input));
        logger.info("当前用户: {}", nowUser());

        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        // 🔍 调试点2: 调用业务方法前
        logger.info("准备调用couponService.getCouponBySku1方法");
        couponService.getCouponBySku1(resultMap, input, nowUser());

        // 🔍 调试点3: 查看返回结果
        logger.info("业务方法执行完成，结果: {}", JSON.toJSONString(resultMap));
        logger.info("=== 调试结束 ===");

        return FmbRespBean.success(resultMap);

    }

    @RequestMapping(value = "/getCouponByAid")
    @ResponseBody
    public FmbRespBean getCouponByAid(@RequestBody @Valid ReqCoupon input) {
//        System.out.println(input.toString());
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        couponService.getCouponByAid(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

}
