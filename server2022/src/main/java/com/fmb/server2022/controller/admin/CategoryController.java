package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.service.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;



/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/category")
public class CategoryController {

    private static Logger logger = LoggerFactory.getLogger(com.fmb.server2022.controller.admin.ActivityController.class);


    @Autowired
    CategoryService categoryService;

    /**
     * 获取分类树结构
     * @return
     */
    @RequestMapping(value = "/getCateTree")
    @ResponseBody
    public FmbRespBean getCateTree() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        categoryService.getCateTree(resultMap);
        return FmbRespBean.success(resultMap);
    }

}
