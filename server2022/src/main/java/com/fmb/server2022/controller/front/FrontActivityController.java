package com.fmb.server2022.controller.front;


import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.service.BpsMediaService;
import com.fmb.server2022.service.FrontHotelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 产品相关
 */
@RestController
@RequestMapping("/front/activity")
public class FrontActivityController extends BaseFrontController {
    @Autowired
    FrontHotelService frontHotelService;
    @Autowired
    BpsMediaService bpsMediaService;

    /**
     * 活动详情
     */
    @RequestMapping(value = "/activityDetail")
    @ResponseBody
    public FmbRespBean activityDetail(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectFrontActvHotel(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 收藏活动
     */
    @RequestMapping(value = "/updateFavorite")
    @ResponseBody
    public FmbRespBean updateFavorite(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.updateFavorite(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 商家相册
     */
    @RequestMapping(value = "/bpsMediaDetail")
    @ResponseBody
    public FmbRespBean bpsMediaDetail(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        if (input.getBpsId()==null) {
            throw new BadLogicException("商户不存在");
        }
        bpsMediaService.getFrontImgCateNameList(input.getBpsId(),resultMap);
        return FmbRespBean.success(resultMap);
    }

    /**
     * 客服配置
     */
    @RequestMapping(value = "/customerService")
    @ResponseBody
    public FmbRespBean customerService(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectCustomerService(resultMap);
        return FmbRespBean.success(resultMap);
    }

}
