package com.fmb.server2022.controller.front.reqdomain;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fmb.server2022.controller.front.SkuDetail;
import com.fmb.server2022.domain.FrontCouponDomain;
import com.fmb.server2022.fmbx.entity.FmbOrderGoods;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbReserveHotelCodes;
import com.fmb.server2022.fmbx.entity.FmbUserExtinfo;
import com.fmb.server2022.fmbx.entity.FmbxActivity;
import com.fmb.server2022.fmbx.entity.FmbxHotelReserveSku;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.entity.FmbxSuite;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class OrderSysInfo {

    private static Logger logger = LoggerFactory.getLogger(OrderSysInfo.class);

    FmbxActivity activity ;


    //酒店日历房订单
    boolean haveHotelDateSku ;
    //房券 订单
    boolean haveHotelReserveSku ;
    /**
     * sku 主表信息
     */
    List<FmbxSku> skuMains ;
    List<SkuDetail> skuDetails ;

    /**
     * 整体订单的商品价格(不包含各种优惠)
     */
    private BigDecimal goodsPriceAll;
    /**
     * 整体订单的商品 结算价
      */
    private BigDecimal settlePriceAll;


    /**
     * 整体订单的商品价格(用户最终需要支付的金额)
     */
    private BigDecimal goodsPriceAllFinal;


    // 优惠券开始------------------------------
    /**
     * 最终优惠券扣减的金额
     */
    boolean isRateCoupon ;
    /**
     * 成功使用优惠券
     */
    boolean useCoupon ;
    private BigDecimal couponMoney ;

    private FrontCouponDomain frontCouponDomain ;


    // 优惠券结束------------------------------


    private BigDecimal shippingFee ;


    // 余额开始------------------------------

    private FmbUserExtinfo userBalance ;
    private BigDecimal  actUserBalance ;
    private boolean useBalance ;

    // 余额结束------------------------------


    // 订单开始----------------------------------------
    FmbOrderInfo fmbOrderInfo ;
    /**
     * 下单就交易成功 用户需要支付的金额为0
     */
    boolean orderDirectSuccess ;
    //订单多久不支付就取消
    int cancelSecond ;
    // 订单结束----------------------------------------


    //房券预约开始---------------------------------------------------------------


    FmbOrderInfo reserveCodeFromOrderInfo  ;
    FmbOrderGoods reserveCodeFromOrderGoods ;
    //房券购买时的 套餐信息
    FmbxSuite reserveCodeSourceSuite ;
    FmbReserveHotelCodes hotelCode ;

    Integer reserveSuiteSplit ;

    FmbxHotelReserveSku reserveCodeSourceSku ;

    /**
     * 预约总间夜数
     */
    int reserveTotalNightRoom ;

    BigDecimal hotelCodeEachNightPay ;
    /**
     * 使用房券
     */
    boolean useHotelCode ;

    //房券预约结束---------------------------------------------------------------



    public void setCancelSecond(int cancelSecondInp) {

        if(cancelSecond==0  ||  cancelSecondInp <this.cancelSecond){
            this.cancelSecond = cancelSecondInp ;
        }
    }

    public OrderSysInfo() {
        this.skuMains = new ArrayList<>() ;
        this.skuDetails = new ArrayList<>() ;
        this.goodsPriceAll = new BigDecimal("0");
        this.settlePriceAll = new BigDecimal("0");
        this.couponMoney = new BigDecimal("0");
        this.shippingFee = new BigDecimal("0");
        this.actUserBalance = new BigDecimal("0");

    }

    /**
     * 将用户的 sku 信息变为 SkuDetail
     *  @param type
     * @param roomSku
     * @param reqGenSku
     * @param fmbxSku
     * @param suite
     */
//    public void addSkuDetail(int type, Object roomSku, ReqGenSku reqGenSku, FmbxSku fmbxSku, FmbxSuite suite){
//        SkuDetail e = new SkuDetail();
//        e.setReqGenSku(reqGenSku);
//        e.setFmbxSku(fmbxSku);
//        e.setSuite(suite);
//        if (SKU_HOTEL_DATE==type) {
//            e.setRoomSku((FmbxSuiteRoomSku) roomSku);
//            e.setSkuType(SKU_HOTEL_DATE);
//            skuDetails.add(e);
//        }else
//        if (SKU_HOTEL_RESERVE==type) {
//            e.setReserveSku((FmbxHotelReserveSku) roomSku);
//            e.setSkuType(SKU_HOTEL_RESERVE);
//            skuDetails.add(e);
//        }
//    }

    /**
     * 创建订单前 做最后的价格计算
     *
     * 商品总额-使用优惠券金额-使用余额+运费
     *
     */
    public void buildMoneyInfoBeforeCreateOrder() {

        if (logger.isInfoEnabled()) {
            logger.info("getGoodsPriceAll()={},getCouponMoney()={} ,userBalance.getMoney()={} ",getGoodsPriceAll(),getCouponMoney(),
                    userBalance!=null?  userBalance.getMoney():"");
        }

        //商品金额刨除优惠券后的金额
        BigDecimal subtract = getGoodsPriceAll().subtract(getCouponMoney());

        logger.info(JSON.toJSONString(this));


        //扣除优惠券后需要支付的金额 大于1元
        if (userBalance != null  && subtract.compareTo(new BigDecimal("1"))==1) {
            //计算最终使用的 余额金额
            if(subtract.subtract(userBalance.getMoney()).compareTo(new BigDecimal("1"))==-1){
                //用户余额比较多 都使用了,支付金额会小于1元
                //计算出最多使用的余额
                actUserBalance = subtract.subtract(new BigDecimal("1")) ;
            }else {
                actUserBalance = userBalance.getMoney() ;
            }
            logger.info("actUserBalance={},{}",actUserBalance,getUserBalance());

            if (actUserBalance.compareTo(new BigDecimal(0))==1) {
                setUseBalance(true);
            }
        }

        //
        setGoodsPriceAllFinal(subtract.subtract(actUserBalance).add(shippingFee));

    }
}
