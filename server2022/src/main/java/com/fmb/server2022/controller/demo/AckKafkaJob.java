package com.fmb.server2022.controller.demo;

import java.util.HashMap;
import java.util.concurrent.Callable;

public abstract class AckKafkaJob implements Callable<HashMap>{

    private String message;

    @Override
    public HashMap call() throws Exception {
        HashMap<String, Object> result = new HashMap<>();
        result.put("result", 0);

        if (doJob(result)) {
            result.put("result", 1);
        }

        return result;
    }

    abstract boolean doJob(HashMap<String, Object> result);

    public AckKafkaJob(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
