package com.fmb.server2022.controller.demo;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class FmbKafkaWorkerAutoAck implements  Runnable{

    private static Logger logger = LoggerFactory.getLogger(FmbKafkaWorkerAutoAck.class);

    /**
     * kafka 消息
     */
    ConsumerRecord<String, String> record ;
    private String consumerGroupId ;

    public ConsumerRecord<String, String> getRecord() {
        return record;
    }

    public String getConsumerGroupId() {
        return consumerGroupId;
    }

    public FmbKafkaWorkerAutoAck(ConsumerRecord<String, String> record, String consumerGroupId) {
        this.record = record;
        this.consumerGroupId = consumerGroupId ;
    }

    public abstract void doBusinessJob(ConsumerRecord<String, String> record)  ;

    @Override
    public void run() {
        doBusinessJob(this.record) ;
    }
}
