package com.fmb.server2022.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.domain.stock.ReqroomStockSet;
import com.fmb.server2022.domain.suite.SuitePriceOperation;
import com.fmb.server2022.fmbx.entity.FmbxSuitePriceOperate;
import com.fmb.server2022.reqdomain.ReqActivityPublishLog;
import com.fmb.server2022.reqdomain.ReqHotelDate;
import com.fmb.server2022.reqdomain.ReqHotelDateSku;
import com.fmb.server2022.reqdomain.ReqHotelPriceChangeData;
import com.fmb.server2022.reqdomain.ReqHotelReserveSku;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.reqdomain.ReqHotelStockChangeData;
import com.fmb.server2022.reqdomain.ReqUpdateHotelDateSkuSortValueList;
import com.fmb.server2022.service.SuiteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.server2022.service.SuiteService.sendHotelSkuChangeNotify;

@RestController
@RequestMapping("/admin/suite")
public class SuiteController {

    @Autowired
    SuiteService suiteService ;


    /**
     * 加价模式 进行加价操作
     * @param operation
     * @return
     */
    @RequestMapping(value = "/priceOperateByAddMode")
    @ResponseBody
    public FmbRespBean priceOperateByAddMode( @RequestBody SuitePriceOperation operation) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.priceOperate(operation,resultMap,SuitePriceOperation.TYPE_ADD_MODE) ;
        return FmbRespBean.success(resultMap);
    }


    /**
     * 当前sku 的加价信息 ,根据sku查询 价格操作列表里最后一次加价操作信息
     * @param skuid
     * @return
     */
    @RequestMapping(value = "/queryLastPriceAddInfo")
    @ResponseBody
    public FmbRespBean queryLastPriceAddInfo(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final FmbxSuitePriceOperate fmbxSuitePriceOperate = suiteService.queryLastPriceAddInfo(skuid,SuitePriceOperation.TYPE_ADD_MODE);
        JSONObject jsonObject = new JSONObject() ;
        if (fmbxSuitePriceOperate != null) {
            jsonObject = JSON.parseObject(fmbxSuitePriceOperate.getOperateData());
        }
        resultMap.put("operateData",jsonObject) ;

        return FmbRespBean.success(resultMap);
    }


    /**
     * 查询加价操作 信息 方便复制
     * @param suiteid 套餐id
     * @return
     */
    @RequestMapping(value = "/queryPriceAddOperation")
    @ResponseBody
    public FmbRespBean queryPriceAddOperation(@RequestParam(value = "suiteid", required = true) Integer suiteid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.queryPriceAddOperation(suiteid,resultMap) ;

        return FmbRespBean.success(resultMap);
    }

    /**
     * 加载加价操作 信息
     * @param operateId
     * @return
     */
    @RequestMapping(value = "/queryOperateInfo")
    @ResponseBody
    public FmbRespBean queryOperateInfo(@RequestParam(value = "operateId", required = true) Integer operateId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.queryOperateInfo(operateId,resultMap) ;

        return FmbRespBean.success(resultMap);
    }


    /**
     * 根据 sku查询 加载加价操作 信息
     *
     * @param skuid 套餐id
     * @return
     */
    @RequestMapping(value = "/queryOperateInfoOfAddMode")
    @ResponseBody
    public FmbRespBean queryOperateInfoOfAddMode(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.queryOperateInfoOfAddMode(skuid,resultMap) ;

        return FmbRespBean.success(resultMap);
    }


    //整价录入模式开始

    /**
     * 整价模式(连续时间)操作
     * @param operation
     * @return
     */
    @RequestMapping(value = "/priceOperateByFullMode")
    @ResponseBody
    public FmbRespBean priceOperateByFullMode( @RequestBody SuitePriceOperation operation) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.priceOperate(operation,resultMap,SuitePriceOperation.TYPE_FULL_RANGE_MODE) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 整价模式(非连续时间)操作
     * @param operation
     * @return
     */
    @RequestMapping(value = "/priceOperateByFixedDate")
    @ResponseBody
    public FmbRespBean priceOperateByFixedDate( @RequestBody SuitePriceOperation operation) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.priceOperate(operation,resultMap,SuitePriceOperation.TYPE_FULL_FIXED_DATE_MODE) ;
        return FmbRespBean.success(resultMap);
    }


    /**
     * 整价模式 下载excel
     * @param skuid
     * @return
     */
    @RequestMapping(value = "/genDatePriceExcel")
    @ResponseBody
    public FmbRespBean genDatePriceExcel(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.genDatePriceExcel(skuid,resultMap,true) ;
        return FmbRespBean.success( resultMap);
    }
    /**
     * 整价模式 下载excel模板
     * @param skuid
     * @return
     */
    @RequestMapping(value = "/genDatePriceBlankExcel")
    @ResponseBody
    public FmbRespBean genDatePriceBlankExcel(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.genDatePriceExcel(skuid,resultMap,false) ;
        return FmbRespBean.success(resultMap);
    }


    /**
     * 导入excel
     * @return
     */
    @RequestMapping(value = "/importPriceExcel")
    @ResponseBody
    public FmbRespBean importPriceExcel(@RequestParam(value = "file", required = true) MultipartFile uploadFile) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.importPriceExcel(uploadFile,resultMap) ;

        return FmbRespBean.success(resultMap);
    }



    /**
     * 酒店sku信息查询,根据套餐分组
     */
    @RequestMapping(value = "/hotelSku")
    @ResponseBody
    public FmbRespBean hotelSku(@RequestBody @Valid ReqHotelSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.selectHotelSku(resultMap, req);
        return FmbRespBean.success(resultMap);
    }


    /**
     * 酒店sku信息查询,不分组
     */
    @RequestMapping(value = "/hotelSkuList")
    @ResponseBody
    public FmbRespBean hotelSkuList(@RequestBody @Valid ReqHotelSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.selectHotelSkuList(resultMap, req);
        return FmbRespBean.success(resultMap);
    }

    /**
     * 酒店sku日期价格范围查询(根据skuid) 
     */
    @RequestMapping(value = "/hotelSkuDatePrice")
    @ResponseBody
    public FmbRespBean hotelSkuDatePrice(@RequestBody @Valid ReqHotelDate req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.selectHotelSkuDatePrice(resultMap, req);

        return FmbRespBean.success(resultMap);
    }
    /**
     * 酒店sku日期价格范围查询(根据xaid)
     */
    @RequestMapping(value = "/hotelSkuDatePriceAll")
    @ResponseBody
    public FmbRespBean hotelSkuDatePriceAll(@RequestBody @Valid ReqHotelDate req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.selecthotelSkuDatePriceAll(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/priceAddModeSwitch")
    @ResponseBody
    public FmbRespBean priceAddModeSwitch(
            @RequestParam(value = "suiteid", required = true) Integer suiteid,
            @RequestParam(value = "aimPriceInputType", required = true) Integer aimPriceInputType

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.priceAddModeSwitch(suiteid, aimPriceInputType,resultMap);

        return FmbRespBean.success(resultMap);
    }


        @RequestMapping(value = "/roomStockSet")
        @ResponseBody
        public FmbRespBean roomStockSet(@RequestBody @Valid ReqroomStockSet input) {
            HashMap<String, Object> resultMap = new HashMap<String, Object>();

            suiteService.roomStockSet(resultMap, input);

            return FmbRespBean.success(resultMap);
        }


    /**
     * 根据活动id 查询所有的套餐下的房型列表
     * @param xaid
     * @return
     */
    @RequestMapping(value = "/stockRoomList")
    @ResponseBody
    public FmbRespBean stockRoomList(@RequestParam(value = "xaid", required = true) Integer xaid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.stockRoomList( xaid, resultMap);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 酒店房态日历查询
     */
    @RequestMapping(value = "/hotelStockDateList")
    @ResponseBody
    public FmbRespBean hotelStockDateList(@RequestBody @Valid ReqHotelDate req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        suiteService.hotelStockDateList(resultMap, req);

        return FmbRespBean.success(resultMap);
    }
    

    /**
     * 酒店价格变动列表
     */
    @RequestMapping(value = "/hotelPriceChangeList")
    @ResponseBody
    public FmbRespBean hotelPriceChangeData(@RequestBody @Valid ReqHotelPriceChangeData req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.selectHotelPriceChangeData(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 价格变动详情
     * @param priceOperateId
     * @return
     */
    @RequestMapping(value = "/hotelPriceChangeDetail")
    @ResponseBody
    public FmbRespBean hotelPriceChangeDetail(@RequestParam(value = "priceOperateId", required = true) Integer priceOperateId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.hotelPriceChangeDetail(resultMap, priceOperateId);

        return FmbRespBean.success(resultMap);
    }

    /**
     * 酒店库存变化
     */
    @RequestMapping(value = "/hotelStockChangeData")
    @ResponseBody
    public FmbRespBean hotelStockChangeData(@RequestBody @Valid ReqHotelStockChangeData req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.selectHotelStockChangeData(resultMap, req);

        return FmbRespBean.success(resultMap);
    }
    /**
     * 酒店库存变化
     */
    @RequestMapping(value = "/hotelStockChangeDetail")
    @ResponseBody
    public FmbRespBean hotelStockChangeDetail(@RequestParam(value = "stockOperateId", required = true) Integer stockOperateId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.hotelStockChangeDetail(resultMap, stockOperateId);

        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/skuStatusCheck")
//    @ResponseBody
//    public FmbRespBean skuStatusCheck(@RequestParam(value = "skuid", required = true) Integer skuid) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        suiteService.skuStatusCheck(resultMap, skuid);
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/skuchangeTest")
    @ResponseBody
    public HashMap<String, Object> skuchangeTest(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        sendHotelSkuChangeNotify(skuid) ;

        return resultMap;
    }
    @RequestMapping(value = "/roomChangeTest")
    @ResponseBody
    public HashMap<String, Object> roomChangeTest(@RequestParam(value = "roomid", required = true) Integer roomid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        suiteService.sendHotelStockChangeNotify(roomid);
        return resultMap;
    }



    /**
     * 日历房酒店sku列表
     */
    @RequestMapping(value = "/hotelDateSku")
    @ResponseBody
    public FmbRespBean hotelDateSku(@RequestBody @Valid ReqHotelDateSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.selectHotelDateSku(resultMap, req);

        return FmbRespBean.success(resultMap);
    }



    /**
     * 房券sku列表
     */
    @RequestMapping(value = "/hotelReserveSku")
    @ResponseBody
    public FmbRespBean hotelReserveSku(@RequestBody @Valid ReqHotelReserveSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.selectHotelReserveSku(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 日历房排序修改
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateHotelDateSkuSortValue")
    @ResponseBody
    public FmbRespBean updateHotelDateSkuSortValue(@RequestBody @Valid ReqUpdateHotelDateSkuSortValueList input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.updateHotelDateSkuSortValue(resultMap, input);

        return FmbRespBean.success(resultMap);
    }

    /**
     * 房券排序修改
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateHotelReserveSkuSortValue")
    @ResponseBody
    public FmbRespBean updateHotelReserveSkuSortValue(@RequestBody @Valid ReqUpdateReserveHotelSkuSortValueList input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.updateHotelReserveSkuSortValue(resultMap, input);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 活动发布日志
     */
    @RequestMapping(value = "/activityPublishLog")
    @ResponseBody
    public FmbRespBean activityPublishLog(@RequestBody @Valid ReqActivityPublishLog req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.selectActivityPublishLog(resultMap, req);

        return FmbRespBean.success(resultMap);
    }

}
