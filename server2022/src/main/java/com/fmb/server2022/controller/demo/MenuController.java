package com.fmb.server2022.controller.demo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmbx.entity.SysMenu;
import com.fmb.server2022.fmbx.entity.SysPermission;
import com.fmb.server2022.fmbx.service.ISysMenuService;
import com.fmb.server2022.fmbx.service.ISysPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/admin/menu")
public class MenuController {


    private static Logger logger = LoggerFactory.getLogger(MenuController.class);

    @Autowired
    ISysMenuService menuService;

    @Autowired
    ISysPermissionService permissionService ;


    /**
     * 增加菜单 目录
     * @param menu
     * @return
     */
    @RequestMapping(value = "/addMenuFolder")
    @ResponseBody
    public FmbRespBean addMenuFolder(@RequestBody SysMenu menu) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        menu.setIsFolder(1);
        menu.setRelatedPermissionid(-1);

        checkParentMenu(menu);


        boolean b = menuService.save(menu);

            resultMap.put("addResult",b);
            resultMap.put("menuid",menu.getMenuId());

        return FmbRespBean.success(resultMap);
    }

    private void checkParentMenu(@RequestBody SysMenu menu) {
        if (menu.getParentMenuid() == null) {
            throw new BadLogicException("父菜单id为空");
        }

        if (menuService.getById(menu.getParentMenuid()) == null) {
            throw new BadLogicException("父菜单不存在:"+menu.getParentMenuid());
        }
    }


    /**
     * 增加菜单 节点
     * @param menu
     * @return
     */
    @RequestMapping(value = "/addMenuNode")
    @ResponseBody
    public FmbRespBean addMenuNode(@RequestBody SysMenu menu) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        menu.setIsFolder(0);

        checkParentMenu(menu);
        checkPermission(menu);

        boolean b = menuService.save(menu);

        resultMap.put("addResult",b);
        resultMap.put("menuid",menu.getMenuId());

        return FmbRespBean.success(resultMap);
    }

    private void checkPermission(@RequestBody SysMenu menu) {
        if (menu.getRelatedPermissionid() == null) {
            throw new BadLogicException("权限id为空") ;
        }
        SysPermission dbsysPermission = permissionService.getById(menu.getRelatedPermissionid());
        if (dbsysPermission == null) {
            throw new BadLogicException(menu.getRelatedPermissionid()+"的权限数据不存在") ;
        }
    }

    /**
     *
     * @param par
     * @return  'key'   menu name 关键字搜索
     */
    @RequestMapping(value = "/getMenuFolderList")
    public FmbRespBean getMenuFolderList(@RequestBody JSONObject par) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        LambdaQueryWrapper<SysMenu> wrapper = new  LambdaQueryWrapper<>(SysMenu.class)
                .select(SysMenu::getMenuId,SysMenu::getMenuName)
                .eq(SysMenu::getIsFolder,1)
                .like(StringUtils.isNotBlank(par.getString("keyword")),SysMenu::getMenuName,par.getString("keyword"))
                .orderByAsc(SysMenu::getMenuId)
                ;
        List<SysMenu> list = menuService.  list(wrapper);

        resultMap.put("menu", list);
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/getMenuNodePermission")
    @ResponseBody
    public FmbRespBean getMenuNodePermission(@RequestBody JSONObject par) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>(SysPermission.class)
                .select(SysPermission::getId,SysPermission::getPermissionName)
                .eq(SysPermission::getRequiredPermission,1)
                .like(StringUtils.isNotBlank(par.getString("keyword")),SysPermission::getMenuName,par.getString("keyword"))
                ;

        List<SysPermission> permissions = permissionService.list(wrapper);

        resultMap.put("permissions",permissions) ;
        return FmbRespBean.success(resultMap);
    }

}
