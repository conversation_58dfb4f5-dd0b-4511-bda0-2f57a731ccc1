package com.fmb.server2022.controller.admin;

import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.domain.AdminOrder;
import com.fmb.server2022.domain.FmbOrderDetailQueryResult;
import com.fmb.server2022.domain.PhpAdminInfo;
import com.fmb.server2022.service.PhpAdmin001UserInfoService;
import com.fmb.server2022.service.order.OrderReturnService;
import com.fmb.server2022.service.order.OrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

@RestController
@RequestMapping("/admin/order/return")
public class AdminOrderReturnController {


    @Autowired
    OrderService orderService ;

    @Autowired
    OrderReturnService returnService ;


    @RequestMapping(value = "/beforeCreateReturn")
    @ResponseBody
    public FmbRespBean beforeCreateReturn(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final FmbOrderDetailQueryResult fmbOrderDetailQueryResult = returnService.orderReturnBeginQuery(input.getOrderSn(), null);

        resultMap.put("orderInfo",fmbOrderDetailQueryResult) ;

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/creteReturn")
    @ResponseBody
    public FmbRespBean creteReturn(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        returnService.createReturn(input,resultMap);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/hotelOrderOfFromReserveReturn")
    @ResponseBody
    public FmbRespBean hotelOrderOfFromReserveReturn(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        PhpAdminInfo phpAdminInfo = null ;
        if (StringUtils.isNotBlank(input.getAsid())) {

            phpAdminInfo = PhpAdmin001UserInfoService.readUserInfo(input.getAsid());

            if (phpAdminInfo == null) {
                throw new BadLogicException("不能识别admin001用户信息");
            }

            input.setPhpAdminInfo(phpAdminInfo);
        }

        returnService.hotelOrderOfFromReserveReturn(input,resultMap);

        return FmbRespBean.success(resultMap);
    }

}
