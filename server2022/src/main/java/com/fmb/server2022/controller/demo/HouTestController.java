package com.fmb.server2022.controller.demo;

import cn.hutool.core.date.ChineseDate;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.domain.BitFlag;
import com.fmb.server2022.fmbx.entity.FmbxBpsMedia;
import com.fmb.server2022.fmbx.entity.FmbxDateTable;
import com.fmb.server2022.fmbx.service.IFmbxBpsMediaService;
import com.fmb.server2022.fmbx.service.IFmbxDateTableService;
import com.fmb.server2022.fmbx.service.IFmbxHotelReserveSkuService;
import com.fmb.server2022.mapper.BpsDao;
import com.fmb.server2022.mapper.HouDao;
import com.fmb.server2022.service.MailService;
import com.fmb.server2022.service.order.OrderStatService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@RestController
@Profile({"local","dev"})
@RequestMapping("/hou")
public class HouTestController {

    private static Logger logger = LoggerFactory.getLogger(HouTestController.class);

    @Autowired
    IFmbxBpsMediaService bpsMediaService ;

    @Autowired
    IFmbxDateTableService dateTableService ;


    @Autowired
    HouDao houDao ;


    @Autowired
    BpsDao bpsDao ;

    @Autowired
    JedisPool jedisPool ;


    @Autowired
    ThreadPoolExecutor poolExecutor ;

    /**
     * 邮件发送测试
     * @return
     */
    @RequestMapping(value = "/t01")
    @ResponseBody
    public FmbRespBean t01() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        ArrayList<String> to =new ArrayList<>();
        to.add("<EMAIL>");
        MailService.sendMimeMail(to,"邮件发送测试","默认邮件内容是HTML的 ,  <a href='http://www.baidu.com'>百度</a> ");




        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/t02")
    @ResponseBody
    public FmbRespBean t02() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        QueryWrapper<FmbxBpsMedia> wr = Wrappers.  .query(FmbxBpsMedia.class).select(" type,count(id) as rn ").eq("bps_id",13)
//                .groupBy("type");
//        List<FmbxBpsMedia> data = bpsMediaService.list(wr);

        List<Map> maps = bpsDao.selectCountType(225);
        resultMap.put("countinfo",maps) ;


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/t03")
    @ResponseBody
    public FmbRespBean t03() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        Wrapper<FmbxBpsMedia> wr = new QueryWrapper<FmbxBpsMedia>().select(" type, count(id) as rn ").eq("bps_id",225).groupBy("type");
        List<Map<String,Object>> countMap = bpsMediaService.listMaps(wr);
        resultMap.put("i",countMap) ;

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/genDateInfo")
    @ResponseBody
    public FmbRespBean genDateInfo() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        boolean b = dateTableService.saveBatch(genDateInfos());
        resultMap.put("result",b) ;

        return FmbRespBean.success(resultMap);
    }



    public ArrayList<FmbxDateTable> genDateInfos() {


        // 春节、劳动节、中秋节、端午节、元旦、清明、国庆
        DateTime now = DateTime.now();
        String dFormat = "yyyy-MM-dd";

        ArrayList<FmbxDateTable> fmbxDateTables = new ArrayList<>();

        for (int i = 0; i < 10*365 ; i++) {


            DateTime dateTimeAdd = now.plusDays(i);
            ChineseDate chineseDate = new ChineseDate(dateTimeAdd.toDate());
            String dateStrTemp = dateTimeAdd.toString(dFormat);

            int dayOfWeek = dateTimeAdd.getDayOfWeek()  ;

            FmbxDateTable insert = new FmbxDateTable();

//            dateTimeAdd.toLocalDate());
            insert.setWeeknum(dayOfWeek);
            insert.setDatecol(  LocalDate.parse (dateStrTemp));

            insert.setHolidayType(0);
            if (dateStrTemp.endsWith("01-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("元旦");
            }
            else if (dateStrTemp.endsWith("05-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("劳动节");
            }
            else if (dateStrTemp.endsWith("10-01")) {
                insert.setHolidayType(1);
                insert.setHoliday("国庆");
            }


//            if (StringUtils.isNotBlank(chineseDate.getFestivals()) || StringUtils.isNotBlank(chineseDate.getTerm())) {
//
//                System.out.println(dateStrTemp+"--星期(1-7):"+  dayOfWeek  +"--"+chineseDate.toString() +" 闰月:"+chineseDate.isLeapMonth());
//                System.out.println( chineseDate.getChineseMonth()+ chineseDate.getChineseDay() +"--"+chineseDate.getFestivals()
//                        +chineseDate.getTerm());
//            }

            if (chineseDate.getFestivals().indexOf("除夕")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("除夕");
            }
            if (chineseDate.getFestivals().indexOf("春节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("春节");
            }
            if (chineseDate.getFestivals().indexOf("元宵节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("元宵节");
            }
            if (chineseDate.getFestivals().indexOf("元宵节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("元宵节");
            }
            if (chineseDate.getFestivals().indexOf("端午节")!=-1) {
                insert.setHolidayType(2);
                insert.setHoliday("端午节");
            }
            if (chineseDate.getTerm().equals("清明")) {
                insert.setHolidayType(2);
                insert.setHoliday("清明");
            }

            insert.setYearMonthStr(dateStrTemp.substring(0,7));

            Wrapper<FmbxDateTable> wr = new LambdaQueryWrapper<FmbxDateTable>()
                    .eq(FmbxDateTable::getDatecol,insert.getDatecol())
                    ;
            dateTableService.count(wr) ;

            if (dateTableService.count(wr)==0) {
                fmbxDateTables.add(insert);
            }

        }

        return fmbxDateTables;


    }


    /**
     * 测试 一个int 代表多个字段的flag
     */
    private void work() {

        int input = 0;

        String platforms[] = {"h5","pc","wx小程序","app_ios","app_android"} ;

        input = pow2Int(platforms.length)-1 ;

        System.out.println("input="+input);
        ArrayList<BitFlag> flags = new ArrayList<>();
        String binStr = Integer.toBinaryString(input);
        System.out.println(binStr);
        char[] chars = new StringBuffer(binStr).reverse().toString().toCharArray();

        for (int i = 0; i < platforms.length; i++) {
            BitFlag bitFlag = new BitFlag();
            bitFlag.setName(platforms[i]);
            bitFlag.setWeightValue(pow2Int(i));

            if(i<chars.length){
                bitFlag.setFlag('1'==chars[i]);
            }


            flags.add(bitFlag) ;
        }

        System.out.println(JSON.toJSONString(flags,true));

        System.out.println(pow2Int(platforms.length));

    }

    int pow2Int(int n){
        return (int) Math.pow(2D, n);
    }


    @RequestMapping(value = "/lotPush")
    @ResponseBody
    public FmbRespBean lotPush() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        List<String> ids = houDao.selectAllIds() ;

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            jedis.lpush("testids",  ids.toArray(new String[]{})) ;

            resultMap.put("idsLen",ids.size()) ;


        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/consumelotPush")
    @ResponseBody
    public FmbRespBean consumelotPush() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        poolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                doReadJob() ;
            }
        }) ;


        return FmbRespBean.success(resultMap);
    }

    private void doReadJob() {

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String info =  "" ;

            while (info!=null){
                info = jedis.rpop("testids");
                if (info != null) {
                    System.out.println(info);
                }
            }

            System.out.println("job done ");


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

    }


    @Autowired
    IFmbxHotelReserveSkuService reserveSkuService ;

    @Autowired
    OrderStatService orderStatService ;


    @RequestMapping(value = "/createtime1.do")
    @ResponseBody
    public HashMap<String, Object> createtime1() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("data", orderStatService.queryHotelReserveMinPrice(105761));
        return resultMap;
    }

}
