package com.fmb.server2022.controller.admin;

import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.reqdomain.DingdingReq;
import com.fmb.server2022.reqdomain.ReqCheckBpReview;
import com.fmb.server2022.reqdomain.ReqFmbxBpList;
import com.fmb.server2022.reqdomain.ReqFmbxBpReviewList;
import com.fmb.server2022.reqdomain.ReqNewFmbxBp;
import com.fmb.server2022.service.BpsService;
import com.fmb.server2022.service.FmbxBpService;
import com.fmb.util.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.util.FmbCommonUtil.reqCheck;

/**
 * <p>
 * 商家表 前端控制器
 * </p>
 *
 * <AUTHOR>
 */



@RestController
@RequestMapping("/admin/bp")
public class FmbxBPController {




    @Autowired
    FmbxBpService fmbBpService ;

    @Autowired
    BpsService bpsService;


    @RequestMapping(value = "/listFmbShopUsers")
    @ResponseBody
    public FmbRespBean listFmbShopUsers(@RequestBody @Valid com.fmb.server2022.reqdomain.ReqCommon input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.listFmbShopUsers(resultMap, input);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/addFmbxBp")
    @ResponseBody
    public FmbRespBean fmbxBpMainAdd(
            @Valid @RequestBody ReqNewFmbxBp input

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("resultMsg","");

        //获取 当前登录用户的信息
        fmbBpService.saveBpFirst(input, resultMap);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/updateFmbxBp")
    @ResponseBody
    public FmbRespBean fmbxBpMainUpdate(@RequestBody ReqNewFmbxBp input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.fmbxBpUpdate(input,resultMap) ;

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/sendBpParterPwd")
    @ResponseBody
    public FmbRespBean sendBpParterPwd(@RequestBody ReqNewFmbxBp input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.sendBpParterPwd(input,resultMap); ;

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/toggleBp")
    @ResponseBody
    public FmbRespBean toggleBp(
            @RequestParam(value = "bpid", required = true) Integer bpid

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.toggleBp(resultMap,bpid) ;

        return FmbRespBean.success(resultMap);
    }




    @PostMapping(value = "/listFmbxBp")
    public FmbRespBean fmbxBpMainList(@RequestBody JSONObject par) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.bpList(par, resultMap);


        return FmbRespBean.success(resultMap);
    }

    @GetMapping(value = "/detailFmbxBp")
    public FmbRespBean fmbxBpMainDetail(
            @RequestParam(value = "bpId", required = true) Integer bpId
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.bpDetailNoPass(bpId, resultMap);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 供应商列表
     */
    @RequestMapping(value = "/fmbxBpList")
    @ResponseBody
    public FmbRespBean fmbxBpList(@RequestBody @Valid ReqFmbxBpList req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        fmbBpService.selectFmbxBpList(resultMap, req);


        return FmbRespBean.success(resultMap);
    }


    /**
     * 供应商列表
     */
    @RequestMapping(value = "/fmbxBpQuery")
    @ResponseBody
    public FmbRespBean fmbxBpQuery(@RequestBody  ReqFmbxBpList req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        fmbBpService.selectFmbxBpQuery(resultMap, req);


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/checkFmbxBpReview")
    @ResponseBody
    public FmbRespBean fmbxBpReviewCheck(@RequestBody ReqCheckBpReview input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.checkFmbxBpReview(input,resultMap) ;

        return FmbRespBean.success(resultMap);
    }



/**
    * 供应商审核列表
    */


    @RequestMapping(value = "/fmbxBpReviewList")
    @ResponseBody
    public FmbRespBean fmbxBpReviewList(@RequestBody @Valid ReqFmbxBpReviewList req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        fmbBpService.selectFmbxBpReviewList(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    /**
     * 获取分类树结构
     * @return
     */
    @RequestMapping(value = "/getBpListByCpsId")
    @ResponseBody
    public FmbRespBean getBpListByCpsId(@RequestParam(value = "cpsId", required = true) Integer cpsId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpsService.getBpListByCpsId(cpsId,resultMap);
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/shopUser2BpMain.do")
    @ResponseBody
    public HashMap<String, Object> shopUser2BpMain(@RequestBody DingdingReq input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        if (reqCheck(input)){
            fmbBpService.shopUser2BpMain() ;
        }

        return resultMap;
    }


    @RequestMapping(value = "/activityQuery")
    @ResponseBody
    public FmbRespBean activityQuery(@RequestBody ReqFmbxBpList input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.activityQuery(resultMap, input.getAid(),input.getLoadImg(),input.getBpsId());

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/roomInfoImport")
    @ResponseBody
    public FmbRespBean roomInfoImport(@RequestBody ReqFmbxBpList input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.roomInfoImport(resultMap, input.getAid(),input.getBpsId());

        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/actRichHtmlImageImport")
    @ResponseBody
    public FmbRespBean actRichHtmlImageImport(@RequestBody ReqFmbxBpList input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.actRichHtmlImageImport(resultMap, input.getBpsId(),input.getImageUrls());

        return FmbRespBean.success(resultMap);
    }




}
