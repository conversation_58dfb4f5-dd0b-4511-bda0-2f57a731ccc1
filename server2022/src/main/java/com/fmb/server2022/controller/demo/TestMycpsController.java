package com.fmb.server2022.controller.demo;

import com.fmb.server2022.service.MyCpsService;
import com.fmb.server2022.service.schedule.FmbScheduleUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date: 2023/6/27 9:57 下午
 */
@Profile({"local","dev"})
@RestController
@RequestMapping("/test")
public class TestMycpsController {


    @Autowired
    MyCpsService myCpsService ;

    /**
     * 准备分销商数据
     * @return
     */
    @RequestMapping(value = "/mycpsStat.do")
    @ResponseBody
    public HashMap<String, Object> mycpsStat() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();



        FmbScheduleUtil.doAsyncJob(() -> {
            myCpsService.doJob();
        });

        return resultMap;
    }

    /**
     * 逐一计算分销商结果
     * @return
     */
    @RequestMapping(value = "/mycpsStat4.do")
    @ResponseBody
    public HashMap<String, Object> mycpsStat4() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        FmbScheduleUtil.doAsyncJob(() -> {
            myCpsService.core1();
        });
        return resultMap;
    }


    @RequestMapping(value = "/mycpsStat2.do")
    @ResponseBody
    public HashMap<String, Object> mycpsStat2() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        myCpsService.readRedis2Excel() ;
        resultMap.put("data", ""+System.currentTimeMillis());
        return resultMap;
    }


    @RequestMapping(value = "/mycpsStat3.do")
    @ResponseBody
    public HashMap<String, Object> mycpsStat3() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        myCpsService.read3();

        return resultMap;
    }

    
    @RequestMapping(value = "/mycpsStat5.do")
    @ResponseBody
    public HashMap<String, Object> mycpsStat5() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        myCpsService.read5();

        return resultMap;
    }

}
