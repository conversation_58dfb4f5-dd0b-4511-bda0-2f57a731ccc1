package com.fmb.server2022.controller.admin;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ReqUpdateReserveHotelSkuSortValueList {


    @JsonProperty("data")
    private List<DataDTO> data;

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataDTO {
        @JsonProperty("skuId")
        private Integer skuId;
        @JsonProperty("skuSortValue")
        private Integer skuSortValue;

        public Integer getSkuId() {
            return skuId;
        }

        public void setSkuId(Integer skuId) {
            this.skuId = skuId;
        }

        public Integer getSkuSortValue() {
            return skuSortValue;
        }

        public void setSkuSortValue(Integer skuSortValue) {
            this.skuSortValue = skuSortValue;
        }
    }
}
