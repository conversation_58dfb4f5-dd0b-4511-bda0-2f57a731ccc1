package com.fmb.server2022.controller.front.reqdomain;

import com.fmb.server2022.config.filter.FrontApiAspect;

public interface FrontApiRequestDomain {

    public String getAppid()  ;

    public String getOpVersion() ;

    public String getVersion()  ;


    /**
     * 是 父母邦微信 小程序
     * 
     * @return
     */
    
    public default boolean checkWxApp_Main(){
        return  FrontApiAspect.WX_APP_FMB_MAIN.equals(getAppid())  ;
    }
    /**
     * 是微信 分享官小程序
     * 
     * @return
     */
    
    public default boolean checkWxApp_Share(){
        return  FrontApiAspect.WX_APP_FMB_SHARE.equals(getAppid())  ;
    }

    /**
     * 是 父母邦的小程序 可能是父母邦也可能是分享官
     * @return
     */
    
    public default boolean checkWxApp(){
        return  FrontApiAspect.WX_APP_FMB_MAIN.equals(getAppid())  || FrontApiAspect.WX_APP_FMB_SHARE.equals(getAppid()) ;
    }

    /**
     * 是 父母邦的APP
     * 
     * @return
     */
    
    public default boolean checkFMB_APP(){
        return  FrontApiAspect.APP_ANDROID.equals(getAppid()) || FrontApiAspect.APP_IOS.equals(getAppid())  ;
    }
    public default boolean checkFMB_SYS(){
        return  FrontApiAspect.FMB_SYS.equals(getAppid())  ;
    }

    /**
     * 是父母邦 Android APP
     * 
     * @return
     */
    
    public default boolean checkFMB_APP_ANDROID(){
        return  FrontApiAspect.APP_ANDROID.equals(getAppid())   ;
    }
    /**
     * 是父母邦 ios APP
     * 
     * @return
     */
    
    public default boolean checkFMB_APP_IOS(){
        return  FrontApiAspect.APP_IOS.equals(getAppid())   ;
    }


    
    public default boolean checkH5(){
        return  FrontApiAspect.FMB_H5.equals(getAppid())   ;
    }




    /**
     *  版本号  小于aim
     * 
     * @param aim
     * @return
     */
    public default boolean checkVersionLess(double aim){
        return aim <  Double.parseDouble(getVersion()) ;
    }
    /**
     * 版本号  小于等于aim
     * 
     * @param aim
     * @return
     */
    public default boolean checkVersionLessAndEqual(double aim){
        return aim <=  Double.parseDouble(getVersion()) ;
    }
    /**
     * 版本号  大于aim
     * 
     * @param aim
     * @return
     */
    public default boolean checkVersionGreat(double aim){
        return aim >  Double.parseDouble(getVersion()) ;
    }
    /**
     * 版本号  大于等于aim
     * 
     * @param aim
     * @return
     */
    public default boolean checkVersionGreatAndEqual(double aim){
        return aim >=  Double.parseDouble(getVersion()) ;
    }

    /**
     * 版本号是
     * 
     * @param aim
     * @return
     */
    public default boolean checkVersionEqual(double aim){
        return aim ==  Double.parseDouble(getVersion()) ;
    }


    public default String apiType(){
        if (checkFMB_APP()) {
            if (checkFMB_APP_IOS()) {
                return "appIos"+getVersion() ;
            }
            if (checkFMB_APP_ANDROID()) {
                return "appAndroid"+getVersion() ;
            }
        }else if(checkWxApp()){
            return "wxapp"+getVersion() ;
        }else if(checkH5()){
            return "h5" ;
        }
        return  "unknown";
    }

    public default String orderType(){
        if (checkFMB_APP()) {
            if (checkFMB_APP_IOS()) {
                return "apple" ;
            }
            if (checkFMB_APP_ANDROID()) {
                return "fmb" ;
            }
        }else if(checkWxApp()){
            return "wxapp" ;
        }else if(checkH5()){
            return "h5" ;
        }else if(checkFMB_SYS()){
            return "fmbxSys" ;
        }
        return  "unknown";
    }

}
