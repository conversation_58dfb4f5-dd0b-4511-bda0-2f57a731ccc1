package com.fmb.server2022.controller.demo;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.config.filter.Logical;
import com.fmb.server2022.config.filter.RequiresPermissions;
import com.fmb.server2022.fmbx.entity.SysPermission;
import com.fmb.server2022.reqdomain.ReqListUser;
import com.fmb.server2022.reqdomain.ReqNewRole;
import com.fmb.server2022.reqdomain.ReqNewUser;
import com.fmb.server2022.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 用户,角色,权限管理类
 */
@RestController
@RequestMapping("/admin/user")
public class AdminUserController {


    @Autowired
    UserService userService;

    /**
     * 查询用户列表
     */
    @RequiresPermissions("user:list")
    @PostMapping("/list")
    public FmbRespBean listUser(@RequestBody ReqListUser req) {

        return userService.listUser(req);

    }

    @RequiresPermissions("user:add")
    @PostMapping("/addUser")
    public FmbRespBean addUser(@RequestBody @Valid ReqNewUser req) {
        return userService.addUser(req);
    }

    @RequiresPermissions("user:update")
    @PostMapping("/updateUser")
    public FmbRespBean updateUser(@RequestBody @Valid ReqNewUser req) {
        return userService.updateUser(req);
    }

    @RequiresPermissions(value = {"user:add", "user:update"}, logical = Logical.OR)
    @GetMapping("/getAllRoles")
    public FmbRespBean getAllRoles() {

        HashMap<String, Object> resultMap = new HashMap<>();
        userService.getAllRoles(resultMap);
        return FmbRespBean.success(resultMap);

    }


    /**
     * 角色列表
     */
    @RequiresPermissions("role:list")
    @GetMapping("/listRole")
    public FmbRespBean listRole() {
        return userService.listRole();
    }

    /**
     * 查询所有权限, 给角色分配权限时调用
     */
    @RequiresPermissions("role:list")
    @GetMapping("/listAllPermission")
    public FmbRespBean listAllPermission() {
        return userService.listAllPermission();
    }

    /**
     * 新增角色
     */
    @RequiresPermissions("role:add")
    @PostMapping("/addRole")
    public FmbRespBean addRole(@RequestBody @Valid ReqNewRole nrole) {
        return userService.addRole(nrole);
    }

    /**
     * 修改角色
     */
    @RequiresPermissions("role:update")
    @PostMapping("/updateRole")
    public FmbRespBean updateRole(@RequestBody @Valid ReqNewRole nrole) {
        return userService.updateRole(nrole);
    }

    /**
     * 角色删除开关
     *
     * @param roleid 角色id
     * @param status 1-打开,0-关闭
     * @return
     */
    @RequiresPermissions("role:update")
    @GetMapping("/roleupdate")
    public FmbRespBean roleupdate(@RequestParam(value = "roleid", required = true) Integer roleid,
                                  @RequestParam(value = "status", required = true) Integer status
    ) {
        return userService.roleUpdate(roleid, status);
    }


    @RequestMapping(value = "/addOrUpdatePermission")
    @RequiresPermissions("permission:addOrUpdate")
    @ResponseBody
    public FmbRespBean addPermission(@RequestBody SysPermission permission) {

        return userService.addPermission(permission) ;
    }

    @RequestMapping(value = "/userTree")
    @ResponseBody
    public FmbRespBean userTree() {

        return userService.genUserTree() ;
    }
    @RequestMapping(value = "/userFullTree")
    @ResponseBody
    public FmbRespBean userFullTree() {

        return userService.genFullTree() ;
    }


    /**
     * 通过菜单列表展示权限,而且同页面的权限要展示出来
     *
     * @return
     */
    @RequestMapping(value = "/treeRoleInfo")
    @ResponseBody
    public FmbRespBean treeRoleInfo() {
        return userService.treeRoleInfo() ;
    }



    /**
     * 角色 配置,采用菜单模式
     *
     * @param roleid 角色id
     * @param menuid 菜单id
     * @return
     */
//    @RequiresPermissions("role:update")
    @GetMapping("/treeRoleInfo2")
    @ResponseBody
    public FmbRespBean treeRoleInfo2(@RequestParam(value = "roleid", required = true) Integer roleid,
                                  @RequestParam(value = "menuid", required = true) Integer menuid
    ) {
        return userService.treeRoleInfo(roleid, menuid);
    }
}
