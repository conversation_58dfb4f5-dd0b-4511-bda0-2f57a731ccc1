package com.fmb.server2022.controller.bp;

import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.basic.user.SessionUser;
import com.fmb.server2022.reqdomain.ReqCommon;
import com.fmb.server2022.reqdomain.ReqLoginUser;
import com.fmb.server2022.service.FmbxBpService;
import com.fmb.server2022.service.UserUnionService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 商家后台登录
 */
@RestController
@RequestMapping("/bp")
public class BpsLoginController {



    @Autowired
    FmbxBpService bpService ;


    @Autowired
    UserUnionService userUnionService ;

    /**
     * 供应商后台登录
     * @param user
     * @return
     */
    @RequestMapping(value = "/authBps")
    @ResponseBody
    public FmbRespBean authBps(@RequestBody @Valid ReqLoginUser user) {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("bpstoken", bpService.authBps(user));
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/frameLogin")
    @ResponseBody
    public FmbRespBean frameLogin(@RequestParam(value = "userInfo", required = true) String frameLogin) {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        String bpstoken = MDC.get(FmbConstants.BPSTOKEN);
//
//        if (StringUtils.isNotBlank(bpstoken)) {
//            resultMap.put("bpstoken", bpstoken);
//        }else {
            resultMap.put("bpstoken", bpService.frameLogin(frameLogin));
//        }

        return FmbRespBean.success(resultMap);
    }


    /**
     * 退出登录
     * @return
     */
    @RequestMapping(value = "/logout")
    @ResponseBody
    public FmbRespBean logout() {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("result", bpService.logout());
        return FmbRespBean.success(resultMap);
    }


    /**
     * 当前用户信息
     * @return
     */
    @RequestMapping(value = "/nowBpInfo")
    @ResponseBody
    public FmbRespBean nowBpInfo() {

        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpService.nowBpInfo(resultMap) ;
        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/bPmenuTree")
    @ResponseBody
    public FmbRespBean menuTree() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpService.bpMenuTree(resultMap);

        return FmbRespBean.success(resultMap);
    }


}
