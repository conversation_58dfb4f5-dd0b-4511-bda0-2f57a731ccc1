package com.fmb.server2022.controller.front;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.service.FrontUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BaseFrontController {

    private static Logger logger = LoggerFactory.getLogger(BaseFrontController.class);

    @Autowired
    FrontUserService frontUserService ;

    public NowUser nowUser(){

        final String uid = MDC.get(FmbConstants.FmbxFront_UID_KEY);

        if (StringUtils.isNotBlank(uid)) {

            final JSONObject jsonObject = JSON.parseObject(uid);

            logger.info(" method_nowUser  uid={}",uid);
            NowUser nowUser = new NowUser();
            nowUser.setUserType(0);
            final int userId = Integer.parseInt(jsonObject.getString("uid"));
            nowUser.setUserId(userId);
            nowUser.setTerminalType(0);

            final FmbUsers one = frontUserService.queryFmbUsers(userId);

            if (one == null) {

                return null ;
            }
            nowUser.setUserInfo(one);
            return nowUser ;
        }

        return null ;
    }




}
