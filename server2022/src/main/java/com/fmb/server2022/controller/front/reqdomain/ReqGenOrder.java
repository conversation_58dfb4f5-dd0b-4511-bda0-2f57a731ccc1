package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 产生订单的载体
 */
@Data
public class ReqGenOrder implements FrontApiRequestDomain  {

    /**
     * 活动id
     */
    Integer xaid ;

    /**
     * 分站id
     */
    Integer siteId ;


    /**
     * 手机号
     */
    String mobile ;
    /**
     * 订单备注
     */
    String note ;

    /**
     * 使用余额
     */
    Integer useBalance ;

//    List<String> couponInfo ;
    String couponSn ;


    String hotelReserveCode ;

    /**
     * sku信息
     */
    List<ReqGenSku> reqGenSkus ;

    String appid ;
    String opVersion ;
    String version  ;


    /**
     * 下单过程中从系统查出的信息 放在这里 方便后续统一调用
     */
    OrderSysInfo orderSysInfo ;

    public ReqGenOrder() {
        this.orderSysInfo = new OrderSysInfo() ;
        this.couponSn = "" ;
    }

    public boolean shouldCheckActivity() {

        if (StringUtils.isNotBlank(hotelReserveCode)) {
            return false ;
        }

        return true ;
    }

    /**
     * 是否在使用 酒店预约码
     * @return
     */
    public boolean useHotelReserveCode() {

        return StringUtils.isNotBlank( hotelReserveCode) ;

    }


    /**
     * 基础校验 是否可以使用优惠券
     *
     * 用了房券 就不能使用优惠券
     *
     * @return
     */
    public boolean shouldUseCoupon(){
        return   !useHotelReserveCode() && StringUtils.isNotBlank(getCouponSn()) ;
    }

    /**
     * 基础校验 是否可以使用余额
     *
     * 用了房券 就不能使用余额
     *
     * @return
     */
    public boolean shouldUseBalance(){
        return   !useHotelReserveCode()   && getUseBalance()!=null  && 1==getUseBalance().intValue() ;
    }


}
