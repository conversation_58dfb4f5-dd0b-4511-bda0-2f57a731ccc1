package com.fmb.server2022.controller.front;


import com.fmb.server2022.controller.front.reqdomain.ReqCoupon;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 测试相关
 * <AUTHOR>
 */
@RestController
@RequestMapping("/front/test")
public class TestsController extends BaseFrontController {




    @RequestMapping(value = "/getVideosFirstImg")
    @ResponseBody
    public void getVideosFirstImg(@RequestBody @Valid ReqCoupon input) {

//        System.out.println("获取视频信息");
//        VideoInfo videoInfo = new VideoInfo(FmbServerConfig.getFfmpegPath());
//        System.out.println(FmbServerConfig.getCategoryLong());
//        System.out.println(JSON.toJSONString(videoInfo));
//        try
//        {
//            videoInfo.getInfo("/Users/<USER>/Desktop/111.mp4");
//            System.out.println(videoInfo);
//        } catch (Exception e)
//        {
//            e.printStackTrace();
//        }
//        System.out.println("获取第1秒的截图");
//        VideoThumbTaker videoThumbTaker = new VideoThumbTaker(FmbServerConfig.getFfmpegPath());
//        try
//        {
//            videoThumbTaker.getThumb("/Users/<USER>/Desktop/111.mp4", "/Users/<USER>/Desktop/111.png",    400, 600, 0, 0, (float) 0.001);
//            System.out.println("over");
//        } catch (Exception e)
//        {
//            e.printStackTrace();
//        }
    }

}
