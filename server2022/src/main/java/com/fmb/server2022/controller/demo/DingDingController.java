package com.fmb.server2022.controller.demo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.domain.DingProcessNotifyResult;
import com.fmb.server2022.reqdomain.DingdingReq;
import com.fmb.server2022.service.DingDingApproveService;
import com.fmb.server2022.service.DingDingTokenService;
import com.fmb.util.DingCallbackCrypto;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

import static com.fmb.server2022.service.DingDingTokenService.DINGDING_OA_CONTRACT_ONLINE_PROC_ID;
import static com.fmb.util.FmbCommonUtil.reqCheck;

@Controller
@RequestMapping("/dingding")
public class DingDingController {

    private static Logger logger = LoggerFactory.getLogger(DingDingController.class);

    @Autowired
    DingDingApproveService dingDingApproveService;

    @Autowired
    DingDingTokenService dingDingTokenService ;



//    @RequestMapping(value = "/ding03.do")
//    @ResponseBody
//    public FmbRespBean ding03() {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        List<OapiV2UserGetResponse.UserGetResponse> strings = dingDingApproveService.queryAllDingDingUser();
//
//
//        resultMap.put("size",strings.size()) ;
//
//        return FmbRespBean.success(resultMap);
//    }


    /**
     * 获取钉钉 token
     * @param input
     * @return
     */
    @RequestMapping(value = "/dingToken.do")
    @ResponseBody
    public HashMap<String, Object> dingToken(@RequestBody  DingdingReq input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("token", "");
        if (reqCheck(input)){
            resultMap.put("token", dingDingTokenService.getDingDingToken());
//            resultMap.put("token", ""+ SnowflakeIdWorker.getInstance().nextId());
        }

        return resultMap;
    }




    /**
     * 手动 更新钉钉用户信息
     * @return
     */
    @RequestMapping(value = "/dingUsers")
    @ResponseBody
    public FmbRespBean updateDingUsersInfo(@RequestBody  DingdingReq input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        if (reqCheck(input)){

            resultMap.put("size",dingDingApproveService.updateThirdUserInfo()) ;
        }

        return FmbRespBean.success(resultMap);
    }


    /**
     * 读取钉钉用户的详细 信息
     * @param id
     * @return
     */
    @RequestMapping(value = "/dingDetail.do")
    @ResponseBody
    public HashMap<String, Object> dingDetail(@RequestParam(value = "id", required = true) String id) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data",  dingDingApproveService.readUserInfo(id));
        return resultMap;
    }

//    /**
//     * 发钉钉消息通知
//     * @return
//     */
//    @RequestMapping(value = "/dingMsg")
//    @ResponseBody
//    public FmbRespBean dingMsg() {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        boolean result = dingDingApproveService.sendMsg("manager4802", "钉钉后台消息测试,来自侯友法的测试");
//
//        resultMap.put("result",result) ;
//
//        return FmbRespBean.success(resultMap);
//    }


    /**
     * 获取oa流程的  process_code
     * @param name
     * @return
     */
    @RequestMapping(value = "/dingProcessByName")
    @ResponseBody
    public FmbRespBean dingProcessByName(@RequestParam(value = "name", required = true) String name) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        String p = dingDingApproveService.processByName(name);

        resultMap.put("data", p);
        return FmbRespBean.success(resultMap);
    }

//
//    /**
//     * 创建 钉钉oa实例 废弃了. 因为只能生成文本,没法上传附件
//     * @return
//     */
//    @RequestMapping(value = "/createOA")
//    @ResponseBody
//    public FmbRespBean createOA() {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        String oaForm = dingDingApproveService.createOAForm("4827080520296281", 17743478L,5);
//
//        resultMap.put("data", oaForm);
//        return FmbRespBean.success(resultMap);
//    }




    /**
     * 获取申请实例的 详情
     * 譬如 合同审批的详情
     * @param instanceid
     * @return
     */
    @RequestMapping(value = "/instanceDetail")
    @ResponseBody
    public FmbRespBean instanceDetail(@RequestParam(value = "instanceid", required = true) String instanceid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo vo = dingDingApproveService.readOaApproveDetail(instanceid);


        resultMap.put("data", vo);

        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/downDingFile")
//    @ResponseBody
//    public FmbRespBean downDingFile(
//            @RequestParam(value = "instanceid", required = true) String instanceid
//, @RequestParam(value = "fileId", required = true) String fileId
//    ) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        String fileurl = dingDingApproveService.queryDownLoadInstanceFileUrl(instanceid, fileId);
//        resultMap.put("fileurl", fileurl);
//        return FmbRespBean.success(resultMap);
//    }

    /**
     * 钉钉审批实例变更通知
     * @param msg_signature
     * @param timeStamp
     * @param nonce
     * @param json
     * @return
     */
    @RequestMapping(value = "/dingNotify")
    @ResponseBody
    public Map<String, String> dingNotify(
            @RequestParam(value = "msg_signature", required = false) String msg_signature,
            @RequestParam(value = "timestamp", required = false) String timeStamp,
            @RequestParam(value = "nonce", required = false) String nonce,
            @RequestBody(required = false) JSONObject json

    ) {

        try {
            // 1. 从http请求中获取加解密参数

            // 2. 使用加解密类型
            // Constant.OWNER_KEY 说明：
            // 1、开发者后台配置的订阅事件为应用级事件推送，此时OWNER_KEY为应用的APP_KEY。
            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
            //      此时OWNER_KEY为：企业的appkey（企业内部应用）或SUITE_KEY（三方应用）
            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(DingDingTokenService.DINGDING_AES_TOKEN, DingDingTokenService.DINGDING_AES_KEY,
                    DingDingTokenService.DINGDING_APPKEY);
            String encryptMsg = json.getString("encrypt");
            String decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);

            // 3. 反序列化回调事件json数据
            JSONObject eventJson = JSON.parseObject(decryptMsg);
            String eventType = eventJson.getString("EventType");

            // 4. 根据EventType分类处理
            if ("check_url".equals(eventType)) {
                // 测试回调url的正确性
                logger.info("测试回调url的正确性");
            }
            //审批实例 变动通知
            else if ("bpms_instance_change".equals(eventType)){


                DingProcessNotifyResult notifyResult = JSON.parseObject(decryptMsg, DingProcessNotifyResult.class);

                //这个 应用的审批结果是 需要 关注的
                if (DINGDING_OA_CONTRACT_ONLINE_PROC_ID.equals(notifyResult.getProcessCode())) {

                    if (logger.isInfoEnabled()) {
                        logger.info(eventJson.toJSONString());
                    }

                    //审批开始
                    if ("start".equals(  notifyResult.getType())) {
                        dingDingApproveService.processFinish(notifyResult.getProcessInstanceId(),"start") ;
                    }
                    else if ("finish".equals(  notifyResult.getType())) {

                        //审批同意
                        if ("agree".equals(notifyResult.getResult())) {

                            dingDingApproveService.processFinish(notifyResult.getProcessInstanceId(),"agree") ;
                        }
                        //审批拒绝
                        if ("refuse".equals(notifyResult.getResult())) {

                            dingDingApproveService.processFinish(notifyResult.getProcessInstanceId(),"refuse") ;

                        }

                    }

                }

            }

            // 5. 返回success的加密数据
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            return successMap;

        } catch (DingCallbackCrypto.DingTalkEncryptException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }
        return null;


    }


    /**
     * 获取某个OA审批的历史审批实例
     * 没有请求防护 临时屏蔽吧
     * @return
     */
//    @RequestMapping(value = "/instanceListids")
//    @ResponseBody
//    public FmbRespBean instanceListids() {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        ArrayList<String> strings = dingDingApproveService.instanceListids(DINGDING_OA_CONTRACT_ONLINE_PROC_ID);
//        resultMap.put("data", strings);
//        return FmbRespBean.success(resultMap);
//    }




    @RequestMapping(value = "/syncAllContract.do")
    @ResponseBody
    public HashMap<String, Object> syncAllContract(@RequestBody  DingdingReq input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("syncAllContract", "0");
        if (reqCheck(input)){
            dingDingApproveService.asyncDownContract(input.getDays());
            resultMap.put("syncAllContract", "1");
        }
        return resultMap;
    }


    @RequestMapping(value = "/dingDepartmentList.do")
    @ResponseBody
    public HashMap<String, Object> dingDepartmentList() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", dingDingApproveService.dingDepartmentList());
        return resultMap;
    }


}
