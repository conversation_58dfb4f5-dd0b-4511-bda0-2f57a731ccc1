package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.service.BpsMediaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/media")
public class MediaController {

    @Autowired
    BpsMediaService bpsMediaService;

    /**
     * 通过商家ID获取图片分类列表
     * @return
     */
    @RequestMapping(value = "/getImgCateNameList", method = RequestMethod.GET)
    @ResponseBody
    public FmbRespBean getImgCateNameList(@RequestParam(value = "bpsId",required = true) Integer bpsId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpsMediaService.getImgCateNameList(bpsId,resultMap);
        return FmbRespBean.success(resultMap);
    }

    /**
     * 通过商家ID获取视频列表
     * @return
     */
    @RequestMapping(value = "/getBpsVideoList", method = RequestMethod.GET)
    @ResponseBody
    public FmbRespBean getBpsVideoList(@RequestParam(value = "bpsId",required = true) Integer bpsId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpsMediaService.getBpsVideoList(bpsId,resultMap);
        return FmbRespBean.success(resultMap);
    }


    /**
     * 通过活动ID获取图片分类列表
     * @return
     */
    @RequestMapping(value = "/imgListByAid", method = RequestMethod.GET)
    @ResponseBody
    public FmbRespBean imgListByAid(@RequestParam(value = "aid",required = true) Integer aid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpsMediaService.imgListByAid(aid,resultMap);
        return FmbRespBean.success(resultMap);
    }



    /**
     * 通过商家ID获取图片分类列表
     * @return
     */
    @RequestMapping(value = "/getImgListByBpsIdAndGroupNum", method = RequestMethod.GET)
    @ResponseBody
    public FmbRespBean getImgListByBpsIdAndGroupNum(
            @RequestParam(value = "bpsId",required = true) Integer bpsId,
            @RequestParam(value = "groupval",required = true) Integer groupval
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        bpsMediaService.getImgListByBpsIdAndGroupNum(bpsId,groupval,resultMap);
        return FmbRespBean.success(resultMap);
    }


}
