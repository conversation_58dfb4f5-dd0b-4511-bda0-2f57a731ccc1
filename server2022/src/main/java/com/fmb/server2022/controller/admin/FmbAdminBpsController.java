package com.fmb.server2022.controller.admin;


import com.fmb.basic.BadLogicException;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmbx.entity.FmbxBps;
import com.fmb.server2022.reqdomain.ReqBpContract;
import com.fmb.server2022.reqdomain.ReqBpsInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomInfo;
import com.fmb.server2022.reqdomain.ReqBpsRoomSku;
import com.fmb.server2022.reqdomain.ReqFmbxBpsRoom;
import com.fmb.server2022.reqdomain.ReqQuickBpBindBps;
import com.fmb.server2022.service.BpsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

@RestController
@RequestMapping("/admin/bps")
public class FmbAdminBpsController {


    @Autowired
    BpsService bpsService ;


    /**
     * 商户查询
     * @param req
     * @return
     */
    @RequestMapping(value = "/bpsList")
    @ResponseBody
    public FmbRespBean bpsList(@RequestBody @Valid ReqBpsInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.selectBpsInfo(resultMap, req);


        return FmbRespBean.success(resultMap);
    }


    /**
     * 商户状态 开关
     */
    @RequestMapping(value = "/bpsStatusToggle")
    @ResponseBody
    public FmbRespBean bpsStatusToggle(
            @RequestParam(value = "bpsId", required = true) Integer bpsId
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsStatusToggle(resultMap, bpsId);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/bpsAdd")
    @ResponseBody
    public FmbRespBean fmbxBpsAdd(@RequestBody @Valid FmbxBps input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.addBps(resultMap,input) ;


        return FmbRespBean.success(resultMap);
    }

    /**
     * 快速建立供应商 和 商户 关联.并指定是否是
     * @param input
     * @return
     */
    @RequestMapping(value = "/quickBpBindBps")
    @ResponseBody
    public FmbRespBean quickBpBindBps(@RequestBody @Valid ReqQuickBpBindBps input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.quickBpBindBps(resultMap,input) ;


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/bpsupdate")
    @ResponseBody
    public FmbRespBean bpsupdate(@RequestBody @Valid FmbxBps input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.updateBps(resultMap,input) ;


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/bps_detail" )
    @ResponseBody
    public FmbRespBean bps_detail(@RequestParam(value = "bps_id", required = true) Integer bpsid
    
                                  , @RequestParam(value = "simple", defaultValue = "0") Integer simple
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsInfo(resultMap,bpsid,simple) ;

        return FmbRespBean.success(resultMap);
    }





    /**
     * 商户管理房型管理
     */
    @RequestMapping(value = "/bpsRoomList")
    @ResponseBody
    public FmbRespBean bpsRoomList(@RequestBody @Valid ReqBpsRoomInfo req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.selectBpsRoomInfo(resultMap, req);

        return FmbRespBean.success(resultMap);
    }

    /**
     * 套餐选择房型管理
     */
    @RequestMapping(value = "/bpsRoomSkuList")
    @ResponseBody
    public FmbRespBean bpsRoomSkuList(@RequestBody @Valid ReqBpsRoomSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.selectBpsRoomSku(resultMap, req);

        return FmbRespBean.success(resultMap);
    }

    /**
     * 商户管理房型管理开关
     */
    @RequestMapping(value = "/bpsRoomStatusToggle")
    @ResponseBody
    public FmbRespBean bpsRoomStatusToggle(
            @RequestParam(value = "roomid", required = true) Integer roomid
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsRoomStatusToggle(resultMap, roomid);

        return FmbRespBean.success(resultMap);
    }
    /**
     * 商户房型复制
     */
    @RequestMapping(value = "/bpsRoomCopy")
    @ResponseBody
    public FmbRespBean bpsRoomCopy(
            @RequestParam(value = "roomid", required = true) Integer roomid
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.bpsRoomCopy(resultMap, roomid);

        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/roomAdd")
//    @RequiresPermissions("fmbxBps:add")
    @ResponseBody
    public FmbRespBean roomAdd(@RequestBody @Valid ReqFmbxBpsRoom input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.saveOrUpdateRoom(resultMap,input) ;

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/roomUpdate")
//    @RequiresPermissions("fmbxBps:add")
    @ResponseBody
    public FmbRespBean roomUpdate(@RequestBody @Valid ReqFmbxBpsRoom input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        if (input.getRoomId()==null) {
            throw new BadLogicException("参数异常,roomId为空");
        }

        bpsService.saveOrUpdateRoom(resultMap,input) ;

        return FmbRespBean.success(resultMap);
    }



    @RequestMapping(value = "/roomDetail")
    @ResponseBody
    public FmbRespBean roomDetail(

            @RequestParam(value = "roomid", required = true) Integer roomid
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.roomDetail(resultMap,roomid) ;


        return FmbRespBean.success(resultMap);
    }





    /**
     * 供应商合同管理
     */
    @RequestMapping(value = "/bpContractList")
    @ResponseBody
    public FmbRespBean bpContract(@RequestBody @Valid ReqBpContract req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.selectBpContract(resultMap, req);


        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/contractDatail")
    @ResponseBody
    public FmbRespBean contractDatail(
            @RequestParam(value = "bpcId", required = true) Integer bpcId
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.contractDatail(resultMap, bpcId);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/contractBindBp")
    @ResponseBody
    public FmbRespBean contractBindBp(
            @RequestBody @Valid ReqBpContract req
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.contractBindBp(resultMap, req);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/contract01")
    @ResponseBody
    public FmbRespBean contract01(
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.checkContractWillEnd();

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/contract02")
    @ResponseBody
    public FmbRespBean contract02(
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.checkContractEnd();

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/downLoadFinalContract")
    @ResponseBody
    public FmbRespBean downLoadFinalContract(
            @RequestParam(value = "bpcId", required = true) Integer bpcId
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpsService.downLoadFinalContract(resultMap,bpcId);

        return FmbRespBean.success(resultMap);
    }




}
