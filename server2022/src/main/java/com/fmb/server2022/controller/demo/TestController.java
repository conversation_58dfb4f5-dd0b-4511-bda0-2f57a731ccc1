package com.fmb.server2022.controller.demo;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.service.IFmbOrderRelatedDataService;
import com.fmb.server2022.service.BpsService;
import com.fmb.server2022.service.order.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;

@Profile({"local","dev"})
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    BpsService bpsService;


    @Autowired
    OrderService orderService ;


    @PostMapping("/testBpsInfoStatus")
    public void testBpsInfoStatus(@RequestParam(value = "bps_id") int bps_id) {
        bpsService.updateBpsInfoStatus(bps_id);
        System.out.println("testBpsInfoStatus");
    }


    @RequestMapping(value = "/orderSuccessPayTest")
    @ResponseBody
    public FmbRespBean orderSuccessPayTest(@RequestParam(value = "orderSn", required = true) String orderSn) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.orderSuccessPay(orderSn,null) ;

        return FmbRespBean.success(resultMap);
    }

    @Autowired
    IFmbOrderRelatedDataService orderRelatedDataService ;
    @RequestMapping(value = "/keywordTest")
    @ResponseBody
    public FmbRespBean keywordTest() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        FmbOrderRelatedData relatedData = new FmbOrderRelatedData();
        relatedData.setId(0);
        relatedData.setOrderSn("");
        relatedData.setUid(0);
        relatedData.setCallUser(0);
        relatedData.setCommonId(0);
        relatedData.setType(0);
        relatedData.setContainTimes(0);
        relatedData.setNumber(0);
        //关键字....
        relatedData.setDesc("");
        relatedData.setCtime(LocalDateTime.now());
        relatedData.setReserveEndStatus(0);

        orderRelatedDataService.save(relatedData) ;


        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/orderCancelTest")
//    @ResponseBody
//    public FmbRespBean orderCancelTest(@RequestParam(value = "orderid", required = true) Integer orderid) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        orderService.cancelOrder(null, front, orderid,"auto_cancel");
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/yearcard")
    @ResponseBody
    public FmbRespBean yearcard() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("data",
        orderService.queryYearCardAids()) ;

        return FmbRespBean.success(resultMap);
    }


}
