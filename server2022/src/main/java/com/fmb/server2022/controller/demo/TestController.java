package com.fmb.server2022.controller.demo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceListidsRequest;
import com.dingtalk.api.response.OapiProcessinstanceListidsResponse;
import com.fmb.basic.BadLogicException;
import com.fmb.server2022.config.filter.FrontApiAspect;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.NowUser;
import com.fmb.server2022.controller.front.reqdomain.ReqGenOrder;
import com.fmb.server2022.domain.FmbOrderDetailQueryResult;
import com.fmb.server2022.domain.NewUserOrderStatDomain;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;
import com.fmb.server2022.fmbx.entity.FmbOrderRelatedData;
import com.fmb.server2022.fmbx.entity.FmbUsers;
import com.fmb.server2022.fmbx.entity.FmbxSku;
import com.fmb.server2022.fmbx.service.IFmbOrderRelatedDataService;
import com.fmb.server2022.mapper.FrontSkuDao;
import com.fmb.server2022.reqdomain.FrameLogin;
import com.fmb.server2022.service.BpsService;
import com.fmb.server2022.service.DBTestService;
import com.fmb.server2022.service.DingDingTokenService;
import com.fmb.server2022.service.FmbxBpService;
import com.fmb.server2022.service.FrontUserService;
import com.fmb.server2022.service.NewUserStatService;
import com.fmb.server2022.service.SuiteService;
import com.fmb.server2022.service.order.OrderReturnService;
import com.fmb.server2022.service.order.OrderService;
import com.fmb.server2022.service.order.OrderStatService;
import com.fmb.server2022.service.order.OrderSuccessHandler;
import com.fmb.server2022.service.schedule.impel.OrderSuccessPayDomain;
import com.fmb.util.AuthCodeUtil;
import com.fmb.util.MD5Util;
import com.fmb.util.SnowflakeIdWorker;
import com.taobao.api.ApiException;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fmb.basic.FmbConstants.IMPORTANT_INFO_LOGGER;
import static com.fmb.server2022.service.DingDingTokenService.DINGDING_OA_CONTRACT_ONLINE_PROC_ID;

@Profile({"local","dev"})
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    BpsService bpsService;


    @Autowired
    OrderService orderService ;


    @Autowired
    SuiteService suiteService ;


    @Autowired
    OrderReturnService orderReturnService ;


    @PostMapping("/testBpsInfoStatus")
    public void testBpsInfoStatus(@RequestParam(value = "bps_id") int bps_id) {
        bpsService.updateBpsInfoStatus(bps_id);
        System.out.println("testBpsInfoStatus");
    }


    @RequestMapping(value = "/orderSuccessPayTest")
    @ResponseBody
    public FmbRespBean orderSuccessPayTest(@RequestParam(value = "orderSn", required = true) String orderSn) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        orderService.orderSuccessPay(orderSn.trim(),null) ;

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/orderSuccessPayTest2")
    @ResponseBody
    public FmbRespBean orderSuccessPayTest2() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        String x = "{\"order_sn\":\"20230713122840687897\",\"money\":\"30\",\"pay_type\":\"pos\",\"type\":\"pay_success_order\"," +
                "\"pay_sn\":\"123\"}" ;
        final OrderSuccessPayDomain orderSuccessPayDomain = JSON.parseObject(x, OrderSuccessPayDomain.class);

        orderService.orderSuccessPay(orderSuccessPayDomain.getOrderSn(),orderSuccessPayDomain) ;

        return FmbRespBean.success(resultMap);
    }


    @Autowired
    IFmbOrderRelatedDataService orderRelatedDataService ;
    @RequestMapping(value = "/keywordTest")
    @ResponseBody
    public FmbRespBean keywordTest() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        FmbOrderRelatedData relatedData = new FmbOrderRelatedData();
        relatedData.setId(0);
        relatedData.setOrderSn("");
        relatedData.setUid(0);
        relatedData.setCallUser(0);
        relatedData.setCommonId(0);
        relatedData.setType(0);
        relatedData.setContainTimes(0);
        relatedData.setNumber(0);
        //关键字....
        relatedData.setDesc("");
        relatedData.setCtime(LocalDateTime.now());
        relatedData.setReserveEndStatus(0);

        orderRelatedDataService.save(relatedData) ;


        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/orderCancelTest")
//    @ResponseBody
//    public FmbRespBean orderCancelTest(@RequestParam(value = "orderid", required = true) Integer orderid) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        orderService.cancelOrder(null, front, orderid,"auto_cancel");
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/yearcard")
    @ResponseBody
    public FmbRespBean yearcard() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("data",
        orderService.queryYearCardAids()) ;

        return FmbRespBean.success(resultMap);
    }

    @Autowired
    OrderSuccessHandler successHandler ;


    @RequestMapping(value = "/smsTest01.do")
    @ResponseBody
    public HashMap<String, Object> smsTest01(@RequestParam(value = "id", required = true) Integer id) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        successHandler.sendSmsWhenSuccess(id);

        return resultMap;
    }

    @Autowired
    OrderStatService statService ;
    @RequestMapping(value = "/datePrime.do")
    @ResponseBody
    public HashMap<String, Object> datePrime(@RequestParam(value = "aid", required = true) Integer aid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        statService.buildHotelDateActSkuInfo(aid) ;

        return resultMap;
    }
    @RequestMapping(value = "/datePrime2.do")
    @ResponseBody
    public HashMap<String, Object> datePrime2(@RequestParam(value = "aid", required = true) Integer aid
    , @RequestParam(value = "begin", required = true) String begin
    , @RequestParam(value = "end", required = true) String end
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("result", statService.testHotelDatePrimeInfo(aid,begin,end) );
        resultMap.put("begin", begin);
        resultMap.put("end", end);
        resultMap.put("aid", aid);

        return resultMap;
    }


    @RequestMapping(value = "/syncActivityInfo2ProductIndex.do")
    @ResponseBody
    public HashMap<String, Object> syncActivityInfo2ProductIndex(@RequestParam(value = "aid", required = true) Integer aid
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        statService.syncActivityInfo2ProductIndex(aid) ;
        resultMap.put("aid", aid);

        return resultMap;
    }


    @RequestMapping(value = "/syncActInfoLot.do")
    @ResponseBody
    public HashMap<String, Object> syncActInfoLot() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        statService.syncActInfoLot() ;
        return resultMap;
    }


    @RequestMapping(value = "/ftl01.do")
    @ResponseBody
    public HashMap<String, Object> ftl01(@RequestParam(value = "orderSn", required = true) String orderSn) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        //resultMap.put("data", orderService.getOrderHotelEmailTemp(orderSn).get("mailText"));

        return resultMap;
    }


    @Autowired
    DBTestService dbTestService ;

    @RequestMapping(value = "/dbTest2.do")
    @ResponseBody
    public HashMap<String, Object> dbTest2() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        dbTestService.queryAndAddTest() ;

        return resultMap;
    }


    @RequestMapping(value = "/addOrderChange.do")
    @ResponseBody
    public HashMap<String, Object> addOrderChange(@RequestParam(value = "orderSn", required = true) String orderSn) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        orderChange(ordersn);

        statService.makeOrderStatDataCore(orderSn);

        return resultMap;
    }


    @RequestMapping(value = "/orderReturn1.do")
    @ResponseBody
    public HashMap<String, Object> orderReturn1(@RequestParam(value = "orderSn", required = true) String orderSn) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final FmbOrderDetailQueryResult fmbOrderDetailQueryResult = orderReturnService.orderReturnBeginQuery(orderSn, null);
        resultMap.put("data", fmbOrderDetailQueryResult);

        return resultMap;
    }


    @RequestMapping(value = "/skuChange.do")
    @ResponseBody
    public HashMap<String, Object> skuChange(@RequestParam(value = "skuid", required = true) Integer skuid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        SuiteService.sendHotelSkuChangeNotify(skuid);

        return resultMap;
    }


//    @RequestMapping(value = "/aidChange.do")
//    @ResponseBody
//    public HashMap<String, Object> aidChange(@RequestParam(value = "aid", required = true) Integer aid) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap.put("data", "");
//
//        suiteService.checkSkuOfHotelActivityAndUpdateActivity(aid) ;
//
//        return resultMap;
//    }


    @RequestMapping(value = "/myCpsTest.do")
    @ResponseBody
    public HashMap<String, Object> myCpsTest(@RequestParam(value = "ordersn", required = true) String ordersn
                                             , @RequestParam(value = "mycps", required = true) String mycps
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        final FmbOrderInfo orderInfo = orderService.queryFmbOrderInfo(ordersn);
        orderService.doMycpsJob(orderInfo,mycps);
        return resultMap;
    }


    @RequestMapping(value = "/createUids.do")
    @ResponseBody
    public HashMap<String, Object> createUid() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        final StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < 40; i++) {
            final String s = AuthCodeUtil.fmbAuthCodeEncode("" + (30001 + i));
            stringBuffer.append(s) ;
            stringBuffer.append("\r\n") ;
        }

        try {
            FileUtils.writeStringToFile(new File("/Users/<USER>/Downloads/uids.csv"),stringBuffer.toString(),"utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }

        return resultMap;
    }




    @RequestMapping(value = "/timeUpdateTest.do")
    @ResponseBody
    public HashMap<String, Object> timeUpdateTest() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");


        return resultMap;
    }



    @RequestMapping(value = "/syncHotelActPriceLot.do")
    @ResponseBody
    public HashMap<String, Object> syncHotelActPriceLot() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        statService.syncHotelActPriceLot() ;

//        statService.syncActInfoLot();


        return resultMap;
    }


    @RequestMapping(value = "/frameLoginTest")
    @ResponseBody
    public FmbRespBean frameLoginTest() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        FrameLogin frameLoginDomain = new FrameLogin() ;
        frameLoginDomain.setShopUserId(55);
        frameLoginDomain.setMillSeconds(""+System.currentTimeMillis());
        frameLoginDomain.setMd5(MD5Util.md5(frameLoginDomain.getShopUserId() + "_" + frameLoginDomain.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY));

        resultMap.put("frameLoginDomain",frameLoginDomain) ;

        resultMap.put("encode", Base64.encode(JSON.toJSONString(frameLoginDomain))) ;

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/adminLogin")
    @ResponseBody
    public FmbRespBean adminLogin() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        FrameLogin frameLoginDomain = new FrameLogin() ;
        frameLoginDomain.setAdminId(147);
        frameLoginDomain.setMillSeconds(""+System.currentTimeMillis());
        frameLoginDomain.setMd5(MD5Util.md5(frameLoginDomain.getAdminId() + "_" + frameLoginDomain.getMillSeconds() + "_" + FmbConstants.PHP_MD5_SALT_KEY));

        resultMap.put("frameLoginDomain",frameLoginDomain) ;

        resultMap.put("encode", Base64.encode(JSON.toJSONString(frameLoginDomain))) ;

        return FmbRespBean.success(resultMap);
    }

    @Autowired
    FrontSkuDao frontSkuDao ;

    @RequestMapping(value = "/t0406.do")
    @ResponseBody
    public HashMap<String, Object> t0406(@RequestParam(value = "aid", required = true) Integer aid,
                                         @RequestParam(value = "flag_sell", defaultValue = "1") Integer flag_sell
                                         ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        HashMap<String, Object> par = new HashMap<>();
        par.put("xaid", aid);
        par.put("flag_sell", flag_sell);
        final List<Integer> integers = frontSkuDao.queryHotelDateSku(par);
        final List<Integer> integers2 = frontSkuDao.queryHotelReserveSku(par);

        resultMap.put("data", integers);
        resultMap.put("data2", integers2);

        return resultMap;
    }


    @RequestMapping(value = "/stat0412.do")
    @ResponseBody
    public HashMap<String, Object> stat0412(@RequestParam(value = "aid", required = true) Integer aid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        statService.queryDatePrimeInfos(aid) ;

        return resultMap;
    }


    @RequestMapping(value = "/t417.do")
    @ResponseBody
    public HashMap<String, Object> t417() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        return resultMap;
    }

    @Autowired
    FmbxBpService bpService ;

    @RequestMapping(value = "/bpStat.do")
    @ResponseBody
    public HashMap<String, Object> bpStat() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        bpService.updateBpOrderStat(100001,new BigDecimal("120"));

        return resultMap;
    }


    private static Logger loggerImportant = LoggerFactory.getLogger(IMPORTANT_INFO_LOGGER);

    @RequestMapping(value = "/touch0425.do")
    @ResponseBody
    public HashMap<String, Object> touch0425(@RequestParam(value = "aid", required = true) Integer aid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap.put("data", "");

        suiteService.touchActivity(aid);
        return resultMap;
    }


    @RequestMapping(value = "/logger515.do")
    @ResponseBody
    public HashMap<String, Object> logger515() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        loggerImportant.info("123123_"+System.currentTimeMillis());
        return resultMap;
    }


    @RequestMapping(value = "/head0612.do")
    @ResponseBody
    public HashMap<String, Object> head0612() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        final HttpRequest request = HttpUtil.createRequest(Method.HEAD, "https://mmbiz.qpic" +
                ".cn/mmbiz_png/L74UkFBtsFyC2ic61PqtpBAjjhuUWqia0CxrA9CKHJlbX6MibAxOhPO62Fx0JMqHK1fOWLfP1y887eVbDvQ8hh4SQ/640?wx_fmt=png" +
                "&wxfrom=5&wx_lazy=1&wx_co=1");

        final Map<String, List<String>> headers = request.execute().headers();

        System.out.println(headers);

        resultMap.put("Content-Type", headers.get("Content-Type"));
        resultMap.put("Size", headers.get("Size"));
        return resultMap;
    }


    @RequestMapping(value = "/order0707.do")
    @ResponseBody
    public HashMap<String, Object> order0707() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        String json = "{\"order_sn\":\"20230728202141657771\",\"money\":\"499\",\"pay_type\":\"pos\",\"type\":\"pay_success_order\"," +
                "\"pay_sn\":\"333\"}" ;
        final OrderSuccessPayDomain orderSuccessPayDomain = JSON.parseObject(json, OrderSuccessPayDomain.class);

//        loggerImportant.info("OrderSuccessPayJob {}",job.getJobExtPars());


        orderService.orderSuccessPayFromRedis(orderSuccessPayDomain) ;

        resultMap.put("data", "");


        return resultMap;
    }


    @Autowired
    JedisPool jedisPool ;


    @RequestMapping(value = "/redis0707.do")
    @ResponseBody
    public HashMap<String, Object> redis0707() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);


            SetParams par = new SetParams().nx().ex(100);

            final String set = jedis.set("fmbx:sendHotelSkuChangeNotify:1000" , "", par);

            resultMap.put("set", set);

        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }


        return resultMap;
    }



    @Autowired
    FrontUserService frontUserService ;

    @RequestMapping(value = "/genOrder712.do")
    @ResponseBody
    public HashMap<String, Object> genOrder712() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        ReqGenOrder input = new ReqGenOrder();


        input.setXaid(105969);
        input.setMobile("14012312312");
        input.setNote("");
        input.setVersion("1.0");
        List<ReqGenSku> skus = new ArrayList<>();
        ReqGenSku sku = new ReqGenSku();

        sku.setSkuId(2077);
        sku.setSkuNumber(1);
        sku.setCheckInDate("2023-07-18");
        sku.setCheckOutDate("2023-07-19");
        sku.setHotelUserName(Arrays.asList("侯测试"));

        skus.add(sku);
        input.setReqGenSkus(skus);

        input.setAppid(FrontApiAspect.FMB_SYS );




//        {
//            "xaid": 105969,
//                "uid": "adb5gd8fGKe677qeykTpfEu9WZuC8ZjRScmzlFXoPVLm0euBZf0hr5IGC9wGzuoggwzNV+eVGIZECwgEB6u7dw4sDY0DWYsl34U",
//                "mycps": null,
//                "cpssrc": null,
//                "mobile": "14012312312",
//                "useBalance": 0,
//                "note": "",
//                "couponSn": "",
//                "hotelReserveCode": "",
//                "reqGenSkus": [
//            {
//                "skuId": 2077,
//                    "skuNumber": 1,
//                    "checkInDate": "2023-07-18",
//                    "checkOutDate": "2023-07-19",
//                    "hotelUserName": [
//                "侯友法"
//            ]
//            }
//    ],
//            "version": "5.06",
//                "scene": 1001,
//                "appid": "wxef0aac3d44dcda51",
//                "seconds": 1689138740986
//        }




        NowUser user = new NowUser();
        final int userId = 32973;
        user.setUserId(userId);

        FmbUsers one = frontUserService.queryFmbUsers(userId);
        user.setUserInfo(one);

        orderService.createOrderMain(resultMap,input,user) ;

        return resultMap;
    }


    /**
     *
     * @param aid 活动id
     * @param skuId skuid
     * @param roomNum 房间数量
     * @param checkInDate 入住日期
     * @param checkOutDate 离店日期
     * @param uid  用户uid
     * @param mobile 手机号
     * @param userNames 入住人
     * @return
     */
    public HashMap<String, Object> createHotelDateOrder(Integer aid,Integer skuId,Integer roomNum,String checkInDate,String checkOutDate,Integer uid  ,String mobile,
                                     ArrayList<String> userNames ){

        HashMap<String, Object> map = new HashMap<>();

        if (userNames != null   && userNames.size()!=roomNum) {
            throw new BadLogicException("房间数量需要和入住人数量相同");
        }

        NowUser user = new NowUser();
        final int userId = uid;
        user.setUserId(userId);

        FmbUsers one = frontUserService.queryFmbUsers(userId);
        if (one == null) {
            throw new BadLogicException("用户不存在");
        }

        user.setUserInfo(one);


        ReqGenOrder input = new ReqGenOrder();
        input.setXaid(aid);
        input.setMobile(mobile);
        input.setNote("");
        input.setVersion("1.0");
        List<ReqGenSku> skus = new ArrayList<>();
        ReqGenSku sku = new ReqGenSku();

        sku.setSkuId(skuId);
        sku.setSkuNumber(roomNum);
        sku.setCheckInDate(checkInDate);
        sku.setCheckOutDate(checkOutDate);
        sku.setHotelUserName(userNames);

        skus.add(sku);
        input.setReqGenSkus(skus);

        input.setAppid(FrontApiAspect.FMB_SYS );


        orderService.createOrderMain(map,input,user) ;

        return map ;
    }



    @Autowired
    IFmbAdminService adminService ;

    @RequestMapping(value = "/userQuery.do")
    @ResponseBody
    public HashMap<String, Object> userQuery() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        Wrapper<FmbAdmin> wrAdmin = new LambdaQueryWrapper<FmbAdmin>()
                .eq(FmbAdmin::getDisabled, 0)
                .and(qr->qr.eq(FmbAdmin::getRealname,"侯友法").or().eq(FmbAdmin::getPhone,"18500191390")) ;

        ;
        final List<FmbAdmin> list = adminService.list(wrAdmin);

        resultMap.put("data", "");
        return resultMap;
    }


    @RequestMapping(value = "/contractCheck.do")
    @ResponseBody
    public HashMap<String, Object> contractCheck(@RequestParam(value = "type", defaultValue = "1") Integer type) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("data", ""+System.currentTimeMillis());

        if (type==1) {
            bpsService.checkContractWillEnd();
        }else {
            bpsService.checkContractEnd();
        }

        return resultMap;
    }


    private static Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    DingDingTokenService dingDingTokenService;
    @RequestMapping(value = "/t718.do")
    @ResponseBody
    public HashMap<String, Object> t718() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        final ArrayList<String> ids = new ArrayList<>();

        try {

            Long nextCursor = 0L;
//            int count = 0 ;
            long now = System.currentTimeMillis();
            while (nextCursor != null) {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/listids");
                OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
                req.setProcessCode(DINGDING_OA_CONTRACT_ONLINE_PROC_ID);
                req.setStartTime(now -100*60*60*24*1000L);
//                req.setEndTime(now);
                req.setSize(20L);
                req.setCursor(nextCursor);
//        req.setUseridList("manager1,manager2");
                OapiProcessinstanceListidsResponse rsp = client.execute(req,  dingDingTokenService.getDingDingToken());

                if (rsp != null && rsp.getResult()!=null ) {
                    ids.addAll(rsp.getResult().getList()) ;

                    System.out.println(rsp.getResult().getList());
//                System.out.println(rsp.getBody());
                    nextCursor = rsp.getResult().getNextCursor();

                }else {
                    break;
                }



//                count ++ ;
//                if (count>10) {
//                    break;
//                }
            }

        } catch (ApiException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }


        resultMap.put("ids",ids.size()) ;
        return resultMap;
    }


    @RequestMapping(value = "/suitGo.do")
    @ResponseBody
    public HashMap<String, Object> suitGo() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

//        FmbxSku skuBaseDB = suiteService.querySkuBase(2285);
//
//        suiteService.checkHotelDateSku(2285,skuBaseDB,true) ;

        SuiteService.sendHotelSkuChangeNotify(2285);

        return resultMap;
    }
    @RequestMapping(value = "/suitGo2.do")
    @ResponseBody
    public HashMap<String, Object> suitGo2(@RequestParam(value = "id", required = true) Integer id) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        FmbxSku skuBaseDB = suiteService.querySkuBase(id);

        suiteService.checkHotelReserveSku(id,skuBaseDB,true) ;

        return resultMap;
    }


    @RequestMapping(value = "/bpNofityTest.do")
    @ResponseBody
    public HashMap<String, Object> bpNofityTest() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        bpService.notifyBD(35);

        resultMap.put("data", ""+System.currentTimeMillis());
        return resultMap;
    }

    @Autowired
    NewUserStatService newUserStatService ;


//    @RequestMapping(value = "/newUserStat.do")
//    @ResponseBody
//    public HashMap<String, Object> newUserStat(
//            @RequestParam(value = "aid", required = true) Integer aid
//            , @RequestParam(value = "dateBegin", required = true) String dateBegin
//            , @RequestParam(value = "dateEnd", required = true) String dateEnd
//    ) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        final NewUserOrderStatDomain newUserOrderStatDomain = newUserStatService.newUserStat(aid, dateBegin, dateEnd, input.getSecond());
//        resultMap.put("newUserOrderStatDomain", newUserOrderStatDomain);
//        return resultMap;
//    }

}
