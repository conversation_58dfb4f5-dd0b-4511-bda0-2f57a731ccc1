package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.reqdomain.ReqActivityTag;
import com.fmb.server2022.reqdomain.ReqActivityX;
import com.fmb.server2022.service.NewActivityService;
import com.fmb.server2022.service.SuiteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/admin/activity")
public class ActivityController {

    private static Logger logger = LoggerFactory.getLogger(ActivityController.class);


    @Autowired
    NewActivityService activityService ;

    @Autowired
    SuiteService suiteService ;

    /**
     * 简单添加活动
     * @param input
     * @return
     */
    @RequestMapping(value = "/addActivity")
    @ResponseBody
    public FmbRespBean addActivity(@RequestBody @Valid com.fmb.server2022.reqdomain.ReqaddNewActivity input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        activityService.addActivity(input,resultMap) ;

        return FmbRespBean.success(resultMap);
    }




    /**
     * 更新活动
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateActivity")
    @ResponseBody
    public FmbRespBean addNewActivity(@RequestBody @Valid com.fmb.server2022.reqdomain.ReqaddNewActivity input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        activityService.updateActivity(input,resultMap) ;

        return FmbRespBean.success(resultMap);
    }

    /**
     * 活动详情
     * @param xaid
     * @return
     */
    @RequestMapping(value = "/activityDetail")
    @ResponseBody
    public FmbRespBean activityDetail(@RequestParam(value = "xaid", required = true) Integer xaid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        activityService.activityDetail(xaid,resultMap) ;

        return FmbRespBean.success(resultMap);
    }



    /**
     * 活动列表
     */
    @RequestMapping(value = "/activityList")
    @ResponseBody
    public FmbRespBean activityX(@RequestBody @Valid ReqActivityX req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        activityService.selectActivityX(resultMap, req);
        return FmbRespBean.success(resultMap);
    }


    /**
     * 上架活动
     * @param xaid
     * @return
     */
    @RequestMapping(value = "/activityPublish")
    @ResponseBody
    public FmbRespBean activityPublish(@RequestParam(value = "xaid", required = true) Integer xaid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.activityPublish(resultMap, xaid);

        return FmbRespBean.success(resultMap);
    }

    /**
     * 下架活动
     * @param xaid
     * @return
     */
    @RequestMapping(value = "/activityUnPublish")
    @ResponseBody
    public FmbRespBean activityUnPublish(@RequestParam(value = "xaid", required = true) Integer xaid) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        suiteService.activityUnPublish(resultMap, xaid);

        return FmbRespBean.success(resultMap);
    }


}
