package com.fmb.server2022.controller.demo;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.config.filter.Logical;
import com.fmb.server2022.config.filter.RequiresPermissions;
import com.fmb.server2022.service.DBTestService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashMap;

@Controller
public class DBTestController {

    @Autowired
    DBTestService dbTestService ;



    @Autowired
    Cache caffeineCache ;

    @RequestMapping(value = "/dbtest01")
    @ResponseBody
    public FmbRespBean dbtest01(@RequestParam(value = "name", defaultValue = "") String name) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        dbTestService.doTest(name) ;

        resultMap.put("data", ""+name);
        return FmbRespBean.success(resultMap);
    }


//    @RequiresPermissions("article:list")
    @RequiresPermissions(value = {"article:list123","article:list243"} ,logical = Logical.OR)
    @RequestMapping(value = "/testPermission.do")
    @ResponseBody
    public FmbRespBean testPermission() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        return FmbRespBean.success(resultMap);
    }
    

    @RequestMapping(value = "/caffeineStat.do")
    @ResponseBody
    public HashMap<String, Object> caffeineStat() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        @NonNull CacheStats stats = caffeineCache.stats();

        HashMap<String, Object> statInfo = new HashMap<>();

        Arrays.asList( CacheStats.class.getMethods()).stream().forEach(method -> {
            System.out.println(method.getReturnType().toString()+"---"+method.getName());
        });

        System.out.println("--------");

        Arrays.asList( CacheStats.class.getMethods()).stream().forEach(method -> {
            String mname = method.getName();

            if (mname.endsWith("Count") || mname.endsWith("Rate")) {
                System.out.println(method.getReturnType().toString()+"---"+ mname);

                try {
                    statInfo.put(mname, ""+method.invoke(stats) ) ;
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }
            }


        });



        System.out.println(FmbServerConfig.getDownPrefix());

//        FieldUtils.getAllFieldsList(CacheStats.class).stream().forEach(x->{
//            System.out.println(x.getName()+"-->" + x.getType().toString());
//
////            Object invoke = ReflectUtil.invoke(stats, x.getName(), null);
////            System.out.println(invoke);
//
//        });

        resultMap.put("data", statInfo);
        return resultMap;
    }
}
