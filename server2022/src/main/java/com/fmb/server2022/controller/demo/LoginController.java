package com.fmb.server2022.controller.demo;


/**
 * 用户登录
 */
//@RestController
//@RequestMapping("/admin/login")
public class LoginController {

//
//    @Autowired
//    private LoginService loginService;
//
//    @Autowired
//    SysTokenService tokenService ;
//
//
//    /**
//     * 登录
//     */
//    @RequestMapping(value = "/auth")
//    @ResponseBody
//    public FmbRespBean auth(@RequestBody @Valid ReqLoginUser user) {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap.put("token", loginService.authLogin(user));
//        return FmbRespBean.success(resultMap);
//    }
//
//
//
//
//
//
//    /**
//     * 查询当前登录用户的信息
//     */
//    @PostMapping("/getInfo")
//    public FmbRespBean getInfo() {
//
//        String token = MDC.get(FmbConstants.TOKEN);
//        SessionUserInfoOfFmbAdmin userInfo = tokenService.getUserInfoBytoken(token);
//
//        //增加健壮性 防止返回空
//        if (userInfo==null) {
//          return   FmbRespBean.success(new HashMap<>()) ;
//        }
//
//        return FmbRespBean.success(userInfo) ;
//    }
//
//
//    /**
//     * 前端每隔几分钟读取一下这个接口 .如果用户的权限信息发生变化或后续有其他需要及时通知用户的情况
//     * 可以通过这个接口 获取  , 想要实时通知系统改造有点重,采用前端定时读取吧 .
//     * @return
//     */
//    @RequestMapping(value = "/readstats")
//    @ResponseBody
//    public FmbRespBean readstats() {
//
//        return FmbRespBean.success(tokenService.readstats());
//    }
//
//
//    /**
//     * 登出
//     */
//    @PostMapping("/logout")
//    public FmbRespBean logout() {
//        return FmbRespBean.success("opresult",tokenService.invalidateToken()) ;
//    }

}
