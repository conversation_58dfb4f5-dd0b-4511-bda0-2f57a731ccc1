package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;

import java.util.List;

@Data
public class ReqGenSku {

    /**
     * skuId
     */
    Integer skuId ;
    /**
     * 酒店入住起始日期
     */
    String checkInDate ;
    /**
     * 离店日期
     */
    String checkOutDate ;

    List<String> hotelUserName ;



    /**
     * 购买的sku 件数
     * 酒店日历房代表的是房间数量
     */
    Integer skuNumber ;

    /**
     * 如果输入0  第一次进入 就推荐一个skuNumber
     */
    Integer skuRecommend ;

    
    public Integer getSkuNumber() {

        if (skuNumber!=null && skuNumber.intValue()==0 && skuRecommend!=null && skuRecommend.intValue()>0) {
            return  skuRecommend ;
        }

        return skuNumber;
    }

    public Integer getSkuNumberSource() {

        return skuNumber;
    }




}
