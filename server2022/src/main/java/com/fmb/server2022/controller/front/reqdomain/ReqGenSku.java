package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;

import java.util.List;

@Data
public class ReqGenSku {

    /**
     * skuId
     */
    Integer skuId ;
    /**
     * 酒店入住起始日期
     */
    String checkInDate ;
    /**
     * 离店日期
     */
    String checkOutDate ;

    List<String> hotelUserName ;



    /**
     * 购买的sku 件数
     * 酒店日历房代表的是房间数量
     */
    Integer skuNumber ;

    /**
     * 如果输入0  第一次进入 就推荐一个skuNumber
     */
    Integer skuRecommend ;

    
    public Integer getSkuNumber() {

        if (skuNumber!=null && skuNumber.intValue()==0 && skuRecommend!=null && skuRecommend.intValue()>0) {
            return  skuRecommend ;
        }

        return skuNumber;
    }

    public Integer getSkuNumberSource() {

        return skuNumber;
    }

    //改签前订单号
    String sourceOrderSn ;
    Integer reasonVal ;
    String reasonTxt ;
    /**
     * 免补差价 1-勾选 0,不勾选
     */
    Integer notPayMoneyDiff ;

    /**
     *  改签 计算价格差时 是否校验库存的标记
     *  等于1 时不校验库存 
     */
    Integer notCheckSkuNumber ;



}
