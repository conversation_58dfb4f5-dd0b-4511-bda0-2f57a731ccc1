package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取最优优惠券参数载体
 */
@Data
public class ReqCouponBySku implements FrontApiRequestDomain  {
    /**
     * skuId
     */
    @NotNull
    Integer skuId ;

    /**
     * 酒店入住起始日期
     */
    String checkInDate ;

    /**
     * 购买的sku 件数
     */
    @NotNull
    Integer skuNumber ;

    /**
     * 调用来源 wxapp || h5 || app
     */
//    @NotNull
//    String from ;

    /**
     * 用户是否手动选中优惠券
     *  0是 1否
      */
    Integer setDefault;

    /**
     * 选中的优惠券sn
     */
    String couponStr;

    String appid ;
    String opVersion ;
    String version  ;

}
