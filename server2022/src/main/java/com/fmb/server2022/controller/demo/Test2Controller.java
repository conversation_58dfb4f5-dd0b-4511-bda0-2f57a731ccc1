package com.fmb.server2022.controller.demo;

import com.fmb.server2022.config.OnlineConsumerCondition;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@RestController
@RequestMapping("/test")
@Conditional(OnlineConsumerCondition.class)
public class Test2Controller {


    @RequestMapping(value = "/con418.do")
    @ResponseBody
    public HashMap<String, Object> con418() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");
        return resultMap;
    }
}
