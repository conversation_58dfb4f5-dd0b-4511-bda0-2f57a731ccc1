package com.fmb.server2022.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmb_new.entity.FmbAdmin;
import com.fmb.server2022.fmb_new.entity.FmbDepartmentList;
import com.fmb.server2022.fmb_new.service.IFmbAdminService;
import com.fmb.server2022.fmb_new.service.IFmbDepartmentListService;
import com.fmb.server2022.reqdomain.ReqAdmin;
import com.fmb.server2022.service.FmbAdminTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/admin/users")
public class AdminController {


    @Autowired
    private FmbAdminTokenService loginService;
    @Autowired
    private IFmbAdminService adminService;
    @Autowired
    private IFmbDepartmentListService departmentListService;


    /**
     * 后台用户模糊搜索
     * @param key
     * @return
     */
    @RequestMapping(value = "/adminSearch")
    @ResponseBody
    public FmbRespBean adminSearch(@RequestParam(value = "key", required = true) String key) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("adminUsers", loginService.adminSearch(key));

        return FmbRespBean.success(resultMap);
    }

    @GetMapping(value = "/adminByUid")
    public FmbRespBean adminByUid(
            @RequestParam(value = "uid", required = true) Integer uid
    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getUid, uid);
        FmbAdmin dbadmin = adminService.getOne(wr1);
        dbadmin.setPassword(null);
        dbadmin.setSalt(null);
        dbadmin.setToken(null);
        dbadmin.setTokenExpires(null);
        resultMap.put("adminInfo", dbadmin);
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/adminByDepid")
    @ResponseBody
    public FmbRespBean adminByDepid(@RequestBody @Valid ReqAdmin req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        LambdaQueryWrapper<FmbAdmin> wr1 = Wrappers.lambdaQuery(FmbAdmin.class).eq(FmbAdmin::getDepartmentId, req.getDepartmentId());
        List<FmbAdmin> dblist = adminService.list(wr1);
        for(FmbAdmin dbadmin : dblist){
            dbadmin.setPassword(null);
            dbadmin.setSalt(null);
            dbadmin.setToken(null);
            dbadmin.setTokenExpires(null);
        }
        resultMap.put("adminUsers", dblist);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/departmentList")
    @ResponseBody
    public FmbRespBean departmentList(@RequestBody @Valid ReqAdmin req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        LambdaQueryWrapper<FmbDepartmentList> wr1 = Wrappers.lambdaQuery(FmbDepartmentList.class).eq(FmbDepartmentList::getDepartmentId, req.getDepartmentId());
        List<FmbDepartmentList> dblist = departmentListService.list(wr1);
        resultMap.put("departmentList", dblist);

        return FmbRespBean.success(resultMap);
    }

}
