package com.fmb.server2022.controller.front;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.ReqHotel;
import com.fmb.server2022.service.FrontHotelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

/**
 * 酒店相关
 */
@RestController
@RequestMapping("/front/hotel")
public class HotelController extends BaseFrontController {
    @Autowired
    FrontHotelService frontHotelService;

    /**
     * 酒店详情
     */
    @RequestMapping(value = "/hotelDetail")
    @ResponseBody
    public FmbRespBean hotelDetail(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        frontHotelService.getHotelDetail(resultMap, input, nowUser());

        return FmbRespBean.success(resultMap);
    }


    /**
     * 日历房套餐详情
     */
    @RequestMapping(value = "/roomSkuDetail")
    @ResponseBody
    public FmbRespBean roomSkuDetail(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        frontHotelService.getRoomSkuDetail(resultMap, input, nowUser());

        return FmbRespBean.success(resultMap);
    }

    /**
     * 预售房劵套餐详情
     */
    @RequestMapping(value = "/reserveSkuDetail")
    @ResponseBody
    public FmbRespBean reserveSkuDetail(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.getResSkuDetail(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 预售房劵套房型列表
     */
    @RequestMapping(value = "/reserveSkuRoom")
    @ResponseBody
    public FmbRespBean reserveSkuRoom(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.getResSkuDetail(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 日历房低价日历
     */
    @RequestMapping(value = "/hotelSkuDatePrice")
    @ResponseBody
    public FmbRespBean hotelSkuDatePrice(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectHotelDatePrice(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 日历房选择日历
     */
    @RequestMapping(value = "/skuDatePrice")
    @ResponseBody
    public FmbRespBean skuDatePrice(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectHotelSkuDatePrice(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 日历房选择日历
     */
    @RequestMapping(value = "/resSkuDatePrice")
    @ResponseBody
    public FmbRespBean resSkuDatePrice(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectResSkuDatePrice(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 预售房劵套餐列表
     */
    @RequestMapping(value = "/reserveSkuList")
    @ResponseBody
    public FmbRespBean reserveSkuList(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectFrontResSku(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 日历房套餐列表
     */
    @RequestMapping(value = "/roomSkuList")
    @ResponseBody
    public FmbRespBean roomSkuList(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectFrontRoomSku(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

    /**
     * 日历房提交订单sku信息
     */
    @RequestMapping(value = "/orderRoomInfo")
    @ResponseBody
    public FmbRespBean orderRoomInfo(@RequestBody @Valid ReqHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        frontHotelService.selectOrderRoomInfo(resultMap, input, nowUser());
        return FmbRespBean.success(resultMap);
    }

}
