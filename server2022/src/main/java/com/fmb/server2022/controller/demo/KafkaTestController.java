package com.fmb.server2022.controller.demo;

import cn.hutool.core.util.RandomUtil;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.fmbx.service.IFmbxKafkaMessageService;
import com.fmb.server2022.service.kafka.FmbKafkaProducerService;
import com.fmb.server2022.service.redis.KafkaRedisRecorder;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Controller
public class KafkaTestController {


    public static final String TEST_1027 = "test1108_1";
    private static Logger logger = LoggerFactory.getLogger(KafkaTestController.class);



    @Value("${fmb.shutdowncode}")
    String shutdowncode ;


    private static boolean CONSUME_FLAG = true ;


    @Autowired
    IFmbxKafkaMessageService kafkaMessageService ;


    @Autowired
    private KafkaListenerEndpointRegistry registry;


    @Autowired
    @Qualifier("asyncServiceExecutor")
    ThreadPoolExecutor executor;


    @RequestMapping(value = "/testPool")
    @ResponseBody
    public FmbRespBean testPool() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        executor.submit(new Runnable() {
            @Override
            public void run() {
                System.out.println("testPool   ---111");
                try {
                    TimeUnit.SECONDS.sleep(60);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                System.out.println("testPool   ---222");
            }
        }) ;

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/test1027.do")
    @ResponseBody
    public FmbRespBean kafka01() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        int co = 0;
        for (int i = 0; i < 3000; i++) {

            HashMap<String, Object> par = new HashMap<>();
            par.put("now", System.currentTimeMillis());
            par.put("i", i);
            FmbKafkaProducerService.send(TEST_1027,""+i,par);

        }
        resultMap.put("co", co);
        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/test1107.do")
    @ResponseBody
    public FmbRespBean test1107() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        KafkaRedisRecorder.checkNotFinishJob();

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/queueInfo4")
    @ResponseBody
    public FmbRespBean queueInfo4() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("getActiveCount", executor.getActiveCount());
        resultMap.put("getCorePoolSize", executor.getCorePoolSize());
        resultMap.put("getMaximumPoolSize", executor.getMaximumPoolSize());
        resultMap.put("getQueue().size()", executor.getQueue().size());


        return FmbRespBean.success(resultMap);
    }

    //    @KafkaListener(topics = {TEST_1027},groupId = "default" )
//    public void cousumerTest01(ConsumerRecord<String,String> record ){
//
//        Optional<String> kafkaMessage = Optional.ofNullable(record.value());
//        if (kafkaMessage.isPresent()) {
//            String s = kafkaMessage.get();
//            logger.info("autoAck message = {}", s);
//        }
//
//    }
//
//
//    @KafkaListener(topics = {TEST_1027}, groupId = "defaultBatch", containerFactory = "batchFactory")
//    public void cousumerBatch(List<ConsumerRecord<String, String>> datas) {
//
//        if (datas.isEmpty()) {
//            return;
//        }
//        logger.info("dataSize=" + datas.size());
//        for (ConsumerRecord<String, String> data : datas) {
////            logger.info("cousumerBatch threadname={} offset={} message={}",Thread.currentThread().getId(),  data.offset(),data.value());
//            simulateJob();
//        }
//
//    }



    @RequestMapping(value = "/consumerOperate")
    @ResponseBody
    public FmbRespBean consumerOperate(
            @RequestParam(value = "open", defaultValue = "1") Integer open,
            @RequestParam(value = "listenerid", defaultValue = "") String listenerid
            ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        CONSUME_FLAG = 1==open.intValue();
        resultMap.put("CONSUME_FLAG",CONSUME_FLAG) ;


        final Set<String> containerIds = registry.getListenerContainerIds();
        resultMap.put("containerIds",containerIds) ;

//        final Collection<MessageListenerContainer> containers = registry.getAllListenerContainers();
//        final List<String> collect = containers.stream().map(c -> {
//            return c.getListenerId();
//        }).collect(Collectors.toList());
//
//        resultMap.put("getListenerIds",collect) ;

        if (containerIds.contains(listenerid)) {
            MessageListenerContainer messageListenerContainer = registry
                    .getListenerContainer(listenerid);
            if (Objects.nonNull(messageListenerContainer)) {
                if (CONSUME_FLAG) {
                    if (!messageListenerContainer.isRunning()) {
                        messageListenerContainer.start();
                        resultMap.put("result", "start");
                    } else  if (messageListenerContainer.isContainerPaused()) {
                            messageListenerContainer.resume();
                            resultMap.put("result", "resume");
                    }
                } else if (!messageListenerContainer.isContainerPaused()) {
                        messageListenerContainer.pause();
                        resultMap.put("result", "pause");
                }
            }
        }

        return FmbRespBean.success(resultMap);
    }



    /**
     * 需要手动 提交的 批量消费
     * @param records
     * @param acknowledgment
     */
    @KafkaListener( id = "cousumerBatchNeedAck", topics = {TEST_1027}, groupId = "defaultBatchAck", containerFactory = "manualBatchAck")
    public void cousumerBatchNeedAck(List<ConsumerRecord<String, String>> records , Acknowledgment acknowledgment) {

//        logger.info("--->>run1 threadName={}",Thread.currentThread().getName());
//        if (!CONSUME_FLAG ) {
//            return;
//        }

        if (records.isEmpty()) {
            return;
        }
        final long start = System.currentTimeMillis();
        final int size = records.size();
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        final CountDownLatch countDownLatch = new CountDownLatch(size);
        for (ConsumerRecord<String, String> r : records) {


            executor.submit(new FmbKafkaWorkerNeedAck(countDownLatch, r,consumerGroupId ,kafkaMessageService) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    simulateJob();
//                    System.out.println(record.value());
                }
            });
        }
        try {
            //一批任务最多等4秒
            countDownLatch.await(4,TimeUnit.SECONDS) ;
        } catch (InterruptedException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }finally {
            acknowledgment.acknowledge();
            logger.info("acknowledge_offset {}" , records.get(size-1).offset() );
        }
        logger.info("needack groupID = {} consumer.commitSync_use {} millionSec, size={}  speed={} mill/条",KafkaUtils.getConsumerGroupId(),
                System.currentTimeMillis()-start,size ,
                (System.currentTimeMillis()-start)*1D/size);
    }

    /**
     * 不需要手动 提交的 批量消费
     * @param records
     */
//    @KafkaListener(topics = {TEST_1027}, groupId = "cousumerBatchAutoAck", containerFactory = "autoBatchAck")
    public void cousumerBatchAutoAck(List<ConsumerRecord<String, String>> records ) {
        if (records.isEmpty()) {
            return;
        }
        final long start = System.currentTimeMillis();
        final int size = records.size();
        final String consumerGroupId = KafkaUtils.getConsumerGroupId();
        for (ConsumerRecord<String, String> r : records) {
            executor.submit(new FmbKafkaWorkerAutoAck( r,consumerGroupId) {
                @Override
                public void doBusinessJob(ConsumerRecord<String, String> record) {
                    simulateJob();
//                    logger.info(record.value());
                }
            });
        }
//        logger.info("autoack groupID = {} consumer.commitSync_use {} millionSec, size={}  speed={} mill/条",KafkaUtils.getConsumerGroupId(),
//                System.currentTimeMillis()-start,size ,
//                (System.currentTimeMillis()-start)*1D/size);
    }

//    @PostConstruct
//    public void kafkaManualAckTest(){
//        logger.info("kafkaManualAckTest run");
//
//
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//
//                Properties props = new Properties();
//                props.setProperty("bootstrap.servers", "192.168.30.225:9092");
//                props.setProperty("group.id", "default");
//                props.setProperty("enable.auto.commit", "false");
//                props.setProperty("max.poll.records", "100");
//                props.setProperty("max.poll.interval.ms", "60000");
//                props.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//                props.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//                KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
//                consumer.subscribe(Arrays.asList(TEST_1027));
//                while (true) {
//                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(500));
//                    if (records.isEmpty()) {
////                        logger.info("no record");
//                        continue;
//                    }
//                    final CountDownLatch countDownLatch = new CountDownLatch(records.count());
//                    for (ConsumerRecord<String, String> r : records) {
//
//                        executor.submit(new FmbKafkaWorker(countDownLatch, r) {
//                            @Override
//                            public void doBusinessJob(ConsumerRecord<String, String> record) {
//
//                                simulateJob();
//
////                                logger.info(" record {}  done ",record.value() );
//                            }
//                        }) ;
//                    }
//                    try {
//                        countDownLatch.await();
//                    } catch (InterruptedException e) {
//                    }
//                    consumer.commitSync();
//                    logger.info("consumer.commitSync()");
//
//                }
//            }
//        },"kafkaManualAckTest").start();
//
//
//    }


    public void simulateJob() {
        try {
            TimeUnit.MILLISECONDS.sleep(RandomUtil.randomInt(300)+1);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


}
