package com.fmb.server2022.controller.demo;

import cn.hutool.core.net.NetUtil;
import com.fmb.basic.FmbRespBean;
import com.fmb.util.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Controller
public class ShutdownController implements ApplicationContextAware {

    private ApplicationContext context;

    @Value("${fmb.shutdowncode}")
    String shutdowncode ;

    @Autowired
    private KafkaListenerEndpointRegistry registry;


    /**
     * 停止或开启所有kafka 消费者 .
     * @param opkey
     * @param operate 1-默认关闭  2-开启
     * @return
     */
    @RequestMapping(value = "/stopAllKafkaConsumer/{opkey}")
    @ResponseBody
    public FmbRespBean stopAllKafkaConsumer( @PathVariable(value="opkey") String opkey
                                             , @RequestParam(value = "operate", defaultValue = "1") String operate

    ) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        if (shutdowncode.equals(opkey)) {
            final ArrayList<String> strings = new ArrayList<>();
            registry.getListenerContainerIds().stream().forEach(id->{
                MessageListenerContainer messageListenerContainer = registry.getListenerContainer(id);

                if ("1".equals(operate)) {
                    if (messageListenerContainer != null) {
                        messageListenerContainer.stop();
                        strings.add(id +" stop");
                    }
                }else if("2".equals(operate)) {
                    if( !messageListenerContainer.isRunning()) {
                        messageListenerContainer.start();
                        strings.add(id +" start");
                    }
                }

            });
            resultMap.put("opresult",strings) ;
            resultMap.put("stopAllKafkaConsumer","ok") ;
        }
        return FmbRespBean.success(resultMap);
    }



    /**
     *
     * @param code 验证密码. 当code正确才执行停机操作
     * @return
     */
    @RequestMapping(value = "/shutdownContext")
    @ResponseBody
    public Map<String, Object> shutdown(@RequestParam(value = "code", defaultValue = "") String code
    , HttpServletRequest request
    ) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        //验证码一致  而且 是内网ip
        if(StringUtils.isNotBlank(code)  && shutdowncode.equals(MD5Util.md5( code))
                && checkRequest(request)
                ){
            resultMap.put("shutdown", 1);
//            System.out.println("do close");
            ((ConfigurableApplicationContext) context).close();
        }else {
            resultMap.put("shutdown", 0);
        }
        resultMap.put("address",request.getRemoteAddr()) ;
        return resultMap;
    }

    /**
     *  1. 读取请求头里的 x-real-ip (忽略大小写) 对应值看是否是内网ip
     *  2. 1不满足就直接用  request.getRemoteAddr 获取地址
     * @param request
     * @return
     */
    private boolean checkRequest(HttpServletRequest request) {


        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            String value = request.getHeader(name);
            if ("x-real-ip".equals( name.toLowerCase()) && NetUtil.isInnerIP(value)) {
                return  true ;
            }
        }
        String remoteAddr = request.getRemoteAddr();
        if (NetUtil.isInnerIP(remoteAddr) && !"127.0.0.1".equals(remoteAddr)) {

            return true ;
        }

        return false ;
    }

    @Override
    public void setApplicationContext(ApplicationContext ctx) throws BeansException {
        this.context = ctx;

    }
}