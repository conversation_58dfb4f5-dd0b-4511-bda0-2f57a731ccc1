package com.fmb.server2022.controller.demo;

import com.fmb.basic.FmbConstants;
import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.service.CrontabService;
import com.fmb.server2022.service.redis.FmbRedisService;
import com.fmb.util.SnowflakeIdWorker;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
public class RedisTestController {


    private static Logger logger = LoggerFactory.getLogger(RedisTestController.class);

    @Autowired
    JedisPool jedisPool ;

    @Autowired
    RedissonClient redissonClient ;


    @Autowired
    FmbRedisService redisService ;




    /**
     * 基础redis应用
     * @return
     */
    @RequestMapping(value = "/redis01.do")
    @ResponseBody
    public FmbRespBean redis01() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        Jedis jedis = null;


        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            String keyname = "joblock";

            String setex = jedis.setex(keyname, 10, "");

            System.out.println(setex);

            jedis.set("now",System.currentTimeMillis()+"") ;



        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        resultMap.put("data", "");
        return FmbRespBean.success(resultMap);
    }


    AtomicInteger  atomicInteger = new  AtomicInteger() ;
    /**
     * 等待特定时间的锁
     * @return
     */
    @RequestMapping(value = "/redis02.do")
    @ResponseBody
    public FmbRespBean redis02() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        RLock stockLock = redissonClient.getLock("stock");

        try {
//            boolean b = stockLock.tryLock(1,5, TimeUnit.SECONDS);
            boolean b = stockLock.tryLock(900, TimeUnit.MILLISECONDS);
            if (b) {
                try{

                    TimeUnit.MILLISECONDS.sleep(1000);
                    System.out.println(StringUtils.leftPad(""+ atomicInteger.addAndGet(1) ,6,"0") + " done");
                    resultMap.put("data", "locked" );

                }catch (Exception e){
                }finally {
                    stockLock.unlock();
                }

            }else {
                resultMap.put("data", "nolock" );
                System.out.println("nolock");
            }

        } catch (InterruptedException ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid_" + errorid + "#" + ex.getMessage(), ex);
        }

        return FmbRespBean.success(resultMap);
    }

    /**
     * 普通锁
     * @return
     */
    @RequestMapping(value = "/redis03.do")
    @ResponseBody
    public FmbRespBean redis03() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        RLock stockLock = redissonClient.getLock("stock");

        if (stockLock.tryLock()) {
            try {
                TimeUnit.MILLISECONDS.sleep(1000);
                resultMap.put("data", "locked");

                System.out.println(StringUtils.leftPad(""+ atomicInteger.addAndGet(1) ,6,"0") + " done");
            } catch (Exception ex) {
                long errorid = SnowflakeIdWorker.getInstance().nextId();
                logger.error("errorid " + errorid , ex);
            } finally {
                stockLock.unlock();
            }
        } else {
            resultMap.put("data", "nolock");
            System.out.println("nolock");
        }

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/redis07.do")
    @ResponseBody
    public FmbRespBean redis07(@RequestParam(value = "size", defaultValue = "1") Integer size) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        // 1、 声明一个限流器
        String key = "testLimit";

        redisService.doRedisJob(jedis -> jedis.del(key));

        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);

        // 2、 设置速率，5秒中产生3个令牌
        rateLimiter.trySetRate(RateType.OVERALL, size, 10, RateIntervalUnit.SECONDS);



        return FmbRespBean.success(resultMap);
    }
    
    /**
     * 限流
     * @return
     */
    @RequestMapping(value = "/redis04.do")
    @ResponseBody
    public FmbRespBean redis04() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        // 1、 声明一个限流器
        String key = "testLimit";
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);

        // 2、 设置速率，5秒中产生3个令牌
        rateLimiter.trySetRate(RateType.OVERALL, 5, 10, RateIntervalUnit.SECONDS);
//        rateLimiter.trySetRate(RateType.PER_CLIENT, 5, 10, RateIntervalUnit.SECONDS);

        // 3、试图获取一个令牌，获取到返回true
        boolean acquire = rateLimiter.tryAcquire(1);

        if (acquire) {

            resultMap.put("result","got it");

//            redisService.expire(key,300);
            System.out.println(StringUtils.leftPad(""+ atomicInteger.addAndGet(1) ,6,"0") + " done");

        } else {
//            System.out.println("failcounter=" + counterFail.incrementAndGet());
            resultMap.put("result","not got it");

            System.out.println("no_get");
        }


        return FmbRespBean.success(resultMap);
    }


    /**
     * redis 操作 简单封装
     * @return
     */
    @RequestMapping(value = "/redis05.do")
    @ResponseBody
    public FmbRespBean redis004() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("data", "");

        HashMap<String, Object> map = new HashMap<>();
        redisService.doRedisJobWithMap((jedis, mapinput)->{


        },map);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/redis06.do")
    @ResponseBody
    public HashMap<String, Object> redis06() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

//        String nowClassNameAndMethord = currentClassNameAndMethodName("redis06");


        String nowClassNameAndMethord = "";
        for (StackTraceElement element : Thread.currentThread().getStackTrace()) {
            if (element.getClassName().startsWith("com.fmb") ) {

                nowClassNameAndMethord = element.getClassName()+":"+element.getMethodName();
                break;
            }
        }


        System.out.println("----"+nowClassNameAndMethord);
        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);


            String script = " local num=redis.call('setnx',KEYS[1],'') " +
                    "if tonumber(num)==1 then " +
                    "    redis.call('EXPIRE',KEYS[1],ARGV[1]) " +
                    "    return 1 " +
                    "else " +
                    "    return 0" +
                    " end";

            String eval = jedis.eval(script, Arrays.asList(nowClassNameAndMethord), Arrays.asList("20")).toString();

            System.out.println(eval);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return resultMap;
    }

//    private String currentClassNameAndMethodName(String redis06) {
//        String nowClassNameAndMethord = "";
//        for (StackTraceElement element : Thread.currentThread().getStackTrace()) {
//            if (element.getClassName().startsWith("com.fmb") && element.getMethodName().equals(redis06)) {
//
//                nowClassNameAndMethord = element.getClassName()+":"+element.getMethodName();
//                break;
//            }
//        }
//        return nowClassNameAndMethord;
//    }


    @RequestMapping(value = "/ding10")
    @ResponseBody
    public FmbRespBean ding10() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        redisService.ding10(resultMap) ;

        return FmbRespBean.success(resultMap);
    }
    @RequestMapping(value = "/ding11")
    @ResponseBody
    public FmbRespBean ding11() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        redisService.getTime2Evict() ;

        return FmbRespBean.success(resultMap);
    }


//    @RequestMapping(value = "/redisLock")
//    @ResponseBody
//    public FmbRespBean redisLock() {
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//
//        boolean runLock = crontabService.getRunLock("xxxx", "methord", 60);
//
//        System.out.println(runLock);
//
//        return FmbRespBean.success(resultMap);
//    }


    @RequestMapping(value = "/redisSet1")
    @ResponseBody
    public FmbRespBean redisSet1() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        Jedis jedis = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(FmbConstants.REDIS_DEFAULT_DB);

            jedis.sadd("set1",Arrays.asList("1","2","3").toArray(new String[]{}));
            jedis.sadd("set2",Arrays.asList("2","3","4").toArray(new String[]{}));

            final Set<String> sdiff = jedis.sdiff("set2", "set1");
//            System.out.println(sdiff);
            resultMap.put("sdiff",sdiff);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }

        return FmbRespBean.success(resultMap);
    }
}
