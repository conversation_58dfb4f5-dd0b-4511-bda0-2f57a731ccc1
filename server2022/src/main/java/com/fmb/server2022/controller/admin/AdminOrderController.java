package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.ReqGenSku;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.domain.AdminOrder;
import com.fmb.server2022.reqdomain.ReqFmbOrderConfirm;
import com.fmb.server2022.reqdomain.ReqFmbOrderHotel;
import com.fmb.server2022.reqdomain.ReqHotelMail;
import com.fmb.server2022.service.order.KefuOrderService;
import com.fmb.server2022.service.order.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.server2022.service.order.OrderService.ADMIN001;

@RestController
@RequestMapping("/admin/order")
public class AdminOrderController {


    @Autowired
    OrderService orderService ;
    @Autowired
    KefuOrderService kefuOrderService ;


    @RequestMapping(value = "/orderDetail")
    @ResponseBody
    public FmbRespBean orderDetail(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        ReqorderDetail inputx = new ReqorderDetail();
        inputx.setAppid("");
        inputx.setOpVersion("");
        inputx.setVersion("");
        inputx.setOrderSn(input.getOrderSn());

        orderService.orderDetail(resultMap, inputx,null,ADMIN001);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/orderChangeBegin")
    @ResponseBody
    public FmbRespBean orderChangeBegin(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        orderService.orderChangeBegin(resultMap, input,ADMIN001);

        return FmbRespBean.success(resultMap);
    }
    @RequestMapping(value = "/orderChangePreview")
    @ResponseBody
    public FmbRespBean orderChangePreview(@RequestBody @Valid ReqGenSku input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        input.setNotCheckSkuNumber(1);
        orderService.orderChangePreview(resultMap, input,ADMIN001);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/orderChangeSubmit")
    @ResponseBody
    public FmbRespBean orderChangeSubmit(@RequestBody @Valid ReqGenSku input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        orderService.orderChangeSubmit(resultMap, input,ADMIN001);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/orderChangeInfos")
    @ResponseBody
    public FmbRespBean orderChangeInfos(@RequestBody @Valid ReqGenSku input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();


        orderService.orderInfoOfChange(resultMap, input);

        return FmbRespBean.success(resultMap);
    }
    @RequestMapping(value = "/orderChangeExtInfo")
    @ResponseBody
    public FmbRespBean changeGoodsExtInfo() {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        String example = "{\n" +
                "    \"skuType\": 1,\n" +
                "    \"reqGenSku\": {\n" +
                "        \"skuId\": 2075,\n" +
                "        \"checkInDate\": \"2023-07-13\",\n" +
                "        \"checkOutDate\": \"2023-07-14\",\n" +
                "        \"hotelUserName\": [\n" +
                "            \"侯友法\"\n" +
                "        ],\n" +
                "        \"skuNumber\": 1\n" +
                "    },\n" +
                "    \"fmbxSku\": {\n" +
                "        \"skuId\": 2075,\n" +
                "        \"skuName\": \"房券修改8(大床房)\",\n" +
                "        \"xaid\": 105969,\n" +
                "        \"bpsId\": 263,\n" +
                "        \"bpId\": 61,\n" +
                "        \"skuType\": 1\n" +
                "    },\n" +
                "    \"suite\": {\n" +
                "        \"suiteId\": 1375,\n" +
                "        \"xaid\": 105969,\n" +
                "        \"name\": \"房券修改8\",\n" +
                "        \"suiteContent\": \"[{\\\"type\\\":\\\"食\\\",\\\"count\\\":1,\\\"intro\\\":\\\"1\\\",\\\"content\\\":\\\"1\\\"},{\\\"type\\\":\\\"享\\\",\\\"count\\\":1,\\\"intro\\\":\\\"2\\\",\\\"content\\\":\\\"2\\\"}]\",\n" +
                "        \"totalNeight\": 1,\n" +
                "        \"nightMin\": 1,\n" +
                "        \"flagStandardHotel\": 1,\n" +
                "        \"flagHotelReserve\": 1,\n" +
                "        \"hotelReserveSplit\": 1,\n" +
                "        \"buyLimit\": 1,\n" +
                "        \"preReserveDay\": 2,\n" +
                "        \"preReserveTime\": \"12:30\",\n" +
                "        \"autoCloseOrderSecond\": 7200,\n" +
                "        \"certFlag\": 0,\n" +
                "        \"certTip\": \"\",\n" +
                "        \"flagRiskWarning\": 0,\n" +
                "        \"riskWarningTip\": \"\",\n" +
                "        \"isReturn\": 0,\n" +
                "        \"returnPolicy\": \"\",\n" +
                "        \"returnValidDay\": 2,\n" +
                "        \"returnValidTime\": \"12:30\",\n" +
                "        \"returnValidSecond\": 214200,\n" +
                "        \"isInvoice\": 1,\n" +
                "        \"versionNum\": 0\n" +
                "    },\n" +
                "    \"roomSku\": {\n" +
                "        \"skuId\": 2075,\n" +
                "        \"skuName\": \"房券修改8(大床房)\",\n" +
                "        \"roomName\": \"大床房\",\n" +
                "        \"suiteId\": 1375,\n" +
                "        \"roomId\": 149,\n" +
                "        \"flagSell\": 1,\n" +
                "        \"status\": 1,\n" +
                "        \"versionNum\": 1,\n" +
                "        \"sellNum\": 2,\n" +
                "        \"sellNumTotal\": 2\n" +
                "    },\n" +
                "    \"priceList\": [\n" +
                "        {\n" +
                "            \"datecol\": 1689177600000,\n" +
                "            \"marketPrice\": 0,\n" +
                "            \"goodsPrice\": 30,\n" +
                "            \"settlePrice\": 20,\n" +
                "            \"roomId\": 149,\n" +
                "            \"skuId\": 2075,\n" +
                "            \"stockNum\": 30,\n" +
                "            \"stockStatus\": 1,\n" +
                "            \"priceId\": 35763,\n" +
                "            \"stockId\": 10005,\n" +
                "            \"versionNum\": 1,\n" +
                "            \"havePrice\": true,\n" +
                "            \"haveStock\": true\n" +
                "        }\n" +
                "    ],\n" +
                "    \"marketPrice\": 0,\n" +
                "    \"marketPriceAll\": 0,\n" +
                "    \"goodsPrice\": 30,\n" +
                "    \"goodsPriceNoSub\": 30,\n" +
                "    \"settlePriceOfHotelCodeSubAll\": 0,\n" +
                "    \"settlePriceOfHotelCodeAll\": 0,\n" +
                "    \"goodsPriceAll\": 30,\n" +
                "    \"settlePrice\": 20,\n" +
                "    \"settlePriceAll\": 20,\n" +
                "    \"totalHotelCodeSubMoney\": 0,\n" +
                "    \"totalHotelCodeSubMoneyOfEach\": 0,\n" +
                "    \"skuNumber\": 1\n" +
                "}" ;

//        orderService.changeGoodsExtInfo(example,null,null,null, null,null);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/saveOrderHotel")
    @ResponseBody
    public FmbRespBean saveOrderHotel(@RequestBody @Valid ReqFmbOrderHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        kefuOrderService.saveFmbOrderHotel(resultMap, input);

        return FmbRespBean.success(resultMap);
    }


    @RequestMapping(value = "/sendOrderHotelMail")
    @ResponseBody
    public FmbRespBean sendOrderHotelMail(@RequestBody @Valid ReqHotelMail input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        kefuOrderService.sendOrderHotelMail(resultMap,input);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/getOrderHotelMail")
    @ResponseBody
    public FmbRespBean getOrderHotelMail(@RequestBody @Valid ReqFmbOrderHotel input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        kefuOrderService.getOrderHotelMail(resultMap,input);

        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/addOrderConfirm")
    @ResponseBody
    public FmbRespBean addOrderConfirm(@RequestBody @Valid ReqFmbOrderConfirm input){
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        kefuOrderService.addKefuConfirm(resultMap,input);
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/addKefuEditorNote")
    @ResponseBody
    public FmbRespBean addKefuEditorNote(@RequestBody @Valid ReqFmbOrderConfirm input){
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        kefuOrderService.addKefuEditorNote(resultMap,input);
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/recheckOrderConfirm")
    @ResponseBody
    public FmbRespBean recheckOrderConfirm(@RequestBody @Valid ReqFmbOrderHotel input){
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        kefuOrderService.recheckOrderConfirm(resultMap,input);
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/changeGoodsSettlePrice")
    @ResponseBody
    public FmbRespBean changeGoodsSettlePrice(@RequestBody @Valid ReqFmbOrderHotel input){
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        kefuOrderService.changeGoodsSettlePrice(resultMap,input);
        return FmbRespBean.success(resultMap);
    }

}
