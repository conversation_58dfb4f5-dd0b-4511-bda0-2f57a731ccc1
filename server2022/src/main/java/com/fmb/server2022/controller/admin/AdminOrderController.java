package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.controller.front.reqdomain.ReqorderDetail;
import com.fmb.server2022.domain.AdminOrder;
import com.fmb.server2022.service.order.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

import static com.fmb.server2022.service.order.OrderService.ADMIN001;

@RestController
@RequestMapping("/admin/order")
public class AdminOrderController {


    @Autowired
    OrderService orderService ;


    @RequestMapping(value = "/orderDetail")
    @ResponseBody
    public FmbRespBean orderDetail(@RequestBody @Valid AdminOrder input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        ReqorderDetail inputx = new ReqorderDetail();
        inputx.setAppid("");
        inputx.setOpVersion("");
        inputx.setVersion("");
        inputx.setOrderSn(input.getOrderSn());

        orderService.orderDetail(resultMap, inputx,null,ADMIN001);

        return FmbRespBean.success(resultMap);
    }
//
//    @RequestMapping(value = "/getOrderHotelEmailTemp")
//    @ResponseBody
//    public void getOrderHotelEmailTemp() {
//        String rs = orderService.getOrderHotelEmailTemp();
//    }


}
