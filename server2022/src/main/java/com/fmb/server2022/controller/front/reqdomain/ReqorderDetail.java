package com.fmb.server2022.controller.front.reqdomain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date: 2023/1/30 2:48 下午
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ReqorderDetail implements FrontApiRequestDomain  {

    String orderSn ;

    //发起 房券发起退款选择的房券编码
    ArrayList<String> reserveCode ;
    //退款原因
    String reason ;
    String reasonUserInput ;
    // 退款金额
    BigDecimal returnMoney ;

    String appid ;
    String opVersion ;
    String version  ;


    //取消订单 开始
    int  cancelId ;
    String mess;
    //取消订单 结束

    int suiteNightTotal ;
    int suiteMinNight ;
    int nightNum ;


    int adminReturn ;
    int adminUid ;

    public boolean isAdminReturn (){
        return 1==adminReturn ;
    }


}
