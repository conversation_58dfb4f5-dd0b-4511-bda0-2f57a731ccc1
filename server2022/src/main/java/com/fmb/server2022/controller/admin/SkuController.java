package com.fmb.server2022.controller.admin;

import com.fmb.basic.FmbRespBean;
import com.fmb.server2022.domain.suite.HotelReserveSkuInfo;
import com.fmb.server2022.domain.suite.SuiteInfo;
import com.fmb.server2022.reqdomain.ReqHotelSku;
import com.fmb.server2022.service.SkuService;
import com.fmb.util.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;

@RestController
@RequestMapping("/admin/sku")
public class SkuController {
    @Autowired
    SkuService skuService ;

    /**
     * 新增套餐
     * @param input
     * @return
     */
    @RequestMapping(value = "/addSuite")
    @ResponseBody
    public FmbRespBean addSuite(@RequestBody @Valid SuiteInfo input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.addSuite(resultMap,input) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 修改套餐
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateSuite")
    @ResponseBody
    public FmbRespBean updateSuite(@RequestBody @Valid SuiteInfo input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.updateSuite(resultMap,input) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 套餐详情
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/suiteDetail" )
    @ResponseBody
    public FmbRespBean suiteDetail(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.suiteDetail(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 复制套餐
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/copySuite" )
    @ResponseBody
    public FmbRespBean copySuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.copySuite(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 屏蔽套餐
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/toggleSuite" )
    @ResponseBody
    public FmbRespBean toggleSuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.toggleSuite(resultMap,suiteId,2) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 上架套餐
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/toggleOnSuite" )
    @ResponseBody
    public FmbRespBean toggleOnSuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.toggleSuite(resultMap,suiteId,1) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 删除套餐
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/deleteSuite" )
    @ResponseBody
    public FmbRespBean deleteSuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.deleteSuite(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 下架sku
     * @param skuId
     * @return
     */
    @RequestMapping(value = "/toggleSku" )
    @ResponseBody
    public FmbRespBean toggleSku(@RequestParam(value = "skuId", required = true) Integer skuId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.toggleSku(resultMap,skuId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 删除sku
     * @param skuId
     * @return
     */
    @RequestMapping(value = "/deleteSku" )
    @ResponseBody
    public FmbRespBean deleteSku(@RequestParam(value = "skuId", required = true) Integer skuId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.deleteSku(resultMap,skuId) ;
        return FmbRespBean.success(resultMap);
    }

    @RequestMapping(value = "/getRoomNightFactors" )
    @ResponseBody
    public FmbRespBean getRoomNightFactors(@RequestParam(value = "roomNight", required = true) Integer roomNight) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("roomNightFactors",Convert.getFactors(roomNight)) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 修改套餐
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateHotelReserve")
    @ResponseBody
    public FmbRespBean updateHotelReserve(@RequestBody @Valid HotelReserveSkuInfo input) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.updateHotelReserve(resultMap,input) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 开关日历房
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/enabledStandardHotel" )
    @ResponseBody
    public FmbRespBean enabledStandardHotelBySuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.enabledStandardHotelBySuite(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 开关房劵
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/enabledHotelReserve" )
    @ResponseBody
    public FmbRespBean enabledHotelReserveBySuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.enabledHotelReserveBySuite(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 是否首次开启房劵
     * @param suiteId
     * @return
     */
    @RequestMapping(value = "/isFirstHotelReserveBySuite" )
    @ResponseBody
    public FmbRespBean isFirstHotelReserveBySuite(@RequestParam(value = "suiteId", required = true) Integer suiteId) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();

        skuService.isFirstHotelReserveBySuite(resultMap,suiteId) ;
        return FmbRespBean.success(resultMap);
    }

    /**
     * 房劵所属套餐列表
     */
    @RequestMapping(value = "/selectHotelReserveSkuMenu")
    @ResponseBody
    public FmbRespBean hotelSku(@RequestBody @Valid ReqHotelSku req) {
        HashMap<String, Object> resultMap = new HashMap<String, Object>();
        skuService.selectHotelReserveSkuMenu(resultMap, req);
        return FmbRespBean.success(resultMap);
    }

}
