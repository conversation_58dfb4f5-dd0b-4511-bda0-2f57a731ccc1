package com.fmb.server2022.controller.front.reqdomain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

@Data
public class ReqHotel implements FrontApiRequestDomain {
    String appid ;
    String opVersion ;
    String version  ;

    /**
     * 活动id
     */
    Integer xaid ;

    /**
     * 1日历房 2预售房券
     */
    Integer type;

    /**
     * skuid
     */
    Integer skuId ;

    Integer suiteId;

    //商家id
    private  Integer bpsId;

    private Integer roomId ;

    private Set<String> roomIds ;

    private Set<Integer> skuIds ;

    BigDecimal resGoodPrice;

    String playDate;
    String leaveDate;

    LocalDate priceStartDate;
    LocalDate priceEndDate;

    LocalDate showStartDate;
    LocalDate showEndDate;

    Double longitude;
    Double latitude;

}
