package com.fmb.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fmb.basic.FmbConstants;
import com.fmb.server2022.config.FmbServerConfig;
import com.fmb.server2022.reqdomain.DingdingReq;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.UUID;

import static com.fmb.basic.FmbConstants.LOG_TRACEID;

/**
 * <AUTHOR>
 * @Date: 2023/7/19 11:27 上午
 */
public class FmbCommonUtil {

    private static Logger logger = LoggerFactory.getLogger(FmbCommonUtil.class);

    /**
     * 有的接口 需要进行直接访问
     * 需要基本的安全校验
     * 使用当前时间的时间戳 3分钟内有效
     * 做md5校验
     * @param input
     * @return
     */
    public static boolean reqCheck(DingdingReq input) {
        return StringUtils.isNotBlank(input.getSign()) && Math.abs(System.currentTimeMillis() - input.getMill()) < 3 * 1000 * 60L && input.getSign().equals(MD5Util.md5("" + input.getMill() + FmbConstants.PHP_MD5_SALT_KEY));
    }


    public static void addLogTraceId(){
        try {
            if (MDC.get(LOG_TRACEID)==null) {
                MDC.put(LOG_TRACEID, UUID.randomUUID().toString().replace("-", "").substring(0, 12));
            }
        }catch (Exception ex){
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

    }


    public static String createShortUrl(String url){

        HashMap<String, Object> p = new HashMap<>();
        p.put("string", url);

        final String post = HttpUtil.post("http://m.fumubang." + FmbServerConfig.netOrCom() + "/mobile/ajax/create_shorturl", p);

        final JSONObject parse = JSONUtil.parseObj(post);
        if (parse != null && 200==parse.getInt("code")  ) {
            return parse.getByPath("data.url",String.class) ;
        }

        return "" ;

    }

}
