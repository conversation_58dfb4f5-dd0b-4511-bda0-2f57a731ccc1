package com.fmb.util;

import com.fmb.basic.FmbConstants;
import com.fmb.server2022.fmbx.entity.FmbOrderInfo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/6/12 11:01 上午
 */
public class FmbOrderInfoUtil {


    /**
     * 是否是 新系统订单
     * @param orderInfo
     * @return
     */
    public static boolean isNewSysOrder(FmbOrderInfo orderInfo) {

        return  orderInfo.getGoodsType().intValue()==FmbConstants.SKU_HOTEL_DATE_GOODS_TYPE || orderInfo.getGoodsType().intValue()==FmbConstants.SKU_HOTEL_RESERVE_GOODS_TYPE;

    }

    /**
     * 老系统订单计算订单实际支付金额
     * @param orderInfo
     * @return
     */
    public static BigDecimal oldOrderRealPayMoney(FmbOrderInfo orderInfo) {
        //老订单 订单实际支付金额获取
//        static function get_order_pay_money($order_info=array()){
//            //优惠券折扣金额
//            $coupon_discount_money = intval($order_info['discount_rate']) > 0 ? $order_info['money']*(100-$order_info['discount_rate'])/100 : 0;
//            //文惠券金额
//            $cultural_coupon_money = intval($order_info['cultural_coupon_money']) > 0 ? intval($order_info['cultural_coupon_money']) : 0;
//            $cash_money = $order_info['cash_money'] > 0 ? $order_info['cash_money'] : 0;
//
//            $pay_money = number_format($order_info['money']+$order_info['shipping_fee']-$order_info['coupon_money']-$order_info['app_sub_money']-$coupon_discount_money-$cultural_coupon_money-$cash_money, 2, '.', '') ;
//            return $pay_money;
//        }

        BigDecimal coupon_discount_money = orderInfo.getDiscountRate()>0?
                orderInfo.getMoney().multiply(new BigDecimal(100-orderInfo.getDiscountRate())).divide(new BigDecimal(100),2,
                        BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO ;

        BigDecimal cultural_coupon_money = orderInfo.getCulturalCouponMoney()>0? new BigDecimal(orderInfo.getCulturalCouponMoney()):
                BigDecimal.ZERO ;
        BigDecimal cash_money = orderInfo.getCashMoney().compareTo(BigDecimal.ZERO)==1? orderInfo.getCashMoney():
                BigDecimal.ZERO ;

        BigDecimal subCoupon = BigDecimal.ZERO;
        if (coupon_discount_money.compareTo(BigDecimal.ZERO)==1) {
            subCoupon = coupon_discount_money ;
        }else  if(orderInfo.getCouponMoney()>0){
            subCoupon = new BigDecimal(orderInfo.getCouponMoney()) ;
        }


        BigDecimal pay_money =
                orderInfo.getMoney().add(orderInfo.getShippingFee())
                        .subtract(new BigDecimal( orderInfo.getAppSubMoney()))
                        .subtract(subCoupon)
                        .subtract(cultural_coupon_money).subtract(orderInfo.getCashMoney()) ;

        return pay_money ;
    }


    /**
     * 父母邦优惠券使用金额
     * @param orderInfo
     * @return
     */
    public static BigDecimal orderCouponMoney(FmbOrderInfo orderInfo) {

        BigDecimal coupon_discount_money = orderInfo.getDiscountRate()>0?
                orderInfo.getMoney().multiply(new BigDecimal(100-orderInfo.getDiscountRate())).divide(new BigDecimal(100),2,
                        BigDecimal.ROUND_HALF_UP):BigDecimal.ZERO ;

        BigDecimal couponMoney =  coupon_discount_money.compareTo(BigDecimal.ZERO)==1 ?coupon_discount_money:
                new BigDecimal( orderInfo.getCouponMoney() );
        return couponMoney ;
    }


    public static String orderPaySource(FmbOrderInfo orderInfo){
        if(FmbConstants.FMB_PAY_SOURCE.containsKey(orderInfo.getPaySource())){
            return FmbConstants.FMB_PAY_SOURCE.get(orderInfo.getPaySource());
        }
        return orderInfo.getPaySource() ;

    }

}
