package com.fmb.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/4/27 11:06 下午
 */
public class CdnClear {

    private static Logger logger = LoggerFactory.getLogger(CdnClear.class);
    public static void clear(List<String> paths) {

        com.aliyun.cdn20180510.Client client = null;
        try {
            client = createClient("LTAI5tGssi58RHxUqB83t8ve", "******************************");
        } catch (Exception ex) {

            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        final String collectStr = paths.stream().collect(Collectors.joining("\r\n"));

        com.aliyun.cdn20180510.models.RefreshObjectCachesRequest refreshObjectCachesRequest = new com.aliyun.cdn20180510.models.RefreshObjectCachesRequest()
                .setObjectType("File")
                .setObjectPath(collectStr);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.refreshObjectCachesWithOptions(refreshObjectCachesRequest, runtime);
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

    }


    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.cdn20180510.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "cdn.aliyuncs.com";
        return new com.aliyun.cdn20180510.Client(config);
    }

}
