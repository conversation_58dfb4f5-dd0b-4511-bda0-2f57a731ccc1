package com.fmb.util;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * author
 */
public class FmbNumberUtil {
    public static final DecimalFormat FORMATTER_PRICE = new DecimalFormat("#.##");
    /**
     *
     *  看一个数 的2位小数商四舍五入和3位小数的商四舍五入是否相等
     * @param inp
     * @param divi
     * @return
     */
    public static boolean canDivide(BigDecimal inp, Integer divi) {

        BigDecimal divide2 = inp.divide(BigDecimal.valueOf(divi),2, RoundingMode.HALF_UP);
        BigDecimal divide3 = inp.divide(BigDecimal.valueOf(divi),3, RoundingMode.HALF_UP);
        return  divide2.compareTo(divide3)==0 ;

    }

    public static double bigDecimal2Double(BigDecimal inp){
        return  inp.setScale(2,RoundingMode.HALF_UP).doubleValue() ;
    }
    public static String bigDecimal2Str(BigDecimal inp){
        return  double2String(bigDecimal2Double(inp)) ;
    }


    public static String double2String(double in){


        String x1 = NumberUtil.decimalFormat("0.00",in) ;
        if (x1.endsWith(".00")){
            x1 = x1.substring(0,x1.length()-3);
        }

        return x1 ;


    }

    /**
     * 默认地球半径
     */
    private static double EARTH_RADIUS = 6371000;//赤道半径(单位m)

    /**
     * 转化为弧度(rad)
     * */
    private static double rad(double d)
    {
        return d * Math.PI / 180.0;
    }
    /**
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位m
     * */
    public static double GetDistance(double lon1,double lat1,double lon2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000) / 10000;
        return s;
    }


    public Integer lowestCommonMultiple(int num1 ,int num2){

        if(num1 < num2){
            int temp = num1;
            num1 = num2;
            num2 = temp;
        }
        for (int i = num1; i > 0; i++) {
            if(i % num1 == 0 && i %num2 == 0){
                return new Integer(i) ;
            }
        }
        return null ;

    }

}
