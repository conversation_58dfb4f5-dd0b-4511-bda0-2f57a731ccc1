package com.fmb.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.fmb.server2022.config.FmbServerConfig;
import de.ailis.pherialize.Mixed;
import de.ailis.pherialize.MixedArray;
import de.ailis.pherialize.Pherialize;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 *
 * 反序列化 php 字符串
 * <AUTHOR>
 * @Date: 2023/1/18 9:56 上午
 */
public class FmbPhpUtil {


    /**
     * 反序列化php 字符串
     *
     * @param in
     * @return
     */
    public static ArrayList<HashMap<String, Object>> unserialize2ListOfMap(String in) {

        ArrayList<HashMap<String, Object>> list = new ArrayList<>();
        MixedArray array = Pherialize.unserialize(in).toArray();

        for (Object key : array.keySet()) {

            Mixed o = (Mixed) array.get(key);
            HashMap<String, Object> resultMap = new HashMap<>();
            for (Object o1 : o.toArray().keySet()) {
                resultMap.put(o1.toString(), o.toArray().get(o1).toString());
            }
            list.add(resultMap);
        }

        return list;
    }


    public static ArrayList<String> unserialize2ListOfString(String in) {

        ArrayList<String> list = new ArrayList<>();
        MixedArray array = Pherialize.unserialize(in).toArray();

        for (Object key : array.keySet()) {

            Mixed o = (Mixed) array.get(key);

            list.add(o.getValue().toString());
        }

        return list;
    }






    public static String fmb_encode(String arg) {
        byte[] b = arg.getBytes(StandardCharsets.UTF_8);
        int m = b.length;
        byte[] encodedBytes = new byte[m];
        for (int i = 0; i < m; i++) {
            encodedBytes[m - i - 1] = (byte) (b[i] + 2);
        }
        String result = Base64.encode(encodedBytes);
        return result;
    }


    public static String fmb_decode(String arg) {
        byte[] decodedBytes = Base64.decode(arg);
        int m = decodedBytes.length;
        byte[] b = new byte[m];

        for (int i = 0; i < m; i++) {
            b[i] = (byte) (decodedBytes[m - i - 1] - 2);
        }

        String result = new String(b, StandardCharsets.UTF_8);
        return result;
    }

}
