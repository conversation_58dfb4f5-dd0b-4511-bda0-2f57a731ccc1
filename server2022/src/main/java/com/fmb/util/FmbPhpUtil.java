package com.fmb.util;

import de.ailis.pherialize.Mixed;
import de.ailis.pherialize.MixedArray;
import de.ailis.pherialize.Pherialize;

import java.util.ArrayList;
import java.util.HashMap;

/**
 *
 * 反序列化 php 字符串
 * <AUTHOR>
 * @Date: 2023/1/18 9:56 上午
 */
public class FmbPhpUtil {


    /**
     * 反序列化php 字符串
     *
     * @param in
     * @return
     */
    public static ArrayList<HashMap<String, Object>> unserialize2ListOfMap(String in) {

        ArrayList<HashMap<String, Object>> list = new ArrayList<>();
        MixedArray array = Pherialize.unserialize(in).toArray();

        for (Object key : array.keySet()) {

            Mixed o = (Mixed) array.get(key);
            HashMap<String, Object> resultMap = new HashMap<>();
            for (Object o1 : o.toArray().keySet()) {
                resultMap.put(o1.toString(), o.toArray().get(o1).toString());
            }
            list.add(resultMap);
        }

        return list;
    }


    public static ArrayList<String> unserialize2ListOfString(String in) {

        ArrayList<String> list = new ArrayList<>();
        MixedArray array = Pherialize.unserialize(in).toArray();

        for (Object key : array.keySet()) {

            Mixed o = (Mixed) array.get(key);

            list.add(o.getValue().toString());
        }

        return list;
    }

}
