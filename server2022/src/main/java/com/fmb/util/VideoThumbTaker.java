package com.fmb.util;

import com.fmb.server2022.config.FmbServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

public class VideoThumbTaker {

    private static Logger logger = LoggerFactory.getLogger(VideoThumbTaker.class);




    /**
     * 根据视频文件路径 生成对应的关键帧 截图
     * @param videoFilename  视频文件绝对路径
     * @param outFile  视频文件关键帧图片 文件绝对地址
     * @return
     */
    public static HashMap<String, String> readVideAndCreateKeySnap(String videoFilename, String outFile) {

        HashMap<String, String> result = new HashMap<>();

        result.put("result","0") ;

//        final String outFile = videoFilename+System.currentTimeMillis() + ".jpg";
        ProcessBuilder processBuilder = new ProcessBuilder(FmbServerConfig.getFfmpegPath(),
                "-i", videoFilename ,"-vf","select='eq(pict_type,I)'","-vsync","2" ,"-f","image2", outFile);

        Process process = null;
        try {
            process = processBuilder.start();
            final File file = new File(outFile);
            int check = 0 ;
            while (!file.exists() && check<10) {
                TimeUnit.MILLISECONDS.sleep(500);
                check ++ ;
            }
            if (file.exists()){
                result.put("result","1") ;
                result.put("img",outFile) ;
            }
        } catch (Exception ex) {
            long errorid = SnowflakeIdWorker.getInstance().nextId();
            logger.error("errorid " + errorid, ex);
        }

        return result ;
    }


}
