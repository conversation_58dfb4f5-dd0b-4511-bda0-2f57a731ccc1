package com.fmb.util;

import org.springframework.util.Base64Utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;

public class AuthCodeUtil {
    public enum DiscuzAuthcodeMode {
        Encode, Decode
    }


    /// <summary>
    /// 从字符串的指定位置截取指定长度的子字符串
    /// </summary>
    /// <param name="str">原字符串</param>
    /// <param name="startIndex">子字符串的起始位置</param>
    /// <param name="length">子字符串的长度</param>
    /// <returns>子字符串</returns>
    static String cutString(String str, int startIndex, int length) {
        if (startIndex >= 0) {
            if (length < 0) {
                length = length * -1;
                if (startIndex - length < 0) {
                    length = startIndex;
                    startIndex = 0;
                } else {
                    startIndex = startIndex - length;
                }
            }

            if (startIndex > str.length()) {
                return "";
            }

        } else {
            if (length < 0) {
                return "";
            } else {
                if (length + startIndex > 0) {
                    length = length + startIndex;
                    startIndex = 0;
                } else {
                    return "";
                }
            }
        }

        if (str.length() - startIndex < length) {

            length = str.length() - startIndex;
        }

        return str.substring(startIndex, startIndex + length);
    }

    /// <summary>
    /// 从字符串的指定位置开始截取到字符串结尾的了符串
    /// </summary>
    /// <param name="str">原字符串</param>
    /// <param name="startIndex">子字符串的起始位置</param>
    /// <returns>子字符串</returns>
    static String cutString(String str, int startIndex) {
        return cutString(str, startIndex, str.length());
    }


    /// <summary>
    /// 用于 RC4 处理密码
    /// </summary>
    /// <param name="pass">密码字串</param>
    /// <param name="kLen">密钥长度，一般为 256</param>
    /// <returns></returns>
    static private byte[] GetKey(byte[] pass, int kLen) {
        byte[] mBox = new byte[kLen];

        for (int i = 0; i < kLen; i++) {
            mBox[i] = (byte) i;
        }

        int j = 0;
        for (int i = 0; i < kLen; i++) {

            j = (j + (mBox[i] + 256) % 256 + pass[i % pass.length]) % kLen;

            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;
        }

        return mBox;
    }

    /// <summary>
    /// 生成随机字符
    /// </summary>
    /// <param name="lens">随机字符长度</param>
    /// <returns>随机字符</returns>
    static String RandomString(int lens) {
        char[] CharArray = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's',
                't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        int clens = CharArray.length;
        String sCode = "";
        Random random = new Random();
        for (int i = 0; i < lens; i++) {
            sCode += CharArray[Math.abs(random.nextInt(clens))];
        }
        return sCode;
    }


    /// <summary>
    /// 使用 Discuz authcode 方法对字符串加密
    /// </summary>
    /// <param name="source">原始字符串</param>
    /// <param name="key">密钥</param>
    /// <returns>加密结果</returns>
    static String authcodeEncode(String source, String key) {
        return authcode(source, key, DiscuzAuthcodeMode.Encode, 0);

    }

    /// <summary>
    /// 使用 Discuz authcode 方法对字符串解密
    /// </summary>
    /// <param name="source">原始字符串</param>
    /// <param name="key">密钥</param>
    /// <returns>解密结果</returns>
    static String authcodeDecode(String source, String key) {
        return authcode(source, key, DiscuzAuthcodeMode.Decode, 0);

    }

    /// <summary>
    /// 使用 变形的 rc4 编码方法对字符串进行加密或者解密
    /// </summary>
    /// <param name="source">原始字符串</param>
    /// <param name="key">密钥</param>
    /// <param name="operation">操作 加密还是解密</param>
    /// <param name="expiry">加密字串过期时间</param>
    /// <returns>加密或者解密后的字符串</returns>
    private static String authcode(String source, String key, DiscuzAuthcodeMode operation, int expiry) {
        try {
            if (source == null || key == null) {
                return "";
            }

            int ckey_length = 4;
            String keya, keyb, keyc, cryptkey, result;

            key = MD52(key);

            keya = MD52(cutString(key, 0, 16));

            keyb = MD52(cutString(key, 16, 16));

            keyc = ckey_length > 0 ? (operation == DiscuzAuthcodeMode.Decode ? cutString(source, 0, ckey_length)
                    : RandomString(ckey_length)) : "";

            cryptkey = keya + MD52(keya + keyc);

            if (operation == DiscuzAuthcodeMode.Decode) {
                byte[] temp;
                temp = Base64Utils.decodeFromString(cutString(source, ckey_length));
                result = new String(RC4(temp, cryptkey));
                if (cutString(result, 10, 16).equals(cutString(MD52(cutString(result, 26) + keyb), 0, 16))) {
                    return cutString(result, 26);
                } else {
                    temp = Base64Utils.decodeFromString(cutString(source + "=", ckey_length));
                    result = new String(RC4(temp, cryptkey));
                    if (cutString(result, 10, 16).equals(cutString(MD52(cutString(result, 26) + keyb), 0, 16))) {
                        return cutString(result, 26);
                    } else {
                        temp = Base64Utils.decodeFromString(cutString(source + "==", ckey_length));
                        result = new String(RC4(temp, cryptkey));
                        if (cutString(result, 10, 16).equals(cutString(MD52(cutString(result, 26) + keyb), 0, 16))) {
                            return cutString(result, 26);
                        } else {
                            return "2";
                        }
                    }
                }
            } else {
                source = "0000000000" + cutString(MD52(source + keyb), 0, 16) + source;

                byte[] temp = RC4(source.getBytes("GBK"), cryptkey);

                return keyc + Base64Utils.encodeToString(temp);

            }
        } catch (Exception e) {
            return "";
        }

    }

    // / <summary>
    // / RC4 原始算法
    // / </summary>
    // / <param name="input">原始字串数组</param>
    // / <param name="pass">密钥</param>
    // / <returns>处理后的字串数组</returns>
    private static byte[] RC4(byte[] input, String pass) {
        if (input == null || pass == null) {
            return null;
        }

        byte[] output = new byte[input.length];
        byte[] mBox = GetKey(pass.getBytes(), 256);

        // 加密
        int i = 0;
        int j = 0;

        for (int offset = 0; offset < input.length; offset++) {
            i = (i + 1) % mBox.length;
            j = (j + (mBox[i] + 256) % 256) % mBox.length;

            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;
            byte a = input[offset];

            // byte b = mBox[(mBox[i] + mBox[j] % mBox.Length) % mBox.Length];
            // mBox[j] 一定比 mBox.Length 小，不需要在取模
            byte b = mBox[(toInt(mBox[i]) + toInt(mBox[j])) % mBox.length];

            output[offset] = (byte) ((int) a ^ toInt(b));
        }

        return output;
    }

    static String MD52(String MD5) {
        StringBuffer sb = new StringBuffer();
        String part = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5 = md.digest(MD5.getBytes());

            for (int i = 0; i < md5.length; i++) {
                part = Integer.toHexString(md5[i] & 0xFF);
                if (part.length() == 1) {
                    part = "0" + part;
                }
                sb.append(part);
            }

        } catch (NoSuchAlgorithmException ex) {
        }
        return sb.toString();

    }

    static int toInt(byte b) {
        return (b + 256) % 256;
    }


//    public static void main(String[] args) {
//        String afStr = AuthCodeUtil.fmbAidAuthCodeEncode("32995");
//        System.out.println("--------encode:" + afStr);
//        String uid = AuthCodeUtil.fmbAidAuthCodeDecode(afStr);
//        System.out.println("--------decode:" + uid);
//
////        System.out.println(AuthCodeUtil.fmbAidAuthCodeDecode("04ebIzZd62y3Eq33OtcHSFJEFvofu//oOAzVe4ucn0EU/e8OgM7mUEo3sebkQ5MqjKp7WgK2mDC/eX3Lt4" +
////            "/RHtbz1g7UHEOfAMakzw"));
//    }

    private final static String KEY_RIGHT = "cc6689ef06599daf87bc15386e02f48c";
    private final static String KEY = "xihuowo";

    public static String fmbAuthCodeEncode(String uid) {
        StringBuffer bu = new StringBuffer();
        bu.append(System.currentTimeMillis());
        bu.reverse();
        return authcodeEncode(bu.toString().substring(0, 8) + uid + KEY_RIGHT, KEY);
    }


    public static String fmbAuthCodeDecode(String s) {
        String deStr1 = AuthCodeUtil.authcodeDecode(s, KEY);
        deStr1 = deStr1.replace(KEY_RIGHT, "");
        deStr1 = deStr1.substring(8);
        return deStr1;
    }
}
