package com.fmb.util.upload;

import cn.hutool.core.util.RandomUtil;
import com.fmb.server2022.config.FmbServerConfig;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public class UploadFileUtil {

    public static String prepareFilePath(String saveBaseDir,String type) {
        return prepareFilePath(saveBaseDir,type,"xls");
    }
    public static String prepareFilePath(String saveBaseDir,String type,String ext) {
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String dateStr = df.format(Calendar.getInstance().getTime());

        String saveFileName = type+"_"+System.currentTimeMillis() + "."+ext;
        String dateDir = saveBaseDir + dateStr;
        File fDateDir = new File(dateDir);
        if (!fDateDir.exists()) {
            fDateDir.mkdirs();
        }
        return dateDir + "/" + saveFileName;
    }


    /**
     *
     * @param saveBaseDir
     * @param baseurl
     * @param type 文件前缀
     * @param ext  后缀
     * @return
     */
    public static UploadFileOutInfo prepareFilePath(String saveBaseDir, String baseurl, String type, String ext){

        return  prepareFilePath(saveBaseDir,baseurl,type,System.currentTimeMillis()+""+"_"+ RandomUtil.randomNumbers(6),ext) ;
    }

    public static UploadFileOutInfo preparePath(String type, String ext){
        return  prepareFilePath(FmbServerConfig.getFmbImgDir(),FmbServerConfig.getFmbImgUrl(),type,System.currentTimeMillis()+"",ext) ;
    }


    public static UploadFileOutInfo prepareFilePath(String saveBaseDir, String baseurl, String type, String name2, String ext){

        UploadFileOutInfo info = new UploadFileOutInfo() ;


        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        String dateStr = df.format(Calendar.getInstance().getTime());

        String saveFileName = type+"_"+ name2 ;

        if (StringUtils.isNotBlank(ext)) {
            saveFileName = saveFileName +"."+ext;
        }

        String dateDir = saveBaseDir + dateStr;
        File fDateDir = new File(dateDir);
        if (!fDateDir.exists()) {
            fDateDir.mkdirs();
        }

        String fullPath = dateDir + File.separator + saveFileName;
        String relativePath = fullPath.replace(saveBaseDir, "");
        info.setFilePath(fullPath);
        info.setRelativePath(relativePath);
        info.setOutUrl(baseurl+"/"+ relativePath);

        return  info ;

    }
}
