package com.fmb.util.upload;


public class phpSerializer {



    public static void getUnserializeList(String content) throws Exception {
//        content = "a:5:{i:0;a:3:{s:5:\"title\";s:8:\"活动ID\";s:5:\"label\";s:3:\"aid\";s:5:\"isImg\";s:1:\"N\";}i:1;a:3:{s:5:\"title\";s:15:\"券组ID_数量\";s:5:\"label\";s:6:\"coupon\";s:5:\"isImg\";s:1:\"N\";}i:2;a:3:{s:5:\"title\";s:12:\"开始日期\";s:5:\"label\";s:10:\"begin_time\";s:5:\"isImg\";s:1:\"N\";}i:3;a:3:{s:5:\"title\";s:12:\"结束日期\";s:5:\"label\";s:8:\"end_time\";s:5:\"isImg\";s:1:\"N\";}i:4;a:3:{s:5:\"title\";s:19:\"推荐aid_排序值\";s:5:\"label\";s:9:\"recommend\";s:5:\"isImg\";s:1:\"N\";}}";
//        PHPSerializer p = new PHPSerializer();
//        AssocArray array = (AssocArray) p.unserialize(content.getBytes());
//        for (int i = 0; i < array.size(); i++) {
//            System.out.println(array.get(i));
////            Test t = (Test) Cast.cast(array.get(i), Test.class);
////            list.add(t);
//        }
    }

}
