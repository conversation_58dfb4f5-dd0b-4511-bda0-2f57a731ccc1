package com.fmb.util.upload;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @Date: 2023/7/13 6:29 下午
 */
public class FmbImgUtil {


    public static String getImageFormat(File imageFile) throws IOException {

        ImageInputStream iis = ImageIO.createImageInputStream(imageFile);
        ImageReader reader = ImageIO.getImageReaders(iis).next();
        String formatName = reader.getFormatName();
        iis.close();
        return formatName;
    }


    public static String getImageStreamFormat(InputStream imageFile) throws IOException {

        ImageInputStream iis = ImageIO.createImageInputStream(imageFile);

        String formatName = "" ;
        if (iis != null) {

            ImageReader reader = ImageIO.getImageReaders(iis).next();
            formatName = reader.getFormatName();
            iis.close();
        }

        return formatName;
    }

}
