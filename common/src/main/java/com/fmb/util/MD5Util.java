package com.fmb.util;

public class MD5Util {

    static String TYPE_ENCR = "MD5";



    public static String md5(String content) {
        {
            String s = null;
            char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
            try {
                java.security.MessageDigest md = java.security.MessageDigest.getInstance(TYPE_ENCR);
                md.update(content.getBytes());
                byte tmp[] = md.digest();
                char str[] = new char[16 * 2];
                int k = 0;
                for (int i = 0; i < 16; i++) {
                    byte byte0 = tmp[i];
                    str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                    str[k++] = hexDigits[byte0 & 0xf];
                }
                s = new String(str);

            } catch (Exception e) {
            }
            return s;
        }
    }
}
