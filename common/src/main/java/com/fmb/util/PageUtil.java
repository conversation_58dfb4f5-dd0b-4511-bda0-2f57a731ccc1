package com.fmb.util;

import lombok.Data;

@Data
public class PageUtil {

    int nowPageNo ;
    int allRecord ;
    int pageSize ;
    int totalPage ;
    int startIdx  = 0 ;

    public PageUtil(int nowPageNo, int pageSize, int allRecord) {
        this.nowPageNo = nowPageNo>0?nowPageNo:1;
        this.allRecord = allRecord>0?allRecord:0;
        this.pageSize = pageSize>0?pageSize:10;


        build() ;

    }

    private void build() {
        this.totalPage = allRecord/pageSize ;
        if(allRecord%pageSize!=0){
            this.totalPage ++ ;
        }

        if(nowPageNo>totalPage){
            nowPageNo = totalPage ;
        }

        if(nowPageNo==0){
            startIdx = 0 ;
        }else {
            startIdx = (nowPageNo-1)*pageSize ;
        }



    }
}
