package com.fmb.util;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import org.apache.commons.lang3.StringUtils;

public class FmbP6SpyLogger implements MessageFormattingStrategy {

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        String source = DynamicDataSourceContextHolder.peek();
        return StringUtils.isNotBlank(sql) ? " Consume Time：" + elapsed + " ms " + now +
                "\n db_source=["+source+"]["+connectionId+"] Execute SQL：" + sql.replaceAll("[\\s]+", " ") + "\n" : "";
    }
}