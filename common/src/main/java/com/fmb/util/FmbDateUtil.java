package com.fmb.util;

import org.joda.time.format.DateTimeFormat;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.HashMap;
import java.util.Map;

/**
 * 日期工具类
 */
public class FmbDateUtil {

    public static final DateTimeFormatter FORMATTER_FULL = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FORMATTER10 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter FORMATTER_HHmm = DateTimeFormatter.ofPattern("HH:mm");

    private static final SimpleDateFormat simpleDateFormatFull = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat simpleDateFormat10 = new SimpleDateFormat("yyyy-MM-dd");

    public static String nowDateTimeStr() {
        return LocalDateTime.now().format(FORMATTER_FULL);
    }

    public static String nowDateStr() {
        return LocalDateTime.now().format(FORMATTER10);
    }

    public static Integer getEndtimeSec(Integer day, String time) {
        if (day == null || day < 1) {
            return 0;
        }
        Integer sec = day * 24 * 3600;
        LocalTime lt = null;
        try {
            lt = LocalTime.parse(time, FORMATTER_HHmm);
            sec = sec - lt.toSecondOfDay();
        } catch (Exception e) {
        }
        return sec;
    }


    public static String localDateTime2String10(LocalDateTime in) {
        return FORMATTER10.format(in);
    }


    public static synchronized String toDate10Str(Date in) {
        return simpleDateFormat10.format(in);
    }

    public static synchronized String toDateFullStr(Date in) {
        return simpleDateFormatFull.format(in);
    }

    public static LocalDateTime dateToLocalDatetime(Date aim) {
        return aim.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    public static synchronized Date strYMD2Date(String in) {
        if (in != null && in.length() == 10) {
            try {
                return simpleDateFormat10.parse(in);
            } catch (ParseException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * date1 和 date2的 天数差
     * 要求是  yyyy-MM-dd 类型
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int dayDiff(String date1, String date2) {
        return (int) ((strYMD2Date(date1).getTime() - strYMD2Date(date2).getTime()) / (24 * 3600 * 1000));
    }


    public static Date str2Date(String in) {
        if (in != null && in.length() == 19) {
            try {
                return simpleDateFormatFull.parse(in);
            } catch (ParseException e) {
                return null;
            }
        }
        return null;
    }


    public static Date localDateTime2Date(LocalDateTime in) {
        return str2Date(FORMATTER_FULL.format(in));
    }


    public static LocalDate dateToLocalDate(String in) {

        if (in.length() == 10) {
            return LocalDate.parse(in);
        }

        return null;
    }

    public static long getLocalDatetimeMillionSeconds(LocalDateTime now) {
        return now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }


    public static Date toFullDate(String date) throws ParseException {
        return simpleDateFormatFull.parse(date);
    }

    /**
     * 返回2个时间 的 中文时间间隔描述
     *
     * @param one
     * @param two
     * @return
     */
    public static String getDistanceDateTime(Date one, Date two) {
        //天数差
        long day = 0;
        //小时数差
        long hour = 0;
        //分钟数差
        long min = 0;
        //秒数差
        long second = 0;
        //毫秒差
        long diff = 0;
        String result = "";

        long time1 = one.getTime();
        long time2 = two.getTime();
        diff = time2 - time1;
        day = diff / (24 * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * 24);
        min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        second = diff / 1000;
        String dayStr = day + "天";
        String hourStr = hour % 24 + "小时";
        String minStr = min % 60 + "分";
        String secondStr = second % 60 + "秒";
        if (day != 0) {
            result = result + dayStr;
        }
        if (hour != 0) {
            result = result + hourStr;
        }
        if (min != 0) {
            result = result + minStr;
        }
        if (second != 0) {
            result = result + secondStr;
        }
        return result;
    }

    private final static Map<String, String> DayOfWeekMap = new HashMap<String, String>() {{
        put("MONDAY", "一");
        put("TUESDAY", "二");
        put("WEDNESDAY", "三");
        put("THURSDAY", "四");
        put("FRIDAY", "五");
        put("SATURDAY", "六");
        put("SUNDAY", "日");
    }};

    public static String getDayOfWeekZh(LocalDate date) {
        String k = String.valueOf(date.getDayOfWeek());
        return "周" + DayOfWeekMap.get(k);
    }

    public static String getNearDateZh(LocalDate date) {
        LocalDate currentDate = LocalDate.now();
        Period p = Period.between(currentDate, date);
        Integer days = p.getDays();
        String re = "";
        switch (days) {
            case 0:
                re = "今天";
                break;
            case 1:
                re = "明天";
                break;
            case 2:
                re = "后天";
                break;
        }
        return re;
    }

    public static Integer compareHourMin(String time, int nowHour, int nowMinute) {
        LocalTime nowTime = LocalTime.of(nowHour, nowMinute);
        int timeHour = Integer.parseInt(time.substring(0, time.indexOf(":")));
        int timeMinute = Integer.parseInt(time.substring(time.indexOf(":") + 1));
        LocalTime timeLocal = LocalTime.of(timeHour, timeMinute);
        return nowTime.compareTo(timeLocal);
    }


    static org.joda.time.format.DateTimeFormatter formatter = DateTimeFormat.forPattern("HH:mm");

    public static boolean nowCheck(String timeString) {
        org.joda.time.LocalTime currentTime = new org.joda.time.LocalTime();
        org.joda.time.LocalTime inputTime = org.joda.time.LocalTime.parse(timeString, formatter);
        return currentTime.isBefore(inputTime) ;
    }

    /**
     * 酒店套餐里计算最小价格和库存 以及房态需要考虑当前套餐的天数
     *
     * 假设今天为6月1日，设置提前1天，17:00前可订，则表示用户在今天17:00之前可以预订到6月2号入住的产品，在今天17:00之后能预订到6月3号入住的产品
     *
     * 返回的数字 作为
     * sql 里  interval xxx  DAY  的值
     * select date_add(current_date,interval  2 DAY )
     *
     * @param suitePreDay
     * @param timeString
     * @return
     */
    public static int suiteDayNeedPreDay(int suitePreDay , String timeString){
        return suitePreDay + (nowCheck(timeString)?0:1 );
    }


}
