package com.fmb.util;

import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by addjava on 2018/5/25.
 */
public class RegStringUtil {
    public static String regSlice(String in, String pat) {
        Pattern pattern = Pattern.compile(pat);
        Matcher matcher = pattern.matcher(in);
        String s = null;
        if (matcher.find()) {
            s = matcher.group(1);
        }
        return s;
    }

    public static ArrayList<String> regSliceGroup(String in, String pat) {
        ArrayList<String> result = new ArrayList<String>();
        String expression = pat;
        Pattern pattern = Pattern.compile(expression, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(in);
        while (matcher.find()) {
            result.add(matcher.group(1));
        }
        return result;
    }
}
