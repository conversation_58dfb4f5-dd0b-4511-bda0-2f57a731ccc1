package com.fmb.basic;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class FmbConstants {

    //fmbx_thirdsys_user
    public static final Map<String, String> FMB_PAY_SOURCE;
    static {
        {
            // 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
            HashMap<String, String> map = new HashMap<>();
            map.put("alipay", "支付宝");
            map.put("alipay_fmblxsh", "支付宝");
            map.put("yeepay", "易宝支付");
            map.put("cod", "货到付款");
            map.put("yinlian", "银联支付");
            map.put("yinlian_h5", "手机银联支付");
            map.put("yinlian_fmblxsh_mobile", "手机银联支付");
            map.put("weixin", "微信支付");
            map.put("wxapp", "微信支付");
            map.put("pos", "POS支付");
            map.put("cash", "现金支付");
            map.put("bank", "银行转账");
            map.put("zhifubao", "支付宝转账");
            map.put("nopay", "无");
            map.put("prepay_balance", "预付款");
            map.put("credit_balance", "信用账户");
            map.put("yeepay_wechat", "微信支付");
            map.put("yeepay_alipay", "支付宝");
            FMB_PAY_SOURCE = Collections.unmodifiableMap(map);
        }


    }


    public static final Map<String, String> FMB_SITE;
    static {
        {
            // 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "北京");
            map.put("2", "上海");
            FMB_SITE = Collections.unmodifiableMap(map);
        }

    }


    public static final String DB_master = "master";
    public static final String DB_slave = "slave";
    public static final String DB_fmbx = "fmbx";

    public static final int REDIS_DEFAULT_DB = 4;
    public static final int REDIS_DB_1 = 1;
    public static final int REDIS_DB_10 = 10;


    public static final String CAPTCHA_CODE_KEY = "captcha:" ;


    public static final String TOKEN = "token";

    public static final String BPSTOKEN = "bpstoken";
    //管理员role 的id
    public static final Integer ADMIN_ROLE_ID = 1 ;

    public static final LocalDateTime DEFAULT_DATETIME = LocalDateTime.of(1970,1,1,8,0,0) ;

    public static final LocalDate DEFAULT_DATE = LocalDate.of(1970,1,1) ;

    public static final String LEAGAL_EMAIL = "<EMAIL>" ;
    public static final String USER_IP = "user_ip";
    public static final String REDIS_RANDOM = "fmbRandom";

    public static final String REDIS_GLOBAL_ID = "fmb_global_id";
    /**
     * 用户 权益资源 redis 锁前缀  后面要跟uid
     *
     * 1.下单时使用
     */
    public static final String USER_RESOURCE_LOCK = "user_resource_lock:";

    /**
     * 酒店库存更新 redis 锁前缀  后面直接跟roomid
     */
    public static final String HOTEL_ROOM_SKU_NUMBER = "hotelStockUpdate:";


    public static final String SKU_NUMBER_COMMON = "commonSKUStockUpdate:";
    public static final String MUTI_CHAR = "×";
    public static final String RMB_CHAR = "¥";


    //token 维持天数 14天
    public static int TOKEN_KEEP_SECONDS = 14 *24 * 60 *60 ;
    //token是存储在 redis里的 并设置了 TOKEN_KEEP_SECONDS 这么长的有效期
    // 如果 有效期小于 TOKEN_REMAIN_SECONDS_LIMIT 那么就需要给用户续期
    public static int TOKEN_REMAIN_SECONDS_LIMIT =  24 * 60 *60 ;

    public static String TOKENSETS_PRE = "user_tokens:" ;



    public static String TOKEN_PRE_OF_FMBADMIN = "fmbadmintoken:" ;
    public static final String TOKEN_NEED_UPDATE = "usertokenNeedUpdate:";



    public static int TOKEN_KEEP_SECONDS_BPS = 14 *24 * 60 *60 ;
    public static String TOKEN_PRE_OF_BPS = "bpstoken:" ;


    public static String FmbxFrontPath_PRE ="/fmbx/front";
    public static String FmbxFront_UID_KEY ="fmbx_uid";



    /**
     * 日历房
     */
    public static final int SKU_HOTEL_DATE = 1;
    public static final int SKU_HOTEL_DATE_GOODS_TYPE = 6;
    /**
     * 酒店预约
     */
    public static final int SKU_HOTEL_RESERVE = 2;
    public static final int SKU_HOTEL_RESERVE_GOODS_TYPE = 7;





}
