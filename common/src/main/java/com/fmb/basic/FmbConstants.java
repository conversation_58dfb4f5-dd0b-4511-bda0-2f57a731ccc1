package com.fmb.basic;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FmbConstants {

    //fmbx_thirdsys_user
    public static final Map<String, String> FMB_PAY_SOURCE;
    public static final String ORDERSN_STAT = "fmbx:orderStat";
    public static final String BALANCE_TIP = "1、订单金额大于1元（含）\n" +
            "2、账户返现余额大于1元（含）\n" +
            "3、抵扣后至少需支付现金1元";
    public static final int ORDER_SNAP_VERSION = 1000;
    public static String PHP_MD5_SALT_KEY ="CeSosdIQpXeXqm5geem4ltFAvp1xU2aX";

    //以前这个值是 "房券" 现在去掉.防止以后有反复.
    public static String RESERVE_SKU_NAME_POSTFIX ="";
    // 单一房券 后缀
    public static String RESERVE_SKU_NAME_SINGLE_POSTFIX ="(自选房型预约)";


    public static final List<String> REASON_LIST_ME = Arrays.asList(new String[]{"买多了",
            "买贵了",
            "信息有误需重新购买",
            "计划有变，没时间消费",
            "不想买了",
            "其他原因"});
    public static final List<String> REASON_LIST_SHOP = Arrays.asList(new String[]{"买多了",
            "服务/设施问题/评价不好",
            "无货/无房/预约不上",
            "活动取消/改期/提前截止",
            "其他原因"});
    public static final List<String> NEW_REASON_LIST = Arrays.asList(new String[]{"预约不上双休日/节假日",
            "临近有效期/已过有效期",
            "活动取消/延期/提前截止",
            "其他渠道有更优惠的价格",
            "实际与网页不符",
            "商家施设问题",
            "服务不满",
            "在其他渠道看到评论差",
            "行程变更",
            "其他原因"});

    public static final List<String> RETURN_INSTRUCTIONS= Arrays.asList(new String[]{    "1、优惠券分摊金额=（所退商品总额/该订单商品总额）x 该订单所用优惠券金额",
            "2、若订单内使用了余额抵扣，退款时优先返还余额部分，再退现金",
            "3、若订单有返现，退款时需要按比例扣除该订单对应产生的下单返现金额，优先从余额扣除，余额不够从现金扣除",
            "4、部分订单退款产生手续费，手续费会从退款金额中扣除"});

    static {
        {
            // 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
            HashMap<String, String> map = new HashMap<>();
            map.put("alipay", "支付宝");
            map.put("alipay_fmblxsh", "支付宝旅行社支付");
            map.put("yeepay", "易宝支付");
            map.put("cod", "货到付款");
            map.put("yinlian", "银联支付");
            map.put("yinlian_h5", "手机银联支付");
            map.put("yinlian_fmblxsh_mobile", "手机银联-旅行社");
            map.put("weixin", "微信支付");
            map.put("wxapp", "微信小程序");
            map.put("pos", "POS支付");
            map.put("cash", "现金支付");
            map.put("bank", "银行转账");
            map.put("zhifubao", "支付宝转账");
            map.put("nopay", "无");
            map.put("prepay_balance", "预付款支付");
            map.put("credit_balance", "信用账户支付");
            map.put("yeepay_wechat", "易宝微信支付");
            map.put("yeepay_alipay", "易宝支付宝");
            FMB_PAY_SOURCE = Collections.unmodifiableMap(map);
        }


    }


    public static final Map<String, String> FMB_SITE;
    static {
        {
            // 票务类型:1-演出,2-景点,3-通用,4-酒店,6-新酒店,7-新酒店预约
            HashMap<String, String> map = new HashMap<>();
            map.put("1", "北京");
            map.put("2", "上海");
            FMB_SITE = Collections.unmodifiableMap(map);
        }

    }


    public static final String DB_master = "master";
    public static final String DB_slave = "slave";
    public static final String DB_fmbx = "fmbx";

    public static final int REDIS_DEFAULT_DB = 4;
    public static final int REDIS_DB_1 = 1;
    public static final int REDIS_DB_10 = 10;


    public static final String CAPTCHA_CODE_KEY = "captcha:" ;


    public static final String TOKEN = "token";

    public static final String BPSTOKEN = "bpstoken";
    //管理员role 的id
    public static final Integer ADMIN_ROLE_ID = 1 ;

    public static final LocalDateTime DEFAULT_DATETIME = LocalDateTime.of(1970,1,1,0,0,0) ;

    public static final LocalDate DEFAULT_DATE = LocalDate.of(1970,1,1) ;

    public static final String LEAGAL_EMAIL = "<EMAIL>" ;
    public static final String USER_IP = "user_ip";
    public static final String REDIS_RANDOM = "fmbRandom";

    public static final String REDIS_GLOBAL_ID = "fmb_global_id";
    /**
     * 用户 权益资源 redis 锁前缀  后面要跟uid
     *
     * 1.下单时使用
     */
    public static final String USER_RESOURCE_LOCK = "user_resource_lock:";

    public static final String SKU_CHANGE_UPDATE_ACTIVITY = "SKU_CHANGE_UPDATE_ACTIVITY:";

    /**
     * 酒店库存更新 redis 锁前缀  后面直接跟roomid
     */
    public static final String HOTEL_ROOM_SKU_NUMBER = "hotelStockUpdate:";


    public static final String SKU_NUMBER_COMMON = "commonSKUStockUpdate:";
    public static final String MUTI_CHAR = "×";
    public static final String RMB_CHAR = "¥";


    //token 维持天数 14天
    public static int TOKEN_KEEP_SECONDS = 14 *24 * 60 *60 ;
    //token是存储在 redis里的 并设置了 TOKEN_KEEP_SECONDS 这么长的有效期
    // 如果 有效期小于 TOKEN_REMAIN_SECONDS_LIMIT 那么就需要给用户续期
    public static int TOKEN_REMAIN_SECONDS_LIMIT =  24 * 60 *60 ;

    public static String TOKENSETS_PRE = "user_tokens:" ;


    public static String IMPORTANT_INFO_LOGGER = "importantInfoLogger" ;




    public static String TOKEN_PRE_OF_FMBADMIN = "fmbadmintoken:" ;
    public static final String TOKEN_NEED_UPDATE = "usertokenNeedUpdate:";



    public static int TOKEN_KEEP_SECONDS_BPS = 14 *24 * 60 *60 ;
    public static String TOKEN_PRE_OF_BPS = "bpstoken:" ;


    public static String FmbxFrontPath_PRE ="/fmbx/front";
    public static String FmbxFront_UID_KEY ="fmbx_uid";

    public static String LOG_TRACEID ="traceId";



    /**
     * 日历房
     */
    public static final int SKU_HOTEL_DATE = 1;
    public static final int SKU_HOTEL_DATE_GOODS_TYPE = 6;
    /**
     * 酒店预约
     */
    public static final int SKU_HOTEL_RESERVE = 2;
    public static final int SKU_HOTEL_RESERVE_GOODS_TYPE = 7;





}
