package com.fmb.basic;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 公共返回对象枚举
 */
@Getter
@ToString
@AllArgsConstructor
public enum RespBeanEnum {
	//通用
	SUCCESS(200, "SUCCESS"),
	ERROR(500, "服务端异常"),
	//登录模块5002xx
	LOGIN_INVALID_VISIT(50002, "非法访问"),
	INVALID_REQPAR(50003, "请求参数不合法"),
	LOGIN_NO_ACCESS(50004, "无权访问"),
	LOGIN_BAD_LOGIC(50005, "业务逻辑错误"),
	FrontApiSignException(50006, "API签名校验失败"),

	;
	private final Integer code;
	private final String message;
}