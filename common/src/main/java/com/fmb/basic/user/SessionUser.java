package com.fmb.basic.user;

import lombok.Data;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
public class SessionUser {



    public  static final Map<Integer, String> userTypeMap;

    static
    {
        {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(1,"父母邦后台用户");
            map.put(2,"供应商后台用户");
            map.put(3,"父母邦前台用户");
            userTypeMap = Collections.unmodifiableMap(map);
        }
    }

    public static int SESSIONUSER_FMB = 1 ;
    public static int SESSIONUSER_BP = 2 ;
    public static int SESSIONUSER_FRONT = 3 ;

    private Integer userId;
    private String username;
    private Integer userType;

    public SessionUser(Integer userId, String username, Integer type) {
        this.userId = userId;
        this.username = username;
        this.userType = type;
    }

    public SessionUser() {
    }

    public String myInfo(){
        return userTypeMap.get(getUserType())+":"+getUsername()+"("+getUserId()+")"   ;
    }

}
