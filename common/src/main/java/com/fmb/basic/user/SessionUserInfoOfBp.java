package com.fmb.basic.user;

import lombok.Data;

/**
 * 保存在session中的用户信息
 */
@Data
public class SessionUserInfoOfBp implements  GenNowUser{
    private Integer userId;
    private String username;

    private Integer version = 1 ;

    @Override
    public SessionUser nowUser() {
        return new SessionUser(userId,username,SessionUser.SESSIONUSER_BP);
    }

//    private List<Integer> roleIds;
//    private Set<String> menuList;
//    private Set<String> permissionList;
}
