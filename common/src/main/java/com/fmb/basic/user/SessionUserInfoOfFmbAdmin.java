package com.fmb.basic.user;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 保存在session中的用户信息
 */
@Data
public class SessionUserInfoOfFmbAdmin implements  GenNowUser{
    private Integer userId;
    private String username;
    private String nickname;
    private String phone;

    //增加版本号. 如果后续升级 redis 里的数据 解析可能用得着
    private Integer version = 1 ;

    private List<Integer> roleIds;
    private Set<String> menuList;
    private Set<String> permissionList;


    @Override
    public SessionUser nowUser() {
        return new SessionUser(userId,username,SessionUser.SESSIONUSER_FMB);
    }
}
