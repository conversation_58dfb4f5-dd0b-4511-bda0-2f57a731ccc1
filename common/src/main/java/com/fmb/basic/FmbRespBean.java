package com.fmb.basic;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

/**
 * 公共返回对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FmbRespBean {

    private long code;
    private String message;
    private Object data;

    /**
     * 功能描述: 成功返回结果
     */
    public static FmbRespBean success() {

        return new FmbRespBean(RespBeanEnum.SUCCESS.getCode(), RespBeanEnum.SUCCESS.getMessage(), null);
    }

    /**
     * 功能描述: 成功返回结果
     */
    public static FmbRespBean success(Object obj) {
        return new FmbRespBean(RespBeanEnum.SUCCESS.getCode(), FmbRespBean.success().getMessage(), obj);
    }

    public static FmbRespBean success(String key,Object obj) {
        HashMap<String, Object> data = new HashMap<>();

        data.put(key,obj) ;

        return new FmbRespBean(RespBeanEnum.SUCCESS.getCode(), FmbRespBean.success().getMessage(), data);
    }


    /**
     * 功能描述: 失败返回结果
     */
    public static FmbRespBean error(RespBeanEnum respBeanEnum) {
        return new FmbRespBean(respBeanEnum.getCode(), respBeanEnum.getMessage(), null);
    }


    /**
     * 功能描述: 失败返回结果
     */
    public static FmbRespBean error(RespBeanEnum respBeanEnum, Object obj) {
        return new FmbRespBean(respBeanEnum.getCode(), respBeanEnum.getMessage(), obj);
    }

}