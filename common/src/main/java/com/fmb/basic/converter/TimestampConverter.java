package com.fmb.basic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

import java.sql.Timestamp;

public class TimestampConverter implements Converter<Timestamp> {

    public static final String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";
    @Override
    public Class supportJavaTypeKey() {
        return Timestamp.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Timestamp convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new Timestamp( DateTime.parse(cellData.getStringValue(),DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")).toDate().getTime());
    }

    @Override
    public CellData convertToExcelData(Timestamp timestamp, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        DateTime dateTime = new DateTime(timestamp.getTime());
        return new CellData<>(dateTime.toString(STANDARD_FORMAT));
    }
}
