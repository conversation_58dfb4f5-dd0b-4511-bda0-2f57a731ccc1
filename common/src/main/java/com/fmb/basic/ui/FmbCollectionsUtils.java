package com.fmb.basic.ui;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FmbCollectionsUtils {
    public static List<FmbSelectOption> mapToList(Map<Integer,String> source_typeMap) {

        List<FmbSelectOption> collect = source_typeMap.entrySet().stream().map(ent -> {
            FmbSelectOption fmbSelectOption = new FmbSelectOption();

            fmbSelectOption.setValue(ent.getKey().toString());
            fmbSelectOption.setKey(ent.getValue());

            return fmbSelectOption;
        }).collect(Collectors.toList());

        collect.sort((o1, o2) -> Integer.parseInt(o1.getValue())-Integer.parseInt(o2.getValue()));

        return collect ;

    }
}
